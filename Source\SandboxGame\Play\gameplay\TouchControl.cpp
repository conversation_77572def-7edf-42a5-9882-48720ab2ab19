#include "TouchControl.h"
#include "PlayerControl.h"
#include "Components/Camera.h"
#include "GameCamera.h"
#include "GunUseComponent.h"
#include "CustomGunUseComponent.h"
#include "Platforms/PlatformInterface.h"
#include "ui_framemgr.h"
#include "xml_uimgr.h"
#include "InputInfo.h"
#include "RecordPkgManager.h"
#include "UILib/ui_scriptfunc.h"
#include "ISnapshotInterface.h"
#include "Misc/GameSetting.h"
#include "GameUI.h"
#include "Graphics/ScreenManager.h"
#include "AdventureGuideMgrProxy.h"
#include "UIRenderer.h"
#include "blocks/special_blockid.h"
#include "PlayManagerInterface.h"
#include "ClientInfoProxy.h"
#include "backpack.h"
#include "LuaInterfaceProxy.h"
#include "OgreTimer.h"
#include "RiddenComponent.h"
#include "ClientActorFuncWrapper.h"
#include "PlayerAttrib.h"
#include "ItemIconManager.h"
#include "ActorVehicleAssemble.h"
#include "VehicleControlInputs.h"
#include "container_driverseat.h"
#include "container_peristele.h"
#include "VehicleWorld.h"

//IMPLEMENT_GETMETHOD_MANUAL_INIT(TouchControl)

static int newGunjumpBtnOffset = -20;

using namespace MNSandbox;
using namespace MINIW;
using namespace Rainbow;
using namespace Rainbow::UILib;
const float MOVE_WIDTH = 228;
const float DEF_SCRW = WIN_DEF_SCRW;
const float DEF_SCRH = WIN_DEF_SCRH;
static float s_ScreenScale = 1.0f;

static const Rainbow::Vector2f VehicleKeyPos[21] =
{
	Vector2f(0,0),
	Vector2f(0,0),
	Vector2f(0,0),
	Vector2f(0,0),
	Vector2f(424,700),
	Vector2f(503,700),
	Vector2f(582,700),
	Vector2f(661,700),
	Vector2f(740,700),
	Vector2f(819,700),
	Vector2f(898,700),
	Vector2f(977,700),
	Vector2f(1068,537),
	Vector2f(1162,451),
	Vector2f(1259,451),
	Vector2f(1162,543),
	Vector2f(1259,543),
	Vector2f(1102,621),
	Vector2f(1102,713),
	Vector2f(1226,670),
	Vector2f(22,325),
};

inline int XFromLeft(int x)
{
	return GetGameUIPtr()->GetSafeArea().GetLeft() + int(x * s_ScreenScale);
}
inline int YFromTop(int y)
{
	return GetGameUIPtr()->GetSafeArea().GetTop() + int(y * s_ScreenScale);
}
inline int XFromRight(int x)
{
	return GetGameUIPtr()->GetSafeArea().GetRight() - int(x * s_ScreenScale);
}
inline int YFromBottom(int y)
{
	return GetGameUIPtr()->GetSafeArea().GetBottom() - int(y * s_ScreenScale);
}

MINIW::Point2D CalMovePointInRange(const MINIW::Point2D& start, const MINIW::Point2D& curpos, int range)
{
	float dx = (float)(curpos.x - start.x);
	float dy = (float)(curpos.y - start.y);
	float r = Sqrt(dx * dx + dy * dy);
	if (r > range)
	{
		dx = range * dx / r;
		dy = range * dy / r;
	}

	return Point2D((int)(start.x + dx), (int)(start.y + dy));
}

/*
// Not used.
int GetFireCircle(int& cx, int& cy, float scale)
{
	int bgw = int(169 * scale);
	cx = XFromRight(161 + 169 / 2);
	cy = YFromBottom(85 + 169 / 2);
	return bgw;
}

bool IsInFireCircle(int x, int y, float scale)
{
	int cx, cy;
	int w = GetFireCircle(cx, cy, scale);
	int dx = x - cx;
	int dy = y - cy;
	return dx * dx + dy * dy <= w * w / 4;
	//	return int(Sqrt((float)((x-cx)*(x-cx) + (y-cy)*(y-cy)))) < w/2;
}
*/

bool IsInJumpCircle(int x, int y, float scale)
{
	return false;
	//bool newGun = false;
	//if (g_pPlayerCtrl && g_pPlayerCtrl->m_GunHoleState == GunHoldState::CUSTOMGUN)
	//	newGun = true;

	//int cx = newGun ? XFromRight(127 + newGunjumpBtnOffset) : XFromRight(113);
	//int cy = newGun ? YFromBottom(115 + newGunjumpBtnOffset) : YFromBottom(103);
	//int dx = x - cx;
	//int dy = y - cy;
	//int r = newGun ? 60 : 75;
	//return dx * dx + dy * dy <= int(r * r * scale * scale);
	//	return int(Sqrt((float)((x-cx)*(x-cx) + (y-cy)*(y-cy)))) < 75*scale;
}

bool IsInSneakCircle(int x, int y, float scale)
{
	bool newGun = false;
	if (g_pPlayerCtrl && g_pPlayerCtrl->m_GunHoleState == GunHoldState::CUSTOMGUN)
		newGun = true;

	// 下蹲按钮位置在跳跃按钮下方80像素
	int cx = newGun ? XFromRight(127 + newGunjumpBtnOffset) : XFromRight(113);
	int cy = newGun ? YFromBottom(115 + newGunjumpBtnOffset + 80) : YFromBottom(103 + 80);
	int dx = x - cx;
	int dy = y - cy;
	int r = newGun ? 60 : 75;
	return dx * dx + dy * dy <= int(r * r * scale * scale);
}

bool IsInRunCircle(int x, int y, float scale)
{
	// 奔跑按钮位置在摇杆上方
	int cx = XFromLeft(133);
	int cy = YFromBottom(151 + 120); // 摇杆上方120像素
	int dx = x - cx;
	int dy = y - cy;
	int r = 60; // 奔跑按钮半径
	return dx * dx + dy * dy <= int(r * r * scale * scale);
}

bool IsInAimCircle(int x, int y, float scale)
{
	bool newGun = false;
	if (g_pPlayerCtrl && g_pPlayerCtrl->m_GunHoleState == GunHoldState::CUSTOMGUN)
		newGun = true;

	// 瞄准按钮位置在跳跃按钮上方80像素
	int cx = newGun ? XFromRight(127 + newGunjumpBtnOffset) : XFromRight(113);
	int cy = newGun ? YFromBottom(115 + newGunjumpBtnOffset - 80) : YFromBottom(103 - 80);
	int dx = x - cx;
	int dy = y - cy;
	int r = newGun ? 60 : 75;
	return dx * dx + dy * dy <= int(r * r * scale * scale);
}

bool TouchControl::IsInUseCircle(int x, int y, float scale)
{
	if (m_ShowBlueprintUseBtn)
	{
		return false; //显示蓝图的时候返回false
	}
	if (m_bHideActionUi)
	{
		return false; //隐藏UI时，返回false
	}

	static int cx = 0;
	static int cy = 0;
	const int radius = 100 * scale;
	if (cx == 0)
	{
		MINIW::ScriptVM::game()->callFunction("GetRightActionBtnPos", ">ii", &cx, &cy);
		if (scale < 1.0f)
		{
			cx = cx * scale;
			cy = cy * scale;
		}
		cx += radius;
		cy += radius;
	}

	return (int(Sqrt((float)((x - cx) * (x - cx) + (y - cy) * (y - cy)))) < radius);

	// int cx = XFromRight(317);
	// int cy = YFromBottom(291);

	// // int x1 = XFromLeft(50);
	// // int y1 = YFromTop(300);
	
	// if (m_ShowGunUseBtn)
	// {
	// 	return (int(Sqrt((float)((x - cx)*(x - cx) + (y - cy)*(y - cy)))) < 100 * scale);// || (int(Sqrt((float)((x - x1)*(x - x1) + (y - y1)*(y - y1)))) < 70 * scale);
	// }

	// return (int(Sqrt((float)((x - cx)*(x - cx) + (y - cy)*(y - cy)))) < 100 * scale);
}

int TouchControl::IsInLeftOrRight(int x, int y, float scale)
{
	// int cx = XFromRight(317*scale);
	// int cy = YFromBottom(291*scale);

	// if ((int(Sqrt((float)((x - cx) * (x - cx) + (y - cy) * (y - cy)))) < 70 * scale))
	// 	m_ShootPosition = 1;
	// else
	// 	m_ShootPosition = 0;

	m_ShootPosition = 1;

	return m_ShootPosition;
}

bool IsInGunReloadCircle(int x, int y, float scale)
{
	return false;
	/*int cx = XFromRight(239);
	int cy = YFromBottom(400);

	return int(Sqrt((x - cx)*(x - cx) + (y - cy)*(y - cy))) < 70 * scale;*/
}

bool IsInPassOrCatchBallCirle(int x, int y, float scale)
{
	int cx = XFromRight(66);
	int cy = YFromBottom(233);

	return (int(Sqrt((float)((x - cx) * (x - cx) + (y - cy) * (y - cy)))) < 70 * scale);
}

int CheckFlyArea(int x, int y, float scale)
{
	int tlx = XFromRight(113);
	int tly = YFromBottom(335);

	if (x >= tlx && x <= tlx + 110 * scale && y >= tly && y <= tly + 80 * scale)
	{
		return 1;
	}

	tly = YFromBottom(165);
	if (x >= tlx && x <= tlx + 110 * scale && y >= tly && y <= tly + 80 * scale)
	{
		return -1;
	}

	return 0;
}

/*
// Not used.
int GetHideBtn(int& rx, int& ry, float scale)
{
	int bgw = int(53 * scale);
	rx = XFromRight(26 + 53 / 2);
	ry = (int)(53 * scale - bgw / 2);
	return bgw;
}

bool IsInHideBtn(int x, int y, float scale)
{
	int rx, ry;
	int w = GetFireCircle(rx, ry, scale);

	return int(Sqrt((float)((x - rx) * (x - rx) + (y - ry) * (y - ry)))) < w / 2;
}
*/

bool IsInDeveloperBtn(int x, int y, MINIW::Point2D resPos, int size)
{
	int w = UILib::GetScreenUIScale() * size;
	x -= GetGameUIPtr()->GetSafeArea().GetLeft();
	return int(Sqrt((float)((x - resPos.x) * (x - resPos.x) + (y - resPos.y) * (y - resPos.y)))) < w / 2;
}

TouchControl::TouchControl() : tapPosX(0),
tapPosY(0),
m_ReloadTime(0),
m_RotateSubType(0),
m_showCrosshair(false),
m_showOperateUI(false),
m_RockerTouchDx(0),
m_TipDuration(0), m_TipTrans(0),
m_AimToColorableObject(false),
m_InterType(0),
m_fBaskeBallFocusX(0),
m_fBaskeBallFocusY(0), m_iAutoFireState(0), m_iAutoAimTime(0), m_iMaxAimTime(1),
m_JoystickDirx(0), m_JoystickDiry(0),m_DpadValueVertical(0), m_DpadValueHorizontal(0)
{
	m_MoveID = -1;
	m_JumpID = -1;
	m_JumpTime = -1;
	m_SneakID = -1;
	m_SneakTime = -1;
	m_SneakState = false; // 默认关闭下蹲
	m_RunID = -1;
	m_RunState = false; // 默认关闭奔跑
	m_AimID = -1;
	m_AimState = false; // 默认关闭瞄准
	m_UseID = -1;
	m_UseTime = -1;
	m_ShootPosition = -1;
	m_FlyID = -1;
	m_FlyType = 0;
	m_PassOrCatchBallID = -1;
	m_PassOrCatchTime = -1;
	m_RotateID = -1;
	m_RotateTime = -1;
	m_ReloadID = -1;
	m_BarDirection = 1;
	m_sensityvity = 3;
	m_isSetSensitivity = false;
	m_reversalY = false;
	m_sightModel = false;
	m_drawSight = true;
	m_rockerControl = true;
	m_isForward = true;
	m_isCanMove = false;
	m_beforeDir = 0;
	m_ControlJumpTime = 0;
	m_ShowUseBtn = false;
	m_ShowGunUseBtn = false;
	m_ShowBallUseBtn = false;
	m_ShowPushSnowBallUseBtn = false;
	m_ShowPushSnowBallMakeBtn = false;
	m_ShowBasketBallUseBtn = false,
		m_GuideTick = -1;
	m_AccumulatorProgress = -1;
	m_BasketBallLockState = false;
	m_BasketBallLockTime = 0;

	m_JumpSubType = 0;
	m_UseSubType = 0;

	// m_ScreenScaleFactor = UILib::GetScreenUIScale();
	const Rainbow::TRect<int>& safeArea = GetGameUIPtr()->GetSafeArea();
	float scalex = static_cast<float>(safeArea.GetWidth()) / 1920;
	float scaley = static_cast<float>(safeArea.GetHeight()) / 1080;
	if (scalex > scaley) m_ScreenScaleFactor = scaley;
	else m_ScreenScaleFactor = scalex;

	UIRenderer& uiRenderer = UIRenderer::GetInstance();
	m_TouchRes = uiRenderer.CreateTexture("ui/mobile/ui.png");
	m_TouchRes1 = uiRenderer.CreateTexture("ui/mobile/ui2.png");
	m_TouchRes2 = uiRenderer.CreateTexture("ui/mobile/effect/ui_button_2.png");
	m_TouchRes3 = uiRenderer.CreateTexture("ui/mobile/ui4.png");

	//提取公共文件夹后，operate皮肤图集被放到texture0了
	m_TouchRes4 = uiRenderer.CreateTexture("ui/mobile/texture0/operate.png");
	m_TouchRes5 = uiRenderer.CreateTexture("ui/mobile/texture2/vehicle.png");
	m_TouchRes6 = uiRenderer.CreateTexture("ui/mobile/texture0/ingame.png");
	m_SightingTeleScope = uiRenderer.CreateTexture("ui/mobile/texture0/bigtex/img_sighting_telescope.png");

	m_ColorableUIRes = uiRenderer.CreateTexture("ui/cursor/czjm_colorable.png");

	m_TouchRes1Material = uiRenderer.CreateInstance();
	m_TouchRes2Material = uiRenderer.CreateInstance();
	m_TouchRes3Material = uiRenderer.CreateInstance();
	m_TouchRes4Material = uiRenderer.CreateInstance();
	m_TouchRes5Material = uiRenderer.CreateInstance();
	m_TouchRes6Material = uiRenderer.CreateInstance();
	m_SightingTeleScopeMaterial = uiRenderer.CreateInstance();

	m_BaojiUIResMaterial = uiRenderer.CreateInstance();
	m_DeadUIResMaterial = uiRenderer.CreateInstance();
	m_NormalUIResMaterial = uiRenderer.CreateInstance();
	m_CursorHandleMaterial = uiRenderer.CreateInstance();
	m_IconMaterial = uiRenderer.CreateInstance();

	material_newGunNormal = uiRenderer.CreateInstance();
	material_newGunHead = uiRenderer.CreateInstance();
	material_newGunDeath = uiRenderer.CreateInstance();

	m_ButtonMap[BUTTON_JUMP] = false;
	m_ButtonMap[BUTTON_ACTION] = false;
	m_ButtonMap[BUTTON_SNEAK] = false;
	m_ButtonMap[BUTTON_RUN] = false;
	m_ButtonMap[BUTTON_GUNAIM] = false;
	m_ButtonMap[BUTTON_FLYUP] = false;
	m_ButtonMap[BUTTON_FLYDOWN] = false;

	m_ButtonDownMap[BUTTON_SNEAK] = false;
	m_ButtonDownMap[BUTTON_SCREENTAP] = false;
	m_ButtonDownMap[BUTTON_ACTION] = false;
	m_ButtonDownMap[BUTTON_JUMP] = false;
	m_ButtonDownMap[BUTTON_RUN] = false;
	m_ButtonDownMap[BUTTON_GUNAIM] = false;

	m_ButtonUpMap[BUTTON_SNEAK] = false;
	m_ButtonUpMap[BUTTON_SCREENTAP] = false;
	m_ButtonUpMap[BUTTON_ACTION] = false;
	m_ButtonUpMap[BUTTON_JUMP] = false;
	m_ButtonUpMap[BUTTON_RUN] = false;

	m_ButtonUpMap[BUTTON_GUNRELOAD] = false;
	m_ButtonUpMap[BUTTON_GUNAIM] = false;

	m_ButtonMap[BUTTON__PASSBALL_OR_CATCHBALL] = false;
	m_ButtonDownMap[BUTTON__PASSBALL_OR_CATCHBALL] = false;
	m_ButtonUpMap[BUTTON__PASSBALL_OR_CATCHBALL] = false;

	touchMoveDx = 0;
	touchMoveDy = 0;
	joystickJump = false;

	needRefreshButtonDownMap = false;
	needRefreshButtonUpMap = false;

	m_KeyUpMark = -1;
	m_KeyDownMark = -1;

	isLongPress = false;
	triggerLongPress = false;
	longPressTriggerMark = -1;
	longPressX = 0;
	longPressY = 0;
	longPressEnd = false;
	longPressEndMark = -1;
	joystickJump = false;

	m_ScreenDpi = GetScreenDpi();
	m_ScreenDpiInv = 1.0f / m_ScreenDpi;
	m_DpiScale = m_ScreenDpi * 1.0f / 102;

	m_DpadWidth = 228;
	m_DpadHeight = 236;
	m_RockerTouchDy = 0;

	m_BaojiUIRes = UIRenderer::GetInstance().CreateTexture("ui/cursor/czjm_baoji.png");
	m_DeadUIRes = UIRenderer::GetInstance().CreateTexture("ui/cursor/czjm_dead.png");
	m_NormalUIRes = UIRenderer::GetInstance().CreateTexture("ui/cursor/czjm_jizhong.png");

	m_newGunNormalUIRes = uiRenderer.CreateTexture("ui/cursor/icon_shoot_normal.png");
	m_newGunHeadUIRes = uiRenderer.CreateTexture("ui/cursor/icon_shoot_head.png");
	m_newGunDeathUIRes = uiRenderer.CreateTexture("ui/cursor/icon_shoot_death.png");
	m_newGunCursorUIRes = uiRenderer.CreateTexture("ui/cursor/cursor_newgun.png");

	m_EnbaleNormalshotTip = false;
	m_EnableHeadshotTip = false;
	m_EnbaleDeadshotTip = false;
	m_TipFadeInTime = 50;
	m_TipFadeOutTime = 250;;
	m_TipStartTime = -1;

	m_NeedShowColorableCursor = false;
	m_HasShownColorTips = false;

	m_PreColor = -1;
	m_TriggerTick = 0;

	m_LetterFrameOpened = false;
	//for (int i = 0; i < 21; i++)
	//m_OverheatTickCount[i] = 0;

	hasTap = false;
	tapTriggerMark = 0;
	hasinteractive = false;
	interactiveTriggerMark = 0;
	interactiveTrigger = false;

	hasmore = false;
	moreTrigger = false;
	moreTriggerMark = 0;

	m_DeveloperPos.SetElement(60, 118);
	m_DeveloperBtnID = -1;
	isOpenDeveloperFrame = false;
	m_DeveloperBtnSize = 108;
	m_ShowBlueprintUseBtn = false;
	m_selfUpdateCount = 0;
}

TouchControl::~TouchControl()
{
	g_pPlayerCtrl = NULL;
	SetIPlayerControl(nullptr);
}

void TouchControl::update(float dtime)
{
	s_ScreenScale = UILib::GetScreenUIScale();
	int frameCount = m_selfUpdateCount;
	for (auto iter = m_ButtonDownMarks.begin(); iter != m_ButtonDownMarks.end(); ++iter)
	{
		if (iter->second != -1 && iter->second != frameCount)
		{
			m_ButtonDownMap[iter->first] = false;
			iter->second = -1;
		}
	}

	for (auto iter = m_ButtonUpMarks.begin(); iter != m_ButtonUpMarks.end(); ++iter)
	{
		if(iter->first == BUTTON_ACTION)
		{
			if (m_ButtonDownMarks[BUTTON_ACTION] == m_ButtonUpMarks[BUTTON_ACTION])
			{
				m_ButtonUpMarks[BUTTON_ACTION]++;
			}
			if (iter->second != -1 && iter->second < frameCount)
			{
				m_ButtonUpMap[iter->first] = false;
				iter->second = -1;
			}
		}
		else
		{
			if (iter->second != -1 && iter->second != frameCount)
			{
				m_ButtonUpMap[iter->first] = false;
				iter->second = -1;
			}
		}
	}
	if (tapTriggerMark != -1 && tapTriggerMark != frameCount)
	{
		tapTriggerMark = -1;
		tapPosX = -1;
		tapPosY = -1;
		hasTap = false;
	}

	if (interactiveTriggerMark != -1 && interactiveTriggerMark != frameCount)
	{
		interactiveTriggerMark = -1;
		hasinteractive = false;
	}

	if (moreTriggerMark != -1 && moreTriggerMark != frameCount)
	{
		moreTriggerMark = -1;
		hasmore = false;
	}

	if (longPressTriggerMark != -1 && longPressTriggerMark != frameCount)
	{
		longPressTriggerMark = -1;
		triggerLongPress = false;
	}

	if (longPressEndMark != -1 && longPressEndMark != frameCount)
	{
		longPressEndMark = -1;
		longPressEnd = false;
	}

	for (auto iter = m_KeyDownMarks.begin(); iter != m_KeyDownMarks.end(); ++iter)
	{
		if (iter->second != -1 && iter->second != frameCount)
		{
			m_KeyDownMap[iter->first] = false;
			iter->second = -1;
		}
	}

	for (auto iter = m_KeyUpMarks.begin(); iter != m_KeyUpMarks.end(); ++iter)
	{
		if (iter->second != -1 && iter->second != frameCount)
		{
			m_KeyUpMap[iter->first] = false;
			iter->second = -1;
		}
	}

	if (m_EnableHeadshotTip || m_EnbaleNormalshotTip)
	{
		m_TipDuration += (int)(dtime * 1000);
		if (m_TipDuration < m_TipFadeInTime)
		{
			m_TipTrans = Clamp((int)(255.0f * m_TipDuration / m_TipFadeInTime), 0, 255);
		}
		else
		{
			m_TipTrans = Clamp(255 - (int)(255.0f * (m_TipDuration - m_TipFadeInTime) / m_TipFadeOutTime), 0, 255);
		}

		if (m_TipDuration > m_TipFadeInTime + m_TipFadeOutTime)
		{
			m_TipTrans = 0;
			m_EnableHeadshotTip = false;
			m_EnbaleNormalshotTip = false;
			m_EnbaleDeadshotTip = false;
		}
	}
	m_selfUpdateCount++;
}

void TouchControl::RotateCamera()
{
	// 冒险强制新手引导控制手机转视角
	if (!GetAdventureGuideMgrProxy()->canPlayerTurn())
	{
		return;
	}
	if (g_pPlayerCtrl)
	{
		GameCamera* pcamera = g_pPlayerCtrl->getCamera();
		if (pcamera) {
			pcamera->rotate(float(touchMoveDx) * m_sensityvity / GetGameUIPtr()->GetWidth(),
							float(touchMoveDy) * m_sensityvity / GetGameUIPtr()->GetHeight());
		}
		if (abs(touchMoveDx) > 0 || abs(touchMoveDy) > 0)
		{
			g_pPlayerCtrl->resetRecoil();
			if (g_pPlayerCtrl->getCustomGunComponent())
				g_pPlayerCtrl->getCustomGunComponent()->resetRecoil();
		}
	}

}

bool TouchControl::onInputEvent(const Rainbow::InputEvent& inevent)
{
#ifndef DEDICATED_SERVER
	if (g_pPlayerCtrl == NULL)
	{
		return false;
	}
	auto player = dynamic_cast<IClientPlayer*>(g_pPlayerCtrl);
	const int frameCount = m_selfUpdateCount;

	if (!g_pPlayerCtrl->m_EnableInput && inevent.type != InputEvent::kMouseUp)
	{
		return true;
	}
	//For PC
	else if (inevent.type == InputEvent::kKeyDown)
	{
		m_KeyMap[inevent.keycode] = true;
		m_KeyDownMap[inevent.keycode] = true;
		m_KeyDownMarks[inevent.keycode] = frameCount;

		// 键盘按下事件
		this->setTriggerKeys(inevent.keycode, 'D');
		player->triggerInputEvent(inevent.keycode, "Down");
	}
	else if (inevent.type == InputEvent::kKeyUp)
	{
		m_KeyMap[inevent.keycode] = false;
		m_KeyUpMap[inevent.keycode] = true;
		m_KeyUpMarks[inevent.keycode] = frameCount;

		// 按键弹起事件
		this->setTriggerKeys(inevent.keycode, 'U');
		player->triggerInputEvent(inevent.keycode, "Up");
	}

	ActorLocoMotion* locomotion = g_pPlayerCtrl->getLocoMotion();

	//隐藏UI的情况
	if (GetGameUIPtr()->isUIHide())
	{
		if (GetGameSetting().m_HideTouchRocker)
			touchForHUD(inevent);	//隐藏游戏内界面功能
		else
		{
			Touch touch;
			if (!Rainbow::GetTouch(0, touch))
			{
				// no touch event, do nothing
			}
			else if (inevent.type == InputEvent::kMouseDown)
			{
				int x = inevent.mousePosition.x;
				int y = inevent.mousePosition.y;

				m_RotateID = inevent.button;
				m_RotateTime = Timer::getSystemTick();
				m_RotateStart.SetElement(x, y);

				return true;
			}
			else if (inevent.type == InputEvent::kMouseUp)
			{
				m_RotateID = -1;
				m_RotateTime = -1;
				touchMoveDx = 0;
				touchMoveDy = 0;

				return true;
			}
			else if (inevent.type == InputEvent::kMouseDrag)
			{
				if (inevent.button == m_RotateID)
				{
					int dx = inevent.mousePosition.x - m_RotateStart.x;
					int dy = inevent.mousePosition.y - m_RotateStart.y;

					if (Abs(dx) > 0 || Abs(dy) > 0)
					{
						m_RotateStart.SetElement(inevent.mousePosition.x, inevent.mousePosition.y);
						touchMoveDx = dx;
						touchMoveDy = dy;
					}
				}

				RotateCamera();

				return true;
			}
		}
	}
	//显示UI的情况
	else
	{
		switch (inevent.type)
		{
		case InputEvent::kMouseDown:
			return onActionDown(inevent);

		case InputEvent::kMouseUp:
			return onActionUp(inevent);

		case InputEvent::kMouseDrag:
			return onActionMove(inevent);

		default:
			break;
		}
	}


	if (inevent.type == InputEvent::kKeyDown ||
		inevent.type == InputEvent::kKeyUp ||
		inevent.type == InputEvent::kScrollWheel)
		return false;

	return true; 
#else
	return false;
#endif //DEDICATED_SERVER
}

bool TouchControl::isFlyButtonVisibleWhenRecording() const
{
	RecordPkgManager& manager = GetRecordPkgManager();
	return manager.isRecordPlaying() && manager.isEdit() && manager.isPause();
}

bool TouchControl::onActionDown(const Rainbow::InputEvent& inevent)
{
	int x = inevent.mousePosition.x;
	int y = inevent.mousePosition.y;
   
	const int frameCount = m_selfUpdateCount;
	if (!isRockerMode())
	{
		m_isCanMove = true;
	}
	auto player = dynamic_cast<IClientPlayer*>(g_pPlayerCtrl);
	const Rainbow::RectInt& safeArea = GetGameUIPtr()->GetSafeArea();
	const int safeWidth = safeArea.GetWidth();
	const int safeHeight = safeArea.getHeight();

	int dx = x;
	int dy = safeHeight - y;
	int r;
	if (GetGameSetting().m_RockerUseRelSize)
		r = int(safeWidth * 0.4);	//海外修改摇杆触控范围大小
	else
		r = int(400 * m_ScreenScaleFactor);


	m_FlyType = CheckFlyArea(x, y, m_ScreenScaleFactor);
	int direction = 0;
	if (IsInControl(x, y, direction) && !isRockerMode())
	{
		//前
		if (direction & 1)
		{
			m_ButtonDownMap[BUTTON_FORWARD] = true;
			m_ButtonDownMarks[BUTTON_FORWARD] = frameCount;

			this->setTriggerKeys(BUTTON_FORWARD, 'D');
			player->triggerInputEvent(BUTTON_FORWARD, "Down");
		}
		//左
		if (direction & 4)
		{
			m_ButtonDownMap[BUTTON_LEFT] = true;
			m_ButtonDownMarks[BUTTON_LEFT] = frameCount;

			this->setTriggerKeys(BUTTON_LEFT, 'D');
			player->triggerInputEvent(BUTTON_LEFT, "Down");
		}
		//右
		if (direction & 8)
		{
			m_ButtonDownMap[BUTTON_RIGHT] = true;
			m_ButtonDownMarks[BUTTON_RIGHT] = frameCount;

			this->setTriggerKeys(BUTTON_RIGHT, 'D');
			player->triggerInputEvent(BUTTON_RIGHT, "Down");
		}
		//后
		if (direction & 2)
		{
			m_ButtonDownMap[BUTTON_BACK] = true;
			m_ButtonDownMarks[BUTTON_BACK] = frameCount;

			this->setTriggerKeys(BUTTON_BACK, 'D');
			player->triggerInputEvent(BUTTON_BACK, "Down");
		}
		/*//中
		if (direction & 64)
		{
		}*/

		//左前
		if (direction & 16)
		{
			m_ButtonDownMap[BUTTON_FORWARDLEFT] = true;
			m_ButtonDownMarks[BUTTON_FORWARDLEFT] = frameCount;

			this->setTriggerKeys(BUTTON_FORWARD, 'D');
			player->triggerInputEvent(BUTTON_FORWARD, "Down");
			this->setTriggerKeys(BUTTON_LEFT, 'D');
			player->triggerInputEvent(BUTTON_LEFT, "Down");
		}
		//右前
		if (direction & 32)
		{
			m_ButtonDownMap[BUTTON_FORWARDRIGHT] = true;
			m_ButtonDownMarks[BUTTON_FORWARDRIGHT] = frameCount;

			this->setTriggerKeys(BUTTON_FORWARD, 'D');
			player->triggerInputEvent(BUTTON_FORWARD, "Down");
			this->setTriggerKeys(BUTTON_RIGHT, 'D');
			player->triggerInputEvent(BUTTON_RIGHT, "Down");
		}
	}

	//开发者悬浮窗开关
	int developerSwitch = GetIWorldConfigProxy()->getGameData("developerfloat");
	if (g_pPlayerCtrl)
	{
		bool isUGCMode = g_WorldMgr->isUGCMode();
		developerSwitch = isUGCMode ? 0 : developerSwitch;//xyang20220531 悬浮窗开关跟UGC开关挂钩
	}
	//是否不在家园场景
	bool isNotHomeLandWorld = true;
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->getSpecialType() == HOME_GARDEN_WORLD)
	{
		isNotHomeLandWorld = false;
	}
	//是否是开发者按钮
	if (developerSwitch == 1 && IsInDeveloperBtn(x, y, m_DeveloperPos, m_DeveloperBtnSize) && isNotHomeLandWorld && GetWorldManagerPtr() && GetWorldManagerPtr()->isGameMakerMode() && GetClientInfoProxy()->getMultiPlayer() <= 0
		&& GetWorldManagerPtr()->getRealOwnerUin() == GetWorldManagerPtr()->getWorldOwnerUin())
	{
		isOpenDeveloperFrame = true;
		m_DeveloperBtnID = inevent.button;
		m_DeveloperStartPos.SetElement(inevent.mousePosition.x, inevent.mousePosition.y);
	}
	else if ((GetGameSetting().m_RockerUseRelSize && dx * dx + dy * dy < r * r && !IsInUseCircle(x, y, m_ScreenScaleFactor))
		|| (!GetGameSetting().m_RockerUseRelSize && dx * dx + dy * dy < r * r))	//修复修改摇杆大小后枪械无法点击问题
	{
		if (isRockerMode() && isShowRocker())
		{
			int limitw = int(MOVE_WIDTH * m_ScreenScaleFactor) / 2;

			if (x < limitw) x = limitw;
			if (y < limitw) y = limitw;
			if (y > safeHeight - limitw) y = safeHeight - limitw;

			m_MoveID = inevent.button;
			m_MoveStart.SetElement(x, y);
			m_MovePos.SetElement(x, y);
            m_MoveTouchState = 1;
		}

	}
	//20210825: 触发器新API允许玩家在游戏中飞行  codeby:wangshuai
	else if (isFlyButtonVisibleWhenRecording() || (g_pPlayerCtrl->getFlying() && (GetWorldManagerPtr()->isGodMode() || GetWorldManagerPtr()->isGameMakerRunMode() || g_pPlayerCtrl->isInSpectatorMode()) && m_FlyType != 0))
	{
		m_FlyID = inevent.button;
		m_FlyPos.SetElement(x, y);
		if (CheckFlyArea(x, y, m_ScreenScaleFactor) == 1)
		{
			m_ButtonMap[BUTTON_FLYUP] = true;
			m_ButtonMap[BUTTON_FLYDOWN] = false;
		}
		else if (CheckFlyArea(x, y, m_ScreenScaleFactor) == -1)
		{
			m_ButtonMap[BUTTON_FLYUP] = false;
			m_ButtonMap[BUTTON_FLYDOWN] = true;
		}
		else if (CheckFlyArea(x, y, m_ScreenScaleFactor) == 0)
		{
			m_ButtonMap[BUTTON_FLYUP] = false;
			m_ButtonMap[BUTTON_FLYDOWN] = false;
		}
	}
	//跳跃判断
	else if (IsInJumpCircle(x, y, m_ScreenScaleFactor) && m_JumpID < 0 && m_showOperateUI && !m_bHideJumpUi)
	{
		//m_JumpID = inevent.button;
		//m_JumpTime = 0;
		//m_JumpSubType = 0;
		//m_JumpPos.SetElement(x, y);
		//m_ButtonMap[BUTTON_JUMP] = true;
		//m_ButtonDownMap[BUTTON_JUMP] = true;
		//m_ButtonMap[BUTTON_FLYDOWN] = false;
		//m_ButtonDownMarks[BUTTON_JUMP] = frameCount;

		//this->setTriggerKeys(BUTTON_JUMP, 'D');
	}
	//下蹲判断
	else if (IsInSneakCircle(x, y, m_ScreenScaleFactor) && m_SneakID < 0 && m_showOperateUI && !m_bHideJumpUi)
	{
		m_SneakID = inevent.button;
		m_SneakTime = 0;
		m_SneakSubType = 0;
		m_SneakPos.SetElement(x, y);
		m_ButtonMap[BUTTON_SNEAK] = true;
		m_ButtonDownMap[BUTTON_SNEAK] = true;
		m_ButtonDownMarks[BUTTON_SNEAK] = frameCount;

		this->setTriggerKeys(BUTTON_SNEAK, 'D');
		player->triggerInputEvent(BUTTON_SNEAK, "Down");
	}
	//奔跑判断
	else if (IsInRunCircle(x, y, m_ScreenScaleFactor) && m_RunID < 0 && m_showOperateUI && !m_bHideRockerUi)
	{
		m_RunID = inevent.button;
		m_RunPos.SetElement(x, y);
		m_RunState = !m_RunState; // 切换奔跑状态
		m_ButtonMap[BUTTON_RUN] = m_RunState;
		m_ButtonDownMap[BUTTON_RUN] = true;
		m_ButtonDownMarks[BUTTON_RUN] = frameCount;

		this->setTriggerKeys(BUTTON_RUN, 'D');
		player->triggerInputEvent(BUTTON_RUN, m_RunState ? "On" : "Off");
	}
	//瞄准判断
	else if (IsInAimCircle(x, y, m_ScreenScaleFactor) && m_AimID < 0 && m_showOperateUI && !m_bHideJumpUi)
	{
		m_AimID = inevent.button;
		m_AimPos.SetElement(x, y);
		m_AimState = !m_AimState; // 切换瞄准状态
		m_ButtonMap[BUTTON_GUNAIM] = m_AimState;
		m_ButtonDownMap[BUTTON_GUNAIM] = true;
		m_ButtonDownMarks[BUTTON_GUNAIM] = frameCount;

		this->setTriggerKeys(BUTTON_GUNAIM, 'D');
		player->triggerInputEvent(BUTTON_GUNAIM, m_AimState ? "On" : "Off");
	}
	//使用判断
	else if (m_ShowUseBtn && !m_bHideActionUi && IsInUseCircle(x, y, m_ScreenScaleFactor) && m_UseID < 0 && m_showOperateUI && !g_pPlayerCtrl->isInSpectatorMode())
	{
		if (m_ShowGunUseBtn && !m_bHideActionUi)
			IsInLeftOrRight(x, y, m_ScreenScaleFactor);
		else
			m_ShootPosition = -1;
		m_UseID = inevent.button;
		m_UseSubType = 0;
		m_UseTime = 0;
		m_UsePos.SetElement(x, y);

		m_ButtonDownMap[BUTTON_ACTION] = true;
		m_ButtonMap[BUTTON_ACTION] = true;
		m_ButtonDownMarks[BUTTON_ACTION] = frameCount;
		
		// 右侧开火按钮按下时也设置旋转ID，允许同时旋转
		if (m_RotateID < 0)
		{
			m_RotateID = inevent.button;
			m_RotateTime = Timer::getSystemTick();
			m_RotateSubType = 0;
			m_RotateStart.SetElement(x, y);
		}
	}
	//枪的reload判断
	else if (m_ShowGunUseBtn && !m_bHideActionUi && IsInGunReloadCircle(x, y, m_ScreenScaleFactor) && m_ReloadID < 0 && m_showOperateUI)
	{
		m_ReloadID = inevent.button;
		m_ReloadTime = 0;
		m_ReloadPos.SetElement(x, y);
		m_ButtonDownMap[BUTTON_GUNRELOAD] = true;
		m_ButtonMap[BUTTON_GUNRELOAD] = true;
		m_ButtonDownMarks[BUTTON_GUNRELOAD] = frameCount;
	}
	else if ((m_ShowBallUseBtn || m_ShowBasketBallUseBtn || m_ShowPushSnowBallUseBtn) && !m_bHideActionUi && IsInPassOrCatchBallCirle(x, y, m_ScreenScaleFactor) && m_PassOrCatchBallID < 0 && !g_pPlayerCtrl->isInSpectatorMode())
	{
		m_PassOrCatchBallID = inevent.button;
		m_PassOrCatchTime = 0;
		m_PassOrCatchPos.SetElement(x, y);
		m_ButtonDownMap[BUTTON__PASSBALL_OR_CATCHBALL] = true;
		m_ButtonMap[BUTTON__PASSBALL_OR_CATCHBALL] = true;
		m_ButtonDownMarks[BUTTON__PASSBALL_OR_CATCHBALL] = frameCount;
	}
	else if ((x > (safeWidth - 170 * m_ScreenScaleFactor) && y > (safeHeight - 310 * m_ScreenScaleFactor)))
	{

	}
	else if (!isRockerMode() && x < safeWidth - 880 * m_ScreenScaleFactor && y >(safeHeight - 405 * m_ScreenScaleFactor))
	{

	}
	else if (m_RotateID < 0)
	{
		//设置挖掘提示进度条的方向
		if (x >= int(GetGameUIPtr()->GetWidth() * 0.5f))
		{
			m_BarDirection = 1;
		}
		else
		{
			m_BarDirection = 2;
		}

		m_RotateID = inevent.button;
		m_RotateTime = Timer::getSystemTick();
		m_RotateSubType = 0;
		m_RotateStart.SetElement(x, y);

		if (!g_pPlayerCtrl->canDig())
			m_RotateSubType = 1;

		//只有显示颜色tips的时候点击tip区域才吸色 20220630 codeby yangjia

		int w, h = 0;
		GetClientInfoProxy()->getClientWindowSize(w, h);
		int tipX = 0.5f * w;
		int tipY = h - 160;
		float tipWidth = 325;
		float tipsHeight = 45;
		bool isInTipsRect = (x >= tipX - (tipWidth / 2) * m_ScreenScaleFactor) && (x >= tipX + (tipWidth / 2) * m_ScreenScaleFactor) && (y >= tipY - (tipsHeight / 2) * m_ScreenScaleFactor) && (y >= tipY + (tipsHeight / 2) * m_ScreenScaleFactor);
				
		if (g_pPlayerCtrl->getViewMode() == CAMERA_FPS || g_pPlayerCtrl->getViewMode() == CAMERA_TPS_BACK || g_pPlayerCtrl->getViewMode() == CAMERA_TPS_BACK_SHOULDER)
		{
			//检测是否可以设置颜色
			bool enable = false;
			MINIW::ScriptVM::game()->callFunction("EnableSetColor", ">b", &enable);
			if (m_AimToColorableObject && m_PreColor > 0 && enable && m_HasShownColorTips && isInTipsRect)
			{
				g_pPlayerCtrl->setSelectedColor(m_PreColor);
				MINIW::ScriptVM::game()->callFunction("SetGunMagazine", "ii", 0, 0);
				MINIW::ScriptVM::game()->callFunction("SetCurSelectedColor", "i", m_PreColor);
				MINIW::ScriptVM::game()->callFunction("SaveCurrentColor", "");
			}
		}


		//吸色
		//if (g_pPlayerCtrl->getViewMode() == CAMERA_FPS || g_pPlayerCtrl->getViewMode() == CAMERA_TPS_BACK)
		//{
		//	if (m_AimToColorableObject && !g_pPlayerCtrl->getIWorld()->isGameMakerRunMode())
		//	{
		//		int color;
		//		if (m_InterType == 1)
		//		{
		//			int blockid = g_pPlayerCtrl->getIWorld()->getBlockID(m_IntersectResult.block);
		//			int userdata = g_pPlayerCtrl->getIWorld()->getBlockData(m_IntersectResult.block);
		//			MINIW::ScriptVM::game()->callFunction("GetColorFromBlockInfo", "ii>i", blockid, userdata, &color);
		//		}
		//		else if (m_InterType == 2)
		//		{
		//			ClientMob* mob = dynamic_cast<ClientMob*>(m_IntersectResult.actor);
		//			if (mob && mob->getDef()->ID == 3403)
		//			{
		//				color = mob->getColor();
		//			}
		//		}
		//		g_pPlayerCtrl->setSelectedColor(color);
		//		MINIW::ScriptVM::game()->callFunction("SetGunMagazine", "ii", 0, 0);
		//	}

		//	m_ColorableCursor = false;
		//}
	}
	return true;
}

bool TouchControl::onActionUp(const Rainbow::InputEvent& event)
{
	const int frameCount = m_selfUpdateCount;
	auto player = dynamic_cast<IClientPlayer*>(g_pPlayerCtrl);
	g_pPlayerCtrl->m_InputInfo->jumpRelease = g_pPlayerCtrl->getTouchControl()->GetButtonUp(BUTTON_JUMP);
    bool bmovebtnup = false;
	if (event.button == m_MoveID && isRockerMode())
	{
		m_MoveID = -1;
		m_RockerTouchDx = 0;
		m_RockerTouchDy = 0;
        m_MoveTouchState = 0;
        bmovebtnup = true;
	}
	else if (event.button == m_JumpID)
	{
		m_JumpID = -1;
		m_JumpSubType = -1;
		m_ButtonMap[BUTTON_JUMP] = false;
		m_ButtonUpMap[BUTTON_JUMP] = true;
		m_ButtonUpMarks[BUTTON_JUMP] = frameCount;
		g_pPlayerCtrl->m_InputInfo->triggerJump = false;

		this->setTriggerKeys(BUTTON_JUMP, 'U');
		player->triggerInputEvent(BUTTON_JUMP, "Up");
	}
	else if (event.button == m_SneakID)
	{
		m_SneakID = -1;
		m_SneakSubType = -1;
		m_ButtonMap[BUTTON_SNEAK] = false;
		m_ButtonUpMap[BUTTON_SNEAK] = true;
		m_ButtonUpMarks[BUTTON_SNEAK] = frameCount;

		this->setTriggerKeys(BUTTON_SNEAK, 'U');
		player->triggerInputEvent(BUTTON_SNEAK, "Up");
	}
	else if (event.button == m_RunID)
	{
		m_RunID = -1;
		m_ButtonUpMap[BUTTON_RUN] = true;
		m_ButtonUpMarks[BUTTON_RUN] = frameCount;

		this->setTriggerKeys(BUTTON_RUN, 'U');
		player->triggerInputEvent(BUTTON_RUN, "Up");
	}
	else if (event.button == m_AimID)
	{
		m_AimID = -1;
		m_ButtonUpMap[BUTTON_GUNAIM] = true;
		m_ButtonUpMarks[BUTTON_GUNAIM] = frameCount;

		this->setTriggerKeys(BUTTON_GUNAIM, 'U');
		player->triggerInputEvent(BUTTON_GUNAIM, "Up");
	}
	else if (event.button == m_UseID)
	{
		m_UseID = -1;
		m_JumpSubType = -1;
		m_ButtonMap[BUTTON_ACTION] = false;
		m_ButtonUpMap[BUTTON_ACTION] = true;
		m_ButtonUpMarks[BUTTON_ACTION] = frameCount;
		if (m_ButtonDownMarks[BUTTON_ACTION] == m_ButtonUpMarks[BUTTON_ACTION])
		{
			m_ButtonUpMarks[BUTTON_ACTION]++;
		}
		
		// 右侧开火按钮抬起时也清理旋转ID
		if (event.button == m_RotateID)
		{
			m_RotateID = -1;
			m_RotateTime = -1;
			m_RotateSubType = -1;
		}
	}
	else if (event.button == m_PassOrCatchBallID)
	{
		m_PassOrCatchBallID = -1;
		m_ButtonMap[BUTTON__PASSBALL_OR_CATCHBALL] = false;
		m_ButtonUpMap[BUTTON__PASSBALL_OR_CATCHBALL] = true;
		m_ButtonUpMarks[BUTTON__PASSBALL_OR_CATCHBALL] = frameCount;

	}
	else if (event.button == m_ReloadID)
	{
		m_ReloadID = -1;
		m_ButtonMap[BUTTON_GUNRELOAD] = false;
		m_ButtonUpMap[BUTTON_GUNRELOAD] = true;
		m_ButtonUpMarks[BUTTON_GUNRELOAD] = frameCount;
	}
	else if (event.button == m_FlyID)
	{
		m_FlyID = -1;
		m_FlyType = 0;
		m_ButtonMap[BUTTON_FLYUP] = false;
		m_ButtonMap[BUTTON_FLYDOWN] = false;
	}
	else if (event.button == m_RotateID)
	{
		m_RotateID = -1;
		m_RotateTime = -1;
		isLongPress = false;

		if (m_RotateSubType == 2)
		{
			longPressEnd = true;
			longPressEndMark = frameCount;
		}

		//点击处理
		if (m_RotateSubType == 0)
		{
			float x, y;
			if (m_sightModel) x = y = 0.5f;
			else
			{
				x = event.mousePosition.x / float(GetGameUIPtr()->GetWidth());
				y = event.mousePosition.y / float(GetGameUIPtr()->GetHeight());
			}

			if (canOnclick())
			{
				m_ButtonDownMap[BUTTON_SCREENTAP] = true;
				m_ButtonUpMarks[BUTTON_SCREENTAP] = frameCount;
				tapPosX = x;
				tapPosY = y;
				hasTap = true;
				tapTriggerMark = frameCount;
			}
		}
	}
	else if (event.button == m_DeveloperBtnID)
	{
		if (isOpenDeveloperFrame)
		{
			MINIW::ScriptVM::game()->callFunction("OpenDeveloperModeSetFrame", "");
		}
		m_DeveloperBtnID = -1;
		isOpenDeveloperFrame = false;
	}

	int direction = 0;
	int x = event.mousePosition.x;
	int y = event.mousePosition.y;
	if (IsInControl(x, y, direction) && !isRockerMode())
	{
		if (direction & 1) //前
		{
			this->setTriggerKeys(BUTTON_FORWARD, 'U');
			player->triggerInputEvent(BUTTON_FORWARD, "Up");
		}
		else if (direction & 4) //左
		{
			this->setTriggerKeys(BUTTON_LEFT, 'U');
			player->triggerInputEvent(BUTTON_LEFT, "Up");
		}
		else if (direction & 8) //右
		{
			this->setTriggerKeys(BUTTON_RIGHT, 'U');
			player->triggerInputEvent(BUTTON_RIGHT, "Up");
		}
		else if (direction & 2) //后
		{
			this->setTriggerKeys(BUTTON_BACK, 'U');
			player->triggerInputEvent(BUTTON_BACK, "Up");
		}
		else if (direction & 16) //左前
		{
			this->setTriggerKeys(BUTTON_FORWARD, 'U');
			player->triggerInputEvent(BUTTON_FORWARD, "Up");
			this->setTriggerKeys(BUTTON_LEFT, 'U');
			player->triggerInputEvent(BUTTON_LEFT, "Up");
		}
		else if (direction & 32) //右前
		{
			this->setTriggerKeys(BUTTON_FORWARD, 'U');
			player->triggerInputEvent(BUTTON_FORWARD, "Up");
			this->setTriggerKeys(BUTTON_RIGHT, 'U');
			player->triggerInputEvent(BUTTON_RIGHT, "Up");
		}
	}

	if (isRockerMode() && bmovebtnup && !m_Triggerkeys.empty())
	{
		for (int i = 6; i < 10; ++i)
		{
            bool btrigger = false;
            auto iter = m_Triggerkeys.find(i);
            while (iter != m_Triggerkeys.end()) {
                m_Triggerkeys.erase(iter);
                iter = m_Triggerkeys.find(i);
                btrigger = true;
            }
			if (btrigger)
			{
				player->triggerInputEvent(i, "Up");
			}
		}
	}


	return true;
}


bool TouchControl::onActionCancel(const Rainbow::InputEvent& event)
{
	return onActionUp(event);
}

bool TouchControl::onActionMove(const Rainbow::InputEvent& event)
{
	if (g_pPlayerCtrl->isDead())
	{
		//因为ClientGame::OnInputEvent中规定谁触发 DOWN 的，后续的 DRAG、UP 事件就由谁来消费得逻辑，
		//所以打开UI得情况下，drag和down事件都会走进来，所以玩家死亡得情况下在onActionMove里面手动调下抬起事件，解决死亡后还可以移动得问题
		if (event.button == m_MoveID)
			onActionCancel(event);
		return true;
	}

	touchMoveDx = 0;
	touchMoveDy = 0;

	const float screenUIScale = UILib::GetScreenUIScale();
	if (event.button == m_MoveID && isRockerMode())
	{
		m_RockerTouchDx = event.mousePosition.x - m_MovePos.x;
		m_RockerTouchDy = event.mousePosition.y - m_MovePos.y;

		//LOG_INFO("m_RockerTouchDy %f", GetRockerTouchDy());

		m_MovePos.SetElement(event.mousePosition.x, event.mousePosition.y);
	}
	else if (event.button == m_JumpID)
	{
		if (m_JumpSubType == 0)
		{
			int maxmove = Max(Abs(event.mousePosition.x - m_JumpPos.x), Abs(event.mousePosition.y - m_JumpPos.y));
			if (maxmove > 5 * screenUIScale * m_DpiScale)
			{
				m_JumpSubType = 1;
				m_JumpPos.SetElement(event.mousePosition.x, event.mousePosition.y);
			}
		}
		else
		{
			int dx = event.mousePosition.x - m_JumpPos.x;
			int dy = event.mousePosition.y - m_JumpPos.y;
			if (m_reversalY) dy = -dy;

			if (Abs(dx) > 0 || Abs(dy) > 0)
			{
				touchMoveDx = dx;
				touchMoveDy = dy;
				m_JumpPos.SetElement(event.mousePosition.x, event.mousePosition.y);
			}
			else
			{
				touchMoveDx = 0;
				touchMoveDy = 0;
			}
		}
	}
	else if (event.button == m_UseID)
	{
		if (m_ShowUseBtn && !m_bHideActionUi)
		{
			if (m_UseSubType == 0)
			{
				int maxmove = Max(Abs(event.mousePosition.x - m_UsePos.x), Abs(event.mousePosition.y - m_UsePos.y));
				if (maxmove > 3 * screenUIScale * m_DpiScale)
				{
					m_UseSubType = 1;
					m_UsePos.SetElement(event.mousePosition.x, event.mousePosition.y);
				}
			}
			else
			{
				int dx = event.mousePosition.x - m_UsePos.x;
				int dy = event.mousePosition.y - m_UsePos.y;
				if (m_reversalY) dy = -dy;

				if (Abs(dx) > 0 || Abs(dy) > 0)
				{
					touchMoveDx = dx;
					touchMoveDy = dy;
					m_UsePos.SetElement(event.mousePosition.x, event.mousePosition.y);
				}
				else
				{
					touchMoveDx = 0;
					touchMoveDy = 0;
				}
			}
		}
		else
		{
			m_UseID = -1;
		}
	}
	else if (event.button == m_PassOrCatchBallID)
	{
		if ((m_ShowBallUseBtn || m_ShowBasketBallUseBtn || m_ShowPushSnowBallUseBtn) && !m_bHideActionUi)
		{
			int dx = event.mousePosition.x - m_PassOrCatchPos.x;
			int dy = event.mousePosition.y - m_PassOrCatchPos.y;
			if (m_reversalY) dy = -dy;

			if (Abs(dx) > 0 || Abs(dy) > 0)
			{
				touchMoveDx = dx;
				touchMoveDy = dy;
				m_PassOrCatchPos.SetElement(event.mousePosition.x, event.mousePosition.y);
			}
			else
			{
				touchMoveDx = 0;
				touchMoveDy = 0;
			}
		}
		else
		{
			m_PassOrCatchBallID = -1;
		}
	}
	else if (event.button == m_FlyID)
	{
		int dx = event.mousePosition.x - m_FlyPos.x;
		int dy = event.mousePosition.y - m_FlyPos.y;
		if (m_reversalY) dy = -dy;

		if (Abs(dx) > 0 || Abs(dy) > 0)
		{
			touchMoveDx = dx;
			touchMoveDy = dy;
			m_FlyPos.SetElement(event.mousePosition.x, event.mousePosition.y);
		}
		else
		{
			touchMoveDx = 0;
			touchMoveDy = 0;
		}
	}
	else if (event.button == m_RotateID)
	{
		if (m_RotateSubType == 0)
		{
			int maxmove = Max(Abs(event.mousePosition.x - m_RotateStart.x), Abs(event.mousePosition.y - m_RotateStart.y));
			if (maxmove > 20)
			{
				m_RotateSubType = 1;
				m_RotateStart.SetElement(event.mousePosition.x, event.mousePosition.y);
			}
		}
		else
		{
			int dx = event.mousePosition.x - m_RotateStart.x;
			int dy = event.mousePosition.y - m_RotateStart.y;
			if (m_reversalY) dy = -dy;

			if (g_pPlayerCtrl->getOWID() == NEWBIEWORLDID && GetClientInfoProxy()->getCurGuideLevel() == 1)
			{
				int step = GetClientInfoProxy()->getCurGuideStep();
				if (step < 5)
					dy = 0;
				if (step == 5 || step == 6)
					dx = 0;
			}
			if ((Abs(dx) > 0 || Abs(dy) > 0) && !isLockCamera())
			{
				if (m_RotateSubType != 2)
					m_RotateSubType = 1;
				touchMoveDx = dx;
				touchMoveDy = dy;
				m_RotateStart.SetElement(event.mousePosition.x, event.mousePosition.y);
				g_pPlayerCtrl->checkNewbieWorldProgress(3, "rotationview");
			}
			else
			{
				touchMoveDx = 0;
				touchMoveDy = 0;
			}
		}
	}

	else if (event.button == m_DeveloperBtnID)
	{
		int x = event.mousePosition.x;
		int y = event.mousePosition.y;
		int size = m_DeveloperBtnSize / 2;
		int offset = Sqrt((float)((x - m_DeveloperStartPos.x) * (x - m_DeveloperStartPos.x) + (y - m_DeveloperStartPos.y) * (y - m_DeveloperStartPos.y)));
		if (offset > 30)
		{
			if ((x < (size + XFromLeft(0))) || (x > (XFromRight(0) - size)))
			{
				x = m_DeveloperPos.x;
			}
			if ((y < (size + YFromTop(0))) || (y > (YFromBottom(120) - size)))
			{
				y = m_DeveloperPos.y;
			}
			if ((x > XFromRight(200)) && (y < YFromTop(200)))
			{
				x = m_DeveloperPos.x;
				y = m_DeveloperPos.y;
			}
			m_DeveloperPos.SetElement(x, y);
			isOpenDeveloperFrame = false;
		}
	}

	RotateCamera();

	CheckColorCursor();

	int x = m_MovePos.x;
	int y = m_MovePos.y;
	int direction = 0;

	if (IsInControl(x, y, direction) && isRockerMode())
	{
		if (direction == 0)
		{
            if (m_MoveTouchState == 1) {
                calculateModeDir(x, y, direction);
            }
		}
		//前
		if (direction & 1)
		{
			this->setTriggerKeys(BUTTON_FORWARD, 'D');
		}
		//左
		else if (direction & 4)
		{
			this->setTriggerKeys(BUTTON_LEFT, 'D');
		}
		//右
		else  if (direction & 8)
		{
			this->setTriggerKeys(BUTTON_RIGHT, 'D');
		}
		//后
		else if (direction & 2)
		{
			this->setTriggerKeys(BUTTON_BACK, 'D');
		}
	}

	return true;
}
/*
//20210812:新UI截获触摸事件,重置镜头旋转触摸ID  codeby hongtao
void TouchControl::resetRotateId(const Rainbow::InputEvent& event)
{
	if (event.msg == GIE_TOUCHRELEASED || event.msg == GIE_TOUCHCANCELLED)
	{
		this->onInputEvent(event);
	}
}
*/
void TouchControl::renderUI(bool hideui, SharePtr<Texture2D> cursorHandle)
{
#ifndef DEDICATED_SERVER
	if (g_pPlayerCtrl == NULL)
	{
		return;
	}
	g_pPlayerCtrl->renderUI();

	//bool recordShowflyButton = false;
	//if (GetRecordPkgManager().isRecordPlaying())
	//{
	//	if (!GetRecordPkgManager().isEdit() || !GetRecordPkgManager().isPause())
	//	{
	//		return;
	//	}

	//	m_showOperateUI = true;
	//	m_showCrosshair = false;
	//	m_sightModel = false;
	//	recordShowflyButton = true;
	//}

	if (SnapshotInterface::GetInstancePtr() && !SnapshotInterface::GetInstancePtr()->isSnapshotFinished())
	{
		return;
	}

	const float scale = m_ScreenScaleFactor;// UILib::GetScreenUIScale();
	UIRenderer& uiRenderer = UIRenderer::GetInstance();
	XMLManager& xmlManager = GetGameUIPtr()->GetXMLManager();
	if (hideui)
	{
		/*int x, y;
		int bgw = GetHideBtn(x, y, scale);
		MINIW::UIRenderer::GetInstance().StretchRect(x-bgw/2, y-bgw/2, bgw, bgw, 0xffffffff, false, 434, 213, 53, 53)*/;
	}
	else
	{
		//自己角色身上需要展示的ui相关
		//if (g_pPlayerCtrl)
		//	g_pPlayerCtrl->renderUI();
		int toolId = g_pPlayerCtrl->getCurToolID();
		// 移动端准星一直显示
		bool isSightMode = GetIWorldConfigProxy()->getGameData("sight") != 0;
		bool shouldShowCrosshair = true;
		if (shouldShowCrosshair)
		{
			ScreenManager& screenManager = GetScreenManager();
			int physicScreenWidth = screenManager.GetWidth();
			int physicScreenHeight = screenManager.GetHeight();

			int cursorw = int(32 * scale);
			int x = int(0.5f * physicScreenWidth);
			int y = int(0.5f * physicScreenHeight);

			// 彩蛋变色提示准心
			//if (m_ColorableCursor)
			//{
			//	int offset;
			//	int w = cursorw / 2;
			//	int span = m_ColorableCursorOffset == 3 ? 120 : 400;

			//	if (Rainbow::Timer::getSystemTick() - m_ColorableCursorLastTick > span)
			//	{
			//		m_ColorableCursorOffset = m_ColorableCursorOffset == 0 ? 3 : 0;
			//		m_ColorableCursorLastTick = Rainbow::Timer::getSystemTick();
			//	}
			//	offset = m_ColorableCursorOffset;

			//	x -= 25;
			//	y -= 25;
			//	MINIW::UIRenderer::GetInstance().BeginDraw(m_ColorableUIRes);
			//	// 				MINIW::UIRenderer::GetInstance().StretchRect(x + w + offset, y + w + offset, w, w, 0xffffffff, 0, 0, 0 0, MINIW::UI_UVT_NORMAL, 0);
			//	// 				MINIW::UIRenderer::GetInstance().StretchRect(x - offset, y + w + offset, w, w, 0xffffffff, 0, 0, 0, 0,MINIW::UI_UVT_NORMAL, 90);
			//	// 				MINIW::UIRenderer::GetInstance().StretchRect(x - offset, y - offset, w, w, 0xffffffff, 0, 0, 0, 0,MINIW::UI_UVT_NORMAL, 180);
			//	// 				MINIW::UIRenderer::GetInstance().StretchRect(x + w + offset, y - offset, w, w, 0xffffffff, 0, 0, 0, 0,MINIW::UI_UVT_NORMAL, 270);
			//	MINIW::UIRenderer::GetInstance().StretchRect(x, y, 48 * scale, 48 * scale, 0xffffffff);
			//	MINIW::UIRenderer::GetInstance().EndDraw();
			//}
			// 普通准心
			if (g_pPlayerCtrl->getUsingEmitter())//手动发射器
			{
				unsigned int color = 0xffffffff;
				uiRenderer.BeginDraw(m_CursorHandleMaterial, cursorHandle);
				uiRenderer.StretchRect(
					(float)(x - cursorw / 2),
					(float)(y + (0.8f) * cursorw / 2),
					(float)(cursorw),
					(float)(cursorw), color);
				uiRenderer.EndDraw();
			}
			else if (g_pPlayerCtrl->m_GunHoleState == GunHoldState::NOGUN)
			{
				unsigned int color = m_NeedShowColorableCursor ? 0x8800FF00 : 0xffffffff;

				if (m_EnableHeadshotTip && toolId != ITEM_COLORED_GUN && toolId != ITEM_COLORED_EGG && toolId != ITEM_COLORED_EGG_SMALL)
				{
					unsigned int c = (m_TipTrans << 24) | (m_TipTrans << 16) | (m_TipTrans << 8) | (m_TipTrans);
					uiRenderer.BeginDraw(m_BaojiUIResMaterial, m_BaojiUIRes);
					uiRenderer.StretchRect((float)(x - cursorw), (float)(y - cursorw), (float)(2 * cursorw), (float)(2 * cursorw), c);
					uiRenderer.EndDraw();
				}

				uiRenderer.BeginDraw(m_CursorHandleMaterial, cursorHandle);
				uiRenderer.StretchRect((float)(x - cursorw / 2), (float)(y - cursorw / 2), (float)cursorw, (float)cursorw, color);
				uiRenderer.EndDraw();
			}
			// 枪械准心
			else if (g_pPlayerCtrl->m_GunHoleState == GunHoldState::GUN)
			{
				int crosshairWidth = (int)(1 * scale);
				if (crosshairWidth == 0) crosshairWidth = 1;

				int crosshairHeight = (int)(15 * scale);
				int spread = (int)(g_pPlayerCtrl->getGunSpread() * scale);

				// 没有开镜的情况下，才显示准心
				if (!g_pPlayerCtrl->getZoom())
				{
					if (g_pPlayerCtrl->GetGunCrosshair() > 0)
					{
						unsigned int color = m_NeedShowColorableCursor ? 0x8800FF00 : 0x88DAE290;

						uiRenderer.BeginDraw(m_CursorHandleMaterial, cursorHandle);
						//right
						uiRenderer.StretchRect((float)(x + spread), (float)(y - crosshairWidth), (float)crosshairHeight, (float)(2 * crosshairWidth), color);
						//top
						uiRenderer.StretchRect((float)(x - crosshairWidth), (float)(y + spread), (float)(2 * crosshairWidth), (float)crosshairHeight, color);
						//left
						uiRenderer.StretchRect((float)(x - crosshairHeight - spread), (float)(y - crosshairWidth), (float)crosshairHeight, (float)(2 * crosshairWidth), color);
						//bottom
						uiRenderer.StretchRect((float)(x - crosshairWidth), (float)(y - spread - crosshairHeight), (float)(2 * crosshairWidth), (float)crosshairHeight, color);
						uiRenderer.EndDraw();
					}
				}
				else
				{
					float weight, height;
					float x_off, y_off, delta;
					weight = physicScreenWidth * scale;
					height = physicScreenHeight * scale;
					if (height - physicScreenHeight < weight - physicScreenWidth)
					{
						delta = height - physicScreenHeight;
					}
					else
					{
						delta = weight - physicScreenWidth;
					}
					delta /= scale;

					weight = (physicScreenWidth + Rainbow::Abs(delta)) * scale;
					height = (physicScreenHeight + Rainbow::Abs(delta)) * scale;

					x_off = (weight - physicScreenWidth) / 2;
					y_off = (height - physicScreenHeight) / 2;

					// 通过动态渲染实现狙击枪开镜效果
					uiRenderer.BeginDraw(m_SightingTeleScopeMaterial, m_SightingTeleScope);
					//MINIW::UIRenderer::GetInstance().StretchRect(-x_off, -y_off, weight, height, 0xffffffff);
					uiRenderer.StretchRect(0, 0, (float)physicScreenWidth, (float)physicScreenHeight, 0xffffffff);
					uiRenderer.EndDraw();
				}
			}
			// 新枪械的准心
			else if (g_pPlayerCtrl->m_GunHoleState == GunHoldState::CUSTOMGUN)
			{
				// 腰射的情况下，才显示准心
				bool isAimState = g_pPlayerCtrl->isAimState();
				auto comp = g_pPlayerCtrl->getCustomGunComponent();
				if (!isAimState && comp)
				{
					int crosshairWidth = (int)(1.0f * scale);
					std::string devicemode = MINIW::GetDeviceModel();
					if (devicemode.find("iPad") != std::string::npos) //如果当前是 ipad
					{
						crosshairWidth = (int)(2.0f * scale);
					}
					if (crosshairWidth == 0) crosshairWidth = 1;

					int crosshairHeight = (int)(15.0f * scale);
					int spread = (int)(comp->getCurrentSightSpread() * scale);

					unsigned int color = m_NeedShowColorableCursor ? 0x8800FF00 : 0xffffffff;//0x88DAE290
					uiRenderer.BeginDraw(m_CursorHandleMaterial, m_newGunCursorUIRes);// cursorHandle
					//right
					uiRenderer.StretchRect((float)(x + spread), (float)(y - crosshairWidth), (float)crosshairHeight, (float)(2 * crosshairWidth), color);
					//top
					uiRenderer.StretchRect((float)(x - crosshairWidth), (float)(y + spread), (float)(2 * crosshairWidth), (float)crosshairHeight, color);
					//left
					uiRenderer.StretchRect((float)(x - crosshairHeight - spread), (float)(y - crosshairWidth), (float)crosshairHeight, (float)(2 * crosshairWidth), color);
					//bottom
					uiRenderer.StretchRect((float)(x - crosshairWidth), (float)(y - spread - crosshairHeight), (float)(2 * crosshairWidth), (float)crosshairHeight, color);
					uiRenderer.EndDraw();
				}
			}

			if (toolId != ITEM_COLORED_GUN && toolId != ITEM_COLORED_EGG && toolId != ITEM_COLORED_EGG_SMALL && (m_EnableHeadshotTip || m_EnbaleNormalshotTip))
			{
				unsigned int c = (m_TipTrans << 24) | (m_TipTrans << 16) | (m_TipTrans << 8) | (m_TipTrans);
				bool newGun = g_pPlayerCtrl->m_GunHoleState == GunHoldState::CUSTOMGUN;
				if (m_EnbaleDeadshotTip)
				{
					//干掉
					uiRenderer.BeginDraw(newGun ? material_newGunDeath : m_DeadUIResMaterial, newGun ? m_newGunDeathUIRes : m_DeadUIRes);
					uiRenderer.StretchRect(
						(float)(x - cursorw),
						(float)(y - cursorw),
						(float)(2 * cursorw),
						(float)(2 * cursorw), c);
				}
				else if (m_EnbaleNormalshotTip)
				{
					//击中
					uiRenderer.BeginDraw(newGun ? material_newGunNormal : m_NormalUIResMaterial, newGun ? m_newGunNormalUIRes : m_NormalUIRes);
					uiRenderer.StretchRect(x - 0.75f * cursorw, y - 0.75f * cursorw, 1.5f * cursorw, 1.5f * cursorw, c);
				}
				else
				{
					//爆头
					uiRenderer.BeginDraw(newGun ? material_newGunHead : m_BaojiUIResMaterial, newGun ? m_newGunHeadUIRes : m_BaojiUIRes);
					uiRenderer.StretchRect((float)(x - cursorw), (float)(y - cursorw), (float)(2 * cursorw), (float)(2 * cursorw), c);
				}
				uiRenderer.EndDraw();
			}

		}
		/*auto RidComp = g_pPlayerCtrl ? g_pPlayerCtrl->getRiddenComponent() : nullptr;
		if (RidComp && RidComp->isVehicleController())
			//物理载具驾驶模式下
			setVehicleControlUI();
		else if (g_pPlayerCtrl && !(g_pPlayerCtrl->getMountType() == MOUNT_DRIVE))
		{
			if (isRockerMode() && m_showOperateUI)
			{
				uiRenderer.BeginDraw(m_TouchRes4Material, m_TouchRes4);
				{
					int bgw = int(m_DpadWidth * scale);
					int bgh = int(m_DpadHeight * scale);
					int btnw = int(64 * scale);
					int arroww = int(35 * scale);
					int arrowh = int(18 * scale);
					unsigned int color;
					Point2D pt1, pt2;


					if (m_MoveID >= 0)
					{
						pt1 = m_MoveStart;
						pt2 = CalMovePointInRange(m_MoveStart, m_MovePos, (bgh - btnw) / 2);
						color = 0xb2ffffff;
					}
					else
					{//摇杆中心点
						pt2 = pt1 = Point2D(XFromLeft(133), YFromBottom(151));
						color = 0x66ffffff;
					}

					if (TouchControl::isShowRocker())
					{
						auto element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "operating_steering_wheel.png");
						if (element)
							uiRenderer.StretchRect((float)(pt1.x - bgw / 2), (float)(pt1.y - bgw / 2), (float)bgw, (float)bgh, color, element->x, element->y, element->w, element->h);

						// element = XMLManager::GetInstance().requestPackElement("ui/mobile/texture2/ingame.xml", "xsyd_arrow.png");
						// if (element)
						// {
						// 	MINIW::UIRenderer::GetInstance().StretchRect(pt1.x - arroww / 2, pt1.y + bgw / 2 - int(35 * scale), arroww arrowh, color, element->x, element->y, element->w, element->h, UI_UVT_TURN180);
						// 	MINIW::UIRenderer::GetInstance().StretchRect(pt1.x - arroww / 2, pt1.y - bgw / 2 + int(20 * scale), arroww arrowh, color, element->x, element->y, element->w, element->h);
						// 	MINIW::UIRenderer::GetInstance().StretchRect(pt1.x - bgw / 2 + int(18 * scale), pt1.y - arroww / 2, arrowh arroww, color, element->x, element->y, element->w, element->h, UI_UVT_TURN270);
						// 	MINIW::UIRenderer::GetInstance().StretchRect(pt1.x + bgw / 2 - int(19 * scale) - arrowh, pt1.y - arroww /2, arrowh, arroww, color, element->x, element->y, element->w, element->h, UI_UVT_TURN90);
						// }

						element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "img_steering_wheel.png");
						if (element)
							uiRenderer.StretchRect((float)(pt2.x - btnw / 2), (float)(pt2.y - btnw / 2), (float)btnw, (float)btnw, color, element->x, element->y, element->w, element->h);
					}
				}
				uiRenderer.EndDraw();
			}
			else if (m_showOperateUI)
			{
				uiRenderer.EndDraw();
				uiRenderer.BeginDraw(m_TouchRes4Material, m_TouchRes4);

				auto element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "icon_rocker.png");

				int direction = 0;
				bool canMove = true;

				const size_t touchCount = GetTouchCount();
				for (size_t i = 0; i < touchCount; ++i)
				{
					Touch touch;
					if(GetTouch(i, touch))
					{
						canMove = IsInControl(touch.pos.x, touch.pos.y, direction);
						if (!canMove) break;
					}
				}

				Point2D pt1;
				int btnw = 0;
				unsigned long cor = 0xffffffff;
				//前
				if (direction & 1)
				{
					pt1 = Point2D(int(150 * scale), YFromBottom(373));
					btnw = int(70 * scale);
					cor = 0xccafafaf;
				}
				else
				{
					pt1 = Point2D(int(145 * scale), YFromBottom(378));
					btnw = int(80 * scale);
					cor = 0x66ffffff;

				}
				if (element)
					uiRenderer.StretchRect((float)pt1.x, (float)pt1.y, (float)btnw, (float)btnw, cor, element->x, element->y, element->w, element->h, UI_UVT_TURN270);
				//左
				if (direction & 4)
				{
					pt1 = Point2D(int(43 * scale), YFromBottom(270));
					btnw = int(70 * scale);
					cor = 0xccafafaf;
				}
				else
				{
					pt1 = Point2D(int(38 * scale), YFromBottom(275));
					btnw = int(80 * scale);
					cor = 0x66ffffff;
				}
				if (element)
					uiRenderer.StretchRect((float)pt1.x, (float)pt1.y, (float)btnw, (float)btnw, cor, element->x, element->y, element->w, element->h, UI_UVT_TURN180);
				//右
				if (direction & 8)
				{
					pt1 = Point2D(int(258 * scale), YFromBottom(270));
					btnw = int(70 * scale);
					cor = 0xccafafaf;
				}
				else
				{
					pt1 = Point2D(int(253 * scale), YFromBottom(275));
					btnw = int(80 * scale);
					cor = 0x66ffffff;
				}
				if (element)
					uiRenderer.StretchRect((float)pt1.x, (float)pt1.y, (float)btnw, (float)btnw, cor, element->x, element->y, element->w, element->h);
				//后
				if (direction & 2)
				{
					pt1 = Point2D(int(150 * scale), YFromBottom(167));
					btnw = int(70 * scale);
					cor = 0xccafafaf;
				}
				else
				{
					pt1 = Point2D(int(145 * scale), YFromBottom(172));
					btnw = int(80 * scale);
					cor = 0x66ffffff;
				}
				if (element)
					uiRenderer.StretchRect((float)pt1.x, (float)pt1.y, (float)btnw, (float)btnw, cor, element->x, element->y, element->w, element->h, UI_UVT_TURN90);

				if (m_isForward)
				{
					//左前
					if (direction & 16)
					{
						pt1 = Point2D(int(62 * scale), YFromBottom(363));
						btnw = int(53 * scale);
						cor = 0xccafafaf;
					}
					else
					{
						pt1 = Point2D(int(59 * scale), YFromBottom(366));
						btnw = int(59 * scale);
						cor = 0x66ffffff;
					}
					if (element)
						uiRenderer.StretchRect((float)pt1.x, (float)pt1.y, (float)btnw, (float)btnw, cor, element->x, element->y, element->w, element->h, UI_UVT_NORMAL, 225);
					//右前
					if (direction & 32)
					{
						pt1 = Point2D(int(260 * scale), YFromBottom(363));
						btnw = int(53 * scale);
						cor = 0xccafafaf;
					}
					else
					{
						pt1 = Point2D(int(257 * scale), YFromBottom(366));
						btnw = int(59 * scale);
						cor = 0x66ffffff;
					}

					if (element)
						uiRenderer.StretchRect((float)pt1.x, (float)pt1.y, (float)btnw, (float)btnw, cor, element->x, element->y, element->w, element->h, UI_UVT_NORMAL, 315);
				}

				element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "icon_rocker_circle.png");
				//中


				if (direction & 64)
				{
					pt1 = Point2D(int(153 * scale), YFromBottom(266));
					if (element)
						uiRenderer.StretchRect((float)pt1.x, (float)pt1.y, 67 * scale, 62 * scale, 0xcc8c8c8c, element->x, element->y, element->w, element->h, UI_UVT_TURN90);
				}
				else
				{
					pt1 = Point2D(int(151 * scale), YFromBottom(278));
					if (element)
						uiRenderer.StretchRect((float)pt1.x, (float)pt1.y, 71 * scale, 66 * scale, 0x668c8c8c, element->x, element->y, element->w, element->h, UI_UVT_TURN90);
				}
				uiRenderer.EndDraw();
			}
		}*/
		// renderRockerOperateUI();
		uiRenderer.BeginDraw(m_TouchRes4Material, m_TouchRes4);
		std::string icon_text1 = "";
		std::string icon_text2 = "";
		std::string icon_text3 = "";
		bool isUGCMode = g_WorldMgr->isUGCMode();
		
		// 瞄准按钮绘制
		if(m_showOperateUI && !m_bHideJumpUi && !GetRecordPkgManager().isEdit())
		{
			bool newGun = g_pPlayerCtrl->m_GunHoleState == GunHoldState::CUSTOMGUN;
			int x = newGun ? XFromRight(187 + newGunjumpBtnOffset) : XFromRight(187);
			int y = newGun ? YFromBottom(179 + newGunjumpBtnOffset - 80) : YFromBottom(179 - 80); // 跳跃按钮上方80像素
			unsigned int color;
			if (m_AimID >= 0 || m_AimState)
			{
				color = 0xffffffff; // 按下或激活时全不透明
			}
			else
			{
				color = 0x66ffffff; // 未激活时半透明
			}

			const TexPackElement* element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "m_aim.png");
			if (element)
			{
				int width = newGun ? 120 : 148;
				int height = newGun ? 128 : 152;
				uiRenderer.StretchRect((float)x, (float)y, width * scale, height * scale, color, element->x, element->y, element->w, element->h);
			}
		}
		
		if(m_showOperateUI && !g_pPlayerCtrl->isFlying() &&!(g_pPlayerCtrl->getMountType()==MOUNT_DRIVE))
		{			
			int x = XFromRight(187);
			int y = YFromBottom(179);
			int nGuideTask = 0;
			MNSandbox::GetGlobalEvent().Emit<int&>("ClientAccountMgr_getCurNoviceGuideTask", nGuideTask);
			if (g_pPlayerCtrl->getOWID() != NEWBIEWORLDID || nGuideTask > 2)
			{
				//unsigned int color;
				//if (m_JumpID >= 0)
				//{
				//	color = 0xffffffff;
				//}
				//else
				//{
				//	color = 0x66ffffff;
				//}

				//const TexPackElement* element = nullptr;
				//if (!m_bHideJumpUi)
				//{
				//	element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "icon_jump.png");
				//}
				//	
				//if (m_ShowBasketBallUseBtn && !m_bHideActionUi)
				//{
				//	if (g_pPlayerCtrl->getCatchBall()) {
				//		element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "icon_sprint.png");
				//		icon_text1 = GetDefManagerProxy()->getStringDef(29998);
				//	}
				//	else {
				//		element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "icon_blocking.png");
				//		icon_text1 = GetDefManagerProxy()->getStringDef(29995);
				//	}
				//}
				//auto RidComp = g_pPlayerCtrl->getRiddenComponent();
				//ClientActor* actor = RidComp ? RidComp->getRidingActor() : NULL;
				//if (actor && actor->getObjType() == OBJ_TYPE_ROCKET)
				//	element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "icon_rocket.png");
				//if (element && !GetRecordPkgManager().isEdit())
				//{
				//	int width = 148, height = 152;
				//	bool newGun = g_pPlayerCtrl->m_GunHoleState == GunHoldState::CUSTOMGUN;
				//	if (newGun)
				//	{
				//		x = XFromRight(187 + newGunjumpBtnOffset);
				//		y = YFromBottom(179 + newGunjumpBtnOffset);
				//		width = 120;
				//		height = 128;
				//	}
				//	uiRenderer.StretchRect((float)x, (float)y, width * scale, height * scale, color, element->x, element->y, element->w, element->h);
				//}
			}
		}

		if (m_iAutoFireState > 0)
		{
			ScreenManager& screenManager = GetScreenManager();
			int x = int(0.5f * screenManager.GetWidth());
			int y = int(0.5f * screenManager.GetHeight());
			int size = 28;
			if (m_iAutoFireState == 1)
			{
				size = (int)(scale * (54 - (m_iMaxAimTime - m_iAutoAimTime) * 3));
			}

			auto bblment = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", m_iAutoFireState == 1 ? "aiming.png" : (m_iAutoFireState == 2 ? "firewait.png" : "fire.png"));
			if (bblment)
				uiRenderer.StretchRect(x - size / 2, y - size / 2, (float)size, (float)size, 0xffffffff, bblment->x, bblment->y, bblment->w, bblment->h);
		}


		if (m_AccumulatorProgress >= 0)
		{
			ScreenManager& screenManager = GetScreenManager();

			int size = (int)(scale * (52 - m_AccumulatorProgress * 48));
			int x = int(0.5f * screenManager.GetWidth());
			int y = int(0.5f * screenManager.GetHeight());

			auto element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "sight.png");
			if (element)
				uiRenderer.StretchRect((float)(x - size / 2), (float)(y - size / 2), (float)size, (float)size, 0xffffffff, element->x, element->y, element->w, element->h);
		}
		uiRenderer.EndDraw();
		
		////20210724: 触发器新API允许玩家在游戏中飞行  codeby:wangshuai
		//if(m_showOperateUI && (recordShowflyButton || (g_pPlayerCtrl->isFlying() && (g_WorldMgr->isGodMode() || g_WorldMgr->isGameMakerRunMode() || g_pPlayerCtrl->isInSpectatorMode()))))
		//{
		//	// renderFlyOperateUI();
		//}

		// 防御状态下玩家的生物韧性 code-by:liya
		// if (g_pPlayerCtrl && g_pPlayerCtrl->IsInDefanceState())
		// {
		// 	auto attrib = g_pPlayerCtrl->getPlayerAttrib();
		// 	if (attrib)
		// 	{
		// 		ScreenManager& screenManager = GetScreenManager();
		// 		int x = int(0.5f * screenManager.GetWidth());
		// 		int y = int(0.5f * screenManager.GetHeight());
		// 		uiRenderer.BeginDraw(m_TouchRes4Material, m_TouchRes4);

		// 		int width = (int)(156 * scale);
		// 		int height = (int)(10 * scale);

		// 		int offsetx = width / 2;
		// 		int offsety = height / 2 + 30 * scale;
		// 		// 绘制底板
		// 		auto element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "img_schedule02.png");
		// 		if (element)
		// 		{
		// 			uiRenderer.StretchRect(x - offsetx, y - offsety, (float)width, (float)height, 0xffffffff, element->x, element->y, element->w, element->h);
		// 		}

		// 		int toughnessMax = attrib->getToughnessTotalMax();
		// 		int toughness = attrib->getToughnessTotal();

		// 		float per = toughness * 1.0f / toughnessMax;

		// 		// 绘制进度
		// 		element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "img_schedule01.png");
		// 		if (element)
		// 		{
		// 			uiRenderer.StretchRect(x - offsetx, y - offsety, (float)width * per, (float)height, 0xffffffff, element->x, element->y, element->w * per, element->h);
		// 		}

		// 		width = (int)(30 * scale);
		// 		height = (int)(30 * scale);
		// 		// 绘制图标
		// 		element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "icon_shield.png");
		// 		if (element)
		// 		{
		// 			uiRenderer.StretchRect(x - offsetx - width - 10 * scale, y - offsety - height / 2 + 5 * scale, (float)width, (float)height, 0xffffffff, element->x, element->y, element->w, element->h);
		// 		}

		// 		uiRenderer.EndDraw();

		// 		//// 剩余韧性百分比
		// 		//const UIFont& oneUIFontConfig = GetGameUIPtr()->GetFrameManagerPtr()->getUIFontByIndex(5);
		// 		//char tmpbuf[64];
		// 		//std::sprintf(tmpbuf, "%d%%", int(per * 100));
		// 		//float top = (float)y + 20 * scale - oneUIFontConfig.height / 2;
		// 		//float left = (float)x - width / 2 - 50 * scale;
		// 		//float bottom = (float)top + oneUIFontConfig.height;
		// 		//float right = (float)left + 45 * scale;
		// 		//Rectf rc(left, top, right - left, bottom - top);
		// 		//uiRenderer.renderTextRect(oneUIFontConfig.h, FONTSYTLE_NORMAL, tmpbuf, rc, 0, 0, false, ColorRGBA32(255, 255, 255, 255), 1.0f);
		// 	}
		// }
		
		//开发者悬浮窗开关
		int developerSwitch = GetIWorldConfigProxy()->getGameData("developerfloat");
		if (g_pPlayerCtrl)
		{
			//bool isUGCMode = g_pPlayerCtrl->Event().Emit("Developer_IsUGCMode", SandboxContext()).GetData_Bool("state");
			developerSwitch = isUGCMode ? 0 : developerSwitch;//xyang20220531 悬浮窗开关跟UGC开关挂钩
		}
	}
	uiRenderer.EndDraw();

	if (!(g_pPlayerCtrl && g_pPlayerCtrl->getMountType() == MOUNT_DRIVE) && ((m_JumpID >= 0 || (g_pPlayerCtrl->m_DisplayOxygen >= 0 && GetIWorldConfigProxy()->getGameData("guideswim") < 3)) && !g_pPlayerCtrl->isFlying()))
	{
		int index = m_JumpTime / 5;
		int indexX = index % 4;
		int indexY = index / 4;
		uiRenderer.BeginDraw(m_TouchRes2Material, m_TouchRes2);
		bool newGun = g_pPlayerCtrl->m_GunHoleState == GunHoldState::CUSTOMGUN;
		int x = newGun ? XFromRight(280 + newGunjumpBtnOffset) : XFromRight(303);
		int y = newGun ? YFromBottom(274 + newGunjumpBtnOffset) : YFromBottom(295);
		int len = newGun ? 310 : 380;
		uiRenderer.StretchRect((float)x, (float)y, len* scale, len* scale, 0xffffffff, indexX * 128, indexY * 128, 128, 128);
		uiRenderer.EndDraw();

		m_JumpTime += 1;
		m_JumpTime = m_JumpTime % 80;
	}

	if (m_UseID >= 0 && m_ShowUseBtn && !m_bHideActionUi)
	{
		int index = m_UseTime / 5;
		int indexX = index % 4;
		int indexY = index / 4;

		int x, y;

		// if (m_ShootPosition == 0)
		// {
		// 	x = XFromLeft(-95);
		// 	y = YFromTop(140);
		// 	uiRenderer.BeginDraw(m_TouchRes2Material, m_TouchRes2);
		// 	uiRenderer.StretchRect((float)x, (float)y, 310 * scale, 310 * scale, 0xffffffff, indexX * 128, index * 128, 128, 128);
		// 	uiRenderer.EndDraw();
		// }
		// else
		// {
		// 	x = XFromRight(317 * scale);
		// 	y = YFromBottom(291 * scale);
		// 	uiRenderer.BeginDraw(m_TouchRes2Material, m_TouchRes2);
		// 	uiRenderer.StretchRect((float)x, (float)y, 310 * scale, 310 * scale, 0xffffffff, indexX * 128, indexY * 128, 128, 128);
		// 	uiRenderer.EndDraw();
		// }

		m_UseTime += 1;
		m_UseTime = m_UseTime % 80;
	}

	renderDigProgress(scale);
	// renderNoviceUIEffect();


	//if (g_pPlayerCtrl->isVehicleController())
		//renderOverheatProgress(scale);
#endif
}

void TouchControl::renderDigProgress(float scale)
{
	if (GetGameUIPtr()->isUIHide() || g_pPlayerCtrl == NULL)
	{
		return;
	}

	float digprogress = g_pPlayerCtrl->getDigProgress();
	bool drawcircle = false;
	bool drawprogress = false;
	int startx, starty;

	if (m_RotateID >= 0)
	{
		if (m_RotateSubType == 0 || m_RotateSubType == 2)
		{
			drawcircle = true;
			if (m_RotateSubType == 2 && digprogress >= 0) drawprogress = true;
			startx = m_RotateStart.x;
			starty = m_RotateStart.y;
		}
	}
	else if (m_UseID > 0 || hasinteractive || hasmore) 
	{
		drawcircle = false;
	}

	// else if (m_UseID >= 0)
	// {
	// 	drawcircle = true;
	// 	drawprogress = true;
	// 	startx = (int)(GetScreenWidth() * 0.5f);
	// 	starty = (int)(GetScreenHeight() * 0.5f);
	// }

	UIRenderer& uiRenderer = UIRenderer::GetInstance();
	XMLManager& xmlManager = GetGameUIPtr()->GetXMLManager();
	FrameManager& frameManager = *GetGameUIPtr()->GetFrameManagerPtr();
	uiRenderer.BeginDraw(m_TouchRes4Material, m_TouchRes4);
	if (drawcircle)
	{
		// int btnw = (int)(83 * scale);
		// auto element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "lzy_37.png");
		// if (element)
		// 	uiRenderer.StretchRect((float)(startx - btnw / 2), (float)(starty - btnw / 2), (float)btnw, (float)btnw, 0xffffffff, element->x, element->y, element->w, element->h);


		// TODO: 这里可以更换一个更匹配ashcraft的图标
		// btnw = (int)(96 * scale);
		// element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "lzy_31.png");
		// if (element)
		// 	uiRenderer.StretchRect((float)(startx - btnw / 2), (float)(starty - btnw / 2), (float)btnw, (float)btnw, 0xffffffff, element->x, element->y, element->w, element->h);
	}

	//更新挖掘进度
	
	if (drawprogress)
	{
		int toolId = g_pPlayerCtrl->getCurToolID();
		auto blockDef = GetDefManagerProxy()->getBlockDef(g_pPlayerCtrl->getDigBlockID());
		unsigned long cor = 0xffffffff;
		float proX = 570;
		float proY = 733;
		auto element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "lzy_11.png");
		if (element)
		{
			proX = (float)element->x;
			proY = (float)element->y;
		}
		bool isRightTool = true;
		int rightToolX = 0;
		int rightToolY = 0;
		bool isExploit = false; //是否显示“开垦中”

		if (blockDef)
		{
			auto curToolDef = GetDefManagerProxy()->getToolDef(toolId);
			//auto mineToolDef = GetDefManagerProxy()->getToolDef(blockDef->MineTool);

			auto element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "lzy_38.png");

			if (element)
			{
				int blockid = g_pPlayerCtrl->GetExploitCheckBlockId(true);

				if (!curToolDef && element && blockDef->MineTool != 0)
				{
					proX = (float)element->x;
					proY = (float)element->y;
					isRightTool = false;
				}
				else if (curToolDef && (curToolDef->Type != blockDef->MineTool || curToolDef->Level < blockDef->ToolLevel))
				{
					if (!(DoByProgress(toolId, blockid) && g_pPlayerCtrl->isCurrentActionState("Exploit")))//开垦的时候 进度条用的是绿色的 不用显示工具图标 code-by:liwentao
					{
						proX = (float)element->x;
						proY = (float)element->y;
						isRightTool = false;
					}
				}

				if (checkCanExploit(toolId, blockid) && g_pPlayerCtrl->isCurrentActionState("Exploit"))
					isExploit = true;
			}

		}

		bool isShowDigPro = true;
		if (!isExploit)
			isShowDigPro = isShowDigProgress();

		const UIFont& oneUIFontConfig = frameManager.getUIFontByIndex(5);
		char tmpbuf[64];

		//不显示进度条的话进度条边上的工具也不显示
		if (!isShowDigPro)
			isRightTool = true;
		g_pPlayerCtrl->setIsRightTool(isRightTool);
		if (!isRightTool)
		{
			int toolid = 0;
			//家园回收工具没有配在tool表里，这里单独处理
			SandboxResult homelandret = SandboxEventDispatcherManager::GetGlobalInstance().
				Emit("Homeland_GetRightTool", SandboxContext(nullptr).SetData_Number("type", 1));
			if (homelandret.IsSuccessed())
			{
				toolid = g_pPlayerCtrl->getCurToolID();
			}
			else
			{
				std::map<int, int>& mineToolIcon = GetDefManagerProxy()->getMineToolIcon();
				auto it = mineToolIcon.find(blockDef->MineTool * 100 + blockDef->ToolLevel);
				if (it != mineToolIcon.end())
				{
					toolid = it->second;
				}
			}
			g_pPlayerCtrl->setRightToolId(toolid);
		}
		isShowDigPro = false;//使用FGUI播放新的挖掘动画
		if (isShowDigPro)
		{
			//按的是屏幕右边
			if (m_BarDirection == 1)
			{
				uiRenderer.BeginDraw(m_TouchRes4Material, m_TouchRes4);
				int btnw = (int)(75 * scale);
				int btnh = (int)(81 * scale);

				//绘制进度条的底
				auto element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "lzy_10.png");
				if (element)
					uiRenderer.StretchRect(startx - btnw - 30 * scale, starty - btnh - 30 * scale, (float)btnw, (float)btnh, 0xffffffff, element->x, element->y, element->w, element->h);

				Rectf rc(startx - 25 * scale, starty - btnh - 30 * scale, 60 * scale, 24 * scale);

				rightToolX = (int)(startx - 125 * scale);
				rightToolY = (int)(starty - 110 * scale);

				btnw = (int)(71 * scale);
				btnh = (int)(77 * scale);


				if (digprogress < 0.5f)
				{
					//只需要绘制一个三角形
					//
					//   0
					//   |  \
					//   1----2
					m_TipTriangleVerts[0].x = startx - btnw - 32 * scale;
					m_TipTriangleVerts[0].y = starty - btnh * 2 * digprogress - 32 * scale;

					m_TipTriangleVerts[1].x = startx - btnw - 32 * scale;
					m_TipTriangleVerts[1].y = starty - 32 * scale;

					m_TipTriangleVerts[2].x = startx - 32 * scale;
					m_TipTriangleVerts[2].y = starty - 32 * scale;

					m_TipTriangleTexCood[0].x = proX;
					m_TipTriangleTexCood[0].y = proY + (1 - 2 * digprogress) * 77;

					m_TipTriangleTexCood[1].x = proX;
					m_TipTriangleTexCood[1].y = proY + 77;

					m_TipTriangleTexCood[2].x = proX + 71;
					m_TipTriangleTexCood[2].y = proY + 77;

					uiRenderer.StretchTriangle(m_TipTriangleVerts[0], m_TipTriangleTexCood[0], m_TipTriangleVerts[2], m_TipTriangleTexCood[2], m_TipTriangleVerts[1], m_TipTriangleTexCood[1], \
						0xffffffff);

				}
				else
				{
					//要绘制两个三角形
					//   0 ---3
					//   |  \ |
					//   1----2
					m_TipTriangleVerts[0].x = startx - btnw - 32 * scale;
					m_TipTriangleVerts[0].y = starty - btnh - 32 * scale;

					m_TipTriangleVerts[1].x = startx - btnw - 32 * scale;
					m_TipTriangleVerts[1].y = starty - 32 * scale;

					m_TipTriangleVerts[2].x = startx - 32 * scale;
					m_TipTriangleVerts[2].y = starty - 32 * scale;

					m_TipTriangleVerts[3].x = startx - 32 * scale - btnw + (digprogress - 0.5f) * 2 * btnw;
					m_TipTriangleVerts[3].y = starty - btnh - 32 * scale;

					m_TipTriangleTexCood[0].x = proX;
					m_TipTriangleTexCood[0].y = proY;

					m_TipTriangleTexCood[1].x = proX;
					m_TipTriangleTexCood[1].y = proY + 77;

					m_TipTriangleTexCood[2].x = proX + 71;
					m_TipTriangleTexCood[2].y = proY + 77;

					m_TipTriangleTexCood[3].x = proX + (digprogress - 0.5f) * 2 * 71;
					m_TipTriangleTexCood[3].y = proY;

					uiRenderer.StretchTriangle(m_TipTriangleVerts[0], m_TipTriangleTexCood[0], m_TipTriangleVerts[2], m_TipTriangleTexCood[2], m_TipTriangleVerts[1], m_TipTriangleTexCood[1], \
						0xffffffff);

					uiRenderer.StretchTriangle(m_TipTriangleVerts[0], m_TipTriangleTexCood[0], m_TipTriangleVerts[3], m_TipTriangleTexCood[3], m_TipTriangleVerts[2], m_TipTriangleTexCood[2], \
						0xffffffff);
				}
				uiRenderer.EndDraw();
				//MINIW::UIRenderer::GetInstance().StretchRect(startx-btnw-32*scale, starty-btnh*digprogress-32*scale,btnw, btnh*digprogress, cor, proX, proY+(1-digprogress)*77, 71, 77*digprogress);

				//绘制进度值
				sprintf(tmpbuf, "%d%%", int(digprogress * 100));
				uiRenderer.renderTextRect(oneUIFontConfig.h, FONTSYTLE_NORMAL, tmpbuf, rc, 0, 0, false, ColorRGBAf::white, 1.0f);

				if (isExploit)
				{
					int tmpBtnw = (int)(75 * scale);
					int tmpBtnh = (int)(81 * scale);

					const float w = 60 * scale, h = 24 * scale;
					rc = Rectf(startx - tmpBtnw - w, starty - tmpBtnh - 30 * scale, w, h);

					const UIFont& tmpUIFontConfig = frameManager.getUIFontByIndex(7);
					uiRenderer.renderTextRect(tmpUIFontConfig.h, FONTSYTLE_NORMAL, GetDefManagerProxy()->getStringDef(2100), rc, 0, 0, false, ColorRGBAf::white, 1.0f);
				}
			}
			//在屏幕左边
			else
			{
				uiRenderer.BeginDraw(m_TouchRes4Material, m_TouchRes4);
				int btnw = (int)(75 * scale);
				int btnh = (int)(81 * scale);
				auto element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "lzy_10.png");
				if (element)
					uiRenderer.StretchRect(startx + 30 * scale, starty - btnh - 30 * scale, (float)btnw, (float)btnh, 0xffffffff, element->x, element->y, element->w, element->h, UI_UVT_MIRROEU);

				Rectf rc(startx - 15 * scale, starty - btnh - 30 * scale, 45 * scale, 24 * scale);

				rightToolX = (int)(startx + 95 * scale);
				rightToolY = (int)(starty - 110 * scale);

				btnw = (int)(71 * scale);
				btnh = (int)(77 * scale);

				if (digprogress < 0.5f)
				{
					//只需要绘制一个三角形
					//
					//       0
					//    /  |
					//   2---1
					m_TipTriangleVerts[0].x = startx + btnw + 32 * scale;
					m_TipTriangleVerts[0].y = starty - btnh * 2 * digprogress - 32 * scale;

					m_TipTriangleVerts[1].x = startx + btnw + 32 * scale;
					m_TipTriangleVerts[1].y = starty - 32 * scale;

					m_TipTriangleVerts[2].x = startx + 32 * scale;
					m_TipTriangleVerts[2].y = starty - 32 * scale;

					m_TipTriangleTexCood[0].x = proX;
					m_TipTriangleTexCood[0].y = proY + (1 - 2 * digprogress) * 77;

					m_TipTriangleTexCood[1].x = proX;
					m_TipTriangleTexCood[1].y = proY + 77;

					m_TipTriangleTexCood[2].x = proX + 71;
					m_TipTriangleTexCood[2].y = proY + 77;

					uiRenderer.StretchTriangle(m_TipTriangleVerts[0], m_TipTriangleTexCood[0], m_TipTriangleVerts[1], m_TipTriangleTexCood[1], m_TipTriangleVerts[2], m_TipTriangleTexCood[2], \
						0xffffffff);

				}
				else
				{
					//要绘制两个三角形
					//   0 ---3
					//   |  \ |
					//   1----2
					m_TipTriangleVerts[0].x = startx + btnw + 32 * scale;
					m_TipTriangleVerts[0].y = starty - btnh - 32 * scale;

					m_TipTriangleVerts[1].x = startx + btnw + 32 * scale;
					m_TipTriangleVerts[1].y = starty - 32 * scale;

					m_TipTriangleVerts[2].x = startx + 32 * scale;
					m_TipTriangleVerts[2].y = starty - 32 * scale;

					m_TipTriangleVerts[3].x = startx + 32 * scale + btnw - (digprogress - 0.5f) * 2 * btnw;
					m_TipTriangleVerts[3].y = starty - btnh - 32 * scale;

					m_TipTriangleTexCood[0].x = proX;
					m_TipTriangleTexCood[0].y = proY;

					m_TipTriangleTexCood[1].x = proX;
					m_TipTriangleTexCood[1].y = proY + 77;

					m_TipTriangleTexCood[2].x = proX + 71;
					m_TipTriangleTexCood[2].y = proY + 77;

					m_TipTriangleTexCood[3].x = proX + (digprogress - 0.5f) * 2 * 71;
					m_TipTriangleTexCood[3].y = proY;

					uiRenderer.StretchTriangle(m_TipTriangleVerts[0], m_TipTriangleTexCood[0], m_TipTriangleVerts[1], m_TipTriangleTexCood[1], m_TipTriangleVerts[2], m_TipTriangleTexCood[2], \
						0xffffffff);

					uiRenderer.StretchTriangle(m_TipTriangleVerts[3], m_TipTriangleTexCood[3], m_TipTriangleVerts[0], m_TipTriangleTexCood[0], m_TipTriangleVerts[2], m_TipTriangleTexCood[2], \
						0xffffffff);
				}
				uiRenderer.EndDraw();
				//MINIW::UIRenderer::GetInstance().StretchRect(startx+32*scale, starty-btnh*digprogress-32*scale, btnw, btnh*digprogress, cor, proX, proY+(1-digprogress)*77, 71, 77*digprogress, UI_UVT_MIRROEU);

				sprintf(tmpbuf, "%d%%", int(digprogress * 100));
				uiRenderer.renderTextRect(oneUIFontConfig.h, FONTSYTLE_NORMAL, tmpbuf, rc, 0, 0, false, ColorRGBAf::white, 1.0f);

				if (isExploit)
				{
					int tmpBtnw = (int)(75 * scale);
					int tmpBtnh = (int)(81 * scale);

					const float w = 60 * scale, h = 24 * scale;
					rc = Rectf(startx - tmpBtnw - w, starty - tmpBtnh - 30 * scale, w, h);

					const UIFont& tmpUIFontConfig = frameManager.getUIFontByIndex(7);
					const char* text = GetDefManagerProxy()->getStringDef(2100);
					uiRenderer.renderTextRect(tmpUIFontConfig.h, FONTSYTLE_NORMAL, text, rc, 0, 0, false, ColorRGBAf::white, 1.0f);
				}
			}
		}

		//不是正确的工具，绘制正确工具图标
		if (!isRightTool)
		{
			int u = 0;
			int v = 0;
			int w = 0;
			int h = 0;
			int r = 255;
			int g = 255;
			int b = 255;


			int toolid = 0;

			//家园回收工具没有配在tool表里，这里单独处理
			SandboxResult homelandret = SandboxEventDispatcherManager::GetGlobalInstance().
				Emit("Homeland_GetRightTool", SandboxContext(nullptr).SetData_Number("type", 2));
			if (homelandret.IsSuccessed())
			{
				toolid = g_pPlayerCtrl->getCurToolID();
			}
			else
			{
				std::map<int, int>& mineToolIcon = GetDefManagerProxy()->getMineToolIcon();
				auto it = mineToolIcon.find(blockDef->MineTool * 100 + blockDef->ToolLevel);
				if (it != mineToolIcon.end())
				{
					toolid = it->second;
				}
			}

			SharePtr<Texture2D> icon = nullptr;
			ItemIconManager* itemIconModule = GET_SUB_SYSTEM(ItemIconManager);
			if (itemIconModule)
			{
				icon = itemIconModule->getItemIcon(toolid, u, v, w, h, r, g, b);
			}
			if (icon)
			{
				uiRenderer.EndDraw();
				uiRenderer.BeginDraw(m_IconMaterial, icon);

				uiRenderer.StretchRect((float)rightToolX, (float)rightToolY, (float)35, (float)35, 0xffffffff, u, v, w, h);
			}
		}

	}

	uiRenderer.EndDraw();
}

static float NoviceUIScale = 1.5;
void TouchControl::renderNoviceUIEffect()
{
	//float scale = UILib::GetScreenUIScale();

	//if(g_pPlayerCtrl->getOWID() == NEWBIEWORLDID && GetClientInfoProxy()->getCurNoviceGuideTask() == 4)
	//{
	//	MINIW::UIRenderer::GetInstance().BeginDraw(3);

	//	int x = Root::GetInstance().getClientInputWidth() - 90*scale;
	//	int y = Root::GetInstance().getClientHeight() - 100*scale;
	//	NoviceUIScale -= 0.01;
	//	if(NoviceUIScale < 1)
	//	{
	//		NoviceUIScale = 1.5;
	//	}
	//	int width = 100 * NoviceUIScale * scale;
	//	MINIW::UIRenderer::GetInstance().StretchRect(x-width/2, y-width/2, width, width, 0xffffffff, 281, 729, 100, 100);

	//	MINIW::UIRenderer::GetInstance().EndDraw();
	//}
}

//方向键判断
bool TouchControl::IsInControl(int x, int y, int& direction)
{
	const TouchControl& self = *this;
	return self.IsInControl(x, y, direction);
}

bool TouchControl::IsInControl(int x, int y, int& direction) const
{
	const float scale = UILib::GetScreenUIScale();
	Point2D pt;
	int btnw = (140 * scale);
	//经典模式下的方向判断（固定区域）
	if (!isRockerMode()) {
		//前进方向
		pt = Point2D(int(59 * scale), YFromBottom(418));
		if (x >= pt.x && x <= pt.x + 254 * scale && y >= pt.y && y <= pt.y + btnw)
		{
			//前
			if (x > pt.x + 86 * scale && x <= pt.x + 166 * scale && y >= pt.y && y <= pt.y + btnw)
			{
				if ((direction & 1) == 0)
				{
					if (direction > 0)
					{
						direction = 0;
						return false;
					}
					else
					{
						direction = 1;
						return true;
					}
				}
			}
			if (m_isForward)
			{
				//左前
				if (x >= pt.x && x <= pt.x + 86 * scale && y >= pt.y && y <= pt.y + btnw)
				{
					if ((direction & 16) == 0)
					{
						if (direction > 0)
						{
							direction = 0;
							return false;
						}
						else
						{
							direction = 16;
							return true;
						}
					}
				}

				//右前
				if (x > pt.x + 166 * scale && x <= pt.x + 254 * scale && y >= pt.y && y <= pt.y + btnw)
				{
					if ((direction & 32) == 0)
					{
						if (direction > 0)
						{
							direction = 0;
							return false;
						}
						else
						{
							direction = 32;
							return true;
						}
					}
				}
			}
		}

		//后
		pt = Point2D(int(145 * scale), YFromBottom(192));
		if (x >= pt.x && x <= pt.x + 80 * scale && y >= pt.y && y <= pt.y + btnw)
		{
			if ((direction & 2) == 0)
			{
				if (direction > 0)
				{
					direction = 0;
					return false;
				}
				else
				{
					direction = 2;
					return true;
				}

			}
		}
		//左
		pt = Point2D(int(38 * scale), YFromBottom(332));
		if (x >= pt.x && x <= pt.x + 100 * scale && y >= pt.y && y <= pt.y + btnw)
		{
			if ((direction & 4) == 0)
			{
				if (direction > 0)
				{
					direction = 0;
					return false;
				}
				else
				{
					direction = 4;
					return true;
				}
			}
		}

		//右
		pt = Point2D(int(253 * scale), YFromBottom(332));
		if (x >= pt.x - 20 * scale && x <= pt.x + btnw && y >= pt.y && y <= pt.y + btnw)
		{
			if ((direction & 8) == 0)
			{
				if (direction > 0)
				{
					direction = 0;
					return false;
				}
				else
				{
					direction = 8;
					return true;
				}
			}
		}

		//跳
		pt = Point2D(int(141 * scale), YFromBottom(278));
		if (x >= pt.x && x <= pt.x + 91 * scale && y >= pt.y && y <= pt.y + 86 * scale)
		{
			if ((direction & 64) == 0)
			{
				if (direction > 0)
				{
					direction = 0;
					return false;
				}
				else
				{
					direction = 64;
					return true;
				}
			}
		}

		//左后
		pt = Point2D(int(59 * scale), YFromBottom(192));
		if (x >= pt.x && x < pt.x + 86 * scale && y >= pt.y && y < pt.y + btnw)
		{
			if ((direction & 128) == 0)
			{
				if (direction > 0)
				{
					direction = 0;
					return false;
				}
				else
				{
					direction = 128;
					return true;
				}
			}
		}

		//右后
		pt = Point2D(int(225 * scale), YFromBottom(192));
		if (x > pt.x && x < pt.x + 88 * scale && y > pt.y && y < pt.y + btnw)
		{
			if ((direction & 256) == 0)
			{
				if (direction > 0)
				{
					direction = 0;
					return false;
				}
				else
				{
					direction = 256;
					return true;
				}
			}
		}

		return true;
	}
	//准心模式下，触摸区域不固定，以中心点为基点
	else
	{
		//右手定则判断当前点的位置
		//如果点不在虚拟摇杆上。不做判断
		if (!((m_MoveStart.x - btnw / 2 < x) && (x < m_MoveStart.x + btnw / 2)) ||
			!((m_MoveStart.y - btnw / 2 < y) && (y < m_MoveStart.y + btnw / 2)))
		{
			return true;
		}
		calculateModeDir(x,y, direction);
	}
	return true;
}

void TouchControl::calculateModeDir(int x, int y, int& direction) const
{
	Point2D DirA = Point2D(m_MoveStart.x + 1 - (m_MoveStart.x - 1), m_MoveStart.y + 1 - (m_MoveStart.y - 1));
	Point2D DirB = Point2D(m_MoveStart.x - 1 - (m_MoveStart.x + 1), m_MoveStart.y + 1 - (m_MoveStart.y - 1));
	Point2D DirAP = Point2D(x - m_MoveStart.x - 1, y - m_MoveStart.y - 1);
	Point2D DirBP = Point2D(x - m_MoveStart.x + 1, y - m_MoveStart.y + 1);
	//向量叉乘判断P相对于A,B向量的位置
	int rA = DirA.x * DirAP.y - DirA.y * DirAP.x;
	int rB = DirB.x * DirBP.y - DirB.y * DirBP.x;
	//前
	if (rA < 0 && rB > 0)
	{
		direction = 1;
		return;
	}
	//后
	else if (rA > 0 && rB < 0)
	{
		direction = 2;
		return;
	}
	//左
	else if (rA > 0 && rB > 0)
	{
		direction = 4;
		return;
	}
	//右
	else if (rA < 0 && rB < 0)
	{
		direction = 8;
		return;
	}
	//右上方
	else if (rA == 0 && y < m_MoveStart.y)
	{
		direction = 32;
		return;
	}
}

void TouchControl::addBeforeDir()
{
	if (g_pPlayerCtrl == NULL)
	{
		return;
	}

	if (m_beforeDir & 1)
	{
		g_pPlayerCtrl->m_MoveForward = 1.0f;
		g_pPlayerCtrl->m_MoveRight = 0.0f;
	}
	//else if(m_beforeDir & 2)
	//{
	//	g_pPlayerCtrl->m_MoveForward = -1.0f;
	//	g_pPlayerCtrl->m_MoveRight = 0.0f;
	//}
	//else if(m_beforeDir & 4)
	//{
	//	g_pPlayerCtrl->m_MoveForward =	0.0f;
	//	g_pPlayerCtrl->m_MoveRight = -1.0f;
	//}
	//else if(m_beforeDir & 8)
	//{
	//	g_pPlayerCtrl->m_MoveForward =	0.0f;
	//	g_pPlayerCtrl->m_MoveRight = 1.0f;
	//}
	else
	{
		if (m_MoveID < 0)
		{
			g_pPlayerCtrl->m_MoveForward = 0.0f;
			g_pPlayerCtrl->m_MoveRight = 0.0f;
		}

	}
}

bool TouchControl::isShowRocker()
{
	if (m_bHideRockerUi || !m_showOperateUI || g_pPlayerCtrl == NULL)
		return false;

	if (g_pPlayerCtrl->getOWID() != NEWBIEWORLDID)
		return true;

	int level = GetClientInfoProxy()->getCurGuideLevel();
	int step = GetClientInfoProxy()->getCurGuideStep();

	if (level == 1)
	{
		if (step == 1 || step == 5 || step == 6 || step == 7 || step == 9 || step == 11 || step == 12 || step == 13 || step == 14 || step == 15 || step == 16 || step == 18)
		{
			return false;
		}
	}

	return true;
}

bool TouchControl::isLockCamera()
{
	if (g_pPlayerCtrl == NULL)
	{
		return false;
	}

	if (g_pPlayerCtrl->getOWID() != NEWBIEWORLDID)
	{
		return false;
	}

	int level = GetClientInfoProxy()->getCurGuideLevel();
	int step = GetClientInfoProxy()->getCurGuideStep();

	if (level == 1)
	{
		if (step == 9 || step == 11 || step == 12 || step == 13 || step == 14 || step == 15)
		{
			return true;
		}
	}

	return false;
}

bool TouchControl::canOnclick()
{
	if (g_pPlayerCtrl == NULL)
	{
		return false;
	}

	if (g_pPlayerCtrl->getOWID() != NEWBIEWORLDID)
		return true;

	int level = GetClientInfoProxy()->getCurGuideLevel();
	int step = GetClientInfoProxy()->getCurGuideStep();

	if (level == 1)
	{
		if (step != 14)
		{
			return false;
		}
	}

	return true;
}

bool TouchControl::canPunch()
{
	if (g_pPlayerCtrl == NULL)
	{
		return false;
	}

	if (g_pPlayerCtrl->getOWID() != NEWBIEWORLDID)
		return true;

	int level = GetClientInfoProxy()->getCurGuideLevel();
	int step = GetClientInfoProxy()->getCurGuideStep();

	if (level == 1)
	{
		if (step != 9 && step != 15)
		{
			return false;
		}
	}

	return true;
}

bool TouchControl::isRockerMode()
{
	const TouchControl& self = *this;
	return self.isRockerMode();
}

bool TouchControl::isRockerMode() const
{
	if (g_pPlayerCtrl && g_pPlayerCtrl->getOWID() == NEWBIEWORLDID)
		return true;

	return m_rockerControl;
}

void TouchControl::setAccumulatorState(float progress)
{
	m_AccumulatorProgress = progress;
}

void TouchControl::triggerHeadshotTip(bool isDead)
{
	m_EnableHeadshotTip = true;
	m_EnbaleDeadshotTip = isDead;
	m_TipTrans = 0;
	m_TipStartTime = Timer::getSystemTick();
	m_TipDuration = 0;
}

void TouchControl::triggerNormalshotTip(bool isDead)
{
	m_EnbaleNormalshotTip = true;
	m_EnbaleDeadshotTip = isDead;
	m_TipTrans = 0;
	m_TipStartTime = Timer::getSystemTick();
	m_TipDuration = 0;
}

float TouchControl::GetAxis(int axisId)
{
	//vertical
	if (axisId == 0)
	{
		if (m_KeyMap['W'])
			return 1;
		if (m_KeyMap['S'])
		{
			if (m_KeyMap['W'])
			{
				return 0;
			}
			return -1;
		}
	}
	//horizontal
	else if (axisId == 1)
	{
		if (m_KeyMap['D'])
			return 1;
		if (m_KeyMap['A'])
		{
			if (m_KeyMap['D'])
			{
				return 0;
			}
			return -1;
		}
	}
	return 0;
}

void TouchControl::SetMore()
{
	moreTrigger = true;
	hasmore = true;
	moreTriggerMark = m_selfUpdateCount;
}

void TouchControl::SetInteractive()
{
	interactiveTrigger = true;
	hasinteractive = true;
	interactiveTriggerMark = m_selfUpdateCount;
}

void TouchControl::SetSnakeState(bool b)
{
	m_SneakState = b;
}

bool TouchControl::GetSnakeState()
{
	return m_SneakState;
}

void TouchControl::SetButton(int key, bool val)
{
	m_ButtonMap[(UIButtonKey)key] = val;
}

void TouchControl::SetButtonDown(int key, bool val)
{
	m_ButtonDownMap[(UIButtonKey)key] = val;
	m_ButtonDownMarks[(UIButtonKey)key] = m_selfUpdateCount;

	// if ((UIButtonKey)BUTTON_ACTION == key) {
		// m_RotateID = 999;
		// m_RotateTime = Timer::getSystemTick();
		// m_RotateSubType = 0;
		// m_RotateStart.SetElement(960, 540);
	// }
}

void TouchControl::SetButtonUp(int key, bool val)
{
	m_ButtonUpMap[(UIButtonKey)key] = val;
	m_ButtonUpMarks[(UIButtonKey)key] = m_selfUpdateCount;

	// if ((UIButtonKey)BUTTON_ACTION == key) {
	// 	m_RotateID = -1;
	// 	m_RotateTime = -1;
	// }
}

bool TouchControl::GetButton(UIButtonKey key)
{
	//if (!g_pPlayerCtrl->GetAllowInput())
	//	return false;

	return m_ButtonMap[key];
}

bool TouchControl::GetButtonDown(UIButtonKey key)
{
	//if (!g_pPlayerCtrl->GetAllowInput())
	//	return false;

	return m_ButtonDownMap[key];
}

bool TouchControl::GetButtonUp(UIButtonKey key)
{
	//if (!g_pPlayerCtrl->GetAllowInput())
	//	return false;

	return m_ButtonUpMap[key];
}

bool TouchControl::GetKey(int id)
{
	//if (!g_pPlayerCtrl->GetAllowInput())
	//	return false;

	return m_KeyMap[id];
}

bool TouchControl::GetKeyUp(int id)
{
	//if (!g_pPlayerCtrl->GetAllowInput())
	//	return false;

	return m_KeyUpMap[id];
}

bool TouchControl::GetKeyDown(int id)
{
	//if (!g_pPlayerCtrl->GetAllowInput())
	//	return false;

	return m_KeyDownMap[id];
}

void TouchControl::ResetKey(int id)
{
	m_KeyMap[id] = false;
}

void TouchControl::ResetAllInput()
{
	m_KeyMap.clear();
	m_KeyUpMap.clear();
	m_KeyDownMap.clear();

	m_KeyDownTime.clear();
	m_KeyUpMarks.clear();
	m_KeyDownMarks.clear();

	m_ButtonMap.clear();
	m_ButtonDownMap.clear();
	m_ButtonUpMap.clear();
	m_ButtonUpMarks.clear();
	m_ButtonDownMarks.clear();

	m_ButtonUpMap[BUTTON_ACTION] = true;
	m_ButtonUpMarks[BUTTON_ACTION] = m_selfUpdateCount;
	m_ButtonUpMap[BUTTON__PASSBALL_OR_CATCHBALL] = true;
	m_ButtonUpMarks[BUTTON__PASSBALL_OR_CATCHBALL] = m_selfUpdateCount;
}

void TouchControl::SetSocJoystickDir(float x, float y)
{
	m_JoystickDirx = x;
	m_JoystickDiry = y;
}

void TouchControl::SetSocDpadValue(float vertical, float horizontal)
{
	m_DpadValueVertical = vertical;
	m_DpadValueHorizontal = horizontal;
}

void TouchControl::GetSocDpadValueRaw(float& forward, float& strafe)
{
	// moveStrafe 是左右移动: -1是左，1是右
	// moveForward 是前后移动: -1是后，1是前
	
	// 初始化输出值
	forward = 0.0f;
	strafe = 0.0f;
	
	if (m_JoystickDirx == 0 && m_JoystickDiry == 0)
	{
		return;
	}
	
	// 计算摇杆角度 (弧度)
	float angle = atan2(m_JoystickDiry, m_JoystickDirx);
	
	// 将弧度转换为度数，并确保在0-360范围内
	float degrees = angle * 180.0f / 3.14159265359f;
	if (degrees < 0.0f)
	{
		degrees += 360.0f;
	}
	
	// 将360度分为8个方向，每个方向45度
	// 0度: 右, 45度: 右上, 90度: 上, 135度: 左上
	// 180度: 左, 225度: 左下, 270度: 下, 315度: 右下
	
	if (degrees >= 337.5f || degrees < 22.5f)
	{
		// 右 (0度)
		strafe = 1.0f;
		forward = 0.0f;
	}
	else if (degrees >= 22.5f && degrees < 67.5f)
	{
		// 右上 (45度)
		strafe = 1.0f;
		forward = 1.0f;
	}
	else if (degrees >= 67.5f && degrees < 112.5f)
	{
		// 上 (90度)
		strafe = 0.0f;
		forward = 1.0f;
	}
	else if (degrees >= 112.5f && degrees < 157.5f)
	{
		// 左上 (135度)
		strafe = -1.0f;
		forward = 1.0f;
	}
	else if (degrees >= 157.5f && degrees < 202.5f)
	{
		// 左 (180度)
		strafe = -1.0f;
		forward = 0.0f;
	}
	else if (degrees >= 202.5f && degrees < 247.5f)
	{
		// 左下 (225度)
		strafe = -1.0f;
		forward = -1.0f;
	}
	else if (degrees >= 247.5f && degrees < 292.5f)
	{
		// 下 (270度)
		strafe = 0.0f;
		forward = -1.0f;
	}
	else if (degrees >= 292.5f && degrees < 337.5f)
	{
		// 右下 (315度)
		strafe = 1.0f;
		forward = -1.0f;
	}
}

void TouchControl::GetSocDpadValue(float& vertical, float& horizontal)
{
	vertical = m_DpadValueVertical;
	horizontal = m_DpadValueHorizontal;
}

void TouchControl::tick()
{
#ifndef DEDICATED_SERVER
	if (g_pPlayerCtrl == NULL)
	{
		return;
	}

	if (m_GuideTick >= 0)
		m_GuideTick++;
	auto player = dynamic_cast<IClientPlayer*>(g_pPlayerCtrl);
	int opreateNum = 0;
	if (m_RotateID >= 0)
	{
		Touch touch;
		if (!GetTouch(m_RotateID, touch))
			return;

		if (m_RotateSubType == 0)
		{
			/*const bool isLongPressEvent = touch.deltaTime > 0.3f;
			if (isLongPressEvent && m_UseID < 0 && canPunch())*/
			if (Rainbow::Timer::getSystemTick() - m_RotateTime > 300 && m_UseID < 0 && canPunch())
			{
				m_RotateSubType = 2;
				longPressTriggerMark = m_selfUpdateCount;
				triggerLongPress = true;
				isLongPress = true;
			}
		}

		longPressX = 0.5f;
		longPressY = 0.5f;

		//采集模式
		if (m_RotateSubType == 2 && !m_sightModel)
		{
			longPressX = float(touch.pos.x) / GetGameUIPtr()->GetWidth();
			longPressY = float(touch.pos.y) / GetGameUIPtr()->GetHeight();
		}

		if (m_RotateSubType == 1 && g_pPlayerCtrl->getOWID() == NEWBIEWORLDID)
		{
			int level = GetClientInfoProxy()->getCurGuideLevel();
			int step = GetClientInfoProxy()->getCurGuideStep();
			if (level == 1)
			{
				if (step == 3 || step == 4)
					opreateNum++;
				else if ((step == 5 || step == 6) && m_GuideTick < 0)
				{
					m_GuideTick = 0;
				}
			}
		}
	}

#ifdef _WIN32m_KeyMap
	if (opreateNum >= 1 && m_GuideTick < 0)
		m_GuideTick = 0;
#else
	if (opreateNum >= 1 && m_GuideTick < 0)
		m_GuideTick = 0;
#endif

	if (m_GuideTick >= 20)
	{
		m_GuideTick = -1;
		if (m_MoveID >= 0)
		{
			m_MoveID = -1;
		}
		if (m_RotateID >= 0)
		{
			m_RotateID = -1;
			m_RotateTime = -1;
		}
		g_pPlayerCtrl->checkNewbieWorldProgress(1, GetClientInfoProxy()->getCurGuideStep());
	}

	if (m_BasketBallLockState)
	{
		if (m_BasketBallLockTime > 0)
			m_BasketBallLockTime -= 1;
		else
			m_BasketBallLockTime = 20;
	}

	if (m_iAutoFireState == 1)
	{
		if (m_iAutoAimTime > 0)
			m_iAutoAimTime--;
	}

	if (!m_Triggerkeys.empty())
	{
		auto iter = m_Triggerkeys.begin();
		for (; iter != m_Triggerkeys.end(); iter++)
		{
			if (iter->second >= 5)
			{
				iter->second = 0;
				player->triggerInputEvent(iter->first, "OnPress");
			}
			else
			{
				iter->second++;
			}
		}
	}
#endif
}

void TouchControl::setTriggerKeys(int vkey, char ktype)
{
	//if (vkey == BUTTON_LEFT || vkey == BUTTON_BACK || vkey == BUTTON_RIGHT ||
	//	vkey == BUTTON_FORWARD || vkey == BUTTON_JUMP || vkey == BUTTON_SNEAK)
	//准心模式下
	if (g_pPlayerCtrl == NULL)
	{
		return;
	}

	if (ktype == 'D') //新增
	{
		if (m_Triggerkeys.find(vkey) == m_Triggerkeys.end())
		{
			g_pPlayerCtrl->triggerInputEvent(vkey, "Down");
			m_Triggerkeys[vkey] = 0;
		}
	}
	else if (ktype == 'U') //删除
	{
		auto iter = m_Triggerkeys.find(vkey);
		if (iter != m_Triggerkeys.end())
		{
			m_Triggerkeys.erase(iter);
		}
	}
	if (isRockerMode()) {
		if (vkey == BUTTON_LEFT || vkey == BUTTON_BACK || vkey == BUTTON_RIGHT || vkey == BUTTON_FORWARD)
		{
			//如果已经存在以上键值，则删除,BUTTON_LEFT值为6
			for (int i = 6; i < 10; ++i)
			{
                if (vkey != i) {
                    bool btrigger = false;
                    auto iter = m_Triggerkeys.find(i);
                    while (iter != m_Triggerkeys.end()) {
                        m_Triggerkeys.erase(iter);
                        iter = m_Triggerkeys.find(i);
                        btrigger = true;
                    }
                    if (btrigger) {
						g_pPlayerCtrl->triggerInputEvent(i, "Up");
                    }
                }
			}

		}
	}
}


//在隐藏UI的情况下控制人物
bool TouchControl::touchForHUD(const Rainbow::InputEvent& event)
{
	const int frameCount = m_selfUpdateCount;
	const int x = event.mousePosition.x;
	const int y = event.mousePosition.y;
	const Rainbow::RectInt& safeArea = GetGameUIPtr()->GetSafeArea();
	const int safeWidth = safeArea.GetWidth();
	const int safeHeight = safeArea.getHeight();
	const int screenHeight = GetGameUIPtr()->GetHeight();

	int touchType = event.type;
	if (touchType == InputEvent::kMouseDrag && g_pPlayerCtrl->isDead())
	{
		//如果死亡后还在移动就调一下抬起
		if (event.button == m_MoveID)
			touchType = InputEvent::kMouseUp;
		
		//死亡后不让移动
		if (touchType == InputEvent::kMouseDrag)
			return true;
	}

	auto player = dynamic_cast<IClientPlayer*>(g_pPlayerCtrl);
	if (touchType == InputEvent::kMouseDown)
	{
		m_RotateID = event.button;
		m_RotateTime = Timer::getSystemTick();
		m_RotateStart.SetElement(x, y);

		if (!isRockerMode())
		{
			m_isCanMove = true;
		}

		int dx = x;
		int dy = screenHeight - y;

		//int r = int(400 * m_ScreenScaleFactor);
		int r = int(safeWidth * 0.4);

		m_FlyType = CheckFlyArea(x, y, m_ScreenScaleFactor);
		int direction = 0;
		if (IsInControl(x, y, direction) && !isRockerMode())
		{
			//前
			if (direction & 1)
			{
				m_ButtonDownMap[BUTTON_FORWARD] = true;
				m_ButtonDownMarks[BUTTON_FORWARD] = frameCount;

				this->setTriggerKeys(BUTTON_FORWARD, 'D');
				player->triggerInputEvent(BUTTON_FORWARD, "Down");
			}
			//左
			if (direction & 4)
			{
				m_ButtonDownMap[BUTTON_LEFT] = true;
				m_ButtonDownMarks[BUTTON_LEFT] = frameCount;

				this->setTriggerKeys(BUTTON_LEFT, 'D');
				player->triggerInputEvent(BUTTON_LEFT, "Down");
			}
			//右
			if (direction & 8)
			{
				m_ButtonDownMap[BUTTON_RIGHT] = true;
				m_ButtonDownMarks[BUTTON_RIGHT] = frameCount;

				this->setTriggerKeys(BUTTON_RIGHT, 'D');
				player->triggerInputEvent(BUTTON_RIGHT, "Down");
			}
			//后
			if (direction & 2)
			{
				m_ButtonDownMap[BUTTON_BACK] = true;
				m_ButtonDownMarks[BUTTON_BACK] = frameCount;

				this->setTriggerKeys(BUTTON_BACK, 'D');
				player->triggerInputEvent(BUTTON_BACK, "Down");
			}
			/*//中
			if (direction & 64)
			{
			}*/

			//左前
			if (direction & 16)
			{
				m_ButtonDownMap[BUTTON_FORWARDLEFT] = true;
				m_ButtonDownMarks[BUTTON_FORWARDLEFT] = frameCount;

				this->setTriggerKeys(BUTTON_FORWARD, 'D');
				player->triggerInputEvent(BUTTON_FORWARD, "Down");
				this->setTriggerKeys(BUTTON_LEFT, 'D');
				player->triggerInputEvent(BUTTON_LEFT, "Down");
			}
			//右前
			if (direction & 32)
			{
				m_ButtonDownMap[BUTTON_FORWARDRIGHT] = true;
				m_ButtonDownMarks[BUTTON_FORWARDRIGHT] = frameCount;

				this->setTriggerKeys(BUTTON_FORWARD, 'D');
				player->triggerInputEvent(BUTTON_FORWARD, "Down");
				this->setTriggerKeys(BUTTON_RIGHT, 'D');
				player->triggerInputEvent(BUTTON_RIGHT, "Down");
			}
		}
		//跳跃判断
		else if (IsInJumpCircle(x, y, m_ScreenScaleFactor) && m_JumpID < 0 && m_showOperateUI && !m_bHideJumpUi)
		{
			//m_JumpID = event.button;
			//m_JumpTime = 0;
			//m_JumpSubType = 0;
			//m_JumpPos.SetElement(x, y);
			//m_ButtonMap[BUTTON_JUMP] = true;
			//m_ButtonDownMap[BUTTON_JUMP] = true;
			//m_ButtonMap[BUTTON_FLYDOWN] = false;
			//m_ButtonDownMarks[BUTTON_JUMP] = frameCount;

			//this->setTriggerKeys(BUTTON_JUMP, 'D');
			//player->triggerInputEvent(BUTTON_JUMP, "Down");
		}
		else if (dx * dx + dy * dy < r * r && !IsInUseCircle(x, y, m_ScreenScaleFactor))
		{
			if (isRockerMode() && TouchControl::isShowRocker())
			{
				int limitw = int(MOVE_WIDTH * m_ScreenScaleFactor) / 2;
				int x0 = std::max(x, limitw);
				int y0 = std::max(y, limitw);

				if (y0 > screenHeight - limitw)
					y0 = screenHeight - limitw;

				m_MoveID = event.button;
				m_MoveStart.SetElement(x0, y0);
				m_MovePos.SetElement(x0, y0);
			}

		}
		else if (m_RotateID < 0)
		{
			//设置挖掘提示进度条的方向
			if (x >= int(GetGameUIPtr()->GetWidth() * 0.5f))
			{
				m_BarDirection = 1;
			}
			else
			{
				m_BarDirection = 2;
			}

			m_RotateID = event.button;
			m_RotateTime = Timer::getSystemTick();
			m_RotateSubType = 0;
			m_RotateStart.SetElement(x, y);

		}

		return true;
	}
	else if (touchType == InputEvent::kMouseUp)
	{
		m_RotateID = -1;
		m_RotateTime = -1;
		touchMoveDx = 0;
		touchMoveDy = 0;
		if (event.button == m_MoveID && isRockerMode())
		{
			m_MoveID = -1;
			m_RockerTouchDx = 0;
			m_RockerTouchDy = 0;
		}
		else if (event.button == m_JumpID)
		{
			//m_JumpID = -1;
			//m_JumpSubType = -1;
			//m_ButtonMap[BUTTON_JUMP] = false;
			//m_ButtonUpMap[BUTTON_JUMP] = true;
			//m_ButtonUpMarks[BUTTON_JUMP] = frameCount;

			//this->setTriggerKeys(BUTTON_JUMP, 'U');
			//player->triggerInputEvent(BUTTON_JUMP, "Up");
		}
		else if (event.button == m_ReloadID)
		{

			m_ReloadID = -1;
			m_ButtonMap[BUTTON_GUNRELOAD] = false;
			m_ButtonUpMap[BUTTON_GUNRELOAD] = true;
			m_ButtonUpMarks[BUTTON_GUNRELOAD] = frameCount;
		}
		else if (event.button == m_RotateID)
		{
			m_RotateID = -1;
			m_RotateTime = -1;
			isLongPress = false;

			if (m_RotateSubType == 2)
			{
				longPressEnd = true;
				longPressEndMark = frameCount;
			}

			//点击处理
			if (m_RotateSubType == 0)
			{
				float x, y;
				if (m_sightModel) x = y = 0.5f;
				else
				{
					x = event.mousePosition.x / float(GetGameUIPtr()->GetWidth());
					y = event.mousePosition.y / float(GetGameUIPtr()->GetHeight());
				}

				if (canOnclick())
				{
					m_ButtonDownMap[BUTTON_SCREENTAP] = true;
					m_ButtonUpMarks[BUTTON_SCREENTAP] = frameCount;
					tapPosX = x;
					tapPosY = y;
					hasTap = true;
					tapTriggerMark = frameCount;
				}
			}
		}
		int direction = 0;

		int dx = x;
		int dy = screenHeight - y;

		//int r = int(400 * m_ScreenScaleFactor);
		int r = int(safeWidth * 0.4);
		if (IsInControl(x, y, direction) && !isRockerMode())
		{
			if (direction & 1) //前
			{
				this->setTriggerKeys(BUTTON_FORWARD, 'U');
				player->triggerInputEvent(BUTTON_FORWARD, "Up");
			}
			else if (direction & 4) //左
			{
				this->setTriggerKeys(BUTTON_LEFT, 'U');
				player->triggerInputEvent(BUTTON_LEFT, "Up");
			}
			else if (direction & 8) //右
			{
				this->setTriggerKeys(BUTTON_RIGHT, 'U');
				player->triggerInputEvent(BUTTON_RIGHT, "Up");
			}
			else if (direction & 2) //后
			{
				this->setTriggerKeys(BUTTON_BACK, 'U');
				player->triggerInputEvent(BUTTON_BACK, "Up");
			}
			else if (direction & 16) //左前
			{
				this->setTriggerKeys(BUTTON_FORWARD, 'U');
				player->triggerInputEvent(BUTTON_FORWARD, "Up");
				this->setTriggerKeys(BUTTON_LEFT, 'U');
				player->triggerInputEvent(BUTTON_LEFT, "Up");
			}
			else if (direction & 32) //右前
			{
				this->setTriggerKeys(BUTTON_FORWARD, 'U');
				player->triggerInputEvent(BUTTON_FORWARD, "Up");
				this->setTriggerKeys(BUTTON_RIGHT, 'U');
				player->triggerInputEvent(BUTTON_RIGHT, "Up");
			}
		}
		else if (event.button == m_JumpID)
		{
			m_JumpID = -1;
			m_JumpSubType = -1;
			m_ButtonMap[BUTTON_JUMP] = false;
			m_ButtonUpMap[BUTTON_JUMP] = true;
			m_ButtonUpMarks[BUTTON_JUMP] = frameCount;

			this->setTriggerKeys(BUTTON_JUMP, 'U');
			player->triggerInputEvent(BUTTON_JUMP, "Up");
		}

		if (isRockerMode())
		{
			for (int i = 6; i < 10; ++i)
			{
                    bool btrigger = false;
                    auto iter = m_Triggerkeys.find(i);
                    while (iter != m_Triggerkeys.end()) {
                        m_Triggerkeys.erase(iter);
                        iter = m_Triggerkeys.find(i);
                        btrigger = true;
                    }
                    if (btrigger) {
                        player->triggerInputEvent(i, "Up");
                    }
			}
		}
		return true;
	}
	else if (touchType == InputEvent::kMouseDrag)
	{
		if (event.button == m_MoveID && isRockerMode())
		{
			m_RockerTouchDx = x - m_MovePos.x;
			m_RockerTouchDy = y - m_MovePos.y;

			//LOG_INFO("m_RockerTouchDy %f", GetRockerTouchDy());

			m_MovePos.SetElement(event.mousePosition.x, event.mousePosition.y);
		}
		else if (event.button == m_RotateID)
		{
			if (m_RotateSubType == 0)
			{
				int maxmove = Max(Abs(x - m_RotateStart.x), Abs(y - m_RotateStart.y));
				if (maxmove > 20)
				{
					m_RotateSubType = 1;
					m_RotateStart.SetElement(x, y);
				}
			}
			else
			{
				int dx = x - m_RotateStart.x;
				int dy = y - m_RotateStart.y;
				if (m_reversalY) dy = -dy;

				if (g_pPlayerCtrl->getOWID() == NEWBIEWORLDID && GetClientInfoProxy()->getCurGuideLevel() == 1)
				{
					int step = GetClientInfoProxy()->getCurGuideStep();
					if (step < 5)
						dy = 0;
					if (step == 5 || step == 6)
						dx = 0;
				}
				if ((Abs(dx) > 0 || Abs(dy) > 0) && !isLockCamera())
				{
					if (m_RotateSubType != 2)
						m_RotateSubType = 1;
					touchMoveDx = dx;
					touchMoveDy = dy;
					m_RotateStart.SetElement(x, y);
					g_pPlayerCtrl->checkNewbieWorldProgress(3, "rotationview");
				}
				else
				{
					touchMoveDx = 0;
					touchMoveDy = 0;
				}
			}
		}
		else if (event.button == m_JumpID)
		{
			//if (m_JumpSubType == 0)
			//{
			//	int maxmove = Max(Abs(x - m_JumpPos.x), Abs(y - m_JumpPos.y));
			//	if (maxmove > 5 * UILib::GetScreenUIScale() * m_DpiScale)
			//	{
			//		m_JumpSubType = 1;
			//		m_JumpPos.SetElement(x, y);
			//	}
			//}
			//else
			//{
			//	int dx = x - m_JumpPos.x;
			//	int dy = y - m_JumpPos.y;
			//	if (m_reversalY) dy = -dy;

			//	if (Abs(dx) > 0 || Abs(dy) > 0)
			//	{
			//		touchMoveDx = dx;
			//		touchMoveDy = dy;
			//		m_JumpPos.SetElement(x, y);
			//	}
			//	else
			//	{
			//		touchMoveDx = 0;
			//		touchMoveDy = 0;
			//	}
			//}
		}

		RotateCamera();

		if (event.button == m_MoveID && isRockerMode())
		{
			m_RockerTouchDx = x - m_MovePos.x;
			m_RockerTouchDy = y - m_MovePos.y;

			//LOG_INFO("m_RockerTouchDy %f", GetRockerTouchDy());

			m_MovePos.SetElement(x, y);
		}

		int x = m_MovePos.x;
		int y = m_MovePos.y;
		int direction = 0;

		if (IsInControl(x, y, direction) && isRockerMode())
		{
			//前
			if (direction & 1)
			{
				this->setTriggerKeys(BUTTON_FORWARD, 'D');
			}
			//左
			else if (direction & 4)
			{
				this->setTriggerKeys(BUTTON_LEFT, 'D');
			}
			//右
			else  if (direction & 8)
			{
				this->setTriggerKeys(BUTTON_RIGHT, 'D');
			}
			//后
			else if (direction & 2)
			{
				this->setTriggerKeys(BUTTON_BACK, 'D');
			}
		}

		return true;
	}

	return true;
}

//在隐藏UI的情况下渲染轮盘，但是透明度为0
void TouchControl::renderUIForHUD()
{
#ifndef DEDICATED_SERVER
	return;

	const float scale = UILib::GetScreenUIScale();
	UIRenderer& uiRenderer = UIRenderer::GetInstance();
	XMLManager& xmlManager = GetGameUIPtr()->GetXMLManager();
	if (g_pPlayerCtrl && !(g_pPlayerCtrl->getMountType() == MOUNT_DRIVE))
	{
		if (isRockerMode() && m_showOperateUI)
		{
			uiRenderer.BeginDraw(m_TouchRes4Material, m_TouchRes4);
			{
				int bgw = int(m_DpadWidth * scale);
				int bgh = int(m_DpadHeight * scale);
				int btnw = int(64 * scale);
				int arroww = int(35 * scale);
				int arrowh = int(18 * scale);
				unsigned int color;
				Point2D pt1, pt2;


				if (m_MoveID >= 0)
				{
					pt1 = m_MoveStart;
					pt2 = CalMovePointInRange(m_MoveStart, m_MovePos, (bgh - btnw) / 2);
					color = 0x00000000;
				}
				else
				{//摇杆中心点
					pt2 = pt1 = Point2D(XFromLeft(133), YFromBottom(151));
					color = 0x00000000;
				}

				if (TouchControl::isShowRocker())
				{
					auto element = xmlManager.requestPackElement("ui/mobile/texture0/operate.xml", "operating_steering_wheel.png");
					if (element)
						uiRenderer.StretchRect((float)(pt1.x - bgw / 2), (float)(pt1.y - bgw / 2), (float)bgw, (float)bgh, color, element->x, element->y, element->w, element->h);

					element = xmlManager.requestPackElement("ui/mobile/texture0/operate.xml", "img_steering_wheel.png");
					if (element)
						uiRenderer.StretchRect((float)(pt2.x - btnw / 2), (float)(pt2.y - btnw / 2), (float)btnw, (float)btnw, color, element->x, element->y, element->w, element->h);
				}
			}
			uiRenderer.EndDraw();
		}
		else if (m_showOperateUI)
		{
			uiRenderer.EndDraw();
			uiRenderer.BeginDraw(m_TouchRes4Material, m_TouchRes4);

			auto element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "icon_rocker.png");


			int direction = 0;
			bool canMove = true;

			const size_t touchCount = GetTouchCount();
			for (size_t i = 0; i < touchCount; ++i)
			{
				Touch touch;
				if(GetTouch(i, touch))
				{
					canMove = IsInControl(touch.pos.x, touch.pos.y, direction);
					if (!canMove)
						break;
				}
			}

			Point2D pt1;
			int btnw = 0;
			unsigned long cor = 0xffffffff;
			//前
			if (direction & 1)
			{
				pt1 = Point2D(int(150 * scale), YFromBottom(373));
				btnw = int(70 * scale);
				cor = 0xccafafaf;
			}
			else
			{
				pt1 = Point2D(int(145 * scale), YFromBottom(378));
				btnw = int(80 * scale);
				cor = 0x66ffffff;

			}
			if (element)
				uiRenderer.StretchRect((float)pt1.x, (float)pt1.y, (float)btnw, (float)btnw, cor, element->x, element->y, element->w, element->h, UI_UVT_TURN270);
			//左
			if (direction & 4)
			{
				pt1 = Point2D(int(43 * scale), YFromBottom(270));
				btnw = int(70 * scale);
				cor = 0xccafafaf;
			}
			else
			{
				pt1 = Point2D(int(38 * scale), YFromBottom(275));
				btnw = int(80 * scale);
				cor = 0x66ffffff;
			}
			if (element)
				uiRenderer.StretchRect((float)pt1.x, (float)pt1.y, (float)btnw, (float)btnw, cor, element->x, element->y, element->w, element->h, UI_UVT_TURN180);
			//右
			if (direction & 8)
			{
				pt1 = Point2D(int(258 * scale), YFromBottom(270));
				btnw = int(70 * scale);
				cor = 0xccafafaf;
			}
			else
			{
				pt1 = Point2D(int(253 * scale), YFromBottom(275));
				btnw = int(80 * scale);
				cor = 0x66ffffff;
			}
			if (element)
				uiRenderer.StretchRect((float)pt1.x, (float)pt1.y, (float)btnw, (float)btnw, cor, element->x, element->y, element->w, element->h);
			//后
			if (direction & 2)
			{
				pt1 = Point2D(int(150 * scale), YFromBottom(167));
				btnw = int(70 * scale);
				cor = 0xccafafaf;
			}
			else
			{
				pt1 = Point2D(int(145 * scale), YFromBottom(172));
				btnw = int(80 * scale);
				cor = 0x66ffffff;
			}
			if (element)
				uiRenderer.StretchRect((float)pt1.x, (float)pt1.y, (float)btnw, (float)btnw, cor, element->x, element->y, element->w, element->h, UI_UVT_TURN90);

			if (m_isForward)
			{
				//左前
				if (direction & 16)
				{
					pt1 = Point2D(int(62 * scale), YFromBottom(363));
					btnw = int(53 * scale);
					cor = 0xccafafaf;
				}
				else
				{
					pt1 = Point2D(int(59 * scale), YFromBottom(366));
					btnw = int(59 * scale);
					cor = 0x66ffffff;
				}
				if (element)
					uiRenderer.StretchRect((float)pt1.x, (float)pt1.y, (float)btnw, (float)btnw, cor, element->x, element->y, element->w, element->h, UI_UVT_NORMAL, 225);
				//右前
				if (direction & 32)
				{
					pt1 = Point2D(int(260 * scale), YFromBottom(363));
					btnw = int(53 * scale);
					cor = 0xccafafaf;
				}
				else
				{
					pt1 = Point2D(int(257 * scale), YFromBottom(366));
					btnw = int(59 * scale);
					cor = 0x66ffffff;
				}

				if (element)
					uiRenderer.StretchRect((float)pt1.x, (float)pt1.y, (float)btnw, (float)btnw, cor, element->x, element->y, element->w, element->h, UI_UVT_NORMAL, 315);
			}

			if (!m_bHideRockerUi)
			{
				element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "icon_rocker_circle.png");
				//中
			}


			if (direction & 64)
			{
				pt1 = Point2D(int(153 * scale), YFromBottom(266));
				if (element)
					uiRenderer.StretchRect((float)pt1.x, (float)pt1.y, 67 * scale, 62 * scale, 0xcc8c8c8c, element->x, element->y, element->w, element->h, UI_UVT_TURN90);
			}
			else
			{
				pt1 = Point2D(int(151 * scale), YFromBottom(278));
				if (element)
					uiRenderer.StretchRect((float)pt1.x, (float)pt1.y, 71 * scale, 66 * scale, 0x668c8c8c, element->x, element->y, element->w, element->h, UI_UVT_TURN90);
			}
			uiRenderer.EndDraw();
			std::string icon_text1 = "";
			std::string icon_text2 = "";
			std::string icon_text3 = "";
			if (m_showOperateUI && !g_pPlayerCtrl->isFlying() && !(g_pPlayerCtrl->getMountType() == MOUNT_DRIVE))
			{
				//int x = XFromRight(187);
				//int y = YFromBottom(179);
				//int nGuideTask = 0;
				//MNSandbox::GetGlobalEvent().Emit<int&>("ClientAccountMgr_getCurNoviceGuideTask", nGuideTask);
				//if (g_pPlayerCtrl->getOWID() != NEWBIEWORLDID || nGuideTask > 2)
				//{
				//	unsigned int color;
				//	if (m_JumpID >= 0)
				//	{
				//		color = 0xffffffff;
				//	}
				//	else
				//	{
				//		color = 0x66ffffff;
				//	}

				//	//auto element = nullptr;
				//	const TexPackElement* element = nullptr;
				//	if (!m_bHideJumpUi)
				//	{
				//		element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "icon_jump.png");
				//	}
				//	if (m_ShowBasketBallUseBtn && !m_bHideActionUi)
				//	{
				//		if (g_pPlayerCtrl && g_pPlayerCtrl->getCatchBall()) {
				//			element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "icon_sprint.png");
				//			icon_text1 = GetDefManagerProxy()->getStringDef(29998);
				//		}
				//		else {
				//			element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "icon_blocking.png");
				//			icon_text1 = GetDefManagerProxy()->getStringDef(29995);
				//		}
				//	}
				//	auto RidComp = g_pPlayerCtrl->getRiddenComponent();
				//	ClientActor* actor = RidComp ? RidComp->getRidingActor() : NULL;
				//	if (actor && actor->getObjType() == OBJ_TYPE_ROCKET)
				//		element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "icon_rocket.png");
				//	if (element && !GetRecordPkgManager().isEdit())
				//		uiRenderer.StretchRect((float)x, (float)y, 148 * scale, 152 * scale, color, element->x, element->y, element->w, element->h);
				//}

			}
		}
	}
#endif
}

void TouchControl::setSensitivity(int senval)
{
	ConstAtLua* pConstAtLua = GetLuaInterfaceProxy().get_lua_const();
	float sensitivity_coef_mobile = 2;
	if (pConstAtLua)
	{
		sensitivity_coef_mobile = pConstAtLua->sensitivity_coef_mobile;
	}
	m_sensityvity = sensitivity_coef_mobile + senval / 50.0f;
	m_isSetSensitivity = true;
	if (g_pPlayerCtrl && g_pPlayerCtrl->getCustomGunComponent())
	{
		g_pPlayerCtrl->getCustomGunComponent()->SetSensitivity();
	}
}

void TouchControl::setSensitivity2(float senval)
{
	m_sensityvity = senval;
	m_isSetSensitivity = false;
}

void TouchControl::setReversalY(int reval)
{
	m_reversalY = reval > 0;
}

void TouchControl::setSightModel(int sightModel)
{
	m_sightModel = sightModel > 0;
}

void TouchControl::setRockerControl(int rocker)
{
	m_rockerControl = rocker > 0;
}

void TouchControl::showOperateUI(bool state)
{
	m_showOperateUI = state;
}
bool TouchControl::getOperateUIState()
{
	return m_showOperateUI;
}


//only get 0,1,-1
void TouchControl::GetDpadValueRaw(float& moveForward, float& moveStrafe)
{
#ifndef DEDICATED_SERVER
	if (g_pPlayerCtrl == NULL)
		return;

	int opreateNum = 0;

	//摇杆状态下禁止触发跳跃
	if (isRockerMode())
	{
		joystickJump = false;
	}

	//摇杆状态下的处理
	if (m_MoveID >= 0 && isRockerMode())
	{
		if (!TouchControl::isShowRocker())
		{
			if (m_MoveID >= 0)
			{
				m_MoveID = -1;
				moveForward = 0;
				moveStrafe = 0;
			}
			return;
		}
		float dx = m_MovePos.x - m_MoveStart.x;
		float dy = m_MovePos.y - m_MoveStart.y;

		float r = Rainbow::Sqrt(dx * dx + dy * dy);
		if (r <= 25.0f * UILib::GetScreenUIScale()) return;

		const float STEP = 30.0f;

		float angle = Rainbow::ATan2ToAngle(dy, dx);
		if (angle < 0) angle += 360.0f;

		if (g_pPlayerCtrl->getViewMode() == CAMERA_TPS_BACK_2)
		{
			angle += 90;
			dx = Rainbow::FastSin(angle);
			dy = Rainbow::FastCos(angle);
		}
		else
		{
			dx = dy = 0;
			int i = int(angle / STEP);
			if (i == 0 || i == 11) dx = 1.0f;
			else if (i == 1)
			{
				dx = 1.0f;
				dy = -1.0f;
			}
			else if (i == 2 || i == 3) dy = -1.0f;
			else if (i == 4)
			{
				dx = -1.0f;
				dy = -1.0f;
			}
			else if (i == 5 || i == 6) dx = -1.0f;
			else if (i == 7)
			{
				dx = -1.0f;
				dy = 1.0f;
			}
			else if (i == 8 || i == 9) dy = 1.0f;
			else if (i == 10)
			{
				dx = 1.0f;
				dy = 1.0f;
			}
		}

		if (g_pPlayerCtrl->getOWID() == NEWBIEWORLDID && GetClientInfoProxy()->getCurGuideLevel() == 1 && GetClientInfoProxy()->getCurGuideStep() == 2 && m_GuideTick < 0)
		{
			m_GuideTick = 0;
		}

		if (g_pPlayerCtrl->getOWID() == NEWBIEWORLDID)
		{
			int level = GetClientInfoProxy()->getCurGuideLevel();
			int step = GetClientInfoProxy()->getCurGuideStep();
			if (level == 1 && (step == 3 || step == 4))
				opreateNum++;
		}

		moveStrafe = dx;
		moveForward = dy;
	}
	//方向键状态
	else if (!isRockerMode() && m_isCanMove)
	{
		int direction = 0;
		bool canMove = true;

		const size_t touchCount = GetTouchCount();
		for(size_t i = 0; i < touchCount; ++i)
		{
			Touch touch;
			if(GetTouch(i, touch))
			{
				canMove = IsInControl(touch.pos.x, touch.pos.y, direction);
				if (!canMove) break;
			}
		}

		m_isForward = true;

		if (direction == 0)
		{
			m_beforeDir = 0;
			if (m_MoveID < 0)
			{
				moveForward = 0.0f;
				moveStrafe = 0.0f;
				m_isCanMove = false;
			}

			m_ControlJumpTime = 0;
			if (m_JumpID < 0)
				joystickJump = false;
		}
		else if (direction & 64)		//跳
		{
			int result = Timer::getSystemTick() - m_ControlJumpTime;
			if (m_ControlJumpTime > 0 && Timer::getSystemTick() - m_ControlJumpTime > 300)
			{
				joystickJump = false;
			}
			else
			{
				if (m_ControlJumpTime == 0)
					m_ControlJumpTime = Timer::getSystemTick();
				if (!g_pPlayerCtrl->GetPlayerControlSneaking())
					joystickJump = true;
			}
			addBeforeDir();
		}
		else
		{
			m_beforeDir = 0;
			m_ControlJumpTime = 0;
			if (m_JumpID < 0)
				joystickJump = false;
			if (direction & 1)		//前
			{
				m_isForward = true;
				moveForward = 1.0f;
				moveStrafe = 0.0f;
				m_beforeDir = 1;
			}
			else if (direction & 2)  //后
			{
				moveForward = -1.0f;
				moveStrafe = 0.0f;
				m_beforeDir = 2;
			}
			else if (direction & 4)	//左
			{
				moveForward = 0.0f;
				moveStrafe = -1.0f;
				m_beforeDir = 4;
			}
			else if (direction & 8)	//右
			{
				moveForward = 0.0f;
				moveStrafe = 1.0f;
				m_beforeDir = 8;
			}
			else if (direction & 16)	//左前
			{
				m_isForward = true;
				moveForward = 1.0f;
				moveStrafe = -1.0f;
				m_beforeDir = 16;
			}
			else if (direction & 32) //右前
			{
				m_isForward = true;
				moveForward = 1.0f;
				moveStrafe = 1.0f;
				m_beforeDir = 32;
			}
			else if (direction & 32) //右前
			{
				m_isForward = true;
				moveForward = 1.0f;
				moveStrafe = 1.0f;
				m_beforeDir = 32;
			}
			else if (direction & 128) //左后
			{

				moveForward = -1.0f;
				moveStrafe = -1.0f;
				m_beforeDir = 128;
			}
			else if (direction & 256) //右后
			{
				moveForward = -1.0f;
				moveStrafe = 1.0f;
				m_beforeDir = 256;
			}
		}
	}
	//pc
	else
	{
		moveForward = GetAxis(0);
		moveStrafe = GetAxis(1);
	}

//#ifdef _WIN32m_KeyMap
//	if (opreateNum >= 1 && m_GuideTick < 0)
//		m_GuideTick = 0;
//#else
//	if (opreateNum >= 1 && m_GuideTick < 0)
//		m_GuideTick = 0;
//#endif

	//if (m_GuideTick >= 20)
	//{
	//	m_GuideTick = -1;
	//	if (m_MoveID >= 0)
	//	{
	//		m_MoveID = -1;
	//		moveForward = 0;
	//		moveStrafe = 0;
	//	}
	//	if (m_RotateID >= 0)
	//	{
	//		m_RotateID = -1;
	//		m_RotateTime = -1;
	//	}
	//	g_pPlayerCtrl->checkNewbieWorldProgress(1, GetClientInfoProxy()->getCurGuideStep());
	//}

#endif
}

void TouchControl::GetDpadValue(float& vertical, float& horizontal)
{
	if (m_MoveID >= 0 && isRockerMode())
	{
		if (!TouchControl::isShowRocker())
		{
			if (m_MoveID >= 0)
			{
				m_MoveID = -1;
				vertical = 0;
				horizontal = 0;
			}
			return;
		}
		float dx = (float)(m_MovePos.x - m_MoveStart.x);
		float dy = (float)(m_MoveStart.y - m_MovePos.y);

		//DeadZone
		float r = Rainbow::Sqrt(dx * dx + dy * dy);
		if (r <= 25.0f * UILib::GetScreenUIScale()) return;

		vertical = dy / m_DpadHeight * 2;
		horizontal = dx / m_DpadWidth * 2;
	}
	else
	{
		vertical = 0;
		horizontal = 0;
	}
}

float TouchControl::GetRockerTouchDy()
{
	return  (float)m_RockerTouchDy;
}

bool checkNeedColoreCursor(PlayerControl* pPlayerCtrl, int& interType, IntersectResult& intersectRt, WCoord& pos, int& color)
{
	// 变色光标和色彩提示判断
	if (pPlayerCtrl && (pPlayerCtrl->getCurToolID() == ITEM_COLORED_GUN || IsColorableItem(pPlayerCtrl->getCurToolID())))
	{
		MINIW::WorldRay ray;
		ActorExcludes excludes;

		excludes.addActor(pPlayerCtrl);
		//Rainbow::Ray rainbow_ray = pPlayerCtrl->getCamera()->getEngineCamera()->ViewportPointToRay(Rainbow::Vector2f(0.5, 0.5));
		Rainbow::Ray rainbow_ray = pPlayerCtrl->getCamera()->getEngineCamera()->ViewportPointToRay( Rainbow::Vector2f( pPlayerCtrl->m_CurMouseX, 1 - pPlayerCtrl->m_CurMouseY ));
		
		ray.m_Origin = Rainbow::WorldPos::fromVector3(rainbow_ray.GetOrigin());
		ray.m_Dir = rainbow_ray.GetDirection();

		ray.m_Range = 6 * 500.0f;
		excludes.addActorWithRiding(pPlayerCtrl);

		if ( interType = (int)pPlayerCtrl->getWorld()->pickAll(ray, &intersectRt, excludes, PICK_METHOD_CLICK) )
		{
			bool need = false;
			//int color;
			//WCoord pos;

			if (interType == 1)
			{
				int blockid = pPlayerCtrl->getWorld()->getBlockID(intersectRt.block);
				int userdata = pPlayerCtrl->getWorld()->getBlockData(intersectRt.block);
				MINIW::ScriptVM::game()->callFunction("IsColorBlock", "i>b", blockid, &need);
				pos = intersectRt.block * BLOCK_SIZE;

				do
				{
					if (blockid == BLOCK_PERISTELE || blockid == BLOCK_TOP_PERISTELE)
					{
						//石柱 柱顶特殊判断
						World* pWorld = pPlayerCtrl->getWorld();
						if (pWorld && pWorld->getContainerMgr())
						{
							PeristeleContainer* container = dynamic_cast<PeristeleContainer*>(pWorld->getContainerMgr()->getContainer(intersectRt.block));
							if (container)
							{
								color = container->getColor();
								break;
							}
						}
					}

					MINIW::ScriptVM::game()->callFunction("GetColorFromBlockInfo", "ii>i", blockid, userdata, &color);
				} while (false);
			}
			else if (interType == 2)
			{
				ClientMob* mob = dynamic_cast<ClientMob*>(intersectRt.actor);
				if (mob && mob->getDef()->ID == 3403)
				{
					color = mob->getColor();
					need = true;
					pos = mob->getPosition();
				}
			}
			return need;
		}
	}
	return false;
}

//彩弹枪吸色
void ColorGunInhale(bool bAimToColorable, int nInterType, WCoord block, ClientActor* actor)
{
	if (bAimToColorable)
	{
		//检测是否可以设置颜色
		bool enable = false;
		MINIW::ScriptVM::game()->callFunction("EnableSetColor", ">b", &enable);
		if (!enable)
		{
			return;
		}

		int color;
		if (nInterType == 1)
		{
			int blockid = g_pPlayerCtrl->getIWorld()->getBlockID(block);
			int userdata = g_pPlayerCtrl->getIWorld()->getBlockData(block);
			MINIW::ScriptVM::game()->callFunction("GetColorFromBlockInfo", "ii>i", blockid, userdata, &color);

			//如果是可染色方块，替换一下手持道具 by:Jeff
			int id = g_pPlayerCtrl->getCurToolID();
			if (g_pPlayerCtrl && IsDyeableBlock(id))
			{
				int new_blockid, new_blockdata;
				int index = g_pPlayerCtrl->getCurShortcut() + g_pPlayerCtrl->getShortcutStartIndex();
				MINIW::ScriptVM::game()->callFunction("Color2BlockInfo", "ii>ii", color, id, &new_blockid, &new_blockdata);
				auto bp = g_pPlayerCtrl->getBackPack();
				if (bp)
				{
					//bp->replaceItem(index, new_blockid, bp->getGridNum(index), -1, 0, 0, nullptr, std::to_string(new_blockdata).c_str());
					GridCopyData data;
					data.resid = new_blockid;
					data.num = bp->getGridNum(index);
					data.userdata_str = std::to_string(new_blockdata).c_str();
					bp->replaceItem_byGridCopyData(data, index);
				}
			}
		}
		else if (nInterType == 2)
		{
			ClientMob* mob = dynamic_cast<ClientMob*>(actor);
			if (mob && mob->getDef()->ID == 3403)
			{
				color = mob->getColor();
			}
		}
		else
		{
			assert(false);
		}
		g_pPlayerCtrl->setSelectedColor(color);
		// 吸色传递bool参数，方便lua处理彩弹枪颜色切换逻辑 by chenshaobin 2023.1.31
		MINIW::ScriptVM::game()->callFunction("SetGunMagazine", "iib", 0, 0, true);
	}
}

void TouchControl::CheckColorCursor()
{
	if (g_pPlayerCtrl == NULL) return;
	if (g_pPlayerCtrl->getViewMode() != CAMERA_FPS && g_pPlayerCtrl->getViewMode() != CAMERA_TPS_BACK && g_pPlayerCtrl->getViewMode() != CAMERA_TPS_BACK_2 && g_pPlayerCtrl->getViewMode() != CAMERA_TPS_BACK_SHOULDER)
		return;

	int color;
	WCoord pos;
	if (checkNeedColoreCursor(g_pPlayerCtrl, m_InterType, m_IntersectResult, pos, color))
	{
		m_AimToColorableObject = true;
		m_NeedShowColorableCursor = true;

		float dis = g_pPlayerCtrl->GetPlayerControlPosition().distanceTo(pos);
		if (dis < 500.f && (!m_HasShownColorTips || m_PreColor != color)
			&& !(g_WorldMgr && g_WorldMgr->isGameMakerRunMode()))
		{
			m_PreColor = color;
			m_HasShownColorTips = true;
			MINIW::ScriptVM::game()->callFunction("ShowColorTip", "i", color);
		}
	}
	else
	{
		// 重置
		m_HasShownColorTips = false;
		m_AimToColorableObject = false;
		m_NeedShowColorableCursor = false;
	}
}

void TouchControl::setVehicleControlUI()
{
#ifndef DEDICATED_SERVER
	if (g_pPlayerCtrl == NULL)
	{
		return;
	}

	ActorVehicleAssemble* vehicle = g_pPlayerCtrl->getDrivingVehicle();
	if (vehicle == nullptr || (vehicle->getEngineNum() <= 0 && vehicle->getSteeringSwitch() == false)) return;

	float scale = UILib::GetScreenUIScale();
	UIRenderer& uiRenderer = UIRenderer::GetInstance();
	XMLManager& xmlManager = GetGameUIPtr()->GetXMLManager();
	if (isRockerMode() && m_showOperateUI)
	{
		uiRenderer.BeginDraw(m_TouchRes4Material, m_TouchRes4);
		{
			int bgw = int(m_DpadWidth * scale);
			int bgh = int(m_DpadHeight * scale);
			int btnw = int(64 * scale);
			int arroww = int(35 * scale);
			int arrowh = int(18 * scale);
			unsigned int color;
			Point2D pt1, pt2;


			if (m_MoveID >= 0)
			{
				pt1 = m_MoveStart;
				pt2 = CalMovePointInRange(m_MoveStart, m_MovePos, (bgh - btnw) / 2);
				color = 0xb2ffffff;
			}
			else
			{//摇杆中心点
				pt2 = pt1 = Point2D(XFromLeft(133), YFromBottom(151));
				color = 0x66ffffff;
			}

			if (TouchControl::isShowRocker())
			{
				auto element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "operating_steering_wheel.png");
				if (element)
					uiRenderer.StretchRect((float)(pt1.x - bgw / 2), (float)(pt1.y - bgw / 2), (float)bgw, (float)bgh, color, element->x, element->y, element->w, element->h);

				// element = XMLManager::GetInstance().requestPackElement("ui/mobile/texture0/ingame.xml", "xsyd_arrow.png");
				// if (element)
				// {
				// 	if (vehicle->getEngineNum() > 0)
				// 	{
				// 		MINIW::UIRenderer::GetInstance().StretchRect(pt1.x - arroww / 2, pt1.y + bgw / 2 - int(35 * scale), arroww, arrowh, color, element->x, element->y, element->w, element->h, UI_UVT_TURN180);
				// 		MINIW::UIRenderer::GetInstance().StretchRect(pt1.x - arroww / 2, pt1.y - bgw / 2 + int(20 * scale), arroww, arrowh, color, element->x, element->y, element->w, element->h);
				// 	}
				// 	if (vehicle->getSteeringSwitch() == true)
				// 	{
				// 		MINIW::UIRenderer::GetInstance().StretchRect(pt1.x - bgw / 2 + int(18 * scale), pt1.y - arroww / 2, arrowh, arroww, color, element->x, element->y, element->w, element->h, UI_UVT_TURN270);
				// 		MINIW::UIRenderer::GetInstance().StretchRect(pt1.x + bgw / 2 - int(19 * scale) - arrowh, pt1.y - arroww / 2, arrowh, arroww, color, element->x, element->y, element->w, element->h, UI_UVT_TURN90);
				// 	}
				// }

				element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "img_steering_wheel.png");
				if (element)
					uiRenderer.StretchRect((float)(pt2.x - btnw / 2), (float)(pt2.y - btnw / 2), (float)btnw, (float)btnw, color, element->x, element->y, element->w, element->h);
			}
		}
		uiRenderer.EndDraw();
	}
	else if (m_showOperateUI)
	{
		uiRenderer.EndDraw();
		uiRenderer.BeginDraw(m_TouchRes4Material, m_TouchRes4);

		auto element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "icon_rocker.png");

		int direction = 0;
		bool canMove = true;

		const size_t touchCount = GetTouchCount();
		for (size_t i = 0; i < touchCount; ++i)
		{
			Touch touch;
			if(GetTouch(i, touch))
			{
				canMove = IsInControl(touch.pos.x, touch.pos.y, direction);
				if(!canMove)
					break;
			}
		}

		Point2D pt1;
		int btnw = 0;
		unsigned long cor = 0xffffffff;
		if (vehicle->getEngineNum() > 0)
		{
			//前
			if (direction & 1)
			{
				pt1 = Point2D(int(150 * scale), YFromBottom(373));
				btnw = int(70 * scale);
				cor = 0xccafafaf;
			}
			else
			{
				pt1 = Point2D(int(145 * scale), YFromBottom(378));
				btnw = int(80 * scale);
				cor = 0x66ffffff;

			}
			if (element)
				uiRenderer.StretchRect((float)pt1.x, (float)pt1.y, (float)btnw, (float)btnw, cor, element->x, element->y, element->w, element->h, UI_UVT_TURN270);

			//后
			if (direction & 2)
			{
				pt1 = Point2D(int(150 * scale), YFromBottom(167));
				btnw = int(70 * scale);
				cor = 0xccafafaf;
			}
			else
			{
				pt1 = Point2D(int(145 * scale), YFromBottom(172));
				btnw = int(80 * scale);
				cor = 0x66ffffff;
			}
			if (element)
				uiRenderer.StretchRect((float)pt1.x, (float)pt1.y, (float)btnw, (float)btnw, cor, element->x, element->y, element->w, element->h, UI_UVT_TURN90);
		}

		if (vehicle->getSteeringSwitch())
		{	//左
			if (direction & 4)
			{
				pt1 = Point2D(int(43 * scale), YFromBottom(270));
				btnw = int(70 * scale);
				cor = 0xccafafaf;
			}
			else
			{
				pt1 = Point2D(int(38 * scale), YFromBottom(275));
				btnw = int(80 * scale);
				cor = 0x66ffffff;
			}
			if (element)
				uiRenderer.StretchRect((float)pt1.x, (float)pt1.y, (float)btnw, (float)btnw, cor, element->x, element->y, element->w, element->h, UI_UVT_TURN180);
			//右
			if (direction & 8)
			{
				pt1 = Point2D(int(258 * scale), YFromBottom(270));
				btnw = int(70 * scale);
				cor = 0xccafafaf;
			}
			else
			{
				pt1 = Point2D(int(253 * scale), YFromBottom(275));
				btnw = int(80 * scale);
				cor = 0x66ffffff;
			}
			if (element)
				uiRenderer.StretchRect((float)pt1.x, (float)pt1.y, (float)btnw, (float)btnw, cor, element->x, element->y, element->w, element->h);
		}

		if (vehicle->getEngineNum() > 0 && vehicle->getSteeringSwitch())
		{
			if (m_isForward)
			{
				//左前
				if (direction & 16)
				{
					pt1 = Point2D(int(62 * scale), YFromBottom(363));
					btnw = int(53 * scale);
					cor = 0xccafafaf;
				}
				else
				{
					pt1 = Point2D(int(59 * scale), YFromBottom(366));
					btnw = int(59 * scale);
					cor = 0x66ffffff;
				}
				if (element)
					uiRenderer.StretchRect((float)pt1.x, (float)pt1.y, (float)btnw, (float)btnw, cor, element->x, element->y, element->w, element->h, UI_UVT_NORMAL, 225);
				//右前
				if (direction & 32)
				{
					pt1 = Point2D(int(260 * scale), YFromBottom(363));
					btnw = int(53 * scale);
					cor = 0xccafafaf;
				}
				else
				{
					pt1 = Point2D(int(257 * scale), YFromBottom(366));
					btnw = int(59 * scale);
					cor = 0x66ffffff;
				}

				if (element)
					uiRenderer.StretchRect((float)pt1.x, (float)pt1.y, (float)btnw, (float)btnw, cor, element->x, element->y, element->w, element->h, UI_UVT_NORMAL, 315);
			}

			//左后
			if (direction & 128)
			{
				pt1 = Point2D(int(62 * scale), YFromBottom(174));
				btnw = int(53 * scale);
				cor = 0xccafafaf;
			}
			else
			{
				pt1 = Point2D(int(59 * scale), YFromBottom(171));
				btnw = int(59 * scale);
				cor = 0x66ffffff;
			}
			if (element)
				uiRenderer.StretchRect((float)pt1.x, (float)pt1.y, (float)btnw, (float)btnw, cor, element->x, element->y, element->w, element->h, UI_UVT_NORMAL, 135);
			//右后
			if (direction & 256)
			{
				pt1 = Point2D(int(260 * scale), YFromBottom(174));
				btnw = int(53 * scale);
				cor = 0xccafafaf;
			}
			else
			{
				pt1 = Point2D(int(257 * scale), YFromBottom(171));
				btnw = int(59 * scale);
				cor = 0x66ffffff;
			}
			if (element)
				uiRenderer.StretchRect((float)pt1.x, (float)pt1.y, (float)btnw, (float)btnw, cor, element->x, element->y, element->w, element->h, UI_UVT_NORMAL, 45);

		}
		uiRenderer.EndDraw();
	}

#endif
}

void TouchControl::renderRockerOperateUI()
{
#ifndef DEDICATED_SERVER
	return;

	const float scale = UILib::GetScreenUIScale();
	UIRenderer& uiRenderer = UIRenderer::GetInstance();
	XMLManager& xmlManager = GetGameUIPtr()->GetXMLManager();

	// 奔跑按钮绘制
	if(m_showOperateUI && !m_bHideRockerUi && !GetRecordPkgManager().isEdit())
	{
		int x = XFromLeft(133);
		int y = YFromBottom(151 + 120); // 摇杆上方120像素
		unsigned int color = 0x66ffffff; // 半透明白色
		
		// 根据奔跑状态选择图标
		const char* iconName = m_RunState ? "m_run_hl.png" : "m_run.png";
		const TexPackElement* element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", iconName);
		
		if (element)
		{
			int width = 120, height = 120;
			uiRenderer.BeginDraw(m_TouchRes4Material, m_TouchRes4);
			uiRenderer.StretchRect((float)(x - width/2), (float)(y - height/2), (float)width, (float)height, color, element->x, element->y, element->w, element->h);
			uiRenderer.EndDraw();
		}
	}

	if (g_pPlayerCtrl&&g_pPlayerCtrl->isVehicleController())
		//物理载具驾驶模式下
		setVehicleControlUI();
	else if (g_pPlayerCtrl && !(g_pPlayerCtrl->getMountType() == MOUNT_DRIVE))
	{
		if (isRockerMode() && m_showOperateUI)
		{
			uiRenderer.BeginDraw(m_TouchRes4Material, m_TouchRes4);
			//uiRenderer.BeginDraw(m_TouchRes4);
			{
				int bgw = int(m_DpadWidth *scale);
				int bgh = int(m_DpadHeight * scale);
				int btnw = int(64 * scale);
				int arroww = int(35 * scale);
				int arrowh = int(18 * scale);
				unsigned int color;
				Point2D pt1, pt2;


				if (m_MoveID >= 0)
				{
					pt1 = m_MoveStart;
					pt2 = CalMovePointInRange(m_MoveStart, m_MovePos, (bgh - btnw) / 2);
					color = 0xb2ffffff;
				}
				else
				{//摇杆中心点
					pt2 = pt1 = Point2D(XFromLeft(133), YFromBottom(151));
					color = 0x66ffffff;
				}

				if (TouchControl::isShowRocker())
				{
					auto element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "operating_steering_wheel.png");
					if (element)
						uiRenderer.StretchRect((float)(pt1.x - bgw / 2), (float)(pt1.y - bgw / 2), (float)bgw, (float)bgh, color, element->x, element->y, element->w, element->h);

					// element = XMLManager::GetInstance().requestPackElement("ui/mobile/texture2/ingame.xml", "xsyd_arrow.png");
					// if (element)
					// {
					// 	MINIW::UIRenderer::GetInstance().StretchRect(pt1.x - arroww / 2, pt1.y + bgw / 2 - int(35 * scale), arroww arrowh, color, element->x, element->y, element->w, element->h, UI_UVT_TURN180);
					// 	MINIW::UIRenderer::GetInstance().StretchRect(pt1.x - arroww / 2, pt1.y - bgw / 2 + int(20 * scale), arroww arrowh, color, element->x, element->y, element->w, element->h);
					// 	MINIW::UIRenderer::GetInstance().StretchRect(pt1.x - bgw / 2 + int(18 * scale), pt1.y - arroww / 2, arrowh arroww, color, element->x, element->y, element->w, element->h, UI_UVT_TURN270);
					// 	MINIW::UIRenderer::GetInstance().StretchRect(pt1.x + bgw / 2 - int(19 * scale) - arrowh, pt1.y - arroww /2, arrowh, arroww, color, element->x, element->y, element->w, element->h, UI_UVT_TURN90);
					// }

					element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "img_steering_wheel.png");
					if (element)
						uiRenderer.StretchRect((float)(pt2.x - btnw / 2), (float)(pt2.y - btnw / 2), (float)btnw, (float)btnw, color, element->x, element->y, element->w, element->h);
				}
			}
			uiRenderer.EndDraw();
		}
		else if (m_showOperateUI)
		{
			uiRenderer.EndDraw();
			uiRenderer.BeginDraw(m_TouchRes4Material, m_TouchRes4);
			//uiRenderer.BeginDraw(m_TouchRes4);

			const TexPackElement* element = nullptr;
			if (!m_bHideRockerUi)
			{
				element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "icon_rocker.png");
			}

			int direction = 0;
			bool canMove = true;
			
			const size_t touchCount = GetTouchCount();
			std::vector<Touch> objs(touchCount);
			for (size_t i = 0; i < touchCount; ++i)
				GetTouch(i, objs[i]);

			for (const Touch& touch : objs)
			{
				canMove = IsInControl(touch.pos.x, touch.pos.y, direction);
				if (!canMove) break;
			}

			/*std::vector<TouchObjectPos> objs;
			InputManager::getSingleton().getTouchObjPos(objs);

			for (const TouchObjectPos& touch : objs)
			{
				canMove = IsInControl(touch.posX, touch.posY, direction);
				if (!canMove) break;
			}*/

			Point2D pt1;
			int btnw = 0;
			unsigned long cor = 0xffffffff;
			//前
			if (direction & 1)
			{
				pt1 = Point2D(int(150 * scale), YFromBottom(373));
				btnw = int(70 * scale);
				cor = 0xccafafaf;
			}
			else
			{
				pt1 = Point2D(int(145 * scale), YFromBottom(378));
				btnw = int(80 * scale);
				cor = 0x66ffffff;

			}
			if (element)
				uiRenderer.StretchRect((float)pt1.x, (float)pt1.y, (float)btnw, (float)btnw, cor, element->x, element->y, element->w, element->h, UI_UVT_TURN270);
			//左
			if (direction & 4)
			{
				pt1 = Point2D(int(43 * scale), YFromBottom(270));
				btnw = int(70 * scale);
				cor = 0xccafafaf;
			}
			else
			{
				pt1 = Point2D(int(38 * scale), YFromBottom(275));
				btnw = int(80 * scale);
				cor = 0x66ffffff;
			}
			if (element)
				uiRenderer.StretchRect((float)pt1.x, (float)pt1.y, (float)btnw, (float)btnw, cor, element->x, element->y, element->w, element->h, UI_UVT_TURN180);
			//右
			if (direction & 8)
			{
				pt1 = Point2D(int(258 * scale), YFromBottom(270));
				btnw = int(70 * scale);
				cor = 0xccafafaf;
			}
			else
			{
				pt1 = Point2D(int(253 * scale), YFromBottom(275));
				btnw = int(80 * scale);
				cor = 0x66ffffff;
			}
			if (element)
				uiRenderer.StretchRect((float)pt1.x, (float)pt1.y, (float)btnw, (float)btnw, cor, element->x, element->y, element->w, element->h);
			//后
			if (direction & 2)
			{
				pt1 = Point2D(int(150 * scale), YFromBottom(167));
				btnw = int(70 * scale);
				cor = 0xccafafaf;
			}
			else
			{
				pt1 = Point2D(int(145 * scale), YFromBottom(172));
				btnw = int(80 * scale);
				cor = 0x66ffffff;
			}
			if (element)
				uiRenderer.StretchRect((float)pt1.x, (float)pt1.y, (float)btnw, (float)btnw, cor, element->x, element->y, element->w, element->h, UI_UVT_TURN90);

			if (m_isForward)
			{
				//左前
				if (direction & 16)
				{
					pt1 = Point2D(int(62 * scale), YFromBottom(363));
					btnw = int(53 * scale);
					cor = 0xccafafaf;
				}
				else
				{
					pt1 = Point2D(int(59 * scale), YFromBottom(366));
					btnw = int(59 * scale);
					cor = 0x66ffffff;
				}
				if (element)
					uiRenderer.StretchRect((float)pt1.x, (float)pt1.y, (float)btnw, (float)btnw, cor, element->x, element->y, element->w, element->h, UI_UVT_NORMAL, 225);
				//右前
				if (direction & 32)
				{
					pt1 = Point2D(int(260 * scale), YFromBottom(363));
					btnw = int(53 * scale);
					cor = 0xccafafaf;
				}
				else
				{
					pt1 = Point2D(int(257 * scale), YFromBottom(366));
					btnw = int(59 * scale);
					cor = 0x66ffffff;
				}

				if (element)
					uiRenderer.StretchRect((float)pt1.x, (float)pt1.y, (float)btnw, (float)btnw, cor, element->x, element->y, element->w, element->h, UI_UVT_NORMAL, 315);
			}

			if (!m_bHideRockerUi)
			{
				element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "icon_rocker_circle.png");
				//中
			}
			//中


			if (direction & 64)
			{
				pt1 = Point2D(int(153 * scale), YFromBottom(266));
				if (element)
					uiRenderer.StretchRect((float)pt1.x, (float)pt1.y, 67 * scale, 62 * scale, 0xcc8c8c8c, element->x, element->y, element->w, element->h, UI_UVT_TURN90);
			}
			else
			{
				pt1 = Point2D(int(151 * scale), YFromBottom(278));
				if (element)
					uiRenderer.StretchRect((float)pt1.x, (float)pt1.y, 71 * scale, 66 * scale, 0x668c8c8c, element->x, element->y, element->w, element->h, UI_UVT_TURN90);
			}
			uiRenderer.EndDraw();
		}
	}
#endif
}

void TouchControl::renderFlyOperateUI()
{
	const float scale = UILib::GetScreenUIScale();
	UIRenderer& uiRenderer = UIRenderer::GetInstance();
	XMLManager& xmlManager = GetGameUIPtr()->GetXMLManager();

	uiRenderer.BeginDraw(m_TouchRes4Material, m_TouchRes4);
	//uiRenderer.BeginDraw(m_TouchRes4);
	int x = XFromRight(109);
	int y = YFromBottom(332);

	auto element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "img_board_res_1.png");
	if (element)
		uiRenderer.StretchRect((float)x, (float)y, 102 * scale, 107 * scale, 0xffffffff, element->x, element->y, element->w, element->h);

	y = YFromBottom(226);

	element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "img_board_res_1.png");
	if (element)
		uiRenderer.StretchRect((float)x, (float)y, 102 * scale, 107 * scale, 0xffffffff, element->x, element->y, element->w, element->h, UI_UVT_MIRROEV);

	const char *pngName1 = "icon_up_down.png";
	const char *pngName2 = "icon_up_down.png";
	// if(m_FlyType == 1)
	// {
	// 	pngName1 = "icon_up_down.png";
	// }
	// else if(m_FlyType == -1)
	// {
	// 	pngName2 = "icon_up_down.png";
	// }

	x = XFromRight(80);
	y = YFromBottom(300);

	element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", pngName1);
	if (element)
		uiRenderer.StretchRect((float)x, (float)y, 42 * scale, 26 * scale, 0xffffffff, element->x, element->y, element->w, element->h);

	y = YFromBottom(172);

	element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", pngName2);
	if (element)
		uiRenderer.StretchRect((float)x, (float)y, 42 * scale, 26 * scale, 0xffffffff, element->x, element->y, element->w, element->h, UI_UVT_TURN180);
	uiRenderer.EndDraw();
}

void TouchControl::renderBlueprintUI()
{

}

void TouchControl::setBasketBallLockState(bool state, float wx, float wy)
{
	m_BasketBallLockState = state;
	if (state)
	{
		m_fBaskeBallFocusX = wx;
		m_fBaskeBallFocusY = wy;
	}
	else
		m_BasketBallLockTime = 0;
}

void TouchControl::setAutoFireState(int iState, int iTime)
{
	m_iAutoFireState = iState;
	if (m_iAutoFireState == 1)
	{
		m_iAutoAimTime = iTime;
		m_iMaxAimTime = iTime;
	}
}

bool TouchControl::checkIsAiming()
{
	if (m_iAutoFireState == 1 && m_iAutoAimTime > 0)
		return true;

	return false;
}

void TouchControl::renderOverheatProgress(float scale)
{
	if (GetGameUIPtr()->isUIHide() || g_pPlayerCtrl == NULL)
	{
		return;
	}

	ActorVehicleAssemble* vehicle = g_pPlayerCtrl->getDrivingVehicle();
	ContainerDriverSeat* seatcontainer = dynamic_cast<ContainerDriverSeat*>(vehicle->getVehicleWorld()->getContainerMgr()->getContainer(vehicle->getRiddenBindSeatPos(g_pPlayerCtrl)));
	if (!seatcontainer || seatcontainer->m_BindKeyData.size() < 1 || vehicle->m_CurOperateKeys.size() < 1)
		return;
	if (vehicle->m_CurOperateKeys.size() < 1)
		return;

	UIRenderer& uiRenderer = UIRenderer::GetInstance();
	XMLManager& xmlManager = GetGameUIPtr()->GetXMLManager();
	float OverheatProgress = 0.0f;
	bool OverheatThreshold = false;
	auto KeyData = seatcontainer->m_BindKeyData;
	for (auto operateiter = vehicle->m_CurOperateKeys.begin(); operateiter != vehicle->m_CurOperateKeys.end(); operateiter++)
	{
		if (KeyData.find(*operateiter) != KeyData.end() && KeyData[*operateiter].size() > 0)
		{
			float tempHeat = vehicle->GetPartOverheatPercent(KeyData[*operateiter][0].position);
			bool tempThreshold = !vehicle->checkifOverheatPartCanWork(KeyData[*operateiter][0].position);

			for (auto partiter = KeyData[*operateiter].begin(); partiter != KeyData[*operateiter].end(); partiter++)
			{
				float PartHeat = vehicle->GetPartOverheatPercent(partiter->position);
				bool PartThreshold = !vehicle->checkifOverheatPartCanWork(partiter->position);
				if (!PartThreshold && tempThreshold)
				{
					tempHeat = PartHeat;
					tempThreshold = PartThreshold;
				}
				else if (PartThreshold == tempThreshold && PartHeat < tempHeat)
				{
					tempHeat = PartHeat;
					tempThreshold = PartThreshold;
				}
			}
			OverheatProgress = tempHeat;
			OverheatThreshold = tempThreshold;

			if (OverheatProgress <= 0)
				continue;

			bool isShown = true;
			if (OverheatThreshold)
			{
				if (m_OverheatTickCount[*operateiter - 1] < 10)
					isShown = true;
				else if (m_OverheatTickCount[*operateiter - 1] < 20)
					isShown = false;
				m_OverheatTickCount[*operateiter - 1] ++;
				if (m_OverheatTickCount[*operateiter - 1] >= 20)
					m_OverheatTickCount[*operateiter - 1] = 0;

			}
			else
				m_OverheatTickCount[*operateiter - 1] = 0;

			uiRenderer.BeginDraw(m_TouchRes5Material, m_TouchRes5);
			int btnw = (int)(119 * scale);
			int btnh = (int)(119 * scale);

			//绘制进度条的底
			int startx = (int)(VehicleKeyPos[*operateiter - 1].x * scale);
			int starty = (int)(VehicleKeyPos[*operateiter - 1].y * scale);
			auto element = xmlManager.requestPackElement("ui/mobile/texture2/vehicle.xml", "operating_board_bar.png");
			if (element)
				uiRenderer.StretchRect(startx - btnw - 9 * scale, starty - btnh - 5 * scale, (float)btnw, (float)btnh, 0xffffffff, element->x, element->y, element->w, element->h);

			btnw = (int)(104 * scale);
			btnh = (int)(104 * scale);
			float proX = 2;
			float proY = 137;
			element = xmlManager.requestPackElement("ui/mobile/texture2/vehicle.xml", "img_operating_bar_r.png");
			if (element)
			{
				proX = (float)element->x;
				proY = (float)element->y;
			}

			Rectf rc(startx, starty - btnh - 3 * scale, 60 * scale, 24 * scale);
			if (isShown)
			{
				if (OverheatProgress < 0.5f)
				{
					//只需要绘制一个三角形
					//
					//   0
					//   |  \
					//   1----2
					m_TipTriangleVerts[0].x = startx - btnw - 16 * scale;
					m_TipTriangleVerts[0].y = starty - btnh * 2 * OverheatProgress - 13 * scale;

					m_TipTriangleVerts[1].x = startx - btnw - 16 * scale;
					m_TipTriangleVerts[1].y = starty - 13 * scale;

					m_TipTriangleVerts[2].x = startx - 16 * scale;
					m_TipTriangleVerts[2].y = starty - 13 * scale;

					m_TipTriangleTexCood[0].x = proX;
					m_TipTriangleTexCood[0].y = proY + (1 - 2 * OverheatProgress) * 104;

					m_TipTriangleTexCood[1].x = proX;
					m_TipTriangleTexCood[1].y = proY + 104;

					m_TipTriangleTexCood[2].x = proX + 104;
					m_TipTriangleTexCood[2].y = proY + 104;

					uiRenderer.StretchTriangle(m_TipTriangleVerts[0], m_TipTriangleTexCood[0], m_TipTriangleVerts[2], m_TipTriangleTexCood[2], m_TipTriangleVerts[1], m_TipTriangleTexCood[1], \
						0xffffffff);

				}
				else
				{
					//要绘制两个三角形
					//   0 ---3
					//   |  \ |
					//   1----2
					m_TipTriangleVerts[0].x = startx - btnw - 16 * scale;
					m_TipTriangleVerts[0].y = starty - btnh - 13 * scale;

					m_TipTriangleVerts[1].x = startx - btnw - 16 * scale;
					m_TipTriangleVerts[1].y = starty - 13 * scale;

					m_TipTriangleVerts[2].x = startx - 16 * scale;
					m_TipTriangleVerts[2].y = starty - 13 * scale;

					m_TipTriangleVerts[3].x = startx - 16 * scale - btnw + (OverheatProgress - 0.5f) * 2 * btnw;
					m_TipTriangleVerts[3].y = starty - btnh - 13 * scale;

					m_TipTriangleTexCood[0].x = proX;
					m_TipTriangleTexCood[0].y = proY;

					m_TipTriangleTexCood[1].x = proX;
					m_TipTriangleTexCood[1].y = proY + 104;

					m_TipTriangleTexCood[2].x = proX + 104;
					m_TipTriangleTexCood[2].y = proY + 104;

					m_TipTriangleTexCood[3].x = proX + (OverheatProgress - 0.5f) * 2 * 104;
					m_TipTriangleTexCood[3].y = proY;

					uiRenderer.StretchTriangle(m_TipTriangleVerts[0], m_TipTriangleTexCood[0], m_TipTriangleVerts[2], m_TipTriangleTexCood[2], m_TipTriangleVerts[1], m_TipTriangleTexCood[1], \
						0xffffffff);

					uiRenderer.StretchTriangle(m_TipTriangleVerts[0], m_TipTriangleTexCood[0], m_TipTriangleVerts[3], m_TipTriangleTexCood[3], m_TipTriangleVerts[2], m_TipTriangleTexCood[2], \
						0xffffffff);
				}
			}
			const UIFont& oneUIFontConfig = GetGameUIPtr()->GetFrameManagerPtr()->getUIFontByIndex(5);
			char tmpbuf[64];
			sprintf(tmpbuf, "%d%%", int(OverheatProgress * 100));
			uiRenderer.renderTextRect(oneUIFontConfig.h, FONTSYTLE_NORMAL, tmpbuf, rc, 0, 0, false, ColorRGBAf::white, 1.0f);
			uiRenderer.EndDraw();
		}
	}


}

void TouchControl::AddInputControl(CInputControl* pControl, int order /* = 0 */)
{

}

void TouchControl::removeInputControl(CInputControl* pControl)
{

}

void TouchControl::removeInputControl(int order)
{

}

int TouchControl::GetWindowWidthIncludeNotch() const
{
	return GetGameUIPtr()->GetWidth();
}

void TouchControl::HideTouchControlUi(bool bHide)
{
	if (bHide)
	{
		m_bHideJumpUi = true;
		m_bHideRockerUi = true;
	}
}

bool TouchControl::isShowDigProgress()
{
	if (g_pPlayerCtrl == NULL)
		return false;

	World* pWorld = g_pPlayerCtrl->getIWorld();
	if (pWorld == NULL)
		return false;

	if (pWorld->IsBrokenBlockEnable(g_pPlayerCtrl->getDigBlockID()))
		return true;
	else
		return false;
}

void TouchControl::SetDrawSight(bool draw)
{
	m_drawSight = draw;
}
bool TouchControl::IsDrawSight()
{
	return m_drawSight;
}

