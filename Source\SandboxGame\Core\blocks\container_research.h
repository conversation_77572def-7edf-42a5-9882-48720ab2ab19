#pragma once

#include "container_erosion_storage.h"
#include "SandboxGame.h"

class ContainerResearch : public ErosionStorageBox
{
public:
    ContainerResearch();
    ContainerResearch(const WCoord& blockpos, const int blockId);
    virtual ~ContainerResearch();

    virtual FBSave::ContainerUnion getUnionType() override
    {
        return FBSave::ContainerUnion_ContainerResearch;
    }

    virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER& builder) override;
    virtual bool load(const void* srcdata) override;

    virtual void enterWorld(World* pworld) override;
    virtual void leaveWorld() override;

    virtual void updateTick() override;

    virtual void dropItems() override;
    virtual void dropItems(WCoord BlockPos) override;

    void StartResearch();

private:
    void StopResearch();
    void Process();

private:
    int researchtickcount;
};