
#include "AIPosWander.h"
#include "ActorLocoMotion.h"
#include "ClientActorManager.h"
#include "ClientMob.h"
#include "ActorVision.h"
#include "OgreUtils.h"
#include "ObserverEventManager.h"
#include "ActorAttrib.h"
#include "ToAttackTargetComponent.h"

#include "world_types.h"
#include "coreMisc.h"
#include "navigationpath.h"

AIPosWander::AIPosWander(ClientMob *pActor, int x, int z, int width, float speed, int prob) :AIBase(pActor),m_Speed(speed)
	, m_Prob(prob), m_initPoint(x, -1, z), m_width(width)
{
	setMutexBits(1);
	m_Tick = 200;
 	m_pMobActor->setHomeDist(m_width * 100);
	isReturnHome = false;
	returnHomeTick = 100;

	if (m_pMobActor->getDefID() > 6000000 || m_pMobActor->getDefID() == 3412) {
		m_width = m_pMobActor->getDef()->TraceRange; //soc npc 从配置读取追击范围(单位：方块)
		m_pMobActor->setHomeDist(m_width * 100);
	}
}

bool AIPosWander::willRun()
{
	WCoord spawnPoint = CoordDivBlock(m_pMobActor->getSpawnPoint());
	WCoord pos_ = spawnPoint;
	if (m_initPoint.x == 0 && m_initPoint.y == -1 && m_initPoint.z == 0) {
		m_initPoint = spawnPoint ;
		m_pMobActor->setHome(m_pMobActor->getHomeDist(), spawnPoint.x*100, spawnPoint.y * 100, spawnPoint.z * 100);
	}

	if (m_pMobActor->isNeedRetHome()) {
		WCoord spawnPoint = CoordDivBlock(m_pMobActor->getSpawnPoint());
		m_ValidPos = spawnPoint* 100;
		return true;
	}

	if (GenRandomInt(m_Prob) != 0)
	{
		return false;
	}

	/*auto targetComponent = m_pMobActor->getToAttackTargetComponent();
	if (targetComponent)
	{
		if (targetComponent->getTarget()) {
			return false;
		}
	}*/


	int count = 4;
	while (count)
	{
		bool ret = m_pMobActor->getLocoMotion()->findRandTargetBlock(m_ValidPos, m_width, 10, NULL);

		int x1 = m_initPoint.x - m_width;
		int x2 = m_initPoint.x + m_width;

		int y1 = m_initPoint.z - m_width;
		int y2 = m_initPoint.z + m_width;

		int x = m_ValidPos.x / BLOCK_SIZE;
		int z = m_ValidPos.z / BLOCK_SIZE;
		if (ret && (x >= x1 && x <= x2 && z >= y1 && z <= y2))
		{
			m_Tick = 200;
			return true;
		}
		int cmp1 = (x - m_initPoint.x) * (x - m_initPoint.x) + (z - m_initPoint.z) * (z - m_initPoint.z);
		int cmp2 = (pos_.x - m_initPoint.x) * (pos_.x - m_initPoint.x) + (pos_.z - m_initPoint.z) * (pos_.z - m_initPoint.z);
		if (cmp1 < cmp2)
		{
			m_Tick = 200;
			return true;
		}
		count--;
	}

	
	
	return false;
}

bool AIPosWander::continueRun()
{
	if (m_Tick-- < 0)
	{
		return false;
	}

	if (m_pMobActor->isNeedRetHome()) {
		WCoord targetpos = m_pMobActor->getLocoMotion()->getPosition();
		WCoord vec = targetpos - m_ValidPos;
		vec.y = 0;
		float dist = vec.length();
		if (dist < 200) {
			m_pMobActor->setNeedRetHome(false);
			return false;
		}
		else {
			ActorAttrib* attrib = m_pMobActor->getAttrib();

			if (attrib->getHP() < attrib->getMaxHP()) {
				if (m_Tick % 20 == 0) {
					float hp = attrib->getMaxHP() * 0.1;
					attrib->addHP(hp);// 每次恢复最大的10%点血量
				}
			}
			return true;
		}
	}

	if (m_pMobActor->getNavigator()->noPath())
	{
		return false;
	}
	else 
	{
		return true;
	}
}

void AIPosWander::start()
{
	m_Tick = 200;
	m_pMobActor->getNavigator()->tryMoveTo(m_ValidPos.x, m_ValidPos.y, m_ValidPos.z, m_Speed,(m_width+10)*100);
}

void AIPosWander::reset()
{
	m_Tick = 200;
	m_pMobActor->getNavigator()->clearPathEntity();
}