syntax = "proto2";
package game.common; //域名

// errorCode
enum ePBErrorCode
{
    PB_ERROR_UNKNOWN = 1;
    PB_ERROR_WRONG_MSG_CODE = 11;
    PB_ERROR_WRONG_REQUESTER = 12;
    PB_ERROR_WRONG_ARGS = 13;
    PB_ERROR_PLAYER_NOT_LOGIN = 14;
    PB_ERROR_ILLEGAL_ARGS = 15;
    PB_ERROR_OP_NOT_FOUND = 16;
    PB_ERROR_BACKPACK_FULL = 21;
    PB_ERROR_STORAGE_FULL = 22;
    PB_ERROR_ENCHANT_NOT_ENOUGH = 25;
    PB_ERROR_ENCHANT_FAILED = 26;
    PB_ERROR_ENCHANT_NOT_CHANGED = 27;
    PB_ERROR_CRAFT_NOT_ENOUGH = 28;
    PB_ERROR_STAR_NOT_ENOUGH = 29;
    PB_ERROR_REPAIR_NOT_ENOUGH = 30;
    PB_ERROR_REPAIR_NOT_DAMAGED = 31;
    PB_ERROR_ROLE_ENTER_BANNED = 10011;
    PB_ERROR_ROLE_ENTER_TOO_OFTEN = 10012;
    PB_ERROR_ROLE_ENTER_SERVER_FULL = 10013;
    PB_ERROR_ROLE_ENTER_SERVER_CLOSING = 10014;
    PB_ERROR_ROLE_MOVE_ILLEGAL_POS = 20031;
    PB_MAX_ERROR_CODE = 102410;
}

// msgCode
enum ePBMsgCode
{
    PB_REQ_ERROR_HC = 10;
    PB_HEARTBEAT_CH = 11;
    PB_HEARTBEAT_HC = 12;
    PB_SYNC_CHUNK_DATA_CH = 101;
    PB_SYNC_CHUNK_DATA_HC = 102;
    PB_BLOCK_DATA_UPDATE_CH = 103;
    PB_BLOCK_DATA_UPDATE_HC = 104;
    PB_ROLE_ENTER_WORLD_CH = 1001;
    PB_ROLE_ENTER_WORLD_HC = 1002;
    PB_ROLE_LEAVE_WORLD_CH = 1003;
    PB_ROLE_LEAVE_WORLD_HC = 1004;
    PB_ACTOR_ENTER_AOI_HC = 1006;
    PB_ACTOR_LEAVE_AOI_HC = 1008;
	PB_GAME_LEADER_SWITCH_HC = 1010;
	PB_GENERAL_ENTER_AOI_HC = 1011;
	PB_PVP_ACTIVITY_CONFIG_CH = 1012;
	
    PB_ROLE_MOVE_CH = 2001;
    PB_TRAIN_MOVE_CH = 2002;
    PB_ACTOR_MOVE_HC = 2004;
    PB_TRAIN_MOVE_HC = 2005;
    PB_ACTOR_MOVEV2_HC = 2006;	
    PB_ACTOR_TELEPORT_CH = 2007;
    PB_ACTOR_TELEPORT_HC = 2008;
    PB_ACTOR_MOTION_HC = 2009;
    PB_MECHA_MOTION_HC = 2010;
    PB_GUN_INFO_CH = 2011;
    PB_SYNC_SETINFO_CH = 2012;
    PB_SYNC_GRIDUSERDATA_CH = 2013;
    PB_SYNC_GRIDUSERDATA_HC = 2014;
    PB_SYNC_TRIGGERBLOCK_HC = 2015;
    PB_FULLROT_ACTOR_MOVE_HC = 2016;	
	PB_ACTOR_MOTIONV2_HC = 2017;
	PB_ACTOR_MOVEV3_HC = 2018;
	PB_ACTOR_MODELCHG_HC = 2019;

    PB_BLOCK_INTERACT_HC = 2997;
    PB_BLOCK_PUNCH_HC = 2998;
    PB_ITEM_USE_HC = 2999;
    PB_ACTOR_INTERACT_HC = 3000;
    PB_BLOCK_INTERACT_CH = 3002;
    PB_BLOCK_PUNCH_CH = 3003;
    PB_ITEM_USE_CH = 3004;
    PB_ACTOR_INTERACT_CH = 3005;
    PB_ACTOR_ANIM_CH = 3006;
    PB_ACTOR_ANIM_HC = 3007;
    PB_BACKPACK_GRID_UPDATE_HC = 3008;
    PB_BACKPACK_GRID_SWAP_CH = 3009;
    PB_BACKPACK_MOVEITEM_CH = 3010;
    PB_BACKPACK_GRID_DISCARD_CH = 3011;
    PB_BACKPACK_EQUIP_WEAPON_CH = 3015;
    PB_BACKPACK_EQUIP_WEAPON_HC = 3016;
    PB_CLOSE_CONTAINER_CH = 3017;
    PB_CLOSE_CONTAINER_HC = 3018;
    PB_OPEN_CONTAINER_HC = 3020;
    PB_UPDATE_CONTAINER_HC = 3022;
    PB_SET_CONTAINERTEXT_CH = 3023;
    PB_ACTOR_EQUIP_ITEM_HC = 3026;
    PB_BACKPACK_STORE_CH = 3029;
    PB_BACKPACK_LOOT_CH = 3031;
    PB_BACKPACK_SORT_CH = 3033;
    PB_BACKPACK_SETITEM_CH = 3034;
    PB_STORAGEBOX_SORT_CH = 3035;
    PB_CRAFT_ITEM_CH = 3037;
    PB_ENCHANT_ITEM_CH = 3039;
    PB_ENCHANT_ITEM_RANDOM_CH = 3041;
    PB_ENCHANT_ITEM_SUCCESS_HC = 3042;
    PB_REPAIR_ITEM_CH = 3043;
    PB_REPAIR_ITEM_SUCCESS_HC = 3044;
    PB_GUN_DORELOAD_CH = 3045;
    PB_GUN_DORELOAD_HC = 3046;
    PB_ACCOUNT_HORSE_CH = 3050;
    PB_ACCOUNT_HORSE_HC = 3051;
	PB_ACTOT_SET_CUSTOM_CH = 3052;
    PB_ACTOT_SET_CUSTOM_HC = 3053;
	PB_ACTOR_PLAY_ANIM_CH = 3054;
    PB_ACTOR_PLAY_ANIM_HC = 3055;
	PB_ACTOR_ATTACK_CH = 3056;
	PB_ACTOR_DEFANCESTATE_CH = 3057;
	PB_RCLICKUP_INTERACT_CH = 3058;
	PB_RCLICKUP_INTERACT_HC = 3059;
	PB_MOUSE_EVENT_CH = 3060;

	PB_CRAFTING_QUEUE_ADD_TASK_CH = 3061;        // 客户端请求：添加任务
    PB_CRAFTING_QUEUE_REMOVE_TASK_CH = 3062;     // 客户端请求：删除任务
    PB_CRAFTING_QUEUE_SWAP_TASK_CH = 3063;       // 客户端请求：交换任务
    PB_CRAFTING_QUEUE_QUEUE_UPDATE_HC = 3064;    // 服务器返回：队列更新
    PB_CRAFTING_QUEUE_PROGRESS_UPDATE_HC = 3065; // 服务器返回：任务进度更新
	
	PB_PLAYER_WAKE_UP_CH = 3066; //客户端请求 玩家唤醒 
	PB_PLAYER_WAKE_UP_Hc = 3067; //服务器返回
	PB_PLAYER_DOWNED_STATE_CHANGE_HC = 3068; //服务端通知倒地状态的变化
	

    PB_PLAYER_REVIVE_REQUEST_CH = 3069;  // 客户端发送救援请求
    PB_PLAYER_REVIVE_PROGRESS_CH = 3070;  // 客户端发送救援进度
	
	PB_BLOCK_ATTACK_CH = 3071;  // 客户端发送方块攻击请求
	PB_BLOCK_ATTACK_HC = 3072;  // 服务器广播方块攻击效果

	PB_PLAYER_SKINNING_CH = 3073;
	PB_PLAYER_SKINNING_HC = 3074;
	PB_PLAYER_DOWNED_HEALTH_UPDATE_HC = 3075; //服务端通知倒地血量更新

    PB_ACTOR_ATTR_CHANGE_HC = 4000;
    PB_ACTOR_BUFF_CHANGE_HC = 4001;
    PB_ACTOR_REVIVE_CH = 4002;
    PB_ACTOR_REVIVE_HC = 4003;
    PB_PLAYER_ATTR_CHANGE_HC = 4004;
    PB_MOB_BODY_CHANGE_HC = 4005;
    PB_ROOM_JRUISDICTION_CH = 4006;
    PB_ROOM_JRUISDICTION_HC = 4007;
    PB_CHAT_CH = 4010;
    PB_CHAT_HC = 4011;
    PB_WGLOBAL_UPDATE_HC = 4012;
    PB_PLAYERS_UPDATEINFO_HC = 4013;
    PB_GAME_TIPS_HC = 4014;
    PB_PLAYEFFECT_HC = 4015;
    PB_PLAYER_MOUNTACTOR_CH = 4016;
    PB_PLAYER_MOUNTACTOR_HC = 4017;
    PB_PLAYER_MOVEINPUT_CH = 4018;
    PB_PLAYER_REVIVEPOINT_CH = 4019;
    PB_PLAYER_REVIVEPOINT_HC = 4027;
    PB_PLAYER_SLEEP_HC = 4020;
    PB_PLAYER_SLEEP_CH = 4021;
    PB_OPENWINDOW_HC = 4022;
    PB_NPCTRADE_CH = 4023;
    PB_LASTPING_HC = 4024;
    PB_CGAMESTAGE_HC = 4025;
    PB_PLAYERPERMIT_HC = 4026;
	PB_PLAYEFFECT_HC_V2 = 4028;
    PB_SKILLCD_HC = 4029;
    PB_ACTOR_MOUNTACTOR_HC = 4030;
    PB_ACTOR_REVERSE_HC = 4031;
    PB_ACTOR_BIND_HC = 4032;
    PB_PLAYWEAPONEFFECT_HC = 4033;
    PB_SCRIPTVAR_HC = 4034;
	PB_PLAYWEAPONEFFECT_CH = 4035;
    PB_SET_SPECTATORMODE_CH = 4037;
    PB_SET_SPECTATORMODE_HC = 4038;
    PB_SET_SPECTATORTYPE_CH = 4039;
    PB_SET_SPECTATORTYPE_HC = 4040;
    PB_SET_SPECTATOR_PLAYER_CH = 4041;
    PB_OTHER_PLAYER_ATTR_CHANGE_HC = 4042;
    PB_PLAYER_LEAVE_HC = 4043;
    PB_TEAM_SCORE_HC = 4044;
    PB_SET_TEAM_HC = 4045;
    PB_SET_PLAYER_GAME_INFO_HC = 4046;
	
    PB_SYNC_MOVE_CH = 4047;
    PB_SYNC_MOVE_HC = 4048;

	PB_UIDISPLAYHORSE_HC = 4049;

    PB_ACTOR_GET_ACCOUNT_ITEM = 5001;
    PB_SPECIALITEM_USE_CH = 5002;
    PB_SPECIALITEM_USE_HC = 5003;
    PB_LEAVE_ROOM_INFO_HC = 5004;
    PB_INVITEJOINROOM_HC = 5005;
	PB_SET_SPECTATOR_PLAYER_HC = 5006;
	PB_SET_PLAYER_MODEL_ANI_CH = 5007;
	PB_SET_PLAYER_MODEL_ANI_HC = 5008;
	PB_SEND_VIEWMODE_SPECTATOR_CH = 5009;
	PB_SEND_VIEWMODE_SPECTATOR_HC = 5010;
	PB_SET_BOBBING_SPECTATOR_CH = 5011;
	PB_SET_BOBBING_SPECTATOR_HC = 5012;
	PB_ITEM_SKILL_USE_CH = 5013;
	PB_ITEM_SKILL_USE_HC = 5014;
	PB_BALL_OPERATE_CH = 5015;
	PB_BALL_OPERATE_HC = 5016;
	PB_RESET_ROUND_HC = 5017;
	PB_ROCKET_ATTRIB_CHANGE_HC = 5018;
	PB_ROCKET_TELEPORT_CH = 5019;
	PB_SET_HOOK_HC = 5020;
	PB_SET_HOOK_CH = 5021;
	PB_WORLD_TIMES_HC = 5022;
	PB_STATISTIC_HC = 5023;
	PB_TOTEMPOINT_HC = 5024;
	PB_NEED_CONTAINER_PASSWORD_HC = 5025;
	PB_NEED_CONTAINER_PASSWORD_CH = 5026;
	PB_HORSEFLYSTATE_HC = 5027;
	PB_OPENDIALOGUE_HC = 5028;
	PB_CLOSEDIALOGUE_HC = 5029;
	PB_CLOSEDIALOGUE_CH = 5030;
	PB_ANSWERTASK_CH = 5031;
	PB_UPDATETASK_HC = 5032;
	PB_SYNCTASK_ENTERWORLD_HC = 5033;
	PB_COMPLETE_TASK_HC = 5034;
	PB_COMPLETE_TASK_CH = 5035;
	PB_ATTRACT_ATTRIB_CHANGE_HC = 5036;
	PB_ACTOR_BODY_TEXTURE_HC = 5037;
	PB_PLAYER_ADDAVARTAR_HC = 5038;
	PB_PLAYER_CHANGEMODEL_HC = 5039;
	PB_PLAYER_AVARTARCOLOR_HC = 5040;
	PB_PLAYER_ACT_CH = 5041;
	PB_PLAYER_ACT_HC = 5042;
	PB_CREATE_BLUEPRINT_HC = 5043;
	PB_MEASURE_DISTANCE_HC = 5044;
	PB_BLUEPRINT_PREBLOCK_CH = 5045;
	PB_BLUEPRINT_PREBLOCK_HC = 5046;
	PB_GRAVITY_OPERATE_CH = 5047;
	PB_GRAVITY_OPERATE_HC = 5048;
	PB_PLAYER_BODY_COLOR_HC = 5049;
	PB_CUSTOM_MODEL_HC = 5050;
	PB_CUSTOM_ITEMIDS_HC = 5051;
	PB_PLAYER_SPAWN_POINT_HC = 5052;
	PB_MAKE_CUSTOM_MODEL_CH = 5053;
	PB_SELECT_MOB_SPAWN_CH = 5054;
	PB_CUSTOM_MODELCLASS_HC = 5055;
	PB_TRANSFER_RECORD_HC	= 5056;
	PB_TRANSFER_RECORD_CH = 5057;
	PB_TRANSFER_ADD_DEL_HC	= 5058;
	PB_TRANSFER_STATUS_CH = 5059;
	PB_TRANSFER_STATUS_HC = 5060;
	PB_SYNC_LOVEAMBASSADOR_ICONID_HC = 5061;
	PB_SYNC_LOVEAMBASSADOR_ICONID_CH = 5062;
	PB_TRANSFER_DATA_HC	= 5063;
	PB_ACTOR_TRANSFER_CH = 5064;
    PB_ACTOR_TRANSFER_HC = 5065;
    PB_BACKPACK_SETITEMWITHOUTLIMIT_CH = 5066;
    PB_NPCSHOP_GETSHOPINFO_CH = 5067;
    PB_NPCSHOP_RESPGETSHOPINFO_HC = 5068;
    PB_NPCSHOP_BUYSKU_CH = 5069;
    PB_NPCSHOP_NOTIFYBUY_HC = 5070;
    PB_VEHICLE_MOVE_HC = 5071;
	PB_OPEN_EDIT_ACTORMODEL_HC = 5072;
	PB_CLOSE_EDIT_ACTORMODEL_CH = 5073;
	PB_CLOSE_EDIT_ACTORMODEL_HC = 5074;
	PB_CUSTOMACTOR_MODELDATA_HC = 5075;
    PB_PACKGIFT_NOTIFYITEMCHANGE_HC = 5076;
	PB_VEHICLE_PREBLOCK_CH = 5077;
	PB_VEHICLE_PREBLOCK_HC = 5078;
	PB_VEHICLE_ITEMUSE_CH = 5079;
	PB_VEHICLE_STARTBLOCK_CH = 5080;
	PB_VEHICLE_ALL_ITEMID_HC = 5081;
	PB_VEHICLE_ONE_ITEMID_HC = 5082;
	PB_VEHICLE_ATTRIB_CHANGE_HC = 5083;
	PB_VEHICLE_ATTRIB_CHANGE_CH = 5084;
	PB_WORKSHOP_ITEMINFO_CH = 5085;
	PB_WORKSHOP_ITEMINFO_HC = 5086;
	PB_VEHICLEASSEMBLEBLOCK_UPDATE_HC = 5087;
	PB_PLAYER_VEHICLE_MOVEINPUT_CH = 5088;
	PB_PLAYER_RESETVEHICLE_CH = 5089;
	PB_PLAYER_MOTIONSTATECHANGE_CH = 5090;
	PB_PLAYER_CLICK_CH = 5091;
	PB_PLAYER_CAMERAROTATE_HC = 5092;
	PB_PLAYER_CHANGEVIEWMODE_HC = 5093;
	PB_PLAYER_CANMOVE_HC = 5094;
	PB_PLAYER_CANCONTROL_HC = 5095;
	PB_PLAYER_SETATTR_HC = 5096;
	PB_PLAYER_FREEZING_HC = 5097;
	PB_PLAYER_SELECTSHORTCUT_CH = 5098;
	PB_GAMERULE_HC = 5099;
	PB_BASKETBALL_OPERATE_HC = 5100;
	PB_BASKETBALL_OPERATE_CH = 5101;
	PB_PLAYER_VEHICLE_MOVEINPUT_HC = 5102;
	PB_PLAYER_CANFIRE_HC = 5103;
	
	PB_BUY_AD_SHOP_GOOD_CH = 5200;   // 购买广告商人商品
	PB_BUY_AD_SHOP_GOOD_HC = 5201;

	PB_SYNC_PLAYER_POS_HC = 5202;          // 同步服务器位置到客户端
	PB_ACHIEVEMENT_AWARD_CH = 5203;        // 领取成就奖励
	PB_SYNC_CLIENT_ACTIONLOG_CH = 5204;    // 客户端上报ActionLog及作弊
	PB_SYNC_ROOM_EXTRA_HC = 5205;          // 同步房间信息给客户端
	PB_UPLOAD_CHECK_INFO_CH = 5206;        // 客户端上报备检信息
	PB_GET_ADSHOP_EXTRA_AWARD_CH = 5207;   // 领取心愿商人累积奖励
	PB_EXTRACT_STORE_ITEM_CH = 5208;       // 提取仓库道具
	PB_UPLOAD_CLIENT_INFO_CH = 5209;
	PB_SYNC_PLAYER_POS_CH = 5210;          // 同步服务器位置到客户端后的客户端返回

	PB_TRIGGER_TIMER_HC = 6000;
	PB_WORKSHOP_BUILD_HC = 6001;
	PB_TRIGGER_PLAYER_ATTRI_CH = 6002;
	PB_PLAYER_ATTR_SCALE_HC = 6003;
	PB_PLAYER_ATTR_SCALE_CH = 6004;
	PB_PLAYER_NAVIGATE_HC = 6005;
	PB_PLAYER_FACE_YAW_HC = 6006;
	PB_OPENEDIT_FULLYCUSTOMMODEL_HC = 6007;
	PB_REQ_DOWNLOADRES_URL_CH = 6008;
	PB_CLOSE_FULLYCUSTOMMODEL_UI_CH = 6009;
	PB_CLOSE_FULLYCUSTOMMODEL_UI_HC = 6010;
	PB_RESP_DOWNLOADRES_URL_HC = 6011;
	PB_PRE_OPEN_EDIT_FCM_UI = 6012;
	PB_EFFECTSCALE_HC = 6013;
	PB_PLAYER_NAVFINISHED_CH = 6014;
	PB_TRIGGER_MUSIC_HC = 6015;
	PB_TRIGGER_SOUND_CH = 6016;
	PB_PLAYER_JUMP_HC = 6017;
	PB_PLAYER_JUMP_CH = 6018;
	PB_PLAYER_SPECIAL_SKILL_CH = 6019;
	PB_HORSE_SKILLCD_HC = 6020;
	PB_CLOUDSERVER_PERMIT_CH = 6021;
	PB_CLOUDSERVER_PERMIT_HC = 6022;
	PB_CLOUDSERVER_AUTHORITY_HC = 6023;
	PB_CLOUDSERVER_AUTHORITY_CH = 6024;
	PB_SS_SYNC_TASK_HC = 6025;
	PB_SS_SYNC_TASK_CH = 6026;
	PB_VEHICLEASSEMBLEBLOCK_ALL_HC = 6027;
	PB_VEHICLE_ASSEMBLE_LINE_CH = 6028;
	PB_VEHICLE_ASSEMBLE_LINE_HC = 6029;
	PB_VEHICLE_ASSEMBLE_LINE_OPERATE_CH = 6030;
	PB_VEHICLE_ASSEMBLE_LINE_OPERATE_HC = 6031;
	PB_ACTIONEDATA_UPDATE_CH = 6032;
	PB_VEHICLE_WORKSHOP_LINE_CH = 6033;
	PB_CLOUDSERVER_CHANGE_TEAM_CH = 6034;
	PB_CLOUDSERVER_CHANGE_STATE_HC = 6035;
	PB_YM_CHANGEROLE_HC = 6036;
	PB_YM_CHANGEROLE_CH = 6037;
	PB_YM_VOICE_CH = 6038;
	PB_YM_VOICE_HC = 6039;
	PB_CLOUDSERVER_ROOM_AUTOMUTE_CH = 6040;
	PB_VEHICLE_WORKSHOP_LINE_UPDATE_CH = 6041;
	PB_MAP_EDIT_HANDLE_CH = 6042;
	PB_MAP_EDIT_REVOKE_CH = 6043;
	PB_CLOUD_ROOM_OWNER_START_GAME_CH = 6044;
	PB_CLOUD_ROOM_KICK_OFF_CH = 6045;
	PB_TRIGGER_OPENSTORE_HC = 6046;
	PB_USE_PACKINGFCMITEM_CH = 6047;
	PB_USE_PACKINGFCMITEM_HC = 6048;
	PB_CREATE_PACKINGCM_CH = 6049;
	PB_CREATE_PACKINGCM_HC = 6050;
	PB_PACKING_FCMDATA_HC = 6051;
	PB_PLAYER_INPUTCONTENT_CH = 6052;
	PB_PLAYER_INPUTKEYS_CH = 6053;
	PB_CLOUD_ROOM_STATUSTIME_HC = 6054;
	PB_PLAYER_VEHICLE_SLEEP_HC = 6055;
	PB_SENSOR_CONTAINER_DATA_CH = 6056;
	PB_SENSOR_CONTAINER_DATA_HC = 6057;
	PB_VEHICLE_BIND_ACTOR_HC = 6058;
	PB_DOOR_DATA_HC = 6059;
	PB_PLAYER_CARRYACTOR_CH = 6060;
	PB_PLAYER_CARRYACTOR_HC = 6061;
	PB_VILLAGER_BODY_CHANGE_HC = 6062;
	PB_PLAYER_TAME_ACTOR_HC = 6063;
	PB_VILLAGER_MODIFY_NAME_CH = 6064;
	PB_VILLAGER_CLOTH_HC = 6065;
	PB_ACTOR_HEAD_DISPLAY_ICON_HC = 6066;
	PB_ACTOR_PLAY_ANIM_BY_ID_HC = 6067;
	PB_VILLAGE_TOTEM_TIP_HC = 6068;
	PB_VILLAGE_TOTEM_ACTIVE_HC = 6069;
    PB_SAVE_TOMB_STONE_HC = 6070;
	PB_PLAYER_LEVELMODE_HC = 6071;
	PB_ACTION_ATTR_STATE_HC = 6072;
	PB_EDU_ROLEINFO_HC = 6073;
	PB_PLAYER_GOTOPOS_CH = 6074;
	PB_IMPORT_MODEL_HC = 6075;
	PB_CUSTOM_MODEL_PRE_HC = 6076;
	PB_CUSTOM_MODEL_PRE_CH = 6077;
	PB_RESETDEFORMATION_CH = 6078;
	PB_DEFORMATION_SKIN_CH = 6079;
	PB_PLAYERTRANSFORMSKIN_HC = 6080;
	PB_RESTORE_DEFORMATION_CH = 6081;
	PB_PLAYER_SAVE_ARCH_HC = 6082;
	PB_PLAYER_PUSH_ARCH_CH = 6083;
	PB_LIGHTNING_HC = 6084;
	PB_INTERACT_MOBPACK_HC = 6085;
	PB_UPDATE_MOB_BACKPACK_HC = 6086;
	PB_MOVE_MOBBACKPACKITEM_CH = 6087;
	PB_INTERACT_MOBBACKPACKITEM_CH = 6088;
	PB_ALTAR_LUCKY_DRAW_CH = 6089;
	PB_SFACTIVITY_HC = 6090;
	PB_OPEN_DEVGOODSBUY_DIALOGHC = 6091;
	PB_HOME_PRAY_INFO_HC = 6092;
	PB_HOME_PRAY_TREE_STATE_HC = 6093;
	PB_HOME_PRAY_REQ_HC = 6094;
	PB_HOME_PRAY_TIME_CH = 6095;
	PB_HOME_PRAY_TIMEUPDATE_HC = 6096;
	PB_HOME_PRAY_ERROR_HC = 6097;
	PB_OPEN_HOMENPC_HC = 6098;
	PB_OPEN_HOMECLOSET_HC = 6099;
	PB_GODTEMPLE_CREATE_HC = 6100;
	PB_SHAPE_ADDITION_ANIM_HC = 6101;	
	PB_HOME_SUMMONPET_CH = 6102;	
	PB_HOMELAND_RANCH_HC = 6103; 
	PB_HOMELAND_RANCH_ANIMAL_UPDATE_CH = 6104;
	PB_TRIGGER_GRAPHICS_HC = 6105;
	PB_USEITEM_BY_HOMELAND_HC = 6106;
	PB_PLAYER_CUSTOM_BASEMODEL_HC = 6107;
	PB_CHANGE_ACTOR_MODEL_HC = 6108;
	PB_REQUEST_MODEL_CH = 6109;
	PB_NOTIFIY_MODEL_HC = 6110;
    PB_VOICE_INFORM_CH = 6111;
	PB_VOICE_INFORM_HC = 6112;
    PB_RUNE_OPERATE_CH = 6113;
    PB_RUNE_OPERATE_SUCCESS_HC = 6114;	
	PB_FURNACE_TEMPERATURE_CH = 6115;
	PB_TAKE_CONTAINER_ITEM_CH = 6116;
	PB_POT_CONTAINER_SET_MAKE_CH = 6117;
	PB_UPDATE_POT_CONTAINER_HC = 6118;
	PB_NOTIFY_STARSTATION_ADDED_HC = 6119;
	PB_NOTIFY_STARSTATION_REMOVED_HC = 6120;
	PB_NOTIFY_STARSTATION_CHANGENAMESTATUS_HC = 6121;
    PB_STARSTATION_CHANGENAMESTATUS_CH = 6122;
	PB_NOTIFY_ENTER_STARSTATION_CABIN_HC =  6123;
	PB_LEAVE_STARSTATION_CABIN_CH =  6124;
	PB_NOTIFY_LEAVE_STARSTATION_CABIN_HC =  6125;
	PB_UPDATE_STARSTATION_CABIN_LEVEL_CH = 6126;
	PB_NOTIFY_UPDATE_STARSTATION_CABIN_LEVEL_HC = 6127;
	PB_UPDATE_STARSTATION_CABIN_STATUS_CH = 6128;
	PB_NOTIFY_UPDATE_STARSTATION_CABIN_STATUS_HC = 6129;
	PB_NOTIFY_UPDATE_STARSTATION_CABIN_ADDED_HC = 6130;
	PB_NOTIFY_UPDATE_STARSTATION_CABIN_REMOVED_HC = 6131;
	PB_ADD_UNFINISHED_TRANSFER_RECORD_CH = 6132;
	PB_NOTIFY_ADD_UNFINISHED_TRANSFER_RECORD_HC = 6133;
	PB_NOTIFY_UPDATE_UNFINISHED_TRANSFER_RECORD_STATUS_HC = 6134;
	PB_REMOVE_UNFINISHED_TRANSFER_RECORD_CH = 6135;
	PB_NOTIFY_REMOVE_UNFINISHED_TRANSFER_RECORD_HC = 6136;
	PB_STARSTATION_DATA_HC = 6137;
	PB_PLAYER_TRANSFER_BY_STRSTATION_CH = 6138;
	PB_NOTIFY_PLAYER_TRANSFER_BY_STRSTATION_HC = 6139;
	PB_NOTIFY_ACTIVATE_STARSTATION_HC = 6140;
	PB_NOTIFY_UPGRADE_STARSTATION_CABIN_HC = 6141;
	PB_NOTIFY_UPDATE_STARSTATION_SIGN_INFO_HC = 6142;
	PB_REQUIRE_STARSTATION_TRANSFER_CH = 6143;
	PB_NOTIFY_STARSTATION_TRANSFER_RESULT_HC = 6144;
	PB_BLOCK_EXPLOIT_CH = 6145;
	PB_BLOCK_EXPLOIT_HC = 6146;
	PB_VACANT_BOSS_STATE_HC = 6147;
	PB_BACKPACK_REMOVEITEM_CH = 6148;
	PB_NOTIFY_PLAYALTMANMUSIC_HC = 6149;
	PB_NOTIFY_UPDATE_TOOL_MODEL_TEXTURE_HC = 6150;	
	PB_GAINITEMSTOBACKPACK_CH = 6151;
	PB_UPDATE_STARSTATION_CABIN_STATUSEND_CH = 6152;
	PB_NOTIFY_UPDATE_STARSTATION_CABIN_STATUSEND_HC = 6153;
	PB_ADD_STARSTATION_TRANSFER_DESC_CH = 6154;
	PB_ADDEXP_CH = 6155;
	PB_ADDEXPRESULT_HC = 6156;
	PB_COUSTOMUI_EVENT_CH = 6157;
	PB_ACHIEVEMENT_SYNC_HC = 6158;
	PB_ACHIEVEMENT_UPDATE_CH = 6159;
	PB_BATTLEPASS_EVENT_HC = 6160;
	PB_ADDSTAR_CH = 6161;
	PB_USEHEARTH_CH = 6162;
	PB_ACTOR_STOP_ANIM_HC = 6163;
	PB_ACTOR_STOP_ANIM_CH = 6164;
	PB_HOMELAND_RANCH_FOODER_CH = 6167;
	PB_HORSE_FLAG_HC = 6168; 
	PB_HOMELAND_RANCH_FOODERSTATE_HC = 6169; 
	PB_ACHIEVEMENT_INITDATA_HC = 6170;
	PB_HOMELAND_COOK_MENUBUY_HC = 6171;
	PB_HOMELAND_COOK_MENUBUY_CH = 6172;
	PB_HOMELAND_FARM_SHOP_HC = 6173;
	PB_HOMELAND_FARM_SHOP_CH = 6174;
	PB_HOMELAND_COOK_SPFURNITUREBUY_HC = 6177;
	PB_HOMELAND_COOK_SPFURNITUREBUY_CH = 6178;

	PB_PLAYER_OPENUI_HC = 6179;
	PB_ANSWER_LANTERNBIRD_CH = 6180;
	PB_EXCHANGEITEMSTOBACKPACK_CH = 6181;
	PB_EXCHANGEITEMSTOBACKPACKRESULT_HC = 6182;
	PB_CHANGE_QQMUSIC_PLAYER_HC = 6183;
	PB_CHANGE_QQMUSIC_PLAYER_CH = 6184;

	PB_SET_TIANGOU_HC = 6185;
	PB_PLAYER_CLOSEUI_CH = 6186;
	PB_RIDE_INVISIBLE_HC = 6187;
	PB_ACTOR_PLAY_SOUND_CH = 6188;
	
	PB_ACTORINVITE_CH = 6189;
	PB_ACTORINVITE_HC = 6190;
	PB_PLAYER_SKIN_ACT_HC = 6191;//装扮互动HC
	PB_PLAYER_SKIN_ACT_CH = 6192;//装扮互动CH
	
	PB_CHANGE_QQMUSIC_CLUB_HC = 6193;
	PB_CHANGE_QQMUSIC_CLUB_CH = 6194;
	
	PB_ACTOR_STOP_SKIN_ACT_HC = 6195;

	PB_MINICLUB_PLAYER_HC = 6196;
	PB_MINICLUB_PLAYER_CH = 6197;
	
	PB_ACTOR_STOP_SKIN_ACT_CH = 6198;

	PB_SPRAY_PAINT_INFO_CH = 6199;
	PB_ADD_PAINTED_INFO_HC = 6200;
	PB_REMOVE_PAINTED_INFO_HC = 6201;
	
	PB_Equip_Weapon_HC = 6202;
	PB_Equip_Weapon_CH = 6203;
	PB_PLAYEFFECT_CH = 6204;
	
	PB_GainItemsUserDatastrToBackPack_CH = 6205;
	PB_UseMusicYuPu_CH = 6206;
	PB_DanceByPlaying_CH = 6207;
	PB_StopDanceByPlaying_CH = 6208;
	PB_StartAct_CH = 6209;
	PB_StopAct_CH = 6210;
	PB_TOP_BRAND_CH = 6211;
    PB_TOP_BRAND_HC = 6212;
	PB_CHEAT_CHECK_CH = 6213;
	PB_WEAPON_POINT_HC = 6214;
	PB_GAME_MODE_CHANGE = 6215;
	PB_ADDLIGHTCHAIN_HC = 6216;
	PB_STARTFISHING_CH = 6217;
	PB_STARTFISHING_HC = 6218;
	PB_ENDFISHING_CH = 6219;
	PB_ENDFISHING_HC = 6220;
	PB_QUITFISHING_CH = 6221;
	PB_QUITFISHING_HC = 6222;
	PB_CHANGEFISHINGSTAGE_HC = 6223;
	PB_CHANGEEXPOSEPOS_CH = 6224;
	PB_NOTIFY_UPDATE_TOOL_MODEL_TEXTURE_CH = 6225;
	PB_BIND_ITEM_TO_ACTOR_HC = 6226;
	PB_FISHING_BEGIN_FLASH_HC = 6227;
	PB_END_PLAY_FISH_CH	= 6228;
	PB_CHANGE_SHOW_EQUIP_HC = 6229;
	PB_RESET_ROLE_FLAGS = 6240;
	PB_BIND_PLAYER_TO_PHYSICS_PLAT_HC = 6241;
	PB_UNBIND_PLAYER_TO_PHYSICS_PLAT_HC = 6242;
	PB_PHYSICS_COM_UPDATE = 6243;
	PB_PHYSICS_COM_PLAT_LOCAL_POS = 6244;
	PB_EFFECT_COM_PARTICLE_UPDATE = 6245;
	PB_SOUND_COM_UPDATE = 6246;
	PB_BIND_PLAYER_TO_PHYSICS_PLAT_CH = 6247;
	PB_UNBIND_PLAYER_TO_PHYSICS_PLAT_CH = 6248;
	PB_METEOR_SHOWER_HC = 6249;
	PB_PLAYER_TRANSFER_HC = 6250;
	PB_NOTIFY_PLAYER_BLOCK_CHANGE_COLOR_ANIM_HC = 6251;
	
	PB_CUSTOM_MSG = 7000;
	PB_BlockData_CH = 7001;
	PB_PUSHSNOWBALL_OPERATE_CH = 7002;
	PB_PUSHSNOWBALL_OPERATE_HC = 7003;
	PB_PUSHSNOWBALL_SIZECHANGE_HC = 7004;
	PB_PLAY_EFFECT_SHADER_HC = 7005;
	PB_NEW_REPAIR_ITEM_CH = 7006;
	PB_SEND_OBJACTOR_MSG = 7007;
	PB_ADD_BULLETHOLE_HC = 7008;
	PB_ACTORSHOOT_CH = 7009;
	PB_ACTOR_FIREWORK_CH = 7010;
	PB_ACTOR_PLAYANIM_NEW_CH = 7011;
    PB_WORLD_SYNC_SAVE_HC = 10002;
    PB_MAX_MSG_CODE = 10240;

	// 科技树
	PB_OPEN_WORKBENCH_CH = 7100;
	PB_OPEN_WORKBENCH_HC = 7101;
	PB_UNLOCK_TECH_NODE_CH = 7102;
	PB_UNLOCK_TECH_NODE_HC = 7103;

	PB_PLAYER_CUSTOM_CH = 7110;
	PB_PLAYER_CUSTOM_HC = 7111;

	PB_DECOMPOSITION_CH = 7115;
	PB_DECOMPOSITION_HC = 7116;

	PB_AllSingleBuildData_CH = 7117;
	PB_AllSingleBuildData_HC = 7118;

	// 空投事件通知
	PB_AIRDROP_EVENT_HC = 7120;
	PB_AIRDROP_GET_CHEST_CH = 7121;
	PB_AIRDROP_GET_CHEST_HC = 7122;
	PB_AIRDROP_DEL_CHEST_HC = 7123;

	//研究台
	PB_Research_CH = 7130;
	PB_Research_HC = 7131;

	//使用蓝图道具
	PB_TechBlueprint_CH = 7132;
	PB_TechBlueprint_HC = 7133;

	PB_BLOCK_TRIGGER_CH = 7140;

	PB_PLAYER_KILLME = 7150;
	PB_DRINKWATERCH = 7151;
	PB_FILLWATERCH = 7153;
	PB_PROCESS_BUILD_CH = 7155;
	PB_MACHINESOURCE_ACTIVE_CH = 7157;

	PB_PLAYER_SOCMOVEITEM = 7160;

	PB_SocWorkbench_HC = 7170;
	PB_SocWorkbench_CH = 7171;

	PB_SocTeamPositions_HC = 7175;

	PB_SocTeamUpData_CH = 7176;
	PB_SocTeamReqTag_CH = 7177;
	PB_SocTeamTagData_CH = 7178;
	PB_SocTeamTagData_HC = 7179;
}

// MPActorTypes
enum ePBActorTypes
{
	PB_ACTORTYPEGENERAL = 0;
	PB_ACTORTYPEROLE = 1;
	PB_ACTORTYPEMONSTER = 2;
	PB_ACTORTYPEBOSS = 3;
	PB_ACTORTYPEBLOCK = 4;
	PB_ACTORTYPEITEM = 5;
}

// MPStanceType
enum ePBStanceType
{
	PB_STANCESTAND = 1;
	PB_STANCEWALK = 2;
	PB_STANCERUN = 3;
	PB_STANCEJUMP = 4;
	PB_STANCELAY = 5;
	PB_STANCESWIM = 6;
	PB_STANCEFLY = 7;
}

// MPEffectType
enum ePBEffectType
{
    PB_EFFECT_PARTICLE = 0;
    PB_EFFECT_PICKITEM = 1;
    PB_EFFECT_SOUND = 2;
    PB_EFFECT_ACTORBODY = 3;
    PB_EFFECT_DESTROYBLOCK = 4;
    PB_EFFECT_PLAYMUSICGRID = 5;
    PB_EFFECT_STOPMUSICGRID = 6;
    PB_EFFECT_STRINGACTORBODY = 7;
    PB_EFFECT_CRACKBLOCK = 8;
    PB_EFFECT_TIRGGERSOUND = 9;
	PB_EFFECT_VEHICLE = 10;
	PB_EFFECT_STOPPARTICLE = 11;
	PB_EFFECT_SOUND_NEW = 12;
	PB_EFFECT_SOUND_NEW_FOR_TRACK = 13;
	PB_EFFECT_SOUND_NEW_STOP = 14;
	PB_EFFECT_SOUND_NOTE = 15;
	PB_EFFECT_SOUND_NOTE_STOP = 16;
    PB_EFFECT_SOUNDID = 17;
    PB_EFFECT_PARTICLEID = 18;	
}

message PB_KVData
{
	required string key = 1;
	required int32 val = 2;
}

message PB_ItemDataComponent
{
	required string name = 1;
	required bytes data = 2;
}

message PB_Vector3
{
	optional sint32 X = 1;
	optional sint32 Y = 2;
	optional sint32 Z = 3;
}

message PB_Vector3f
{
	optional float X = 1;
	optional float Y = 2;
	optional float Z = 3;
}

message PB_Quaternion
{
	optional float X = 1;
	optional float Y = 2;
	optional float Z = 3;
	optional float W = 4;
}

message PB_Pos
{
	optional sint32 X = 1;
	optional sint32 Y = 2;
	optional sint32 Z = 3;
	optional uint32 Map = 4;
}

message PB_BodyDir
{
	optional float RotationYaw = 1;
	optional float RotationPitch = 2;
	optional PB_Vector3 Motion = 3;
}

message PB_Item
{
	optional uint32 DefID = 1;
	optional uint32 Idx = 2;
	optional uint32 Pile = 3;
	optional string UserDataStr = 4;
}

message PB_ItemRune{
	optional int32 RuneID = 1;
	optional float RuneVal0 = 2;
	optional float RuneVal1 = 3;  
	optional int32 ItemID = 4;
}

message PB_ArmFumo
{
	repeated int32 FumoIDs = 1;
	repeated PB_ItemRune Runes = 2;
}

message PB_Arm
{
	optional uint32 DefID = 1;
	optional uint32 Idx = 2;
	optional uint32 Color = 3;
	optional uint32 Dur = 4;
	optional uint32 RepairNum = 5;
	optional string Name = 6;
	optional PB_ArmFumo Fumo = 7;
	optional uint32 UserDataInt = 8;
	repeated PB_ItemDataComponent datacomponents = 9;
	optional uint32 maxduration = 10;
	optional uint32 dataex = 11;
}

message PB_ItemSpecial
{
	optional int32 SpecailTypeID = 1;
	optional string Buff = 2;
}

message PB_ItemGridEffects 
{
	optional string effectname = 1;
	optional float effecscale = 2;
}

message PB_ItemGridData
{
	optional PB_Item Item = 1;
	optional PB_Arm Arm = 2;
	optional PB_ItemSpecial ItemSpecial = 3;
	repeated PB_KVData kvs = 4;
}

message PB_ItemGrid
{
	optional PB_BodyDir Dir = 1;
	optional float FallDistance = 2;
	optional int32 SpecialFlag = 3;
	optional int32 Type = 4;
	optional PB_ItemGridData ItemGridData = 5;
	repeated PB_ItemGridEffects grideffects = 6;
}

message PB_DieInfo 
{
	optional int32 atktype = 1;
	optional int32 buffId = 2;
	optional int32 buffLevel = 3;
	optional int32 toolid = 4;
	optional int32 playerid = 5;
	optional int32 length = 6;
	optional int32 mobid = 7;
	optional uint64 survival_time = 8;
}

message PB_RoleBackpack
{
	optional int32 PackTmp = 1;
}

message PB_PlayerVipInfo
{
    optional int32 VipType = 1;
    optional int32 VipLevel = 2;
    optional int32 VipExp = 3;
}

message PB_ChunkBlob
{
    optional int32 UnzipLen = 1;
    optional int32 BlobLen = 2;
    optional string BlobDetail = 3;
}

message PB_ChunkSaveDB
{
    optional uint64 OWID = 1;
    optional uint32 MapID = 2;
    optional sint32 x = 3;
    optional sint32 z = 4;
    optional int32 Version = 5;
    optional int32 ShareFlag = 6;
    optional PB_ChunkBlob ChunkBlob = 7;
}

message PB_GridPak
{
    repeated PB_ItemGrid Grids = 1;
}

message PB_ShortcutPak
{
    optional int32 HandIdx = 1;
    repeated PB_ItemGrid Grids = 2;
}

message PB_ArmPak
{
    repeated PB_ItemGrid Grids = 1;
}

message PB_ActorBuff
{
    optional uint32 BuffID = 1;
    optional uint32 BuffLV = 2;
    optional int32 Ticks = 3;
	optional int32 BuffInstanceId = 4;
}

message PB_BuffAttr
{
    optional uint32 AttrID = 1;
    optional float Val = 2;
}

message PB_RolePackage
{
    optional PB_GridPak GridPak = 1;
    optional PB_ShortcutPak ShortcutPak = 2;
    optional PB_ArmPak ArmPak = 3;
    optional PB_Pos RevicePos = 4;
	optional PB_GridPak ExtPak = 5;
}

message PB_ActorBuffList
{
    repeated PB_ActorBuff Buffs = 1;
    repeated PB_BuffAttr RelBuffAttrs = 2;
    repeated PB_BuffAttr AbsBuffAttrs = 3;
}

message PB_RoleData
{
	optional int32 Uin = 1;
	optional uint64 OWID = 2;
	optional float HP = 3;
	optional int32 Oxygen = 4;
	optional int32 FoodLevel = 5;
	optional int32 FoodSatLevel = 6;
	optional int32 UsedStamina = 7;
	optional int32 Exp = 8;
	optional int32 Level = 9;
	optional int32 LastLoginTime = 10;
	optional int32 LoginNum = 11;
	optional float FallDist = 12;
	optional int32 Flags = 13;
	optional int32 LiveTicks = 14;
	optional uint64 RideActorID = 15;

	optional PB_Pos Pos = 16;
	optional PB_BodyDir Dir = 17;
	optional PB_RolePackage Package = 18;
	optional PB_ActorBuffList Buff = 19;
	
	optional uint64 CarringActorID = 20;

	optional float STRENGTH = 21;
	optional bool ENABLE_STRENGTH = 22;
	optional float max_strength = 23;
	optional float Armor = 24;
	optional float Perseverance = 25;
	optional float MaxHP = 26;
	optional int32 StrengthFoodShowState = 27;
	optional int32 StarDebuffStage = 28;

	optional PB_DieInfo dieinfo = 29;
}

message PB_SOCTEAMDATA
{
	repeated int32 uins = 1;
	optional int32 mainuin = 2;
	optional int32 teamid = 3;
}

message PB_BodyEffectBrief
{
    optional int32 effectID = 1;
	optional float effectScale = 2;
	optional int32 effectClass = 3;
	optional float effectTime = 4;
}
message PB_AOIBodyEffectBrief
{
    optional int32 effectID = 1;
	optional int32 effectScale = 2;
	optional int32 effectClass = 3;
	optional int32 effecttime = 4;
}

message PB_PlayerInfo
{
    optional uint64 ObjID = 1;
    optional int32 anim = 2;
    optional int32 anim1 = 3;
    optional PB_RoleData RoleData = 4;
	optional uint32 BodyColor = 5;
	optional float bodyscale_invalid = 6;
	repeated PB_BodyEffectBrief effectList = 7;
	optional float customscale = 8;
	repeated PB_EffectTriggerSound soundList = 9;
	optional int32 actSeqId = 10;
	optional int32 accountSkinID = 11;
	optional int32 TeamID = 12;
	repeated PB_SawtoothInfo sawtooth = 13;
	optional PB_FishingInfo fishing = 14;
	optional uint64 nodeid = 15;
	optional uint64 CurDisplayHorseObjID = 16;
	optional int32 animweapon = 17;
}

message PB_RedStoneIdx
{
	optional int32 x = 1;
	optional int32 z = 2;
}

message PB_RedStoneData
{
	repeated PB_RedStoneIdx RedStoneIdxList = 1;
}

message PB_GlobalBin
{
	optional int32 BinLen = 1;
	optional string BinContent = 2;
}

message PB_OWGlobalMisc
{
	optional int32 GlobalFlag = 1;
	optional int32 ChunkVer = 2;
	optional int32 ChunkVerBroadCast = 3;
	optional PB_Pos InitPos = 4;
	optional PB_Pos RevicePos = 5;
	optional PB_RedStoneData RedStoneData = 6;
	optional PB_GlobalBin GlobalBin = 7;
}

message PB_OWGlobal
{
	optional uint64 OWID = 1;
	optional int32 ID = 2;
	optional int32 Uin = 3;
	optional int32 SvrStart = 4;
	optional int32 GridChgNum = 5;
	optional PB_OWGlobalMisc Misc = 6;
}

message PB_WorldCreateData
{
	optional int32 TerrType = 1;
	optional uint32 RandSeed1 = 2;
	optional uint32 RandSeed2 = 3;
	optional int32 RoleModel = 4;
	optional string SeedStr = 5;
	optional uint32 TilesX = 6;
	optional uint32 TilesZ = 7;
}

message PB_WorldDesc
{
	optional uint64 WorldId = 1;
	optional int32 WorldType = 2;
	optional int32 OwnerUin = 3;
	optional PB_WorldCreateData CreateData = 4;
	optional int32 DeveloperFlag = 5;
	optional uint64 FromOWID = 6;
	optional int32 RealOwnerUin = 7;
	optional int32 WorldOpen = 8;
	optional string WorldName = 9;
	optional int32 TempType = 10;
	optional int64 pwid = 11;
	optional uint32 SpecialType = 12;
	optional uint32 editorSceneSwitch = 13;
	optional uint32 ctype = 14;
	optional int32 fissionType = 15;
	optional uint64 fissionFrom = 16;
	optional int32 fissionVersion = 17;
    optional string extraInfo = 18;
}

message PB_SkillCDData
{
    optional int32 NumSkillCD = 1;
    repeated int32 ItemID = 2;
    repeated float CD = 3;
}

message PB_RoleInfo
{
    optional int32 Model = 1;
    optional string NickName = 2;
    optional int32 SkinID = 3;
    optional string CustomJson = 4;
	optional int32 FrameID = 5;
	optional string BPtitle = 6;
}

message PB_ActorRoleInfo
{
    optional PB_RoleInfo Info = 1;
    optional PB_PlayerInfo Player = 2;
}

message PB_ActorAttInfo {
	optional uint32 maxhp = 1;
	optional uint32 hprecover = 2;
	optional uint32 walkspeed = 3;
	optional uint32 swimspeed = 4;
	optional uint32 jumppower = 5;
	optional uint32 punchattack = 6;
	optional uint32 rangeattack = 7;
	optional uint32 punchdefense = 8;
	optional uint32 rangedefense = 9;
	optional uint32 dodge = 10;
	optional uint32 attacktype = 11;
	optional uint32 immunetype = 12;
	optional uint32 settingatt = 13;
}

message PB_SawtoothInfo {
	optional uint32 sawtoothid = 1;
	optional PB_Vector3 pos = 2;	
}

message PB_FishingInfo {
	optional int32 FishingState = 14;
	optional int32 FishingItemId = 15;
	optional uint64 HookID = 1;	
}

message PB_ActorCommon {
	optional uint64 wid = 1;	
	optional PB_Vector3 pos = 2;
	optional PB_Vector3 motion = 3;	
	optional sint32 yaw = 4;
	optional sint32 pitch = 5;
	optional sint32 falldist = 6;
	optional uint32 flags = 7;
	optional uint32 liveticks = 8;
	optional PB_ActorAttInfo attinfo = 9;
	optional uint64 masterobjid = 10;
	repeated PB_SawtoothInfo sawtooth = 11;
	optional bytes sandboxnodes = 12;
	optional uint32 teamid = 13;
	optional uint32 flagsex = 14;
}

message PB_AOIActorBuff {
	optional uint32 buffid = 1;
	optional uint32 bufflv = 2;
	optional uint32 ticks = 3;
}

message PB_AttribMod {
	optional int32 attr = 4;
	optional int32 val = 5;
}

message PB_ItemIndexGrid {
	optional int32 index = 1;
	optional int32 itemid = 2;
	optional int32 num = 3;
	optional int32 durable = 4;
	repeated int32 enchants = 5;
	optional int32 userdata = 6;
	optional string userdata_str = 7;
	optional string sid_str = 8;
	optional int32 userdataEx = 9;
	repeated PB_ItemRune runes = 10;
	repeated PB_ItemGridEffects effects = 11;
	optional int32 toughness = 12;
	repeated PB_ItemDataComponent datacomponents = 13;
	optional int32 maxdurable = 14;
	repeated PB_KVData kvs = 15;
	optional uint32 dataex = 16;
}
message PB_ActorItem
{
	optional PB_ActorCommon basedata = 1;
	optional int32 itemid = 2;
	optional int32 num = 3;
	optional int32 durable = 4;
	optional int32 delayticks = 5;
	repeated int32 enchants = 6;
	optional string userdatastr = 7;
	optional string serverid = 8;
	repeated PB_ItemRune runes = 9;	
	optional int32 toughness = 10;
	repeated PB_ItemDataComponent datacomponents = 11;
	optional int32 maxdurable = 12;
	repeated PB_KVData kvs = 13;
}
message PB_ActorFlyBlock
{
	optional PB_ActorCommon basedata = 1;
	optional uint32 blockid = 2;
	optional uint32 blockdata = 3;
	optional sint32 maxdist = 4;
	optional bool dropitem = 5;
	optional PB_Vector3 startblock = 6;
}

message PB_ActorProjectile {
	optional PB_ActorCommon basedata = 1;
	optional uint64 shooter = 2;
	optional sint32 itemid = 3;
	optional sint32 durable = 4;
	repeated sint32 enchants = 5;
	optional uint32 color = 6;
	optional sint32 blockid = 7;
	optional PB_Vector3 blockpos = 8;
	repeated sint32 rotatequat = 9;
	repeated PB_ItemRune runes = 10;
	repeated sint32 prerotatequat = 11;
	optional sint32 maxdurable = 12;
}
message PB_ActorThornBall {
	optional game.common.PB_ActorProjectile ActorProjectile = 1;
	optional bool impactActor = 2;
	optional bool isDrop = 3;
}

message PB_ActorFishhook {
	optional game.common.PB_ActorProjectile ActorProjectile = 1;
	optional uint64 resultId = 2;
}

message PB_ActorFlyMob
{
	optional PB_ActorMob mobdata = 1;
	optional PB_Vector3 moveTarget = 2;	
	optional string luadata = 3;
}
message PB_ActorGhost
{
	optional PB_ActorCommon basedata = 1;
	optional int32 defid = 2;
	optional int32 hp = 3;
	optional int32 missionflags = 4;
}

message PB_ActorPipeline {
	optional PB_ActorItem itemData = 1;
}

message PB_BindPlayerToPhysicsPlat {
	optional int32 uin = 1;    					// 被绑定的player uin
	optional uint64 objId = 2;					// 绑定到 ObjId
	optional PB_Vector3f localPos = 3;			// 没有考虑父节点缩放的localPos
}

message PB_UnBindPlayerToPhysicsPlat {
	optional uint64 objId = 1;					// 绑定到 ObjId
	repeated int32 uin = 2;    					// 被绑定的player uin
}

message PB_OnPlatPlayerInfo {
	optional int32 uin = 1;    					// 被绑定的player uin
	optional PB_Vector3f localPos = 2;			// 没有考虑父节点缩放的localPos
}


message PB_ActorPhysicsCom {
	optional int32 type = 1;    				// 类型
	optional int32 shape = 2;					// 碰撞盒形状
	optional PB_Vector3f centerOffset = 3;		// 中心点偏移
	optional PB_Vector3f boxSize = 4;			// BoxCollider Size
	optional float radius = 5;					// CapsuleRadius or SphereRadius
	optional float height = 6;					// Capsule height
	optional float drag	  = 7;					// 线性阻尼
	optional float angularDrag = 8;				// 角阻尼
	optional bool useGravity = 9;				// 是否应用重力
	optional float mass	= 10;					// 质量
	optional bool isKinematic = 11;				// 是否运动学
	optional PB_Vector3f centerOfMass = 12;		// 质量中心点
	optional bool bConvex = 13;					// 减面
	optional bool bPlatform = 14;				// 平台
	optional float staticFriction = 15; 		// 静态摩擦
	optional float dynamicFriction = 16;		// 动态摩擦
	optional float restitution = 17;			// 弹性
	repeated PB_OnPlatPlayerInfo onPlatPlayers = 18;	// 当前在板子上的players
}

message PB_ActorPhysicsComUpdate {
	optional uint64 objId = 1;					// ObjId
	optional int32 type = 2;    				// 类型
	optional int32 shape = 3;					// 碰撞盒形状
	optional PB_Vector3f centerOffset = 4;		// 中心点偏移
	optional PB_Vector3f boxSize = 5;			// BoxCollider Size
	optional float radius = 6;					// CapsuleRadius or SphereRadius
	optional float height = 7;					// Capsule height
	optional bool bConvex = 8;					// 减面
	optional bool bPlatform = 9;				// 平台
	optional uint64 objIdRoot = 10;				// 根节点id
}

message PB_EffectComParticleInfo {
	optional int32 particleId = 1;
	optional bool isLoop = 2;
}

message PB_EffectComParticleUpd
{
	optional uint64 objId = 1;
	optional PB_EffectComParticleInfo info = 2;
	optional uint64 objIdRoot = 3;				// 根节点id
}

message PB_SoundComInfo {
	optional int32 soundId = 1;
	optional bool isPlayNow = 2;
	optional bool isLoop = 3;
	optional float volume = 4;
	optional float pitch = 5;
	optional int32 playingMode = 6;
	optional string soundStrId = 7;
}

message PB_SoundComUpd
{
	optional uint64 objId = 1;
	optional PB_SoundComInfo info = 2;
}

message PB_MeteorShowerInfo 
{
	optional int32 type = 2;
}

message PB_ActorObj
{
	optional PB_ActorCommon basedata = 1;
	optional string modelpath = 2;
	optional int32 modeltype = 3;
	optional int32 extradata = 4;
	optional PB_Vector3f scale = 5;
	optional bool interacted = 6;
	optional string modelcomponent = 7;
	optional float roll = 8;
	optional PB_ActorPhysicsCom physicsCom = 9;
	optional PB_EffectComParticleInfo effectComPtclInfo = 10;
	optional int64 nodeid = 11;
	optional bool isparent = 12;
	optional int64 parentwid = 13;
	optional string children = 14;
	optional string script = 15;
	optional PB_SoundComInfo soundComInfo = 16;
}

message PB_ActorObjArray
{
	repeated PB_ActorObj child = 1;	
}

message PB_ActorMob
{
	optional PB_ActorCommon basedata = 1;

	optional int32 defid = 2;
	optional uint32 hp = 3;
	optional uint32 owner = 4;
	optional int32 color = 5;
	repeated PB_AOIActorBuff buffs = 6;
	repeated PB_AttribMod mods = 7;
	repeated PB_ItemIndexGrid equips = 8;
	optional int32 growage = 9;
	optional int32 scale = 10;
	optional int32 dieticks = 11;
	repeated PB_ItemIndexGrid bags = 12;
	optional int32 food = 13;
	optional int32 bodyscale = 14;
	optional PB_Vector3 bonfirepos = 15;
	optional int32 animwaketicks = 16;
	optional string displayname = 17;
	optional bool climbing = 18;
	optional string serverid = 19;
	optional bool eaten = 20;
	optional int32 needeat = 21;
	optional uint32 maxhp = 22;
	optional int32 growtime = 23;
	optional int32 growdvalue = 24;
	optional string componentData = 25;
	optional uint64 RideActorID = 26;
	optional string modelcomponent = 27;
	optional string scriptcomponent = 28;
	optional int32 loctype = 29;
}
message PB_ActorAquaticMob
{
	optional PB_ActorMob mobdata = 1;
	optional sint32 droughtTolerance = 2;
	optional PB_Vector3 moveTarget = 3;
}

message PB_ActorGeneral
{
    optional int32 MapId = 1;
    optional int32 InfoLen = 2;
    optional string ActorDetail= 3;
	optional string actorType = 4;
}

message PB_ActorInfo
{
    optional PB_ActorGeneral ActorGeneral = 1;
    optional PB_ActorRoleInfo RoleInfo = 2;
}

message PB_MoveMotion
{
	optional PB_Vector3 Position = 1;
	optional uint32 Yaw = 2;
	optional uint32 Pitch = 3;
	optional uint32 MapID = 4;
	optional int32 ChangeFlags = 5;
}


message PB_ItemData
{
	optional int32 Index = 1;
	optional int32 ItemID = 2;
	optional int32 Durable = 3;
	optional uint32 Num = 4;
	optional int32 UserData = 5;
	repeated int32 Enchs = 6;
	optional string UserDataStr = 7;
	repeated PB_ItemRune Runes = 8;	
	repeated PB_ItemGridEffects grideffects = 9;	
	optional int32 Toughness = 10;
	repeated PB_ItemDataComponent datacomponents = 11;
	optional int32 MaxDurable = 12;
	repeated PB_KVData kvs = 13;
	optional uint32 dataex = 14;
}

message PB_PlayerBriefInfo
{
	optional int32 Uin = 1;
	optional int32 MapID = 2;
	optional float HP = 3;
	optional string NickName = 4;
	optional int32 PlayerIndex = 5;
	optional int32 TeamID = 6;
	repeated int32 CGVars = 7;
	optional int32 InSpectator = 8;
	optional PB_Vector3 Pos = 9;
	optional PB_PlayerVipInfo VipInfo = 10;
    optional string CustomJson = 11;
	optional int32 SkinID = 12;
	optional int32 FrameID = 13;
	optional string CustomModel = 14;
	optional int32 AcctountSkinID = 15;
	optional float Strength = 16;
	optional float MaxHP = 17;
	optional float OverflowHP = 18;
	optional float MaxStrength = 19;
	optional float OverflowStrength = 20;
	optional float Armor = 21;
	optional float Perseverance = 22;
	optional float exposePosToOther = 23;
	optional float Thirst = 24;
	optional float MaxThirst = 25;
	optional float OverflowThirst = 26;
}

message PB_EffectParticle
{
	optional string Name = 1;
	optional int32 Age = 2;
	optional int32 Color = 3;
	optional int32 Yaw = 4;
	optional int32 Pitch = 5;
	optional PB_Vector3 Pos = 6;
	optional string ziprespath = 7;
	optional bool isPersistent = 8;
}
message PB_EffectParticleID
{
	optional uint32 id = 1;
	optional int32 Age = 2;
	optional int32 Color = 3;
	optional int32 Yaw = 4;
	optional int32 Pitch = 5;
	optional PB_Vector3 Pos = 6;
	optional string ziprespath = 7;
}
message PB_EffectParticleID_V2
{
	optional uint32 id = 1;
	optional int32 Age = 2;
	optional int32 Color = 3;
	optional uint32 Yaw_Pitch = 4;
	repeated sint32 Pos = 5 [packed=true];
	optional string ziprespath = 6;
}

message PB_EffectPickItem
{
	optional uint64 PickerObj = 1;
	optional uint64 ItemObj = 2;
	optional int32 YOffset = 3;
}

message PB_EffectSound
{
	optional string Name = 1;
	optional float Volume = 2;
	optional float Pitch = 3;
	optional int32 Flags = 4;
	optional int32 Segment = 5;
	optional PB_Vector3 Pos = 6;
	optional bool fixpitch = 7;
}

enum eEffectIDX {
	Volume = 1;
	Pitch = 2;
	Flags = 3;
	Segment = 4;
	PosX=5;
	PosY=6;
	PosZ=7;
}

message PB_EffectSound_V2
{
	optional string Name = 1; 
	repeated sint32 Effnd = 2 [packed=true];
}

message PB_EffectSoundID
{
	optional uint32 id = 1;
	optional int32 Volume = 2;
	optional int32 Pitch = 3;
	optional int32 Flags = 4;
	optional int32 Segment = 5;
	optional PB_Vector3 Pos = 6;
	optional bool fixpitch = 7;
}

message PB_EffectSoundID_V2
{
	repeated sint32 Effd = 1 [packed=true];
}

message PB_EffectTriggerSound
{
	optional string Name = 1;
	optional float Volume = 2;
	optional float Pitch = 3;
	optional bool IsLoop = 4;
	optional int32 PlayState = 5;
	optional uint64 ObjId = 6;
	optional PB_Vector3 Pos = 7;
	optional int32 position = 8;
	optional int32 dist = 9;
}
message PB_AOIEffectTriggerSound
{
	optional string Name = 1;
	optional int32 Volume = 2;
	optional int32 Pitch = 3;
	optional bool IsLoop = 4;
	optional int32 PlayState = 5;
	optional uint64 ObjId = 6;
	optional PB_Vector3 Pos = 7;
	optional int32 position = 8;
}

message PB_EffectActorBody
{
	optional uint64 ObjId = 1;
	optional int32 BodyEffect = 2;
	optional int32 Status = 3;
}

message PB_EffectStringActorBody
{
	optional uint64 ObjId = 1;
	optional string EffectName = 2;
	optional int32 Status = 3;
	optional float loopPlayTime = 4;
}

message PB_EffectDestroyBlock
{
	optional int32 Face = 1;
	optional int32 SubType = 2;
	optional int32 Age = 3;
	optional PB_Vector3 Pos = 4;
	optional int32 ID = 5;
}

message PB_EffectCrackBlock
{
	optional int32 Stage = 1;
	optional uint64 ActorId = 2;
	optional PB_Vector3 BlockPos = 3;
}

message PB_EffectVehicle
{
	optional string Name = 1;
	optional uint64 ActorId = 2;
	optional PB_Vector3 BlockPos = 3;
	optional int32 Age = 4;
}

message PB_EffectPlayMusicGrid
{
	optional string Name = 1;
	optional float Pitch = 2;
	optional int32 Flags = 3;
	optional int32 Segment = 4;
	optional PB_Vector3 BlockPos = 5;
}

message PB_EffectStopMusicGrid
{
	optional PB_Vector3 BlockPos = 1;
}

message PB_IntertactData
{
	optional int32 Type = 1;
	optional int32 ID = 2;
	optional int32 Show = 3;
}

message PB_TaskContentData
{
	optional int32 Type = 1;
	optional int32 ID = 2;
	optional int32 Num = 3;
	optional int32 CompletedNum = 4;
}

message PB_TaskInfoData
{
	optional int32 ID = 1;
	optional int32 State = 2;
	optional int32 PlotID = 3;
	repeated game.common.PB_TaskContentData Contents = 4;
}

message PB_PreBlockData
{
	optional int32 ID = 1;
	optional PB_Vector3 BlockPos = 2;
	optional int32 SpecialBlockColor = 3;
	optional int32 EXID = 4;
}

message PB_CustomModelClassData
{
	optional string ClassName = 1; 
	repeated string ModelNames = 2; 
	optional int32  folderindex = 3;
}

message PB_MobDisplayData
{
    optional int32 mobId = 1;
    optional int32 itemId = 2;
    optional int32 itemCount = 3;
    optional int32 animId = 4;
}

message PB_NpcShopItemData
{
    optional int32 SkuID = 1;//商品ID
    optional int32 ItemID = 2;//出售的物品ID
    optional int32 OnceBuyNum = 3;//单次购买的数量
    optional int32 MaxCanBuyCount = 4;//最大可购买的次数
	optional int32 RefreshDuration = 5;//商品恢复刷新时间间隔，当为0时表示不可恢复
    optional int32 StarNum = 6;//购买该商品需要的星星的数量
    optional int64 CostItemInfo1 = 7;//购买该商品需要的道具1的ID和数量（id*1000+num）插件ID扩展 int32改为int64
    optional int64 CostItemInfo2 = 8;//购买该商品需要的道具2的ID和数量（id*1000+num）插件ID扩展 int32改为int64
    optional int32 LeftCount = 9;//剩余可购买次数
    optional int32 EndTime = 10;//刷新倒计时
    optional int32 iShowAD = 11;//是否开启广告
}

message PB_NpcShopData
{
    optional int32 ShopID = 1;
	optional string ShopName = 2;
	optional string ShopDesc = 3;
	optional string InnerKey = 4;
	repeated game.common.PB_NpcShopItemData ShopItemData = 5;
}

message PB_ActorOneAvatarModelData
{
	optional string ModelFilename = 1;
	optional float Scale = 2;
	optional int32 Yaw = 3;
	optional int32 Pitch = 4;
	optional PB_Vector3 OffsetPos = 5;
	optional int32 Roll = 6;
	optional bool NewRotateMode = 7;
	optional PB_Vector3 Scale3 = 8;
}

message PB_ActorOneBoneModelData
{
	optional string BoneName = 1;
	repeated PB_ActorOneAvatarModelData AvatarModels = 2;
}

message PB_BasketBallOperate
{
	optional int32 Type = 1;
	optional uint64 ActorID = 2;
	optional bool	IsSelectedTarget = 3;
	optional int32  FallResult = 4;
	optional int32  ExtendData = 5;
	optional int32  Uin = 6;
	optional game.common.PB_Vector3 pos = 7;
	optional float	yaw = 8;
	optional int32	SelectedActorID = 9;
	optional float	pitch = 10;
}

message PB_CSAuthorityData
{
    optional int32  Uin = 1;
    optional int32  Type = 2;
    optional uint32 Flag = 3;
}

message PB_CSPermitData
{
    optional int32  Uin = 1;
	optional int32  Type = 2;
    optional uint32 Flag = 3;
}

message PB_Edu_RoleInfo
{
	optional uint32 objId = 1;
	optional float yaw = 2;
}

message PB_ImportModelData
{
	optional string Key = 1;
	optional string Name = 2;
	optional string Desc = 3;
	optional int32 Type = 4;
	optional int32 AuthUin = 5;
	optional string AuthName = 6;
	optional string FileName = 7;
}


message PB_GraphicsAttr  //图文信息参数
{
	optional int32 grapicsid = 1; // 创建的图文信息ID
	optional int32 Type = 2; // 图文信息类型
	optional int32 Groupid = 3; //组信息
	optional string Title = 4; //文字图文信息的标题
	optional int32 Apha = 5; //透明度
	optional int32 CurVal = 6; //进度条的当前值
	optional int32 MaxVal = 7; //进度条的最大值
	optional int32 Color = 8; //进度条的颜色值
	optional float Fontsize = 9; //进度条的颜色值
	optional PB_Vector3 WordPos = 10; //位置
	optional uint64 bindObjID = 11; //所属actor的ObjID
	optional int32 x2 = 12; //2d坐标
	optional int32 y2 = 13; //2d坐标
	optional PB_Vector3 offset = 14; //偏移
	optional uint64 destObjID = 15; //终点的ObjID
	optional string Translate = 16; // 主机翻译信息
	optional bytes AutoWrap = 17; // 是否自动换行
}

message PB_UnfinishedStarStationTransferRecord
{
	required int32 destStarStationID = 1;
	required int32 destMapID = 2;
	required game.common.PB_Vector3 srcCabinPos = 3;
	required int32 cabinStatus = 4;
}

message PB_StarStationCabinDef
{
	required game.common.PB_Vector3 cabinPos = 1;
	required int32 cabinStatus = 2;
	required int32 bindPlayerUin = 3;
}

message PB_StarStationDef
{
	required int32 starStationID = 1;
	required string starStationName = 2;	
	required int32 mapID = 3;
	required bool isConsoleActive = 4;
	required game.common.PB_Vector3 consolePos = 5;	
	required bool isConsoleSign = 6;
	optional int32 stationType = 7;
	optional int32 stationExtraData = 8;
}

message PB_ChangeStarStationNameStatus
{
	required int32 starStationID = 1;
	required bool isActive = 2;
	optional string starStationName = 3;
	required bool isSign = 4;
}

message PB_EnterStarStationCabin
{
	required int32 uin = 1;
	required int32 starStationID = 2;
	required game.common.PB_Vector3 cabinPos = 3;
	required int32 status = 4;
	required bool result = 5;
}

message PB_LeaveStarStationCabin
{
	required int32 uin = 1;
	required int32 starStationID = 2;
	required game.common.PB_Vector3 cabinPos = 3;
	required int32 status = 4;
}

message PB_UpdateStarStationCabinAdded
{
	required int32 starStationID = 1;
	required game.common.PB_StarStationCabinDef cabinDef = 2;
}

message PB_UpdateStarStationCabinRemoved
{
	required int32 starStationID = 1;
	required game.common.PB_Vector3 cabinPos = 2;
	optional int32 mapID = 3;
}

message PB_UpdateStarStationCabinStatus
{
	required int32 starStationID = 1;
	required game.common.PB_Vector3 cabinPos = 2;
	required int32 status= 3;
}

message PB_addStarStationTransferDesc
{
	required int32 srcStarStationID = 1;
	required int32 descStarStationID = 2;
}


message PB_AddStarStationDef
{
	required game.common.PB_StarStationDef starStationDef = 1;															
}

message PB_DelStarStationDef
{
	required int32 srcStarStationID = 1;
	required game.common.PB_Vector3 cabinPos = 2;
}

message PB_AddUnfinishedTransferRecord
{
	required int32 srcStarStationID = 1;
	required game.common.PB_UnfinishedStarStationTransferRecord unfinishedTransferRecord = 2;
}

message PB_UpdateUnfinishedTransferRecordStatus
{
	required int32 starStationID = 1;
	required game.common.PB_Vector3 cabinPos = 2;
	required int32 status = 3;
}

message PB_RemoveUnfinishedTransferRecord
{
	required int32 starStationID = 1;
	required game.common.PB_Vector3 cabinPos = 2;
}

message PB_AchievementInfo
{
	required int64 playerID = 1;
	required int32 achievementID = 2;
	required int32 achievementState = 3;
	required int32 rewardState = 4;
	required int32 arryNum = 5;
	required int32 completeYear = 6;
	required int32 completeMonth = 7;
	required int32 completeDay = 8;
}

message PB_EffectSoundNew
{
	optional string Name = 1;
	optional float Volume = 2;
	optional float Pitch = 3;
	optional int32 SoundType = 4;
	optional PB_Vector3 Pos = 5;
	optional int64 ObjId = 6;
	optional int64 StartTime = 7;//开始播放的时间戳
	optional int32 Duration = 8;//时长
	optional int32 SoundPos = 9;//播放位置
	optional int32 TrackId = 10;//播放音轨id
	optional int32 InstrumentCode = 11;//乐器编码
	optional string Url = 12;//url
	optional int32 WorldId = 13;//世界id
	optional string ExtraStr = 14;
	optional bool IsLoop = 15;
	optional int32 NoteCode = 16;//音符编码
	optional int32 TpqCount = 17;//节拍
}

message PB_TeamScore
{
	required int32 teamID = 1;
	optional int32 score = 2;
	optional int32 flags = 3;
}

message PB_ActorSnowHare
{
	optional PB_ActorMob mobdata = 1;
	optional int32 FurColor = 2;
}

message PB_EffectShader
{
	optional string Name = 1;
	optional float Radius = 2;
	optional float Duration = 3;
	optional PB_Vector3 Pos = 4;
}

message PB_SkillExpandCDData_SkillInfo
{
	required float cd = 1;
	required float maxCd = 2;
	required int32  cdDisplay = 3;
	optional string button = 4;
}

message PB_SkillExpandCDData_Skill
{
	required string name = 1;
	optional PB_SkillExpandCDData_SkillInfo info = 2;
}

message PB_SkillExpandCDData
{
   required int32 ItemID = 1;
   repeated PB_SkillExpandCDData_Skill skillData= 2;
}

message PB_SkillExpandCDDataGather
{
   repeated PB_SkillExpandCDData data= 1;
}

// 任务结构
message PB_CraftingQueueTask {
    required int32 crafting_id = 1;       // 合成配方ID
    required int32 count = 2;            // 合成物品数量
    required int32 ticks_per_item = 3;   // 每个物品需要的滴答数（游戏帧数）
    required int32 remaining_ticks = 4; // 当前物品剩余的滴答数
}

message PB_Empty
{
	
}



/////////////////////////////////////////////////////////////////////////
// 科技树
/////////////////////////////////////////////////////////////////////////

message PB_TechNode
{
    optional int32 Id = 1;
    optional int32 PreId = 2;
    optional int32 ItemId = 3;
    optional int32 Level = 4;
    optional bool IsUnlocked = 5;
}

message PB_TechTree
{
	optional int32 Level = 1;
    optional int32  RootTreeId = 2;
    repeated PB_TechNode TreeNodes = 3; // 科技树节点,数组结构存储，树状关系前端自己通过配置文件构建
}

message PB_OpenWorkbenchCH
{
    optional int64 Uin = 1;
    optional int32 Level = 2; // 科技树等级 0-查询所有等级 1-一级 2-二级 3-三级
}

message PB_OpenWorkbenchHC
{
    optional int32 Ret = 1;
    optional string Msg = 2;
    repeated PB_TechTree TechTreeList = 3;
}

message PB_UnlockTechNodeCH
{
    optional int32 Level = 1;
    optional int32 RootTreeId = 2;
    optional int32 NodeId = 3;
}

message CostItem {
	required int32 ItemId = 1;
	required int32 ItemNum = 2;
}

message PB_UnlockTechNodeHC
{
    optional int32 Ret = 1;
    optional string Msg = 2;
	optional int32 Level = 3;
	optional int32 NodeId = 4;
	repeated CostItem ItemNums = 5;
}

message PB_ItemSocBedData
{
	optional int32 owner = 1;
	optional int32 itemid = 2;
	optional PB_Pos pos = 3;
	repeated int32 shared = 4;
}

message PB_SocBedData
{
	repeated PB_ItemSocBedData BedData = 1;
}


message PB_AirDrop_Event
{
	optional int32 EventID = 1;
	optional string EventType = 2;
	optional string EventName = 3;
	optional int32 Start_Pos_X = 4;
	optional int32 Start_Pos_Y = 5;
	optional int32 Start_Pos_Z = 6;
	optional int32 End_Pos_X = 7;
	optional int32 End_Pos_Y = 8;
	optional int32 End_Pos_Z = 9;
	optional int32 Drop_Pos_X = 10;
	optional int32 Drop_Pos_Y = 11;
	optional int32 Drop_Pos_Z = 12;
	optional int32 Current_Pos_X = 13;
	optional int32 Current_Pos_Y = 14;
	optional int32 Current_Pos_Z = 15;
	optional int32 Timestamp = 16;
}

message PB_AIRDROP_GetChestReq
{
	optional int32 Uin = 1;
	 
}

message ChestItem
{
	optional int32 EventId = 1;
	optional int32 SpawnTime = 2;
	optional PB_Pos Pos = 3;
}	

message PB_AIRDROP_GetChestRsp
{
	optional int32 Ret = 1;
	optional string Msg = 2;
	repeated ChestItem List = 3;
	 
}

message PB_AIRDROP_DelChest
{
	repeated ChestItem List = 1;
}

enum ePBSkinningOP
{
	eUnknownSkinning = 0;
	eStartSkinning = 1;
	eCancelSkinning = 2;
	eFinishSkinning = 3;
}

message PB_PlayerSkinning
{
	required ePBSkinningOP op = 1;
	required int32 uin = 2;
	optional uint32 ts = 3;
	optional uint64 mobid = 4;
	optional int32 toolid = 5;
}