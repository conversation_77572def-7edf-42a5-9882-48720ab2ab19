#include "EquipGridContainer.h"
#include "ActorBody.h"
#include "ClientActor.h"
#include "ClientMob.h"
#include "MobAttrib.h"
#include "DropItemComponent.h"
#include "DefManagerProxy.h"
#include "container_backpack.h"
#include "ScriptComponent.h"
#include <random>
#include <ctime>

IMPLEMENT_COMPONENTCLASS(EquipGridContainer)
EquipGridContainer::EquipGridContainer()
{
	m_weaponVisible = true;
	m_Grids.resize(MAX_EQUIP_SLOTS);
	for (int i = 0; i < MAX_EQUIP_SLOTS; i++)
	{
		BackPackGrid* grid = index2Grid(i);
		if (grid)
		{
			grid->setIndex(i);
		}
	}
}

EquipGridContainer::~EquipGridContainer()
{
}

void  EquipGridContainer::setGridInfo(int index, const jsonxx::Object& info)
{
	BaseGridContainer::setGridInfo(index, info);
	sendIndexChange(index, info);
}
void EquipGridContainer::loadStrJson(const std::string& data)
{
	BaseGridContainer::loadStrJson(data);
}
void EquipGridContainer::loadJson(const jsonxx::Array& data)
{
	BaseGridContainer::loadJson(data);
	sendAllChange(data);
}
float EquipGridContainer::getGunDamage(EQUIP_SLOT_TYPE t)
{
	BackPackGrid* itemgrid = getEquipGrid(t);
	if (itemgrid && !itemgrid->isEmpty())
	{
		return itemgrid->getGunDamage();
	}
	return 0.f;
}

void EquipGridContainer::setWeaponVisible(bool visible)
{
	if (m_weaponVisible == visible)
	{
		return;
	}
	m_weaponVisible = visible;
	if (m_weaponVisible)
	{
		equip(EQUIP_WEAPON);
	}
	else
	{
		ClientActor* actor = GetOwnerActor()->ToCast<ClientActor>();
		if (actor)
		{
			ActorBody* body = actor->getBody();
			if (body)
			{
				//卸载掉装备
				body->setEquipItem(EQUIP_WEAPON, 0);
			}
		}
	}
}

BackPackGrid* EquipGridContainer::getEquipGrid(EQUIP_SLOT_TYPE t)
{
	return index2Grid(static_cast<int>(t));
}

bool EquipGridContainer::equipWeapon(const std::string& gridinfo)
{
	ClientActor* actor = GetOwnerActor()->ToCast<ClientActor>();
	if (actor)
	{
		return equip(EQUIP_WEAPON, gridinfo);
	}
	return false;
}
bool EquipGridContainer::equipWeapon(const jsonxx::Object& gridinfo)
{
	ClientActor* actor = GetOwnerActor()->ToCast<ClientActor>();
	if (actor)
	{
		return equip(EQUIP_WEAPON, gridinfo);
	}
	return false;
}

bool EquipGridContainer::equip(EQUIP_SLOT_TYPE t, const std::string& gridinfo)
{
	jsonxx::Object info;
	if (info.parse(gridinfo))
	{
		return equip(t, info);
	}
	return false;
}

bool EquipGridContainer::equip(EQUIP_SLOT_TYPE t, const jsonxx::Object& gridinfo)
{
	ClientActor* actor = GetOwnerActor()->ToCast<ClientActor>();
	if (actor)
	{
		ActorBody* body = actor->getBody();
		if (body)
		{
			body->setTakeoffAble(true);

			if (t == MAX_EQUIP_SLOTS)
			{
				for (int i = 0; i < MAX_EQUIP_SLOTS; i++)
				{
					BackPackGrid* grid = index2Grid(i);
					int itemid = grid->getItemID();
					body->setEquipItem(static_cast<EQUIP_SLOT_TYPE>(i), itemid);
				}
			}
			else
			{
				BackPackGrid* grid = getEquipGrid(t);
				if (grid)
				{
					int olditemid = grid->getItemID();
					grid->load(gridinfo);
					grid->setIndex(t);
					int newitemid = grid->getItemID();

					//怪物枪词条注册与卸载
					ClientMob* mob = dynamic_cast<ClientMob*>(actor);
					LivingAttrib* attr = mob ? mob->getLivingAttrib() : nullptr;
					if (attr && GetDefManagerProxy())
					{
						if (olditemid > 0 && GetDefManagerProxy()->getCustomGunDef(olditemid))
							attr->removeEquipEntryBuff(olditemid);

						if (newitemid > 0 && GetDefManagerProxy()->getCustomGunDef(newitemid))
							attr->addEquipEntryBuff(newitemid, 0);
					}

					body->setEquipItem(t, newitemid);
				}
				sendIndexChange(t, gridinfo);

			}
			BackPackGrid* grid = getEquipGrid(EQUIP_WEAPON);
			if (grid)
			{
				int itemid = grid->getItemID();
				ClientMob* pClientMob = dynamic_cast<ClientMob*>(actor);
				if (pClientMob && pClientMob->getMobAttrib())
				{
					pClientMob->getMobAttrib()->applyEquipToughness(itemid);
				}
			}
			
			//需要发事件
			return true;
		}

	}

	return false;
}



void EquipGridContainer::equip(EQUIP_SLOT_TYPE t, BackPackGrid* itemgrid)
{
	if (itemgrid)
	{
		jsonxx::Object info;
		itemgrid->save(info);
		equip(t, info);
	}

}

void EquipGridContainer::equip(EQUIP_SLOT_TYPE t, int itemid, int durable, int toughness)
{
	BackPackGrid grid;
	grid.setItem(itemid, 1, durable, toughness);
	jsonxx::Object info;
	grid.save(info);
	equip(t, info);
}
void EquipGridContainer::equip(EQUIP_SLOT_TYPE t)
{
	ClientActor* actor = GetOwnerActor()->ToCast<ClientActor>();
	if (actor)
	{
		ActorBody* body = actor->getBody();
		if (body)
		{
			body->setTakeoffAble(true);
			BackPackGrid* grid = getEquipGrid(t);
			int itemid = grid->getItemID();
			body->setEquipItem(t, itemid);
		}
	}
}

void EquipGridContainer::dropOneEquipItem(EQUIP_SLOT_TYPE slot)
{
	BackPackGrid* itemgrid = getEquipGrid(slot);
	if (itemgrid && !itemgrid->isEmpty())
	{
	/*	if (itemgrid->getUserDataInt() == 0 || itemgrid->getUserDataInt() == 1 && GenRandomInt(100) < 10)
		{*/
			dropItem(*itemgrid);
			itemgrid->setItem(0, 0);
			jsonxx::Object info;
			itemgrid->save(info);
			sendIndexChange(slot, info);
			
		/*}*/
	}
}

void EquipGridContainer::randomDropOneEquipItem()
{

	std::vector<int> indexs;
	for (int i = 0; i < MAX_EQUIP_SLOTS; ++i)
	{
		if (!m_Grids[i].isEmpty())
		{
			indexs.push_back(i);
		}
	}
	if (indexs.size() > 0)
	{
		int seed = std::rand();
		std::default_random_engine gen(seed);
		std::uniform_int_distribution<int> distribution(0, indexs.size());
		int result = distribution(gen);
		if (result < indexs.size())
		{
			dropOneEquipItem(static_cast<EQUIP_SLOT_TYPE>(indexs.at(result)));
		}
	}
}

//掉落所有的
void EquipGridContainer::dropAllEquip()
{
	for (int i = 0; i < MAX_EQUIP_SLOTS; ++i)
	{
		dropOneEquipItem(static_cast<EQUIP_SLOT_TYPE>(i));
	}
}

void EquipGridContainer::drop(int index, int len)
{
	if (index + len > m_Grids.size() || index + len < 0 || index < 0 || len < 0)
	{
		return;
	}
	ClientActor* actor = GetOwnerActor()->ToCast<ClientActor>();
	if (actor)
	{
		
			for (int i = 0; i < len; ++i)
			{
				int realindex = index + i;

				if (!m_Grids[realindex].isEmpty())
				{
					dropItem(m_Grids[realindex]);
					m_Grids[realindex].setItem(0, 0);
					jsonxx::Object info;
					m_Grids[realindex].save(info);
					sendIndexChange(realindex, info);
				}
			}
	}
}

void  EquipGridContainer::sendIndexChange(int index, const jsonxx::Object& info)
{
	auto pScriptComponent = GetOwnerActor()->ToCast<ClientActor>()->getScriptComponent();
	if (pScriptComponent)
	{
		pScriptComponent->OnEvent((int)CE_OnEquipChange, true, index);
	}
}

void  EquipGridContainer::sendAllChange(const jsonxx::Array& info)
{

}

void EquipGridContainer::OnBindGameObject(MNSandbox::GameObject* owner)
{
	ClientActor* Actor = dynamic_cast<ClientActor*>(owner);
	if (Actor)
	{
		Actor->BindActorEquiptComponent(this);
	}
}

void EquipGridContainer::OnUnbindGameObject(MNSandbox::GameObject* owner)
{
	ClientActor* Actor = dynamic_cast<ClientActor*>(owner);
	if (Actor)
	{
		Actor->BindActorEquiptComponent(nullptr);
	}
}
