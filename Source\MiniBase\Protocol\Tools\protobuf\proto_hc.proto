syntax = "proto2";
import "proto_common.proto";
package game.hc;

message PB_MsgErrorHC
{
	optional int32 ErrCode = 1;
}

message PB_HeartBeatHC
{
	optional int32 BeatCode = 1;
}

message PB_RoomExtraInfoHC
{
	required bytes room_extra = 1;
	optional string CMURL = 2;
	optional string MapMD5 = 3;
	optional int64 MapID = 4;
}

message PB_SyncChunkDataHC
{
	optional int32 SectionFlags = 1;
	optional int32 Initialize = 2;
	optional game.common.PB_ChunkSaveDB ChunkData = 3;
}

message PB_GameLeaderSwitchHC
{
    optional int32 Uin = 1;
}

message PB_BlockUpdateHC
{
	optional sint32 ChunkX = 1;
	optional sint32 ChunkZ = 2;
	optional int32 MapID = 3;
	repeated uint32 Blocks = 4;
	optional string ContainerBuf = 5;
	repeated uint32 BlocksEx = 6;
}


message PB_RoleEnterWorldHC
{
    optional int32 Uin = 1;
    optional game.common.PB_PlayerInfo PlayerInfo = 2;
    optional game.common.PB_OWGlobal GlobalInfo = 3;
    optional game.common.PB_WorldDesc WorldDesc = 4;
    optional game.common.PB_SkillCDData SkillCDData = 5;
	repeated int32 UnlockItems = 6;
	optional string Url = 7;
	optional bool HasRole = 8;
	optional game.common.PB_SkillExpandCDDataGather SkillExpandCDDataGather = 9;
	optional string TeleportMsg = 10;
}

message PB_RoleLeaveWorldHC
{
    optional int32 Uin = 1;
}

message PB_ActorEnterAOIHC
{
	optional uint64 ObjID = 1;
    optional int32 ActorType = 2;
    optional game.common.PB_ActorInfo ActorInfo = 3;
	optional int32 Spectator_Mode = 4;
	optional int32 Spectator_Type = 5;
	optional uint64 HookID = 6;
	optional uint32 PlayMode = 7;
	optional uint32 PlayOperate = 8;
	optional uint64 childUUID = 9;
	optional uint32 TeamID = 10;
	optional bool OffLine = 11;
}

message PB_GeneralEnterAOIHC
{
	optional uint64 ObjID = 1;
    optional int32 MapId = 3;	
	repeated game.common.PB_AOIBodyEffectBrief effectList = 4;
	repeated game.common.PB_AOIEffectTriggerSound soundList = 5;
    optional game.common.PB_ActorMob ActorMob = 6;
    optional game.common.PB_ActorItem ActorItem = 7;
    optional game.common.PB_ActorMob ActorNpc = 8;
    optional game.common.PB_ActorAquaticMob ActorAquaticMob = 9;
	optional game.common.PB_ActorFlyBlock ActorFlyBlock = 10;
	optional game.common.PB_ActorProjectile ActorProjectile = 11;
	optional game.common.PB_ActorFlyMob ActorFlyMob = 12;
	optional game.common.PB_ActorGhost ActorGhost = 13;
	optional game.common.PB_ActorThornBall ActorThornBall = 14;
	optional game.common.PB_ActorFishhook ActorFishhook = 15;
	optional game.common.PB_ActorPipeline ActorPipeline = 16;
	optional game.common.PB_ActorSnowHare ActorSnowHare = 17;
	optional game.common.PB_ActorObj ActorObj = 18;
}

message AssembleBlockInfo
{
	optional uint32 Block = 1;
	optional uint32 data = 2;
	optional uint32 Info = 3;
	optional uint32 BlockEx = 4;
}

message PB_VehicleAssembleBlockUpdateHC
{
	optional uint64 ObjID = 1;
	repeated AssembleBlockInfo BlockInfo = 2;
	optional string ContainerBuf = 3;
	repeated PB_VehiclePosDesc ChassisPos = 4;
	repeated PB_VehiclePosDesc WheelPos = 5;
	
}

message PB_VehicleAssembleBlockAllHC
{
	optional uint64 ObjID = 1;
	optional int32 UnzipLen = 2;
    optional int32 BlobLen = 3;
    optional string BlobDetail = 4;
    optional int32 BlockVersion = 5;
	repeated PB_VehiclePosDesc ChassisPos = 6;
	repeated PB_VehiclePosDesc WheelPos = 7;
}

message PB_ActorLeaveAOIHC
{
	optional uint64 ObjID = 1;
}

message PB_ActorModelChange
{
	optional uint64 ObjID = 1;
	optional string modelcomponent = 2;
}

message PB_ActorMoveV2HC
{
	optional uint64 ObjID = 1;
	repeated sint32 Position = 2 [packed = true];
	optional uint32 Yaw_Pitch = 3;
	optional int32 ChangeFlags = 4;
}

message PB_ActorMoveV3HC_Batch
{
	repeated PB_ActorMoveV2HC MoveBatch =1;
}

message PB_ActorMoveHC
{
	optional uint64 ObjID = 1;
	optional game.common.PB_MoveMotion MoveMotion = 2;
}

message PB_FullrotActorMoveHC
{
	optional uint64 ObjID = 1;
	optional game.common.PB_Vector3 Position = 2;
	optional uint32 Yaw = 3;
	optional int32 ChangeFlags = 4;
}

message PB_TrainMoveHC
{
	optional uint64 ObjID = 1;
	optional uint32 MapID = 2;
	optional float CurveT = 3;
	optional int32 CarReverse = 4;
	optional uint64 HeadCar = 5;
	optional uint64 TailCar = 6;
	optional uint32 OutIndex = 7;
	optional game.common.PB_Vector3 RailKnot = 8;
}

message PB_ActorTeleportHC
{
	optional uint64 ObjID = 1;
	optional int32 TargetMap = 2;
	optional game.common.PB_Vector3 TargetPos = 3;
}

message PB_ActorMotionHC
{
	optional uint64 ObjID = 1;
	optional float x = 2;
	optional float y = 3;
	optional float z = 4;
    optional bool isChangePos = 5;
}

message PB_ActorMotionV2HC
{
	optional uint64 ObjID = 1;
	optional sint32 x = 2;
	optional sint32 y = 3;
	optional sint32 z = 4;
    optional bool isChangePos = 5;
}

message PB_MechaMotionHC
{
	optional uint64 ObjId = 1;
	optional int32 MotionType = 2;
	optional float MotionParam = 3;
}

message PB_SyncTriggerBlock
{
    optional int32 Uin = 1;
    optional game.common.PB_Vector3 BlockPos = 2;
}

message PB_BlockInteractHC
{
    optional uint64 ObjID = 1;
    optional int32 face = 2;
    optional game.common.PB_Vector3 blockpos = 3;
}

message PB_BlockPunchHC
{
    optional uint64 ObjID = 1;
    optional int32 status = 2;
    optional int32 face = 3;
    optional int32 digmethod = 4;
    optional game.common.PB_Vector3 blockpos = 5;
	optional uint64 vehicleObjID = 6 [default=0];
}

message PB_ItemUseHC
{
	optional uint64 ObjID = 1;
	optional int32 itemid = 2;
	optional int32 status = 3;
	optional int32 shift = 4;
}

message PB_SetHookHC
{
	optional uint64 ObjID = 1;
	optional uint64 hookID = 2;
}

message PB_ItemSkillUseHC
{
	optional uint64 ObjID = 1;
	optional int32 itemid = 2;
	optional int32 status = 3;
	optional int32 skillid = 4;
}

message PB_ActorInteractHC
{
	optional uint64 ObjID = 1;
	optional uint64 target = 2;
	optional int32 itype = 3;
	optional int32 iplot = 4;
}

message PB_RClickUpInteractHC
{
	optional uint64 ObjID = 1;
	optional uint64 target = 2;
}


message PB_ActorAnimHC
{
	optional uint64 actorid = 1;
	optional sint32 anim = 2;
	optional sint32 anim1 = 3;
	optional sint32 actid = 4;
	optional float bodyscale_invalid = 5;
	optional float customscale = 6;
	optional sint32 actidTrigger = 7;
	optional bool sideAct = 8; //是否装扮互动副动作
	optional int32 animSeq = 9; // 序列号，标记是否直接重置并播放动作
	optional int32 isLoop = 10; // 是否重复播放
	optional sint32 animweapon = 11;
	optional int32 changeflag = 12;
}

message PB_BackPackGridUpdateHC
{
	repeated game.common.PB_ItemData ItemInfo = 1;
}

message PB_BackPackEquipWeaponHC
{
	optional int32 GridId = 1;
}

message PB_EquipWeaponHC
{
	required int32 itemId = 1;
	optional int32 uin = 2;
}

message PB_CloseContainerHC
{
	optional int32 BaseIndex = 1;
}

message PB_OpenContainerHC
{
	optional int32 BaseIndex = 1;
	optional int32 TotalItemGrids = 2;
	repeated float AttribInfo = 3;
	optional string Text = 4;
	optional uint64 NpcID = 5;
	optional game.common.PB_Vector3 Pos = 6;
	repeated game.common.PB_ItemData ItemInfo = 7;
	optional uint64 VehicleObjID = 8;
}

message PB_NeedContainerPasswordHC
{
	optional game.common.PB_Vector3 Pos = 1;
	optional int32 state = 2;
	optional uint64 VehicleObjID = 3;
}

message PB_UpdateContainerHC
{
    optional int32 BaseIndex = 1;
    repeated float AttribInfo = 2;
    optional string Text = 3;
    optional game.common.PB_Vector3 Pos = 4;
    repeated game.common.PB_ItemData ItemInfo = 5;
}

message PB_ActorEquipItemHC
{
    optional uint64 ObjId = 1;
    optional int32 SlotType = 2;
    optional game.common.PB_ItemData ItemInfo = 3;
}

message PB_EnchantItemSuccessHC
{
    optional int32 GridIndex = 1;
}

message PB_RuneOperateSuccessHC
{
    optional int32 OpType = 1;
    optional int32 GridIndex = 2;
    optional int32 Result = 3;
}

message PB_RepairItemSuccessHC
{
    optional int32 GridIndex = 1;
}

message PB_GunDoReloadHC
{
    optional int32 BulletID = 1;
    optional int32 Num = 2;
    optional int32 Total = 3;
	optional bool isCustomGun = 4;//是否是新的枪
	optional int32 curShortcut = 5; //当前手持的快捷栏位置
}

message PB_AccountHorseHC
{
    optional int32 HorseID = 1;
    optional int32 SyncType = 2;
    optional int32 SyncData = 3;
}

message PB_UIDisplayHorseHC
{
    optional uint64 HorseObjID = 1;
	optional uint64 PlayerObjID = 2;
}

message PB_ActorAttrChangeHC
{
    optional uint64 ObjID = 1;
    optional float HP = 2;
    optional uint64 BeHurtTarget = 3;
	optional float Armor = 4;
	optional float ExtarHP = 5;
	optional bool OffLine = 6;
}

message PB_ActorBuffChangeHC
{
    optional uint64 ObjID = 1;
    repeated game.common.PB_ActorBuff Buffs = 2;
}

message PB_ActorReviveHC
{
	optional uint64 ObjID = 1;
	optional int32 MapID = 2;
	optional int32 ReviveType = 3;
	optional float ReviveYaw = 4;
	optional float RevivePitch = 5;
	optional game.common.PB_Vector3 RevivePosition = 6;
}

message PB_PlayerAttrChangeHC
{
	optional float HP = 1;
	optional float Oxygen = 2;
	optional int32 Exp = 3;
	optional int32 FoodLevel = 4;
	optional float Strength = 5;
    optional float MaxHP = 6;
    optional float OverflowHP = 7;
    optional float MaxStrength = 8;
    optional float OverflowStrength = 9;
	optional float Armor = 10;
	optional float Perseverance = 11;
	optional float Thirst = 12;
	optional float MaxThirst = 13;
	optional float OverflowThirst = 14;
	optional float Radiation = 15;
	optional game.common.PB_DieInfo dieinfo = 16;
}

message PB_MobBodyChangeHC
{
	optional uint64 ObjId = 1;
	optional int32 BodyColor = 2;
	optional int32 Sheared = 3;
}

message PB_JruisdicTionHC
{
	optional int32 Ret = 1;
}

message PB_ChatHC
{
	optional int32 ChatType = 1;
	optional int32 Uin = 2;
	optional string Speaker = 3;
	optional string Content = 4;
	optional int32 Language = 5;
	optional string Extend = 6;
	optional string Translate = 7; // 主机翻译信息	
}

message PB_ActorInviteHC
{
	optional int32 InviteType = 1;
	optional int32 Targetuin = 2;
	optional int32 ActID = 3;
	optional int32 inviterPosX = 4;
	optional int32 inviterPosZ = 5;
}

message PB_WGlobalUpdateHC
{
	optional int32 WorldTime = 1;
	optional int32 ViewRange = 2;
	optional int32 MapID = 3;
	optional int32 Raining = 4;
	optional int32 DayNightTime = 5;
	optional int32 Darking = 6;
	required int32 CurWeather = 7;
}

message PB_PlayersUpdateInfoHC
{
	repeated int32 TeamScores = 1;
	repeated int32 TeamFlags = 2;
	repeated game.common.PB_PlayerBriefInfo Players = 3;
}

message PB_PlayerLeaveHC
{
	repeated int32 Uins = 1;
}
message PB_TeamScoreHC
{
	repeated game.common.PB_TeamScore Teams = 1;
}
message PB_SetTeamIDHC
{
	required int32 TeamID = 1;
	optional uint64 ObjID = 2;
}
message PB_SetPlayerGameInfoHC
{
	optional int32 Score = 1;
	optional int32 Result = 2;
	optional int32 Ranking = 3;
	optional int32 PlayerResult = 4;
}

message PB_GameTipsHC
{
	optional int32 TipsType = 1;
	optional int32 Id = 2;
	optional int32 Num = 3;
	optional string OtherName = 4;
	optional string TranslateName = 5;
}

message PB_PlayEffectHC
{
	optional int32 EffectType = 1;
	optional game.common.PB_EffectParticle Particle = 2;
	optional game.common.PB_EffectPickItem PickItem = 3;
	optional game.common.PB_EffectSound Sound = 4;
	optional game.common.PB_EffectActorBody ActorBody = 5;
	optional game.common.PB_EffectDestroyBlock DestroyBlock = 6;
	optional game.common.PB_EffectPlayMusicGrid PlayMusicGrid = 7;
	optional game.common.PB_EffectStopMusicGrid StopMusicGrid = 8;
	optional game.common.PB_EffectStringActorBody StringActorBody = 9;
	optional game.common.PB_EffectCrackBlock CrackBlock = 10;
	optional int32 effectScale = 11;
	optional int32 effectClass = 12;
	optional game.common.PB_EffectTriggerSound TriggerSound = 13;
	optional game.common.PB_EffectVehicle EffectVehicle = 14;
	optional game.common.PB_EffectSoundNew SoundNew = 15;
	optional game.common.PB_EffectSoundID SoundID = 16;
	optional game.common.PB_EffectParticleID ParticleID = 17;	
}

message PB_PlayEffectHC_V2
{
	optional int32 EffectType = 1;
	optional game.common.PB_EffectParticle Particle = 2;
	optional game.common.PB_EffectPickItem PickItem = 3;
	optional game.common.PB_EffectSound_V2 Sound = 4;
	optional game.common.PB_EffectActorBody ActorBody = 5;
	optional game.common.PB_EffectDestroyBlock DestroyBlock = 6;
	optional game.common.PB_EffectPlayMusicGrid PlayMusicGrid = 7;
	optional game.common.PB_EffectStopMusicGrid StopMusicGrid = 8;
	optional game.common.PB_EffectStringActorBody StringActorBody = 9;
	optional game.common.PB_EffectCrackBlock CrackBlock = 10;
	optional int32 effectScale = 11;
	optional int32 effectClass = 12;
	optional game.common.PB_EffectTriggerSound TriggerSound = 13;
	optional game.common.PB_EffectVehicle EffectVehicle = 14;
	optional game.common.PB_EffectSoundNew SoundNew = 15;
	optional game.common.PB_EffectSoundID_V2 SoundID = 16;
	optional game.common.PB_EffectParticleID_V2 ParticleID = 17;	
	optional bool usePlayerViewRange = 18;
}

message PB_EffectScaleHC
{
	optional int32 EffectType = 1;
	optional float effectScale = 2;
	optional uint64 objID = 3;
	optional string effectName = 4;
	optional game.common.PB_Vector3 Pos = 5;
}

message PB_PlayerMountActorHC
{
	optional int32 PlayerUIN = 1;
	optional int32 RidePosIndex = 2;
	optional uint64 ActorID = 3;
	optional int32 force = 4;
	optional int32 InteractBlockId = 5;
}

message PB_PlayerSleepHC
{
	optional int32 Flags = 1;
	optional game.common.PB_Vector3 Pos = 2;
	optional int32 Uin = 3;
	optional game.common.PB_Vector3 targetPos = 4;
}

message PB_OpenWindowHC
{
	optional int32 id = 1;
	optional int32 x = 2;
	optional int32 y = 3;
	optional int32 z = 4;
}

message PB_LastPingHC
{
	repeated int32 MPLastPingOne = 1;
}

message PB_CGameStageHC
{
	optional int32 Stage = 1;
	optional int32 GameTime = 2;
}

message PB_PlayerPermitHC
{
	optional uint32 RoomFlags = 1;
	optional uint32 PlayerFlags = 2;
	optional uint32 SpamPreventionMinutes = 3;
	repeated int32 BanItems = 4;
	repeated uint32 MutePlayerlist = 5;
	repeated uint32 PlayerSpeakingWhitelist = 6;
}


message PB_YMVoiceHC
{
	optional int32 Uin = 1;
	optional int32 YMMemberID = 2;
	optional int32 SpeakerSwitch = 3;
	optional int32 MicSwitch = 4;
	optional int32 YMMemberRole = 5;
}

message PB_SkillCDHC
{
	optional int32 ItemID = 1;
	optional float CD = 2;
}

message PB_Horse_SkillCDHC
{
	optional uint64 ActorID = 1;
	optional int32 Index = 2;
	optional float CD = 3;
}

message PB_ActorMountActorHC
{
	optional uint64 ActorIDOwner = 1;
	optional uint64 ActorID = 2;
	optional int32 RidePosIndex = 3;
}

message PB_ActorReverseHC
{
	optional uint64 ActorID = 1;
	optional int32 Reverse = 2;
}

message PB_ActorBindHC
{
	optional uint64 ActorID = 1;
	optional uint64 ActorIDBind = 2;
	optional game.common.PB_Vector3 OffetBind = 3;
}

message PB_PlayWeaponEffectHC
{
	optional string EffectName = 1;
	optional int32 EffectID = 2;
	optional int32 EffectStatus = 3;
	optional int32 EffectScale = 4;
	optional uint64 ObjId = 5;
}

message PB_ScriptVarHC
{
	repeated float ScriptVars = 1;
}

message PB_GVChangeRoleHC
{
	optional int32 GVMemberRole = 1;
}

message PB_YMChangeRoleHC
{
	optional int32 YMMemberRole = 1;
}

message PB_SpecialItemUseHC
{
	optional sint32 ItemId = 1;
	optional int32 ItemNum = 2;
}

message PB_LeaveRoomInfoHC
{
	optional int32 Cause = 1;
	optional int32 KickerType = 2; 
}

message PB_InviteJoinRoomHC
{
	optional int32 Uin = 1;
	optional string RoomState = 2;
	optional string PassWorld = 3;
}

message PB_SetSpectatorModeHC
{
	optional int32 Uin = 1;
	optional int32 SpectatorMode = 2;
}

message PB_SetSpectatorTypeHC
{
	optional int32 Uin = 1;
	optional int32 SpectatorType = 2;
}

message PB_SetSpectatorPlayerHC
{
    optional int32 SpectatorUin = 1;
    optional int32 ToSpectatorUin = 2;
}

message PB_SetPlayerModelAniHC
{
    optional int32 SpectatorUin = 1;
	optional int32 ToSpectatorUin = 2;
	optional int32 ModelAnimalType = 3; 
    optional int32 ModelAnimalExt = 4;   
}

message PB_SendMyViewmodeToSpectatorHC
{
    optional int32 SpectatorUin = 1;
	optional int32 ToSpectatorUin = 2;
	optional int32 MyViewmode = 3;  
}

message PB_SetBobbingToSpectatorHC
{
    optional int32 SpectatorUin = 1;
	optional int32 ToSpectatorUin = 2;
	optional int32 Bobbing = 3;  
}

message PB_BallOperateHC
{
	optional int32 Type = 1;
	optional uint64 ActorID = 2;
	optional int32 ExtendData = 3;
	optional int32 Uin = 4;
}

message PB_BasketBallOperateHC
{
	optional int32 Type = 1;
	optional uint64 ActorID = 2;
	optional bool	IsSelectedTarget = 3;
	optional int32  FallResult = 4;
	optional int32  ExtendData = 5;
	optional int32  Uin = 6;
}

message PB_ResetRoundHC
{
}

message PB_RocketAttribChangeHC
{
	optional uint64 ObjID = 1;
	optional int32 State = 2;
	optional int32 Fuel = 3;
}

message PB_ActorBodyTextureHC
{
	optional uint64 ObjID = 1;
	optional string TexName = 2;
	optional string MeshName = 3;
}

message PB_AttractAttribChangeHC
{
	optional uint64 ObjID = 1;
	optional int32 State = 2;
	optional int32 blockID = 3;
	optional int32 blockExID = 4;
}

message PB_WorldTimesHC
{
	repeated int32 Times = 1;
}

message PB_StatisticHC
{
	optional int32 EventID = 1;
	optional int32 WorldType = 2;
	optional string FristName = 3;
	optional string Param1 = 4;
	optional string Param2 = 5;
	optional string Param3 = 6;
	optional string Param4 = 7;
	optional string Param5 = 8;
	optional string Param6 = 9;
	optional string Param7 = 10;
}

message PB_TotemPointHC
{
	required int32 Op = 1;
	optional game.common.PB_Vector3 Point = 2;
	optional int32 MapID = 3;
}

message PB_HorseFlyStateHC
{
	optional float m_fEnergy = 1;
	optional int32 m_bTired = 2;
}

message PB_OpenDialogueHC
{
	optional uint64 ObjID = 1;
	repeated game.common.PB_IntertactData InteractData = 2;
	optional int32 ItemID = 3;
	optional int32 PlotType = 4;
	optional game.common.PB_Vector3 Openpos = 5;
}

message PB_CloseDialogueHC
{
}

message PB_UpdateTaskHC
{
	optional int32 Type = 1;
	optional int32 ID = 2;
	optional int32 Num = 3;
}

message PB_SyncTaskEnterWorldHC
{
	repeated game.common.PB_TaskInfoData TaskInfoData = 1;
}

message PB_CompleteTaskHC
{
	optional int32 TaskID = 1;
}

message PB_PlayerAddAvartarHC
{
	optional int32 Uin = 1;
	optional int32 avatarmodel = 2;
	optional int32 index = 3;
}

message PB_PlayerChangeModelHC
{
	optional int32 Uin = 1;
	optional int32 playerindex = 2;
	optional string customskins = 3;
	optional int32 Reason = 4;
}

message PB_PlayerAvartarColorHC
{
	optional int32 Uin = 1;
	optional float r = 2;
	optional float g = 3;
	optional float b = 4;
	optional float partID = 5;
	optional float modelID = 6;
	optional float block = 7;
}

message PB_PlayActHC
{
    optional int32 Uin = 1;
	optional int32 ActID = 2;
	optional int32 ActIDTrigger = 3;
}

message PB_CreateBlueprintHC
{
	optional game.common.PB_Vector3 Point = 1;
	optional string sheetname = 2;
	optional string authorname = 3;
}

message PB_MeasureDistanceHC
{
	optional int32 Uin = 1;
	optional int32 MapID = 2;
	optional game.common.PB_Vector3 blockpos = 3;
	repeated game.common.PB_Vector3 findpos = 4;
}

message PB_BluePrintPreBlockHC
{
	optional game.common.PB_Vector3 BlockPos = 1;
	optional int32 MapID = 2;
	repeated game.common.PB_PreBlockData PreBlocks = 3;
}

message PB_GravityOperateHC
{
	optional int32 Type = 1;
	optional uint64 ActorID = 2;
	optional int32 ExtendData = 3;
	optional int32 Uin = 4;
}

message PB_PlayerBodyColorHC
{
	optional uint32 DestColor = 1;
	optional uint32 CurColor = 2;
	optional int32 Uin = 3;
}

message PB_CustomModelHC
{
    optional int32 UnzipLen = 1;
    optional int32 BlobLen = 2;
    optional string BlobDetail = 3;
	optional string FileName = 4;
	optional int32 ItemID = 5;
	optional string ModelName = 6;
	optional string ModelDesc = 7;
	optional string ClassName = 8;
	optional int32 Type = 9;
	optional game.common.PB_Vector3 Box = 10;
	optional int32 folderIndex = 11;
	optional bool IsDownload = 12;
	optional int32 AuthUin  =13;
	optional int32 BlockIdVersion  =14;
}

message PB_CustomModelPrepareHC
{
	optional  string FileName = 1;
	optional  int32 Index = 2;
}

message PB_CustomItemIDsHC
{
	repeated int32 CustomItemIDs = 1; 
	repeated string CustomModelFileNames = 2; 
	repeated string CustomModelClassNames = 3; 
	repeated int32 CustomTypes = 4;
	repeated int32 InvolvedIds = 5;
	repeated int32 CustomModelFolderIndexs = 6;
}

message PB_PlayerSpawnPointHC
{
	optional int32 x = 1;
	optional int32 y = 2;
	optional int32 z = 3;
	optional int32 Uin = 4;
	optional int32 mapid = 5;
}

message PB_CustomModelClassHC
{
	repeated game.common.PB_CustomModelClassData Data = 1; 
}

message PB_TransferOneRecordHC
{
	optional int32 DesID = 1;
    optional int32 TeamColor = 2;
	optional int32 PassItemID = 3;
    optional int32 PassItemNum = 4;
	optional int32 ForbidItemID = 5;
    optional bool  IsExpendable = 6;
}

message PB_TransferRecordHC
{
	optional int32 SrcID = 1;
	optional bool  IsEdit = 2;
	optional PB_TransferOneRecordHC OneRecord = 3;
}

message PB_TransferNameTipHC
{
	optional int32 SrcID = 1;
	optional string TransferName = 2;
	optional string TransferTip = 3;
	optional bool ShowName = 4;
	optional int32 Status = 5;
}

message PB_TransferAddDelHC
{
	optional bool AddDel = 1;
	optional int32 MapID = 2;
	optional game.common.PB_Vector3 postion = 3;
	optional int32 Status = 4;
	optional int32 TransferID = 5;
	optional uint64 vehicleObj = 6;
	optional game.common.PB_Vector3 postion_v = 7;
}
message PB_TransferDataHC
{
	optional int32 ID = 1;
	optional string TransferName = 2;
	optional string TransferTip = 3;
	optional bool ShowName = 4;
	optional int32 MapID = 5;
	optional game.common.PB_Vector3 postion = 6;
	optional int32 Status = 7;
	repeated PB_TransferOneRecordHC OneRecord = 8;
	optional uint64 vehicleObj = 9;
	optional game.common.PB_Vector3 postion_v = 10;
}

message PB_TransferTargetHC
{
	optional int32 Uin = 1;
	optional int32 DesID = 2;
	optional int32 SrcID = 3;
}
message PB_OpenUIHC
{
	optional int32 Uin = 1;
	optional int32 Type = 2;
	optional int32 ID = 3;
}

message PB_RespNpcShopInfoHC
{
	optional game.common.PB_NpcShopData NpcShopInfo = 1;
}

message PB_NotifyNpcShopBuySkuHC
{
	optional int32 Ret = 1;
	optional int32 ShopID = 2;
	optional int32 SkuID = 3;
	optional int32 LeftNum = 4;
	optional int32 EndTime = 5;
	optional int32 Uin = 6;
	optional int32 BuyCount = 7;
}

// 同步玩家位置到客户端
message PB_SyncPlayerPositionHC
{
	required game.common.PB_Vector3 Position = 1;
	optional game.common.PB_Vector3f Motion = 2;
}

message PB_VehiclePosDesc
{
	optional game.common.PB_Vector3 Position = 1;
	optional game.common.PB_Quaternion RotateQuat = 2;
}

message PB_VehicleSTrustersPowerLevel
{
	required game.common.PB_Vector3 Position = 1;
	required uint32 PowerLevel = 2;
	optional int64 CurPower = 3;
}

message PB_VehicleMoveHC
{
	optional uint64 ObjID = 1;
	repeated PB_VehiclePosDesc ChassisPos = 2;
	repeated PB_VehiclePosDesc WheelPos = 3;
	repeated PB_VehicleSTrustersPowerLevel STrustersPower = 4;
}

message PB_OpenEditActorModelHC
{
	optional game.common.PB_Vector3 ContainerPos = 1;
	optional int32 MapID = 2;
}

message PB_CloseEditActorModelHC
{
	repeated game.common.PB_ActorOneBoneModelData BoneModels = 1;
	optional int32 MapID = 2;
	optional game.common.PB_Vector3 ContainerPos = 3;
	optional int32 ModelType = 4;
	optional string ModelMark = 5;
	optional string ModelName = 6;
	optional bool  SkinDisplay = 7;
}

message PB_OneCustomActorModelDataHC
{
	repeated game.common.PB_ActorOneBoneModelData BoneModels = 1;
	optional string ModelMark = 2;
	optional int32 Type = 3;
	optional string ModelName = 4;
	optional bool  SkinDisplay = 5;
	optional int32 AuthUin = 6;
}

message PB_CustomActorModelDataHC
{
	repeated PB_OneCustomActorModelDataHC ModelDatas = 1;
}

message PB_VehiclePreBlockHC
{
	optional game.common.PB_Vector3 BlockPos = 1;
	optional int32 MapID = 2;
	optional string AttrInfo = 3;
	repeated game.common.PB_PreBlockData PreBlocks = 4;
}

message PB_VehicleItemIdHC
{
	repeated int32 ItemID = 1;
}

message PB_VehicleAttribChangeHC
{
    optional uint64 ObjID = 1;
    optional int32 Fuel = 2;
	optional int32 PartIndex = 3;
	optional int32 ActualSpeed = 4;
	optional int32 ShowSpeed = 5;
	optional float EngineRotationSpeed = 6;
	optional int32 EngineState = 7;
	repeated PB_VehicleSTrustersPowerLevel STrustersPower = 8;
}

message PB_WorkshopItemInfoHC
{
	optional int32	ItemID = 1;
    optional string ItemName = 2;
	optional string ItemDesc = 3;
	optional bool	IsStart = 4;
}

message PB_PlayerCameraRotateHC
{
	optional float Pitch = 1;
	optional float Yaw = 2;
}

message PB_PlayerChangeViewModeHC
{
	optional int32 ViewMode = 1;
	optional bool Lock = 2;
	optional bool ClientMode = 3;
}

message PB_PlayerCanMoveHC
{
	optional bool CanMove = 1;
}

message PB_PlayerCanControlHC
{
	optional bool CanControl = 1;
}

message PB_PlayerSetAttrHC
{
	optional int32 AttrType = 1;
	optional float Val = 2;
}

message PB_TriggerTimerDataHC
{
	optional int32 TimerID = 1;
	optional int32 Time = 2;
	optional int32 Type = 3;
	optional string Title = 4;
}

message PB_PlayerFreezingHC
{
	optional int32 freezingflag = 1;
}

message PB_WorkshopBuildHC
{
	optional bool Isbuild = 1;
	optional int32 Mapid = 2;
	optional game.common.PB_Vector3 ContainerPos = 3;
}

message PB_GameRuleHC
{
	optional int32 ruleid = 1;
	optional int32 optionid = 2;
	optional float value = 3;
}

message PB_PlayerScaleHC
{
	optional int32 Uin = 1;
	optional float Scale = 2;
	optional uint64 ObjID = 3;
}

message PB_PlayerNavigateHC
{
   optional game.common.PB_Vector3 TargetPos = 1;
   optional float Speed = 2;
   optional bool CanControl = 3;
   optional bool showTip = 4;
}

message PB_PlayerCommonSetHC
{
	optional int32 Uin = 1;
	optional float Value = 2;
}

message PB_OpenEditFullyCustomModelHC
{
	optional game.common.PB_Vector3 ContainerPos = 1;
	optional int32 MapID = 2;
	optional bool Edited = 3;
	optional string url = 4;
	optional int32 version = 5;
	optional int32 result = 6;
}

message PB_CloseFullyCustomModelUIHC
{
	optional int32 Result = 1;
	optional int32 MapID = 2;
	optional game.common.PB_Vector3 ContainerPos = 3;
	optional string Skey = 4;
}

message PB_RespDownLoadResUrlHC
{
	optional int32 Type = 1;
	optional string ExternData = 2;
	optional string DownloadUrl = 3;
}

message PB_PreOpenEditFCMUIHC
{
	optional int32 State = 1;
}

message PB_VehicleAssembleLineHC
{
	optional uint64 ObjID = 1;
	optional int32 from = 2;
	optional int32 to = 3;
}

message PB_VehicleAssembleLineOperateHC
{
	optional uint64 ObjID = 1;
	optional bool State = 2;
	optional game.common.PB_Vector3 BlockPos = 3;
}

message PB_VehicleBindActorHC
{
	optional uint64 vehicleObjID = 1;
	optional uint64 bindObjID = 2;
	optional game.common.PB_Vector3 BlockPos = 3;
}

message PB_TriggerMusicHC
{
   	optional int32 objid = 1;
   	optional string Name = 2;
	optional float Volume = 3;
	optional float Pitch = 4;
	optional bool IsLoop = 5;
	optional int32 PlayState = 6;
}

message PB_CSPlayerPermitHC
{
    optional int32  TargetUin = 1;
	optional uint32 Flags = 2;
	repeated int32  BanItems = 3;
	optional int32  DataType = 4;
    repeated game.common.PB_CSPermitData Permits = 5;
}

message PB_CSAuthorityHC
{
    optional int32 DataType = 1;
    repeated game.common.PB_CSAuthorityData Authorities = 2;
}

message PB_SSTaskHC
{
    optional int32 TargetUin = 1;
	optional int32 TaskId = 2;
	optional string ParamJson = 3;
}

message PB_CloudServerChangeHC
{
    optional int32 type = 1;
}

message PB_TriggerOpenStoreHC
{
	optional uint64 ObjID = 1;
}

message PB_UsePackingFcmItemHC
{
	optional int32 result = 1;
}

message PB_CreatePackingCMHC
{
	optional int32 result = 1;
}

message PB_OnePackingCMDataHC
{
	optional game.common.PB_Vector3 PackingPos = 1;
	optional game.common.PB_Quaternion Quat = 2;
	optional string Name = 3;
	optional string Model = 4;
	optional int32 Dir = 5;
}

message PB_OnePackingFCMDataHC
{
	optional string Name = 1;
	optional string Desc = 2;
	optional string SKey = 3;
	repeated PB_OnePackingCMDataHC PackingCMs = 4;
	optional int32 Dir = 5;
	optional game.common.PB_Vector3 MinPos = 6;
	optional game.common.PB_Vector3 MaxPos = 7;
	optional int32  AuthUin = 8;
}

message PB_PackingFCMDataHC
{
	repeated PB_OnePackingFCMDataHC PackingFCMs = 1;
}

message PB_CloudRoomStatusTimeHC
{
	optional int32 Status = 1;
	optional int32 Time = 2;
}

message PB_SensorContainerDataHC
{
	optional game.common.PB_Vector3 BlockPos = 1;
	optional int32 SensorValue = 2;
	optional bool  IsBreverse = 3;
	optional uint64 ObjID = 4;
}

message PB_DoorDataHC
{
	optional game.common.PB_Vector3 BlockPos = 1;
}

message PB_PlayerCarryActorHC
{
	optional uint64 ActorID = 1;
	optional int32 PlayerUIN = 2;
}

message PB_VillagerBodyChangeHC
{
	optional uint64 ObjId = 1;
	optional int32 ChangeType = 2;
	optional int64 ChangeValue = 3;
	optional string OtherValue = 4;
}

message PB_PlayerTameActorHC
{
	optional uint64 ActorID = 1;
	optional int32 PlayerUIN = 2;
}

message PB_VillagerCloth
{
	optional uint64 ActorID = 1;
	optional bool bshow= 2;
	optional string modlename= 3;
}

message PB_ActorHeadDisplayIconHC
{
	optional uint64 ActorID = 1;
	optional int32 ItemID = 2;
	optional int32 Tick = 3;
}

message PB_ActorPlayAnimByIdHC
{
	optional uint64 ActorID = 1;
	optional int32 AnimID = 2;
	optional int32 Loopmode = 3;
	optional int32 Layer = 4;
	optional float crossfade = 5;
}

message PB_VillageTotemTipHC
{
	optional int32 villagerNum = 1;
	optional game.common.PB_Vector3 blockPos = 2;
}

message PB_VillageTotemActiveHC
{
	optional int32 uin = 1;
	optional game.common.PB_Vector3 blockPos = 2;
}

message PB_SaveTombStoneHC
{
	optional game.common.PB_Vector3 Point = 1;
	optional string title = 2;
}

message PB_PlayerLevelModeHC
{
	optional int32 SumExp = 1;
	optional int32 CurLevel = 2;
	optional int32 CurExp = 3;
}

message PB_ActionAttrStateHC
{
	optional uint32 Attr = 1;
}

message PB_Edu_RolesInfoHC
{
	repeated game.common.PB_Edu_RoleInfo rolesInfo = 1;
}

message PB_ImportModelHC
{
	repeated game.common.PB_ImportModelData models = 1;
}

message PB_LightningHC
{
	required game.common.PB_Vector3 targetpos = 1;
}

message PB_InteractMobPackHC
{
	required string uiName = 1;
	required string param = 2;
	required int64 mobID = 3;
}

message PB_UpdateMobBackpackHC
{
	required int64 mobID = 1;
	repeated game.common.PB_ItemData ItemInfo = 2;
}

message PB_PlayerTransformSkinHC
{
	optional int32 Uin = 1;
	optional int32 playerindex = 2;
	optional string customskins = 3;
	optional int32 Reason = 4;
	optional uint64 MainPlayerId = 5;
}

message PB_PlayerSaveArchHC
{
    optional int32 Uin = 1;
    optional int32 playTime = 2;
    optional bool showTip = 3;
    optional string userdata = 4;
}
message PB_PrayTreeStageHC
{
	optional int32 stage = 1;
}
message PB_PrayTreeReqHC
{
	optional int32 Uin = 1;
	optional int32 stage = 2;
	optional string treeId = 3;
}
message PB_PrayTreeInfoHC
{
	optional int32 stage = 1;
	optional string treeId = 2;
	optional int32 hostuin = 3;
}
message PB_PrayTreeTimeUpdateHC
{
	optional int32 stage = 1;
	optional string treeTime = 2;
}
message PB_HomeNpcOpenHC
{
    optional int32 Uin = 1;
    optional int32 npcType = 2;
    optional int32 npcId = 3;
    optional bool  activeNpcDialogue = 4;
}
message PB_PrayErrorHC
{
    optional int32 Uin = 1;
    optional int32 errorType = 2;
}

message PB_Open_HomeCloset_HC
{
	optional int32 Uin = 1;
	optional string skinIDs = 2;
	optional string skinPartIDs = 3;
}

message PB_OpenDevGoodsBuyDialogHC
{
	optional int32 itemid = 1;
	optional string desc = 2;
}

message PB_PlayGraphicsHC
{
	optional int32 Operateid = 1; // ���� ɾ�� �ƶ�
	repeated game.common.PB_GraphicsAttr graphicsinfo = 2; //ͼ�Ļ�����Ϣ
}

message PB_GodTempleCreateHC
{
	optional bool onoff = 1;
	optional int32 worldid = 2;
}
message PB_SFActivity_HC
{
	optional int32 type = 1;
	optional int32 taskId = 2;
	optional int32 value = 3;
}

message PB_ShapeAdditionAnimHC
{
	required int32 Uin = 1;
	required bool  status = 2;
}

message PB_OneHomelandRanchAnimal
{
	optional int32	SeedID = 1;
	optional int32	GrowthTime = 2;
	optional int32	FeedTime = 3;
	optional int32	Stage = 4;
	optional string	Serverid = 5;
	optional int32	SowTime = 6;
	optional int32	FooderLevel = 7;
	optional int32	FooderInterval = 8;
	optional int32	FooderID = 9;
	optional int32	FooderTime = 10;
	optional int32	FooderTimeStatr = 11;
	optional string	FooderDesc = 12;
}

message PB_HomelandRanchInfoHC
{
	required bool full = 1;
	repeated PB_OneHomelandRanchAnimal Animals = 2;
}

message PB_UseItemByHomelandHC
{
	required int32 ItemID = 1;
	required int32 ItemNum = 2;
}

message PB_CustomBaseModelHC
{
	optional int32 Uin = 1;
	optional int32 TeamId = 2;
	optional string ModelData = 3;
}

message PB_ChangeActorModelHC
{
	repeated uint64 ObjID = 1;
	repeated string modelID = 2;
}

message PB_NotifiyActorModelHC
{
	repeated string modelID = 1;
	repeated string modelData = 2;
}


message PB_VoiceInformHC
{
	required int32 Uin = 1;
	required uint32 type = 2;
	optional string voiceId = 3;
	required uint32 reportUin = 4;
	optional string node = 5;
	optional string dir = 6;
}

message PB_UpdatePotContainerHC
{
	optional int32 uin = 1;
	optional int32 x = 2; 
	optional int32 y = 3;
	optional int32 z = 4;
	optional bool isMaking = 5;
	optional int32 progress = 6;
	optional int32 craftID = 7;
}

message PB_StarStationDataHC
{
	required int32 starStationID = 1;
	required string starStationName = 2;	
	required int32 mapID = 3;
	required bool isConsoleActive = 4;
	required bool isSign = 5;
	required game.common.PB_Vector3 consolePos = 6;	
	repeated game.common.PB_StarStationCabinDef starStationCabinDef = 7;	
	repeated game.common.PB_UnfinishedStarStationTransferRecord unfinishedTransferRecord = 8;	
	optional int32 stationType = 9;
	optional int32 stationExtraData = 10;							
}

message PB_BlockExploitHC
{
    optional uint64 ObjID = 1;
    optional int32 status = 2;
    optional int32 face = 3;
    optional game.common.PB_Vector3 blockpos = 4;
	optional int32 picktype = 5;
}

message PB_PlayerTransferByStarStationHC
{
	required int32 uin = 1;
	required int32 destMapID = 2;
}

message PB_VacantBossStateHC
{
	optional uint64 objid = 1;
	optional int32 type =2;
	optional float fval0 = 3;
	optional float fval1 = 4;
	optional int32 ival0 = 5;
	optional int32 ival1 = 6;
	optional game.common.PB_Vector3 TargetPos = 7;
}

message PB_ActivateStarStationHC
{
	required int32 starStationID = 1;
	required string starStationName = 2;
	required int32 mapID = 3;
	required game.common.PB_Vector3 consolePos = 4;
	required bool result = 5;
	optional int32 playerUin = 6;
}

message PB_UpgradeStarStationCabinHC
{
	required int32 starStationID = 1;
	required game.common.PB_Vector3 cabinPos = 2;
	required int32 playerUin = 3;
	required bool result = 4;
}

message PB_UpdateStarStationSignInfoHC
{
	required int32 starStationID = 1;
	required bool isSign = 2;
}

message PB_PlayerRevivePointHC
{
	optional int32 uin = 1;
	optional int32 mapid = 2;
	optional game.common.PB_Vector3 revivepoint = 3;
	optional game.common.PB_Vector3 spawnpoint = 4;
}

message PB_StarStationTransferDeductFeeHC
{
	required int32 transferType = 1;
	required int32 result = 2;
}

message PB_PlayAltmanMusicHC
{
	required int32 blockID = 1;
}

message PB_AchievementSyncHC
{
	repeated game.common.PB_AchievementInfo achievementList = 1;
}

message PB_NotifyUpdateToolModelTextureHC
{
    optional int32 Uin = 1;
    optional int32 textureIndex = 2;
}

message PB_AddExpResultHC
{
    required int32 op = 1;
    required int32 result = 2;
    optional int32 Uin = 3;
}

message PB_BPEventHC
{
    required string sType = 1;
    required int32 val = 2;
	optional string extenddata = 3;
}

message PB_HorseFlagHC
{
    required uint32 flag  = 1;
	required uint64 objid = 2;
}

message PB_HomeLandRanchFooderStateHC
{
	optional uint64 objid = 1;
	optional int32	enterstate = 2;
	optional string serverid = 3;
}

message PB_ActorStopAnimHC
{
	required int32 anim = 1;
	optional uint64 actorid = 2;
	optional bool isSeq = 3 [default = false];
}

message PB_HomeLandMenuBuyHC
{
	
}

message PB_HomeLandSpecialFurnitureBuyHC
{
	
}

message PB_HomeLandShopCellHC
{
	optional int32  uin = 1;
	optional int32	itemid = 2;
	optional int32	num = 3;
}

message PB_Custom_Msg
{
	optional string msgname = 1;
	optional string content = 2;
	optional int32	ziplen = 3;
	optional int32	unziplen = 4;
}
message PB_ExchangeItemsToBackPackResultHC
{
    required int32 result = 1;
	required int32 opertype = 2;
}

message PB_InteractLanternBirdHC
{
	optional int32  uin = 1;
	optional int32	guessid = 2;
}

message PB_ChangeQQMusicPlayerHC
{
	required int32  type = 1;
	optional int32	musicId = 2;
	optional bool	state = 3;
	optional int32	volume = 4;
	optional int32  uin = 5;
	repeated int32	musicList = 6;
	repeated int32	uinList = 7;
	optional int32	startPos = 8;
	optional int32	duration = 9;
	optional int32	playMode = 10;
	optional bool	isPaused = 11;
	optional bool	isOpen = 12;
	repeated string nameList = 13;
}

message PB_SetTiangouHC
{
	optional int32 strID = 1;
	optional int32 moonPhase = 2;
	optional string moonRes = 3;
	optional bool defaultControlMoon = 4;
	optional bool tiangouStart = 5;
}

message PB_PlayerOpenUIHC
{
	required string uiName = 1;
	required string uiParam = 2;
}

message PB_RideInvisibleHC
{
	required bool invisible = 1;
	repeated uint64 ObjIDList = 3;
}

// 装扮互动协议
message PB_PlaySkinActHC
{
	optional int32 ActID = 1;
	optional int32 ActIDTrigger = 2;
	optional int32 InviteUin = 3;  //邀请者UIN
	optional int32 AcceptUin = 4;  //接受者UIN
}

message PB_ActorStopSkinActHC
{
	optional int32 ActorID1 = 1;
	optional int32 ActorID2 = 2;
}

message PB_ChangeQQMusicClubHC
{
	required int32 type = 1;
	optional int32 pointIndex = 2;
	optional int32 actinIndex = 3;
	optional int32 actionId = 4;
	optional int32 time = 5;
	repeated int32 beginPos = 6;
	repeated int32 endPos = 7;
	optional int32 uin = 8;
	optional bool enterArea = 9;
	optional bool playingMusic= 10;
	optional string fractions = 11;
	optional string uins = 12;
	optional string dataset = 13;
}

message PB_MiniClubMusicPlayerHC
{
	required int32  type = 1;
	optional int32	musicId = 2;
	optional bool	state = 3;
	optional int32	volume = 4;
	optional int32  uin = 5;
	repeated int32	musicList = 6;
	repeated int32	uinList = 7;
	optional int32	startPos = 8;
	optional int32	duration = 9;
	optional int32	playMode = 10;
	optional bool	isPaused = 11;
	optional bool	isOpen = 12;
	repeated string nameList = 13;
}

message PaintedInfo
{
	required string key = 1;
	required int32 paintid = 2;
	required game.common.PB_Vector3 pos = 3;
	required string texname = 4;
	required int32 showtime = 5;
	required int32 dir = 6;
	required int32 mapid = 7;
}

message PB_AddPaintedInfoHC
{
	repeated PaintedInfo paintedinfos = 1;
}

message BulletholeInfo
{
	optional game.common.PB_Vector3f pos = 1;//弹孔位置
	optional game.common.PB_Vector3 blockPos = 2;//弹孔所在方块
	optional string texname = 3;//弹孔贴图
	optional int32 showtime = 4;//弹孔显示时间s
	optional int32 dir = 5;//弹孔在方块的哪个面
	optional int32 secondDir = 6;//Y方向的弹孔，射击者的朝向
	optional int32 mapid = 7;
	required bool isBlock = 8;//是否方块
	optional uint64 objId = 9;//actorid
	optional game.common.PB_Vector3f normal = 10;//法线
}

message BulletEffect
{
	optional int32 particleId = 1;
	optional float duration = 2;
	optional game.common.PB_Vector3f start = 3;
	optional game.common.PB_Vector3f dir = 4;
	optional int32 worldid = 5;
	optional float size = 6;
	optional float range = 7;
}

message BulletHit
{
	optional int32 particleId = 1;
	optional float size = 2;
	optional game.common.PB_Vector3 point = 3;
	optional float yaw = 4;
	optional float pitch = 5;
	optional int32 worldid = 6;
}

message PB_AddBulletholeInfoHC
{
	repeated BulletholeInfo infos = 1;
	repeated BulletEffect effects = 2;
	repeated BulletHit hits = 3;
}

message PB_RemovePaintedInfoHC
{
	repeated string key = 1;
}

message PB_TopBrandHC
{
	optional int32 targetUin = 1;
	optional string brandName = 2;
}

message PB_WeaponPointHC
{
	optional int32 targetUin = 1;
	optional int32 pointType = 2;
	optional int32 itemid = 3;
}

message PB_AddLightningChainHC
{
	required uint64 targetWID = 1;
	required int32 chainID = 2;
	required uint64 linkSrcWID = 3;
	optional int32 chainType = 4;
	optional game.common.PB_Vector3 startPos = 5;
}

message PB_STARTFISHINGHC
{
	required uint64 playerID = 1;
	required uint64 hookID = 2;
	required game.common.PB_Vector3 targetpos = 3;
}

message PB_ENDFISHINGHC
{
	required uint64 playerID = 1;
	required int32 resultID = 2;
}

message PB_QUITFISHINGHC
{
	required uint64 playerID = 1;
}

message PB_CHANGEFISHINGSTAGEHC
{
	required uint64 playerID = 1;
	required int32 state = 2;
}

message PB_FishingBeginFlashHC
{
	required int32 playerUin = 1;
}

message PB_PlayerVehicleMoveInputHC
{
	required uint64 vehicleID = 1;
	optional float Accel = 2;
	optional float Brake = 3;
	optional float Left  = 4;
	optional float Right = 5;
}

message PB_BindItemToActorHC
{
	required uint64 playerID = 1;
	required bool isBind = 2;
	required int32 bindId = 3;
	optional int32 itemId = 4;
	optional int32 anchorId = 5;
}

message PB_ChangeShowEquipHC
{
	required int32 playerUin = 1;
	required int32 itemId = 2;
}

message PB_GameModeChangeHC
{
    optional int32 oldGameMode = 1;
    optional int32 newGameMode = 2;
}

message PB_PushSnowBallOperateHC
{
	optional int32 Type = 1;
	optional uint64 ActorID = 2;
	optional uint64 ExtendData = 3;
	required game.common.PB_Vector3 TargetPos = 4;
	optional int32 Uin = 5;
}

message PB_PushSnowBallSizeChangeHC
{
	optional uint64 ActorID = 1;
	optional int32 Size = 2;
}

message PB_ActorPlayAnimHC
{
	required uint64 objId = 1;
	required int32 seqId = 2;
	optional int32 loop = 3;
	optional float speed = 4;
	optional int32 layer = 5;
	optional int32 preSeqId = 6;
	optional int32 preLayer = 7;
}

message PB_MoveSyncHC
{
	required uint32 id = 1;
	optional bool accept = 2;
	optional game.common.PB_Vector3 pos = 3;
	optional game.common.PB_Vector3f motion = 4;
}

message PB_ResetRoleFlagsHC
{
	required uint32 flags = 1;
	optional uint32 types = 2;
}

message PB_PlayerCanFireHC
{
	required bool fire = 1;
}

message PB_PlayerTransferHC
{
	optional int32 uin = 1;
	optional game.common.PB_Vector3 targetpos = 2;
	optional int32 destMapID = 3;
}

message PB_ModBlockColorAnimHC
{
	required int32 mapid = 1;
	required game.common.PB_Vector3 targetpos = 2;
	required float interval = 3;
	required int32 color = 4;
	optional int32 changetimes = 5;
}

// 服务器返回：队列更新
message PB_CraftingQueueUpdateHC 
{
    repeated game.common.PB_CraftingQueueTask tasks = 1; // 更新后的合成任务队列
}

// 服务器返回：任务进度更新
message PB_CraftingProgressUpdateHC {
    required int32 task_index = 1;     // 任务索引
    required int32 remaining_ticks = 2; // 剩余滴答数
}

message PB_PlayerDownedStateChangeHC {
    required int64 playerid = 1;         // 玩家ID
    required int32 oldstatetype = 2;     // 原状态类型 (0=正常, 1=倒地, 2=被救援)
    required int32 newstatetype = 3;     // 新状态类型 (0=正常, 1=倒地, 2=被救援)
    required float downedhealth = 4;     // 当前倒地血量
    required float maxdownedhealth = 5;  // 最大倒地血量
    optional int64 reviveractorid = 6;   // 救援者ID (仅当进入被救援状态时有效)
    optional float currenthp = 7;        // 玩家当前血量
	optional float revive_success_chance = 8;
	optional float total_downed_ticks = 9;
	optional float downed_health_decay_per_tick = 10;
}

// 倒地血量更新消息 (专门用于血量变化，不涉及状态变化)
message PB_PlayerDownedHealthUpdateHC {
    required int64 playerid = 1;         // 玩家ID
    required int32 currentstate = 2;     // 当前状态类型 (1=倒地, 2=被救援)
    required float downedhealth = 3;     // 当前倒地血量
    required float maxdownedhealth = 4;  // 最大倒地血量
    optional float currenthp = 5;        // 玩家当前血量
    optional int32 reviveprogress = 6;   // 救援进度 (0-100, 仅被救援状态有效)
    optional int64 reviveractorid = 7;   // 救援者ID (仅被救援状态有效)
}

message PB_PlayerCustomHC {
	required int32 type = 1;
	required string data = 2;
}

message PB_DecompositionHC {
	required int32 type = 1;

	required int32 x = 2;
	required int32 y = 3;
	required int32 z = 4;

	required int32 data = 5;
}

message PB_SingleBuildItemData{
	required int32 x = 1;
	required int32 z = 2;
	required int32 crangex = 3;
	required int32 crangez = 4;
	required int32 brangex = 5;
	required int32 brangez = 6;
	required bool issafezone = 7;
}

message PB_SingleBuildData {
	required string buildName = 1;
	repeated PB_SingleBuildItemData buildItem = 2;
}

message PB_AllSingleBuildDataHC {
	repeated PB_SingleBuildData BuildDatas = 1;
}

message PB_ResearchHC {
	optional int32 x = 1;
	optional int32 y = 2;
	optional int32 z = 3;
	optional int32 data = 4;
}

message PB_TechBlueprintHC {
	required int32 code = 1;
	optional int32 level = 2;
	optional int32 nodeid = 3;
}

message PB_SocWorkbenchData {
	required int32 x = 1;
	required int32 y = 2;
	required int32 z = 3;
	required int32 itemid = 4;
}

message PB_SocWorkbenchHC {
	required int32 type = 1;
	repeated PB_SocWorkbenchData SocWorkbenchDatas = 2;
}

message PB_ItemSocTeam {
	optional int32 uin = 1;
	optional float rotyaw = 2;
	optional game.common.PB_Vector3 pos = 3;
	optional string name = 4;
}
message PB_SocTeamPositions {
	repeated PB_ItemSocTeam teams = 1;
}

message TagDataItem {
	required int32 x = 1;
	required int32 z = 2;
	required int32 data = 3;
	optional string tagname = 4;
}
message PB_SocTeamTagDataHC {
	repeated TagDataItem DataItems = 1;
}