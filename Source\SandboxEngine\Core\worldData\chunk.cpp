
#include "chunk.h"
//#include "Profiny.h"
#include "section.h"
#include "world.h"
#include "IClientActor.h"
#include "DefManagerProxy.h"
#include "block_tickmgr.h"
#include "blocks/BlockMaterialMgr.h"
#include "ActorManagerInterface.h"
#include "blocks/container_world.h"
#include "EcosysManager.h"
#include "WorldManager.h"
#include "ChunkGenerator.h"
#include "File/FileManager.h"
#include "zlib.h"
#include <algorithm>
#include "OgreTimer.h"
#include "block_tickmgr.h"
#include "IClientItem.h"
#include "BlockScene.h"
#include "OgrePhysXManager.h"
#include "blocks/special_blockid.h"
#include "OgrePrerequisites.h"
#include "RenderSection.h"
#include "ObserverEventManager.h"
#include "WorldStringManagerProxy.h"
#include "SandboxEventDispatcherManager.h"

#include "SandboxObject.h"
#include "SandboxCoreDriver.h"
#include "SandboxEventDispatcherManager.h"
#include "worldMesh/MiniCraftRenderer.h"
#include "ClientInfoProxy.h"
#include "SandboxMacros.h"
#include "PlayManagerInterface.h"
#include "WorldStringManagerProxy.h"
#include "WorldEventMgrProxy.h"

#include "worldMesh/ChunkRenderer.h"
#include "WorldScene.h"
#include "Optick/optick.h"
#include "IGameMode.h"
#include "ClientActorDef.h"

using namespace Rainbow;

const short INVALID_HEIGHT = -5678;

//extern FILE* litlog;
//#ifndef IWORLD_SERVER_BUILD 
#define DIRTY_LIGHT
//#endif
using namespace MNSandbox;
void Chunk::_init(World *pworld, int section_x, int section_z)
{
	m_pWorld = pworld;
	m_EmptyBlock = pworld->m_EmptyBlock;
	m_MeshFlags = 0;
	m_GenMeshFlags = 0;
	m_pMesh = NULL;

	m_Populated = false;
	m_DirtyLightsGenerated = false;

	m_Origin = WCoord(section_x*CHUNK_BLOCK_X, 0, section_z*CHUNK_BLOCK_Z);
	m_RandGen.setSeed64(pworld->getChunkSeed(section_x, section_z));
	m_LastSaveTick = m_pWorld->m_CurWorldTick;
	m_LastActorSaveTick = m_pWorld->m_CurWorldTick;
	m_Dirty = false;
	m_ActorDirty = false;
	m_NeedGenImmediate = false;
	m_SaveIntegralCounter = 0;
	m_MapID = m_pWorld->getCurMapID();
	m_ActorNum = 0;
	m_isGapLightingUpdated = false;
	m_RelightCheckCount = 4096;
	m_MinTopHeight = 0;

	m_DungeonPos = WCoord(0,-1,0);

	memset(m_TopHeight, 0, sizeof(m_TopHeight));

	if (Rainbow::GetMiniCraftRenderer().GetClientSetting().m_UseAutoBatch) 
	{
		m_ChunkRenderer = ENG_NEW(Rainbow::ChunkRenderer)(this);
	}

	for (int dx = -1; dx <= 1; dx++)
	{
		for (int dz = -1; dz <= 1; dz++)
			setNeighbourChunk(dx, dz, NULL);
	}

	for (int i=0; i<CHUNK_SECTION_DIM; i++)
	{
		m_Sections[i] = ENG_NEW_LABEL(Section, kMemTerrain)(this, i);
		m_Sections[i]->initialize();
	}

	for (int i=0; i<CHUNK_BLOCK_X*CHUNK_BLOCK_Z; i++)
	{
		m_PrecipitationHeight[i] = INVALID_HEIGHT;
	}


	m_bNeedGenMesh = false;
	m_IsSquareBlock = true;
	m_hasNoSaveData = false;
	memset(m_updateSkylightColumns, 0, sizeof(m_updateSkylightColumns));

	m_SharedChunkData = ENG_NEW(SharedChunkData)();
}

Chunk::Chunk(World *pworld, int ox, int oz, BLOCK_DATA_TYPE*chunkdata)
	: m_ChunkRenderer(nullptr)
{
	//MemStat::singleton.trackObj(this);

	//m_sceneChunk = SANDBOX_NEW(MNSandbox::SceneChunk, ox, oz, static_cast<MNSandbox::Scene*>(pworld->GetWorldScene()));

	_init(pworld, ox, oz);

	if(chunkdata)
	{
		//int waterNum = 0;
		//int waterHeight = 0;

		for(int y=0; y<MAX_TERRGEN_Y; y++)
		{
			int curSectionIndex = y / SECTION_BLOCK_DIM;

			Section *psection = m_Sections[curSectionIndex];
			SharedSectionData& sectionData = psection->GetWritableSharedSectionData();
			int sy = y%SECTION_BLOCK_DIM;
			for(int z=0; z<CHUNK_BLOCK_Z; z++)
			{
				for(int x=0; x<CHUNK_BLOCK_X; x++)
				{
					BLOCK_DATA_TYPE block = chunkdata[xyz2Index(x,y,z)];
					if(block != m_EmptyBlock.getAll())
					{
						sectionData.setBlockByIndex(xyz2Index(x, sy, z), block);
						sectionData.nonEmptyBlocks()++;

						int resId = Block::toResID(block);
						BlockMaterial *pmtl = g_BlockMtlMgr.getMaterial(resId);
						if (resId == BLOCK_PLANTSPACE_RAINBOWGRASS) 
						{
							//generate material texture
							GetISandboxActorSubsystem()->GenerateRainbowGrassMaterial(pworld, psection, pmtl, this,x,sy,z);
						}

						if(pmtl && pmtl->GetToggleTickRandomly())
							psection->m_RandomTickBlocks++;
						if (pmtl && pmtl->needCheckVoidNight())
							psection->m_VoidRandomTickBlocks++;

						if (pmtl && pmtl->hasContainer())
						{
							WCoord blockPos = WCoord(x, y, z ) + m_Origin;
							WorldContainer* container = pmtl->createContainer(pworld, blockPos);
							if (container)
							{
								addContainer(container);
							}
						}
						if (pmtl && pmtl->hasTimer())
						{
							WCoord blockPos = WCoord(x, y, z) + m_Origin;
							pmtl->createTimer(pworld, blockPos);
						}
						psection->setMeshInvalid(true);
						psection->setPhyInvalid();
						psection->setNeedUpdateVisibility();
						if (y == MAX_TERRGEN_Y - 1 || chunkdata[xyz2Index(x,y+1,z)] == 0) // ???????????????????????��??????????????? chenzihang
						{
							psection->setLightDirty(x, sy, z, 0);
						}
						sectionData.initBlockTemperature(x, y, z, BlockMaterial::getTemperatureValue(resId));
					}
				}
			}
		}
		for (int i = 0; i < SECTION_BLOCK_DIM; i++)
		{
			SharedSectionData& sectionData = m_Sections[i]->GetWritableSharedSectionData();
			sectionData.updataSectionBlocks();
		}
	}

	m_CurSaveTick = 0;
}

Chunk::~Chunk()
{
	//m_sceneChunk = nullptr;
	//ENG_DELETE(m_sceneChunk);

	ENG_RELEASE(m_SharedChunkData);

	if (m_ChunkRenderer != nullptr) 
	{
#ifndef IWORLD_SERVER_BUILD
		m_ChunkRenderer->BeginDestroy();
#else
		m_ChunkRenderer->BeginDestroy(true);
		m_ChunkRenderer = nullptr;
#endif
		//m_ChunkRenderer = nullptr;
	}

	for (size_t i = 0; i < m_ChunkContainers.size(); i++)
	{
		if (m_pWorld && m_pWorld->getContainerMgr())
		{
			m_pWorld->getContainerMgr()->removeContainerByChunk(m_ChunkContainers[i]);
		}
	}

	m_pWorld = NULL;

	if(m_pMesh) DESTORY_GAMEOBJECT_BY_COMPOENT(m_pMesh);

	for(int i=0; i<CHUNK_SECTION_DIM; i++)
	{
		if (m_Sections[i])
		{
			m_Sections[i]->destroy();
		}
		ENG_DELETE_LABEL(m_Sections[i], kMemTerrain);
	}


	for(size_t i=0; i<m_ChunkContainers.size(); i++)
	{	
		SANDBOX_DELETE(m_ChunkContainers[i]);
	}
	
	for(size_t i=0; i<m_SearchBlocks.size(); i++)
	{
		ENG_DELETE(m_SearchBlocks[i]);
	}
}

size_t Chunk::getMemStat(MemStat& ms)
{
	//ms.begin();

	//ms.add(*this);

	//ms.addSize(m_ChunkContainers.capacity() * sizeof(m_ChunkContainers[0]));

	//ms.addSize(m_SuppendLightSrc.capacity() * sizeof(m_SuppendLightSrc[0]));

	//ms.addSize(m_suspendDirtyLights.size() * sizeof(MINIW::UInt32));

	//for (int i = 0; i < CHUNK_SECTION_DIM; ++i)
	//	ms.addSize(m_Sections[i]->getMemStat(ms));

	//if (m_pMesh && IS_KIND_OF(SectionMergeObject, m_pMesh))
	//{
	//	SectionMergeObject* smo = (SectionMergeObject*)m_pMesh;
	//	ms.addSize(smo->getMemStat(ms));
	//}

	//return ms.end();
	return 0;
}

size_t Chunk::printMemStat(FILE* f)
{
	//size_t size = getMemStat(MemStat::singleton);
	//double mb = size / 1024.0 / 1024.0;
	//fprintf(f, "%s,%lf,Chunk_%d_%d\n", "Chunk", mb, m_Origin.x, m_Origin.z);
	//return size;
	return 0;
}

bool Chunk::needSave(bool forcesave)
{
	if (m_pWorld == nullptr) return false;
	bool inUGCEdit = m_pWorld->GetWorldMgr() && (m_pWorld->GetWorldMgr()->isUGCEditMode() || m_pWorld->GetWorldMgr()->isUGCEditBuildMode());

	if (forcesave)//forcesave?true???????????????��
	{
		if (m_Dirty || m_hasNoSaveData)
		{
			return true;
		}
		else if (inUGCEdit)
		{
			return true;
		}
		else
		{
			return false;
		}
	}

	if ((m_Dirty || inUGCEdit) && m_pWorld->m_CurWorldTick > m_LastSaveTick + 200)
	{
		//LOG_INFO("Chunk::needSave_dirty: x=%d, z=%d", BlockDivSection(m_Origin.x), BlockDivSection(m_Origin.z));
		return true;
	}
	else return false;
}

bool Chunk::needSaveActor(bool forcesave)
{
	if (m_pWorld == nullptr) return false;
	bool inUGCEdit = m_pWorld->GetWorldMgr() && (m_pWorld->GetWorldMgr()->isUGCEditMode() || m_pWorld->GetWorldMgr()->isUGCEditBuildMode());

	if (forcesave)//forcesave?true???????????????��
	{
		if ((m_ActorNum > 0) && m_pWorld->m_CurWorldTick != m_LastActorSaveTick || m_ActorDirty || inUGCEdit)

		{
			return true;
		}
		else return false;
	}

	if (m_pWorld->m_CurWorldTick > m_LastActorSaveTick + 200 && (m_ActorDirty || inUGCEdit)) {
		return true;
	}
	else if ((m_ActorNum > 0) && m_pWorld->m_CurWorldTick > m_LastActorSaveTick + 600)
	{
		//LOG_INFO("Chunk::needSave: x=%d, z=%d, lastsavetick=%d, worldtick=%d", BlockDivSection(m_Origin.x), BlockDivSection(m_Origin.z), m_LastSaveTick, m_pWorld->m_CurWorldTick);
		return true;
	}
	return false;
}


void Chunk::setBlockLight(int lttype, int x, int y, int z, int value)
{
	assert(x>=0 && x<CHUNK_BLOCK_X);
	assert(y>=0 && y<CHUNK_BLOCK_Y);
	assert(z>=0 && z<CHUNK_BLOCK_Z);

	Section *psection = m_Sections[y>>4];
	//if (psection->isEmpty())
	//	return;

	m_Dirty = true;

	if(lttype==1 || lttype==0&&m_pWorld->hasSky())
	{
		psection->setBlockLight(lttype, x, y&15, z, value);
	}
}

void Chunk::markLightDirty(UInt8 lttype, UInt8 x, UInt8 y, UInt8 z)
{
#ifndef DIRTY_LIGHT
	return;
#endif
	assert(x >= 0 && x < CHUNK_BLOCK_X);
	assert(y >= 0 && y < CHUNK_BLOCK_Y);
	assert(z >= 0 && z < CHUNK_BLOCK_Z);

	Section *psection = m_Sections[y >> 4];

#ifdef _OLD_DIRTYLIGHT
	UInt32 v = (lttype << 16) | (x << 8) | (z << 4) | (y & 0x0f);
	if (psection->m_dirtyLights.size() < 16000)  //??????
		psection->m_dirtyLights.push_back(v);
#else
	psection->setLightDirty(x, y&0x0f, z, lttype);
#endif

	//fprintf(litlog, " lit (%d,%d,%d) dirty %d\n", m_Origin.x + x, m_Origin.y + y, m_Origin.z + z, lttype);
}

void Chunk::markLightDirtySuspended(UInt8 lttype, UInt8 x, UInt8 y, UInt8 z)
{
#ifndef DIRTY_LIGHT
	return;
#endif
	assert(x >= 0 && x < CHUNK_BLOCK_X);
	assert(y >= 0 && y < CHUNK_BLOCK_Y);
	assert(z >= 0 && z < CHUNK_BLOCK_Z);

	UInt32 v = (lttype << 16) | (x << 12) | (z << 8) | (y);
	if (m_suspendDirtyLights.size() < 16000)  //??????
		m_suspendDirtyLights.insert(v);

	//fprintf(litlog, " lit (%d,%d,%d) dirty suspend %d\n", m_Origin.x + x, m_Origin.y + y, m_Origin.z + z, lttype);
}

void Chunk::markNeighbourLightDirty(int lttype, int rx, int y, int rz)
{
#ifndef DIRTY_LIGHT
	return;
#endif
	for (int dir = 0; dir < 6; ++dir)
	{
		WCoord offset = g_DirectionCoord[dir];

		int nrx = rx + offset.x;
		int nrz = rz + offset.z;
		int ny = y + offset.y;

		Chunk *neighbourChunk = getNeighbourChunkFast(nrx >> 4, nrz >> 4);
		if (neighbourChunk && ny >= 0 && ny < CHUNK_BLOCK_Y)
		{
			neighbourChunk->markLightDirty(lttype, (nrx & 0x0f), ny, (nrz & 0x0f));
		}
	}
}

void Chunk::markRemoteLightDirty(UInt8 lttype, UInt8 x, UInt8 y, UInt8 z)
{
#ifndef DIRTY_LIGHT
	return;
#endif
	assert(x >= 0 && x < CHUNK_BLOCK_X);
	assert(y >= 0 && y < CHUNK_BLOCK_Y);
	assert(z >= 0 && z < CHUNK_BLOCK_Z);

	Section* psection = m_Sections[y >> 4];
	psection->setRemoteLightDirty(x, y & 0x0f, z, lttype);
}

int Chunk::calBlockLightValue(int lttype, int rx, int rz, int y, int* outNeibourLights)
{
	int bx = rx & 0x0f;
	int by = y & 0x0f;
	int bz = rz & 0x0f;
	int sectiony = (y >> 4);

	outNeibourLights[6] = 0;  //light not fetched

	if (lttype == 0 && y >= getTopHeight(bx, bz))
	{
		return 15;
	}
	else
	{
		Section* psect = getIthSection(sectiony);
		if (!psect) { return 0; }
		int blockid = psect->getBlockID(bx, by, bz);

		//int lightsrc = BlockMaterial::m_LightValue[blockid];
		int lightsrc = 0;
		BlockMaterial* mtl = g_BlockMtlMgr.getMaterial(blockid);
		if (mtl)
		{
			lightsrc = mtl->getBlockLightSrc(psect->getBlockData(bx, by, bz));
		}

		if (m_pWorld)
		{
			int lt_ex = m_pWorld->getBlockLightEx(psect->m_Origin.x + bx, psect->m_Origin.y + by, psect->m_Origin.z + bz);
			if (lt_ex > lightsrc)
				lightsrc = lt_ex; 
		}


		int lightvalue = lttype == 0 ? 0 : lightsrc;
		int lightatten = BlockMaterial::getLightOpacity(blockid);

		if (lightatten >= 15 && lightsrc > 0)
		{
			lightatten = 1;
		}

		if (lightatten < 1)
		{
			lightatten = 1;
		}

		if (lightatten >= 15)
		{
			return 0;
		}
		else if (lightvalue >= 14)
		{
			return lightvalue;
		}
		else
		{
			outNeibourLights[6] = 1;  //light fetched

			for (int dir = 0; dir < 6; ++dir)
			{
				WCoord offset = g_DirectionCoord[dir];

				int nrx = rx + offset.x;
				int nrz = rz + offset.z;
				int ny = y + offset.y;

				Chunk *neighborchunk = getNeighbourChunkFast((nrx >> 4), (nrz >> 4));
				if (neighborchunk && ny >= 0 && ny < CHUNK_BLOCK_Y)
				{
					int nbx = nrx & 0x0f;
					int nby = ny & 0x0f;
					int nbz = nrz & 0x0f;
					int neighborlight = neighborchunk->getIthSection(ny >> 4)->getBlockLight(lttype, nbx, nby, nbz);

					outNeibourLights[dir] = neighborlight;

					lightvalue = (neighborlight - lightatten > lightvalue) ? (neighborlight - lightatten) : lightvalue;
				}
				else
				{
					outNeibourLights[dir] = 0;
				}
			}

			return lightvalue;
		}
	}
}
void Chunk::generateHeightMap()
{
	OPTICK_EVENT();
	m_MinTopHeight = 999999;
	int topsecty = getTopFilledSegment();
	int x;
	int z;

	for (int i = 0; i < CHUNK_SECTION_DIM; i++)
	{
		m_Sections[i]->setEmptyBlockLight(BlockLight::s_EmptyBlockLightAboveGround);
	}

	for (x = 0; x < 16; x++)
	{
		for (z = 0; z < 16; z++)
		{
			setPrecipitationHeight(x, z, INVALID_HEIGHT);
			int topyy = topsecty + SECTION_BLOCK_DIM - 1;

			while (true)
			{
				if (topyy > 0)
				{
					if (getBlockLightOpacity(x, topyy - 1, z) == 0)
					{
						--topyy;
						continue;
					}
				}

				setTopHeight(x, z, topyy);
				if (topyy < m_MinTopHeight)
				{
					m_MinTopHeight = topyy;
				}

				break;
			}
		}
	}

	for (int i = 0; i < CHUNK_SECTION_DIM; i++)
	{
		Section* sect = m_Sections[i];
		if (sect->m_Origin.y + SECTION_BLOCK_DIM - 1 < m_MinTopHeight && sect->GetSharedSectionData().hasAllocated() == false)
		{
			sect->setEmptyBlockLight(BlockLight::s_EmptyBlockLightUnderGround);
		}
	}
}
void Chunk::generateSkylightMap()
{
	m_MinTopHeight = 999999;
	int topsecty = getTopFilledSegment();
	int x;
	int z;

	for (int i = 0; i < CHUNK_SECTION_DIM; i++)
	{
		m_Sections[i]->setEmptyBlockLight(BlockLight::s_EmptyBlockLightAboveGround);
	}

	for (x = 0; x < 16; x++)
	{
		for (z = 0; z < 16; z++)
		{
			setPrecipitationHeight(x, z, INVALID_HEIGHT);
			int topyy = topsecty + SECTION_BLOCK_DIM - 1;

			while (true)
			{
				if (topyy > 0)
				{
					if (getBlockLightOpacity(x, topyy - 1, z) == 0)
					{
						--topyy;
						continue;
					}
				}

				setTopHeight(x, z, topyy);
				if (topyy < m_MinTopHeight)
				{
					m_MinTopHeight = topyy;
				}

				if (m_pWorld && m_pWorld->hasSky())
				{
					int ltvalue = SUN_LIGHT;
					int y = topsecty + SECTION_BLOCK_DIM - 1;

					do
					{
						ltvalue -= getBlockLightOpacity(x, y, z);
						if (ltvalue > 0)
						{
							if (!m_Sections[y >> 4]->GetSharedSectionData().isEmpty())
							{
								m_Sections[y >> 4]->setBlockLight(0, x, y & 15, z, ltvalue);
								m_pWorld->markBlockForUpdate(WCoord(x, y, z) + m_Origin, false);
							}
						}

						y--;
					} while (y > 0 && ltvalue > 0);
				}
				break;
			}
		}
	}

	for (int i = 0; i < CHUNK_SECTION_DIM; i++)
	{
		Section* sect = m_Sections[i];
		if (sect->m_Origin.y + SECTION_BLOCK_DIM - 1 < m_MinTopHeight && sect->GetSharedSectionData().hasAllocated() == false)
		{
			sect->setEmptyBlockLight(BlockLight::s_EmptyBlockLightUnderGround);
		}
	}

	m_Dirty = true;
}


bool Chunk::updateSkylight()
{
	OPTICK_EVENT();
	if (m_isGapLightingUpdated && m_pWorld->hasSky())
	{
		updateSkylight_do();
		if (!m_isGapLightingUpdated) return true;
	}

	return false;
}

bool g_EnableReLighting = true;
void Chunk::relightBlock(int blockx, int blocky, int blockz)
{
	int index = blockz*SECTION_BLOCK_DIM + blockx;
	int toph = m_TopHeight[index];
	
	//g_EnableReLighting = false;

	int y = blocky>toph ? blocky : toph;
	while (y > 0 && getBlockLightOpacity(blockx, y-1, blockz) == 0)
	{
		y--;
	}

	if(y != toph)
	{
		int wx = m_Origin.x + blockx;
		int wz = m_Origin.z + blockz;
		if(g_EnableReLighting) m_pWorld->markBlocksDirtyVertical(wx, wz, y, toph);
		m_TopHeight[index] = y;
		GetPixelMapMgrInterface()->OnBlockChange(m_Origin.x + blockx, m_Origin.y + blocky, m_Origin.z + blockz);
		if(m_pWorld->hasSky())
		{
			if (y < toph)
			{
				for(int i = y; i < toph; ++i)
				{
					setBlockLight(0, blockx, i, blockz, SUN_LIGHT);
					m_pWorld->markBlockForUpdate(WCoord(wx,i,wz), false);
					//markLightDirty(0, blockx, i, blockz);
				}
			}
			else
			{
				for(int i = toph; i < y; ++i)
				{
					setBlockLight(0, blockx, i, blockz, 0);
					m_pWorld->markBlockForUpdate(WCoord(wx, i, wz), false);
					markLightDirty(0, blockx, i, blockz);
				}
			}

			int skylight = 15;
			while(y > 0 && skylight > 0)
			{
				--y;
				int lightatten = Rainbow::Max(1, getBlockLightOpacity(blockx, y, blockz));

				skylight -= lightatten;
				if(skylight < 0)
				{
					skylight = 0;
				}

				setBlockLight(0, blockx, y, blockz, skylight);
			}
		}

		int newtoph = m_TopHeight[index];
		if(newtoph < m_MinTopHeight)
		{
			m_MinTopHeight = newtoph;
		}

		int miny = toph;
		int maxy = newtoph;
		if(newtoph < toph)
		{
			miny = newtoph;
			maxy = toph;
		}
		
		if(g_EnableReLighting && m_pWorld->hasSky())
		{
			updateSkylightNeighborHeight(wx - 1, wz, miny, maxy);
			updateSkylightNeighborHeight(wx + 1, wz, miny, maxy);
			updateSkylightNeighborHeight(wx, wz - 1, miny, maxy);
			updateSkylightNeighborHeight(wx, wz + 1, miny, maxy);
			updateSkylightNeighborHeight(wx, wz, miny, maxy);
		}

		m_Dirty = true;
	}
}

void Chunk::addActor(IClientActor *actor, int ithSection)
{
	getIthSection(ithSection)->addActor(actor);
	if(actor->getObjType() != OBJ_TYPE_ROLE) m_ActorNum++;

	//if (m_sceneChunk) {
	//	m_sceneChunk->OnAddActor(static_cast<MNSandbox::SandboxNode*>(actor));
	//}
	m_ActorDirty = true;
}

void Chunk::removeActor(IClientActor *actor, int ithSection)
{
	if(actor->getObjType() != OBJ_TYPE_ROLE) m_ActorNum--;
	getIthSection(ithSection)->removeActor(actor);

	//if (m_sceneChunk) {
	//	m_sceneChunk->OnRemoveActor(static_cast<MNSandbox::SandboxNode*>(actor));
	//}
	m_ActorDirty = true;
}


void Chunk::addContainer(WorldContainer *container)
{	
	m_ChunkContainers.push_back(container);
}

void Chunk::removeContainer(WorldContainer *container, bool needdel, bool delboard)
{
	for(size_t i=0; i<m_ChunkContainers.size(); i++)
	{
		if(m_ChunkContainers[i] == container)
		{
			m_ChunkContainers[i] = m_ChunkContainers.back();
			m_ChunkContainers.resize(m_ChunkContainers.size()-1);
			if(needdel){
				//?��????????
				if (delboard)
				{
					char strKey[64];
					sprintf(strKey, "%d%d%d%lld", container->m_BlockPos.x, container->m_BlockPos.y, container->m_BlockPos.z, container->m_ObjId);

					//SandboxEventDispatcherManager::GetGlobalInstance().Emit("WorldStringManager_remove", SandboxContext(nullptr)
					//	.SetData_String("strKey", strKey)
					//	.SetData_Number("type", 0  /*SAVEFILETYPE::MESSAGE_BOARD*/));

					//SandboxEventDispatcherManager::GetGlobalInstance().Emit("WorldStringManager_remove", SandboxContext(nullptr)
					//	.SetData_String("strKey", strKey)
					//	.SetData_Number("type", 1/*SAVEFILETYPE::PLAQUE_BOARD*/));
					GetWorldStringManagerProxy()->remove(strKey, SAVEFILETYPE::MESSAGE_BOARD);
					GetWorldStringManagerProxy()->remove(strKey, SAVEFILETYPE::PLAQUE_BOARD); //???????????????�g??????????????????????????????????��????
				}
				SANDBOX_DELETE(container);
			}
			return;
		}
	}	
}

//char skylitVisited[CHUNK_TOTAL_BLOCKS * 9];
//UInt32 skylitQueue[CHUNK_TOTAL_BLOCKS * 9];

//void updateSkylightFast(Chunk *chunk)
//{
//	//float t1 = SimpleProfiler::getTime();
//
//	//fprintf(litlog, "updateSkylightFast (%d,%d)\n", chunk->m_Origin.x, chunk->m_Origin.z);
//
//	if (!chunk->getWorld()->hasSky())
//		return;
//
//	WCoord minpos = chunk->m_Origin - 8;
//	WCoord maxpos = chunk->m_Origin + (16 + 8);
//
//	if (!chunk->getWorld()->checkChunksExist(minpos, maxpos))
//		return;
//
//	static const int offsets[][2] = { { 1, 0 }, { -1, 0 }, { 0, 1 }, { 0, -1 } };
//	char sectUpdated[CHUNK_SECTION_DIM * 9];
//	memset(sectUpdated, 0, sizeof(sectUpdated));
//
//	int rx, rz;
//	Chunk *neib;
//	int h0, hn, minh;
//
//	//memset(skylitVisited, 0, sizeof(skylitVisited));
//
//	int queueBegin = 0;
//	int queueEnd = 0;
//
//	int numset = 0, nummark=0;
//
//	// init queue with skylit=14 blocks
//	for (int bz = 0; bz < CHUNK_BLOCK_Z; ++bz)
//	{
//		for (int bx = 0; bx < CHUNK_BLOCK_X; ++bx)
//		{
//			h0 = (int)chunk->getTopHeight(bx, bz) - 1;  //highest non-air block y coord
//			if (h0 >= 1)
//			{
//				minh = 999;
//
//				for (int i = 0; i < 4; i++)
//				{
//					rx = bx + CHUNK_BLOCK_X + offsets[i][0];
//					rz = bz + CHUNK_BLOCK_Z + offsets[i][1];
//					neib = chunk->getNeighbourChunkFast((rx >> 4), (rz >> 4));
//					if (neib)
//					{
//						hn = (int)neib->getTopHeight((rx & 0x0f), (rz & 0x0f)) - 1;  //highest non-air block y coord
//						minh = (hn < minh) ? hn : minh;
//					}
//				}
//
//				//fprintf(litlog, "g(%d,%d)=[%d,%d)\n", chunk->m_Origin.x + bx, chunk->m_Origin.z + bz, minh + 1, h0);
//
//				for (int y = minh + 1; y < h0; y++)
//				{
//					Section *sect = chunk->getIthSection(y >> 4);
//					int blockid = sect->getBlockID(bx, (y & 0x0f), bz);
//					if (blockid == 0)
//					{
//						UInt32 pos = (y << 16) | ((bz + CHUNK_BLOCK_Z) << 8) | (bx + CHUNK_BLOCK_X);
//												
//						sect->setBlockLight(0, bx, (y & 0x0f), bz, SUN_LIGHT - 1);
//						sectUpdated[((1 * 3 + 1) << 4) | (y >> 4)] = 1;
//						numset++;
//						skylitQueue[queueEnd++] = pos;
//						//skylitVisited[pos] = 1;
//
//						//fprintf(litlog, " (%d,%d,%d)>%d\n", sect->m_Origin.x + bx, sect->m_Origin.y + (y & 0x0f), sect->m_Origin.z + bz, SUN_LIGHT - 1);
//					}
//					else
//					{
//						chunk->markLightDirty(0, bx, y, bz);
//						nummark++;
//					}
//				}
//			}
//		}
//	}
//
//	// process queue
//	while (queueBegin < queueEnd)
//	{
//		UInt32 pos = skylitQueue[queueBegin++];
//
//		int y = (pos >> 16) & 0xff;
//		int rz = (pos >> 8) & 0xff;
//		int rx = pos & 0xff;
//
//		int bx = rx & 0x0f;
//		int bz = rz & 0x0f;
//
//		Chunk *chunk0 = chunk->getNeighbourChunkFast(rx >> 4, rz >> 4);
//
//		int light0 = chunk0->getIthSection(y >> 4)->getBlockLight(0, bx, y & 0x0f, bz);
//
//		//fprintf(litlog, "p(%d,%d,%d)%d\n", chunk0->m_Origin.x + bx, y, chunk0->m_Origin.z + bz, light0);
//
//		if (light0 <= 1)
//			continue;
//
//		for (int dir = 0; dir < 6; ++dir)
//		{
//			WCoord offset = g_DirectionCoord[dir];
//
//			int nrx = rx + offset.x;
//			int nrz = rz + offset.z;
//			int ny = y + offset.y;
//
//			int nbx = nrx & 0x0f;
//			int nbz = nrz & 0x0f;
//			int nby = ny & 0x0f;
//
//			UInt32 npos = (ny << 16) | (nrz << 8) | (nrx);
//
//			Chunk* nchunk = chunk->getNeighbourChunkFast(nrx >> 4, nrz >> 4);
//			if (nchunk && ny >= 0 && ny < CHUNK_BLOCK_Y)
//			{
//				Section *sect = nchunk->getIthSection(ny >> 4);
//
//				int blockid = sect->getBlockID(nbx, nby, nbz);
//				if (blockid == 0)
//				{
//					int savedLight = sect->getBlockLight(0, nbx, nby, nbz);
//					if (light0 - 1 > savedLight)
//					{
//						sect->setBlockLight(0, nbx, nby, nbz, light0 - 1);
//						sectUpdated[(((nrz >> 4) * 3 + (nrx >> 4)) << 4) | (ny >> 4)] = 1;
//						numset++;
//						skylitQueue[queueEnd++] = npos;
//
//						//fprintf(litlog, " (%d,%d,%d)%d>%d\n", sect->m_Origin.x + nbx, sect->m_Origin.y + nby, sect->m_Origin.z + nbz, savedLight, light0 - 1);
//					}
//					else
//					{
//						//fprintf(litlog, " (%d,%d,%d)%d\n", sect->m_Origin.x + nbx, sect->m_Origin.y + nby, sect->m_Origin.z + nbz, savedLight);
//					}
//				}
//				else
//				{
//					nchunk->markLightDirty(0, nbx, ny, nbz);
//					nummark++;
//				}
//			}
//			else
//			{
//				//TODO: what if chunk not exist?
//			}
//		}
//	}
//
//	chunk->m_isGapLightingUpdated = false;
//
//
//	//float t2 = SimpleProfiler::getTime();
//	//LOG_INFO("chunk (%d,%d) updateSkylightFast time=%f ms, #=%d,%d,%d", chunk->m_Origin.x, chunk->m_Origin.z, t2 - t1, numset, nummark, nummesh);
//
//	//fprintf(litlog, "updateSkylightFast (%d,%d) time=%f ms, #=%d,%d,%d\n", chunk->m_Origin.x, chunk->m_Origin.z, t2 - t1, numset, nummark, nummesh);
//
//	return;
//}

int generateDirtySkylights(Chunk *chunk, int x, int z)
{
	assert(x >= 0 && x < CHUNK_BLOCK_X);
	assert(z >= 0 && z < CHUNK_BLOCK_Z);

	static const int offsets[][2] = { { 1, 0 }, { -1, 0 }, { 0, 1 }, { 0, -1 } };

	int rx, rz;
	Chunk *neib;
	int hn;

	int count = 0;

	int h0 = (int)chunk->getTopHeight(x, z) - 1;  //highest non-air block y coord
	if (h0 >= 1)
	{
		int minh = 999;

		for (int i = 0; i < 4; i++)
		{
			rx = x + CHUNK_BLOCK_X + offsets[i][0];
			rz = z + CHUNK_BLOCK_Z + offsets[i][1];
			neib = chunk->getNeighbourChunkFast((rx >> 4), (rz >> 4));
			if (neib)
			{
				hn = (int)neib->getTopHeight((rx & 0x0f), (rz & 0x0f)) - 1;  //highest non-air block y coord
				minh = (hn < minh) ? hn : minh;
			}
		}

		for (int y = minh + 1; y < h0; y++)
		{
			chunk->markLightDirty(0, x, y, z);
			count++;
		}
	}

	return count;
}

void Chunk::generateDirtyLights()
{
#ifndef DIRTY_LIGHT
	return;
#endif

	//float t1 = SimpleProfiler::getTime();
	int count0 = 0;
	int count1 = 0;

	//fprintf(litlog, "Chunk::generateDirtyLights (%d,%d)\n", m_Origin.x, m_Origin.z);

	const int bedrock_level = 17;  //5
	int blockid, by;

	for (int bz = 0; bz < SECTION_BLOCK_DIM; ++bz)
	{
		for (int bx = 0; bx < SECTION_BLOCK_DIM; ++bx)
		{
			int topheight = getTopHeight(bx, bz);

			int upperLightSrc = 0;
			int upperLightAtten = 0;
			for (int y = topheight; y >= bedrock_level; --y)
			{
				by = y & 0x0f;

				Section *psect = getIthSection(y >> 4);
				
				blockid = psect->getBlockID(bx, by, bz);

				//int lightsrc = BlockMaterial::m_LightValue[blockid];
				int lightsrc = 0;
				BlockMaterial *mat = g_BlockMtlMgr.getMaterial(blockid);
				if (mat)
					lightsrc = mat->getBlockLightSrc(psect->getBlockData(bx, by, bz));
				if (m_pWorld)
				{
					 int lt_ex = m_pWorld->getBlockLightEx(psect->m_Origin.x + bx, psect->m_Origin.y + by, psect->m_Origin.z + bz);
					 if (lt_ex > lightsrc)
						lightsrc = lt_ex; 
				}

				if (lightsrc > 0 && upperLightSrc == 0)
				{
					count0++;
					markLightDirty(1, bx, by + psect->m_Origin.y, bz);
					//markNeighbourLightDirty(1, bx + CHUNK_BLOCK_X, by + psect->m_Origin.y, bz + CHUNK_BLOCK_Z);
				}

				//if (m_pWorld->hasSky() && def->LightAtten == 0 && upperLightAtten > 0)
				//{
				//	count++;
				//	markLightDirty(0, bx, by + psect->m_Origin.y, bz);
				//}
				
				upperLightSrc = lightsrc;
				upperLightAtten = BlockMaterial::getLightOpacity(blockid);
				
				if (psect->isEmpty())
					y = psect->m_Origin.y - 15;
			}
		}
	}

	//float t2 = SimpleProfiler::getTime();

	if (m_pWorld->hasSky())
	{
		for (int bz = 0; bz < CHUNK_BLOCK_Z; ++bz)
		{
			for (int bx = 0; bx < CHUNK_BLOCK_X; ++bx)
			{
				count1 += generateDirtySkylights(this, bx, bz);
			}
		}
	}

	m_DirtyLightsGenerated = true;

	//float t3 = SimpleProfiler::getTime();

	//fprintf(litlog, "Chunk::generateDirtyLights (%d,%d) end count=%d+%d, duration=%f+%f\n", m_Origin.x, m_Origin.z, count0, count1, t2 - t1, t3 - t2);

	//LOG_INFO("chunk (%d,%d) generateDirtyLights count=%d+%d, duration=%f+%f ms", m_Origin.x, m_Origin.z, count0, count1, t2-t1, t3-t2);
}

void Chunk::onEnterWorld(World *pworld)
{
	if (!MNSandbox::Config::GetSingleton().IsOpenMiniCraftRender())
	{
		return;
	}
	//if (pworld->isSOCCreateMap())
	//{
	//	setUpdateSkyLight();
	//	updateSkylight();
	//}
	OPTICK_EVENT();
	m_pWorld = pworld;
	ActorManagerInterface* actormgr = pworld->getActorMgr();
	
	CHUNK_INDEX ind(BlockDivSection(m_Origin.x), BlockDivSection(m_Origin.z));

	ChestManager* chestMgr = pworld->GetChestMgr();
	if (chestMgr) {
		chestMgr->onChunkLoaded(ind.x, ind.z);
	}

	WorldEventMgrProxy* world_eventMgr = GetWorldEventMgrProxy();
	if (world_eventMgr) {
		world_eventMgr->onChunkLoaded(ind.x, ind.z);
	}

	for (int dx = -1; dx <= 1; dx++)
	{
		for (int dz = -1; dz <= 1; dz++)
		{
			Chunk *neighbourChunk = pworld->getChunk(CHUNK_INDEX(ind.x + dx, ind.z + dz));						
			if (neighbourChunk)
			{
				setNeighbourChunk(dx, dz, neighbourChunk);
				neighbourChunk->onNeighbourChunkEnterWorld(-dx, -dz, this);
			}
		}
	}

	std::unordered_map<int, std::vector<Rainbow::Vector3f>> BlockScriptMtlMap;

	for(int i=0; i<CHUNK_SECTION_DIM; i++)
	{
		Section *psection = getIthSection(i);
		if(psection == NULL) continue;
		if (!NeighbourChunkHasData())
		{
			psection->setNeighborMeshInvalid();
		}

		std::vector<IClientActor *>::iterator iter = psection->m_Actors.begin();
		while(iter != psection->m_Actors.end())
		{
			IClientActor *actor = *iter;
			if(actor != NULL && actormgr != NULL && actor->managedByChunk() && !actormgr->addActorByChunk(actor))
			{
				actor->release();
				iter = psection->m_Actors.erase(iter);
			}
			else iter++;
		}
		//??????????????,???????????chunk??????
		/*if (psection->m_RandomTickBlocks > 0)
		{
			for (int x = 0; x < SECTION_BLOCK_DIM; ++x)
			{
				for (int y = 0; y < SECTION_BLOCK_DIM; ++y)
				{
					for (int z = 0; z < SECTION_BLOCK_DIM; ++z)
					{
						BlockMaterial* pmtl = g_BlockMtlMgr.getMaterial(psection->getBlock(x, y, z).getResID());
						if (pmtl && pmtl->GetToggleTickRandomly())
						{
							pmtl->forceResh(pworld, WCoord(x, y, z) + psection->m_Origin);
						}
					}
				}
			}
		}*/

		bool isgamerun = false;
		WorldManager* pWorldMgr = GetWorldManagerPtr();
		if (pWorldMgr)
		{
			if (pWorldMgr->isSurviveMode() || pWorldMgr->isGameMakerRunMode() || pWorldMgr->isCreateRunMode())
				isgamerun = true;
		}

		if (!psection->GetSharedSectionData().isEmpty())
		{
			SharedSectionData& sectionData = psection->GetWritableSharedSectionData();
			int SectionX = psection->m_Origin.x;
			int SectionY = psection->m_Origin.y;
			int SectionZ = psection->m_Origin.z;

			for (int x = 0; x < SECTION_BLOCK_DIM; ++x)
			{
				for (int y = 0; y < SECTION_BLOCK_DIM; ++y)
				{
					for (int z = 0; z < SECTION_BLOCK_DIM; ++z)
					{
						int resid = psection->getBlock(x, y, z).getResID();
						BlockMaterial* pmtl = g_BlockMtlMgr.getMaterial(resid);
						if (!pmtl)
							continue;
						auto worldpos = WCoord(x, y, z) + psection->m_Origin;
						pmtl->ShowBlockCrack(m_pWorld, worldpos);
						if (pmtl->GetBlockScriptComponent())
						{
							sectionData.SetBlockScript(x, y, z, resid);

							if (isgamerun)
							{
								BlockScriptMtlMap[resid].push_back(Rainbow::Vector3f(SectionX + x, SectionY + y, SectionZ + z));
							}
						}

						if (psection->m_RandomTickBlocks > 0 && pmtl->GetToggleTickRandomly())
						{
							pmtl->forceResh(pworld, worldpos);
						}
					}
				}
			}
		}
	}

	for (const auto& it : BlockScriptMtlMap)
	{
		BlockMaterial* pmtl = g_BlockMtlMgr.getMaterial(it.first);
		if (pmtl)
		{
			pmtl->OnChunkEnterWorld(it.second);
		}
	}

	for(size_t i=0; i<m_ChunkContainers.size(); i++)
	{
		WorldContainer *container = m_ChunkContainers[i];
		/*
		WCoord offset = container->m_BlockPos - m_Origin;

		if(isgamerun && getBlockID(offset.x, offset.y, offset.z) == BLOCK_RANDITEMBOX) //load???????????????
		{
			setBlockAll(offset.x, offset.y, offset.z, 0, 0);

			WorldStorageBox *box = dynamic_cast<WorldStorageBox *>(container);
			BackPackGrid *grid;
			if(box && (grid=box->getRandomGrid()))
			{
				WCoord pos = BlockCenterCoord(container->m_BlockPos);
				IClientItem *item = m_pWorld->getActorMgr()->SpawnIClientItem(pos, *grid);
				if(item) item->setFlagBit(ACTORFLAG_PERSISTENCE, true);
			}
		}
		else */
		WorldContainerMgr* containerMgr =  dynamic_cast<WorldContainerMgr*>(m_pWorld->getContainerMgr());
		if(containerMgr) containerMgr->addContainerByChunk(container);
	}

	if (!m_DirtyLightsGenerated)
	{
		generateDirtyLights();
	}
	
	//if (m_pWorld && m_sceneChunk) {
	//	m_sceneChunk->OnEnterScene(pworld->GetWorldScene());
	//}

	actormgr->onChunkLoad(this);

	//m_nGameDay = m_pWorld->GetWorldMgr()->getWorldTimeDay();
}

void Chunk::onLeaveWorld(bool bReload)
{
	OPTICK_EVENT();
	if(m_pWorld == NULL) return;
	//if (m_pWorld->isSOCCreateMap())
	//{
	//	setUpdateSkyLight();
	//	updateSkylight();
	//}
	ActorManagerInterface* actormgr = m_pWorld->getActorMgr();
	std::unordered_map<int, std::vector<Rainbow::Vector3f>> BlockScriptMtlMap;

	bool isgamerun = true;
	WorldManager* pWorldMgr = GetWorldManagerPtr();
	if (pWorldMgr)
	{
		if (pWorldMgr->isSurviveMode() || pWorldMgr->isGameMakerRunMode() || pWorldMgr->isCreateRunMode())
			isgamerun = true;
	}

	for(int i=0; i<CHUNK_SECTION_DIM; i++)
	{
		Section *psection = getIthSection(i);
		if (m_pWorld->m_PhysScene)
		{
			if (psection->m_PhysActor)
			{
				m_pWorld->m_PhysScene->DeleteRigidActor(psection->m_PhysActor);
				psection->m_PhysActor = NULL;
			}
			if (psection->m_TriggerActor)
			{
				m_pWorld->m_PhysScene->DeleteRigidActor(psection->m_TriggerActor);
			}
			if (psection->m_TriangleActor)
			{
				m_pWorld->m_PhysScene->DeleteRigidActor(psection->m_TriangleActor);
				psection->m_TriangleActor = NULL;
			}
		}
		//if (psection->m_RenderSection != nullptr)
		//{
		//	psection->m_RenderSection->BeginDestroy();
		//}

		//if(!m_pWorld->isRemoteMode())
		{
			std::vector<IClientActor*> actors = psection->m_Actors;
			for(size_t i=0; i< actors.size(); i++)
			{
				IClientActor *actor = actors[i];
				if (actor->managedByChunk())
				{
					if (!actor->IsObject())
					{
						actormgr->removeActorByChunk(actor, true);
					}
					else
					{
						if (!bReload)
						{
							actormgr->removeActorByChunk(actor, true);
						}
					}
				}
			}
		}

		if (psection && isgamerun)
		{
			const SharedSectionData& sectionData = psection->GetSharedSectionData();
			const std::unordered_map<short, int>& ScriptBlocks = sectionData.GetBlockScriptMap();
			for (const auto& it : ScriptBlocks)
			{
				int x = 0;
				int y = 0;
				int z = 0;
				index2XYZ(x, y, z, it.first);
				BlockScriptMtlMap[it.second].push_back(Rainbow::Vector3f(psection->m_Origin.x + x, psection->m_Origin.y + y, psection->m_Origin.z + z));
			}
		}
	}

	if (isgamerun)
	{
		for (const auto& it : BlockScriptMtlMap)
		{
			BlockMaterial* pmtl = g_BlockMtlMgr.getMaterial(it.first);
			if (pmtl)
			{
				pmtl->OnChunkLeaveWorld(it.second);
			}
		}
	}

	for(size_t i=0; i<m_ChunkContainers.size(); i++)
	{
		WorldContainerMgr* containerMgr =  dynamic_cast<WorldContainerMgr*>(m_pWorld->getContainerMgr());	
		if(containerMgr){
			containerMgr->removeContainerByChunk(m_ChunkContainers[i]);
		}
	}
	
	for (int dx = -1; dx <= 1; dx++)
	{
		for (int dz = -1; dz <= 1; dz++)
		{
			Chunk *neighbourChunk = getNeighbourChunk(dx, dz);
			if (neighbourChunk)
			{
				neighbourChunk->onNeighbourChunkLeaveWorld(-dx, -dz, this);
				setNeighbourChunk(dx, dz, NULL);
			}
		}
	}

	m_pWorld = NULL;
	//if (m_sceneChunk) {
	//	m_sceneChunk->OnLeaveScene();
	//}
}

void Chunk::clearPhysActor()
{
	for (int i = 0; i < CHUNK_SECTION_DIM; i++)
	{
		Section *psection = getIthSection(i);
		if (m_pWorld->m_PhysScene)
		{
			if (psection->m_PhysActor)
			{
				m_pWorld->m_PhysScene->DeleteRigidActor(psection->m_PhysActor);
				psection->m_PhysActor = NULL;
			}
			if (psection->m_TriggerActor)
			{
				m_pWorld->m_PhysScene->DeleteRigidActor(psection->m_TriggerActor);
				psection->m_TriggerActor = nullptr;
			}
			if (psection->m_TriangleActor)
			{
				m_pWorld->m_PhysScene->DeleteRigidActor(psection->m_TriangleActor);
				psection->m_TriangleActor = nullptr;
			}
		}
	}
}

void Chunk::onNeighbourChunkEnterWorld(int dx, int dz, Chunk *neighbourChunk)
{
	setNeighbourChunk(dx, dz, neighbourChunk);
#ifndef DIRTY_LIGHT
	return;
#endif
	int targetx;
	if (dx == -1)
		targetx = 0;
	else if (dx == 1)
		targetx = 15;
	else
		targetx = -1;

	int targetz;
	if (dz == -1)
		targetz = 0;
	else if (dz == 1)
		targetz = 15;
	else
		targetz = -1;

	//fprintf(litlog, "Chunk::onNeighbourChunkEnterWorld (%d,%d) neib=(%d,%d)\n", m_Origin.x, m_Origin.z, neighbourChunk->m_Origin.x, neighbourChunk->m_Origin.z);

	for (auto it = m_suspendDirtyLights.begin(); it != m_suspendDirtyLights.end();)
	{
		UInt32 v = (*it);
		int lttype = (v >> 16);
		int bx = (v >> 12) & 0x0f;
		int bz = (v >> 8) & 0x0f;
		int y = v & 0xff;

		bool remarkAsDirty = (targetx == -1 || bx == targetx) && (targetz == -1 || bz == targetz);
		if (remarkAsDirty)
		{
			markLightDirty(lttype, bx, y, bz);
			m_suspendDirtyLights.erase(it++);
		}
		else
		{
			++it;
		}
	}

	if (m_pWorld->hasSky())
	{
		if (targetx == -1 && targetz != -1)
		{
			for (int bx = 0; bx < CHUNK_BLOCK_X; ++bx)
			{
				generateDirtySkylights(this, bx, targetz);
			}
		}
		else if (targetx != -1 && targetz == -1)
		{
			for (int bz = 0; bz < CHUNK_BLOCK_Z; ++bz)
			{
				generateDirtySkylights(this, targetx, bz);
			}
		}
	}
}

void Chunk::onNeighbourChunkLeaveWorld(int dx, int dz, Chunk *neighbourChunk)
{
	setNeighbourChunk(dx, dz, NULL);
}

int Chunk::getTopFilledSegment()
{
	for(int i=CHUNK_SECTION_DIM-1; i>=0; i--)
	{
		Section *psection = getIthSection(i);
		if(!psection->isEmpty()) return i*SECTION_BLOCK_DIM;
	}

	return 0;
}

int Chunk::getPrecipitationHeight(int x, int z)
{
	int index = xz2Index(x, z);
	int h = m_PrecipitationHeight[index];

	if (h == INVALID_HEIGHT)
	{
		int y = getTopFilledSegment() + 15;
		h = -1;

		while (y > 0)
		{
			int blockid = getBlockID(x, y, z);
			auto block = getBlock(x, y, z);
			if (!block.isEmpty() && block.moveCollide() != 0)
			{
				h = y + 1;
				break;

			}
			/*if(blockid > 0)
			{
				const BlockDef *def = GetDefManagerProxy()->getBlockDef(blockid);
				if(def && def->MoveCollide != 0)
				{
					h = y + 1;
					break;
				}
			}*/
			y--;
		}

		m_PrecipitationHeight[index] = h;
	}

	return h;
}

/*
void Chunk::addBlock(const WCoord &cc, int resid, int data)
{
	const BlockDef *def = GetDefManagerProxy()->getBlockDef(resid);

	if(def->Height > 1)
	{
		setOneBlock(cc.x, cc.y+1, cc.z, resid, data|(1<<3));
	}

	setOneBlock(cc.x,cc.y,cc.z, resid, data);

	Block pblock = getBlock(cc);
	int gravityeffect = pblock->gravityEffect();
	if(gravityeffect == 1)
	{
		if(cc.y==0 || getBlock(cc.x,cc.y-1,cc.z)->moveCollide()!=1)
		{
			setBlockFalling(cc.x, cc.y, cc.z);
		}
	}
	else if(gravityeffect == 2)
	{

	}

	for(int i=0; i<CHUNK_SECTION_DIM; i++)
	{
		Section *psection = getIthSection(i);
		if(psection->getMeshState()==SECTION_MESH_BLOCKS) psection->genConnectGraph();
	}

	return;
}

static bool SingleBlockRemove(Block pblock, const WCoord &grid)
{
	return true;
}
void Chunk::removeBlock(const WCoord &cc)
{
	removeBlock(cc, cc, SingleBlockRemove);
}

void Chunk::removeBlock(const WCoord &g1, const WCoord &g2, RemoveBlockFunc pfunc)
{
	for(int z=g1.z; z<=g2.z; z++)
	{
		for(int x=g1.x; x<=g2.x; x++)
		{
			int miny = g1.y;
			int maxy = g2.y;
			if(miny < 0) miny = 0;
			int topheight = getTopHeight(x,z);
			if(maxy >= topheight) maxy = topheight-1;

			removeBlockColumn(x, z, miny, maxy, pfunc);
			checkBlockGravity(x, z, miny, maxy);
			calTopHeight(x, z);
		}
	}

	for(int i=0; i<CHUNK_SECTION_DIM; i++)
	{
		Section *psection = getIthSection(i);
		if(psection->getMeshState() == SECTION_MESH_BLOCKS) psection->genConnectGraph();
	}

}*/

void Chunk::checkSkylightNeighborHeight(int x, int z, int h)
{
	int toph = m_pWorld->getTopHeight(x, z);

	if (toph > h)
	{
		updateSkylightNeighborHeight(x, z, h, toph + 1);
	}
	else if (toph < h)
	{
		updateSkylightNeighborHeight(x, z, toph, h + 1);
	}
}

void Chunk::updateSkylightNeighborHeight(int x, int z, int miny, int maxy)
{
	if(maxy > miny)
	{
		WCoord center(x, 0, z);
		if(m_pWorld->checkChunksExist(center-16, center+16))
		{
			for(int y = miny; y < maxy; ++y)
			{
				m_pWorld->blockLightingChange(0, WCoord(x, y, z));
			}

			m_Dirty = true;
		}
	}
}

void Chunk::updateSkylight_do()
{
	WCoord minpos = m_Origin - 8;
	WCoord maxpos = m_Origin + (16 + 8);

	if (m_pWorld->checkChunksExist(minpos, maxpos))
	{
		//updateSkylightFast(this);

		m_pWorld->cacheChunks(BlockDivSection(minpos.x), BlockDivSection(minpos.z), BlockDivSection(maxpos.x), BlockDivSection(maxpos.z));
		for (int bx = 0; bx < 16; ++bx)
		{
			for (int bz = 0; bz < 16; ++bz)
			{
				int index = xz2Index(bx, bz);
				if (m_updateSkylightColumns[index])
				{
					m_updateSkylightColumns[index] = false;

					int topheight = m_TopHeight[index];
					int wx = m_Origin.x + bx;
					int wz = m_Origin.z + bz;
					int max_minh = m_pWorld->getChunkHeightMapMinimum(wx - 1, wz);
					int minh1 = m_pWorld->getChunkHeightMapMinimum(wx + 1, wz);
					int minh2 = m_pWorld->getChunkHeightMapMinimum(wx, wz - 1);
					int minh3 = m_pWorld->getChunkHeightMapMinimum(wx, wz + 1);

					if (minh1 < max_minh)
					{
						max_minh = minh1;
					}

					if (minh2 < max_minh)
					{
						max_minh = minh2;
					}

					if (minh3 < max_minh)
					{
						max_minh = minh3;
					}

					checkSkylightNeighborHeight(wx, wz, max_minh);
					checkSkylightNeighborHeight(wx - 1, wz, topheight);
					checkSkylightNeighborHeight(wx + 1, wz, topheight);
					checkSkylightNeighborHeight(wx, wz - 1, topheight);
					checkSkylightNeighborHeight(wx, wz + 1, topheight);
				}
			}
		}

		m_pWorld->cancelCacheChunks();

		m_isGapLightingUpdated = false;
	}
}

bool Chunk::ms_SetBlockCreateContainer = true;
bool Chunk::ms_RelightBlock = true;

int Chunk::setBlockAll(int x, int y, int z, int resid, int data, int flag, int dataEx)
{
	int ret = 0;
	if (m_pWorld) {
		auto callback = m_pWorld->GetChunkSetBlockCallback();
		if (callback != nullptr) {
			if (callback(m_Origin.x, m_Origin.z) == false) {
				return ret;
			}
		}
	}
	//if (SandboxCoreDriver::GetInstancePtr() != nullptr)
	//{
	//	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().
	//		Emit("Homeland_ChunkSetBlock", SandboxContext(nullptr).SetData_Number("blockx", m_Origin.x).SetData_Number("blockz", m_Origin.z));
	//	if (result.IsSuccessed() == false)
	//	{
	//		return false;
	//	}
	//}

	int index = z*SECTION_BLOCK_DIM + x;
	if(y >= m_PrecipitationHeight[index]-1) m_PrecipitationHeight[index] = INVALID_HEIGHT;
	int topheight = m_TopHeight[index];

	Section *psection = getSectionByY(y);
	int sy = y%SECTION_BLOCK_DIM;
	Block pBlock = psection->getBlock(x, sy, z);
	int oldid = pBlock.getResID();
	int olddata = pBlock.getData();
	int olddataEx = pBlock.getDataEx();
	BlockMaterial *oldmtl = NULL;

	if (oldid == resid && olddata == data)
	{
		if (olddataEx != dataEx)
		{
			ret = 8;
			psection->setBlock(x, sy, z, resid, data, dataEx);
			oldmtl = g_BlockMtlMgr.getMaterial(oldid);
			oldmtl->ShowBlockCrack(m_pWorld, WCoord(x, y, z) + m_Origin);
		}
		return ret;
	}

	bool needgenmap = 0;

	if (psection->GetSharedSectionData().hasAllocated() == false)
	{
		if(resid == m_EmptyBlock.getResID()) return ret;
		needgenmap = y >= topheight;
	}

	bool cullingFlag = m_pWorld->CheckTriggerBlockAddRemoveEnable() && !m_pWorld->CheckBlockRemoveCulling(oldid, resid);
	WCoord abspos = WCoord(x, y, z) + m_Origin;

	if (cullingFlag && oldid != m_EmptyBlock.getResID() && !m_pWorld->isRemoteMode())
	{
		ObserverEvent_Block obevent(abspos.x, abspos.y, abspos.z, oldid);
		GetISandboxActorSubsystem()->OnBlockEvent("CE_OnBlockRemove", &obevent);
		//GetObserverEventManager().OnBlockEvent(17, &obevent);  //CE_OnBlockRemove : 17
	}

	ret = 3;
	psection->setBlock(x, sy, z, resid, data, dataEx);
	if ((BlockMaterialMgr::isWater(oldid) && BlockMaterialMgr::isWater(resid))/*((oldid == BLOCK_STILL_WATER || oldid == BLOCK_FLOW_WATER) && (resid == BLOCK_STILL_WATER || resid == BLOCK_FLOW_WATER))*/
		|| (BlockMaterialMgr::isLava(oldid) && BlockMaterialMgr::isLava(resid))/*((oldid == BLOCK_STILL_LAVA || oldid == BLOCK_FLOW_LAVA) && (resid == BLOCK_STILL_LAVA || resid == BLOCK_FLOW_LAVA))*/
		|| (BlockMaterialMgr::isHoney(oldid) && BlockMaterialMgr::isHoney(resid))/*((oldid == BLOCK_STILL_HONEY || oldid == BLOCK_FLOW_HONEY) && (resid == BLOCK_STILL_HONEY || resid == BLOCK_FLOW_HONEY))*/)
	{
		ret = 1;
	}
	if (BLOCK_GRAVITY_SYSTEM == oldid || BLOCK_MUSIC_BOX == oldid)
	{
		m_pWorld->removeSpecialRefershBlock(m_Origin, WCoord(m_Origin.x + x, m_Origin.y + y, m_Origin.z + z), oldid);
	}

	if (BLOCK_GRAVITY_SYSTEM == resid || BLOCK_MUSIC_BOX == resid)
	{
		m_pWorld->addSpecialRefershBlock(m_Origin, WCoord(m_Origin.x + x, m_Origin.y + y, m_Origin.z + z), resid);
	}
	else if (BLOCK_PLANTSPACE_RAINBOWGRASS == resid)
	{
		GetISandboxActorSubsystem()->GenerateRainbowGrassMaterial(m_pWorld, psection, g_BlockMtlMgr.getMaterial(resid), this, x,sy,z);
	}

	if(oldid != m_EmptyBlock.getResID())
	{
		psection->GetWritableSharedSectionData().nonEmptyBlocks()--;
		psection->m_RefreshedEmptySection = false;
		oldmtl = g_BlockMtlMgr.getMaterial(oldid);
		if (oldmtl)
		{
			if (oldmtl->GetToggleTickRandomly())
			{
				psection->m_RandomTickBlocks--;
			}

			if (oldmtl && oldmtl->needCheckVoidNight())
				psection->m_VoidRandomTickBlocks--;

			oldmtl->onStopEffect(m_pWorld, abspos);
			if (!m_pWorld->isRemoteMode())
			{
				// ?????????????????
				if (cullingFlag)
				{
					// ???????????
					if (GetObserverEventManagerPtr() != nullptr)
					{
						ObserverEvent_Block obevent(abspos.x, abspos.y, abspos.z, oldmtl->getBlockResID());
						GetObserverEventManager().OnTriggerEvent("Block.Remove", &obevent);
					}
				}
				TriggerBlockAddRemoveDisable tmp(m_pWorld); //???????????????��???
				if (!(flag & kBlockUpdateFlagIgnoreRemoveCallBack))
				{
					oldmtl->DoOnBlockRemoved(m_pWorld, abspos, oldid, olddata);
				}
			}
			if (oldid != resid && oldmtl->hasContainer())
			{
				m_pWorld->getContainerMgr()->destroyContainer(abspos);
			}
		}
	}

	BlockMaterial *newmtl = NULL;
	if(resid != m_EmptyBlock.getResID())
	{
		psection->GetWritableSharedSectionData().nonEmptyBlocks()++;
		newmtl = g_BlockMtlMgr.getMaterial(resid);
		if(newmtl && newmtl->GetToggleTickRandomly())
		{
			psection->m_RandomTickBlocks++;
		}
		if (newmtl && newmtl->needCheckVoidNight())
		{
			psection->m_VoidRandomTickBlocks++;
		}

	}

	bool phyChangeByData = false;
	if (resid == oldid && olddata!=data)
	{
		const BlockDef* blockDef = GetDefManagerProxy()->getBlockDef(oldid);
		if (blockDef && (blockDef->PhyCollide == 3 || blockDef->PhyCollide == 6))
			phyChangeByData = true;
		if (newmtl)
		{
			newmtl->ChangeBlockCrackModel(m_pWorld, abspos);
		}
	}

	bool bPhyUpdate = false;
	if((resid != oldid) || (phyChangeByData)) 
	{
		psection->setNeedUpdateVisibility();
		bool oldphy = false;
		const BlockDef* blockDef = GetDefManagerProxy()->getBlockDef(oldid);
		if (blockDef && (blockDef->PhyCollide > 0 || blockDef->Type == "clitter" || blockDef->Type == "peristele" || blockDef->Type == "mineralpile"))
		{
			oldphy = true;
		}

		bool newphy = false;
		blockDef = GetDefManagerProxy()->getBlockDef(resid);
		if (blockDef && (blockDef->PhyCollide > 0 || blockDef->Type == "clitter" || blockDef->Type == "peristele" || blockDef->Type == "mineralpile"))
		{
			newphy = true;
		}
		
		if (blockDef && (blockDef->PhyCollide == 6))
		{
			phyChangeByData = true;
		}

		if (newphy != oldphy || phyChangeByData) {
			m_pWorld->markBlockForPhyUpdate(abspos - WCoord(1, 1, 1), abspos + WCoord(1, 1, 1));
			bPhyUpdate = true;
		}
	}
	// ?????????? ??????????????
	if (!bPhyUpdate)
	{
		bool bPlayerRigidBody = false;
		if (g_WorldMgr && g_WorldMgr->m_RuleMgr)
		{
			float val = 0.f;
			int optid = 0;
			g_WorldMgr->m_RuleMgr->getRuleOptionID(GAMEMAKER_RULE::GMRULE_PLAYERPHYSTYPE, optid, val);
			if (optid == 2)
			{
				bPlayerRigidBody = true;
			}
		}
		if (bPlayerRigidBody && resid != oldid && ((newmtl && newmtl->defBlockMove()) || (oldmtl && oldmtl->defBlockMove())))
		{
			m_pWorld->markBlockForPhyUpdate(abspos - WCoord(1, 1, 1), abspos + WCoord(1, 1, 1));
			bPhyUpdate = true;
		}
	}
	
	if (ms_RelightBlock)
	{
		if(needgenmap)
		{
			generateSkylightMap();
		}
		else
		{
			int lightOpacity = BlockMaterial::getLightOpacity(resid);

			if (lightOpacity > 0)
			{
				if(y >= topheight)
				{
					relightBlock(x, y+1, z);
				}
			}
			else if(y == topheight-1)
			{
				relightBlock(x, y, z);
			}

			if (lightOpacity != BlockMaterial::getLightOpacity(oldid) ||
				BlockMaterial::getLightValue(resid) != BlockMaterial::getLightValue(oldid))
			{
				propagateSkylightOcclusion(x, z);
			}
		}
	}
	else
	{
		//?????topH??????
		int curHeight = y + 1;
		curHeight  = curHeight > topheight ? curHeight : topheight;
		while (curHeight > 0 && getBlockLightOpacity(x, curHeight - 1, z) == 0)
		{
			curHeight--;
		}

		if (curHeight != topheight)
		{
			m_TopHeight[index] = curHeight;
		}
		int newtoph = m_TopHeight[index];
		if (newtoph < m_MinTopHeight)
		{
			m_MinTopHeight = newtoph;
		}
	}

	if(newmtl)
	{
		if (!m_pWorld->isRemoteMode())
		{
			// ???????????
			if (m_pWorld->CheckTriggerBlockAddRemoveEnable())
			{
				ObserverEvent_Block obevent(abspos.x, abspos.y, abspos.z, newmtl->getBlockResID());
				GetObserverEventManager().OnTriggerEvent("Block.Add", &obevent);
			}
			TriggerBlockAddRemoveDisable tmp(m_pWorld); //???????????????��???
			newmtl->DoOnBlockAdded(m_pWorld, abspos);
		}
		else
		{
			newmtl->DoOnPlayRandEffect(m_pWorld, abspos);
		}

		if(ms_SetBlockCreateContainer && newmtl->hasContainer())
		{
			WorldContainer *container = m_pWorld->getContainerMgr()->getContainer(abspos);
			if(container == NULL)
			{
				container = newmtl->createContainer(m_pWorld, abspos);
				if(container)
				{
					container->m_OwnerUin = 0;
					m_pWorld->getContainerMgr()->spawnContainer(container);
				}
			}

			if(container) container->updateContainingBlockInfo();
		}

		ObserverEvent_Block obevent(abspos.x, abspos.y, abspos.z, newmtl->getBlockResID());
		if (flag & kBlockUpdateFlagBlockEventImmediate)
		{
			GetISandboxActorSubsystem()->OnBlockEvent("CE_OnBlockAdd", &obevent, 0);
			//GetObserverEventManager().OnBlockEvent(16, &obevent, 0); //CE_OnBlockAdd : 16
		}
		else 
			GetISandboxActorSubsystem()->OnBlockEvent("CE_OnBlockAdd", &obevent, 1);
			//GetObserverEventManager().OnBlockEvent(16, &obevent, 1);  //CE_OnBlockAdd : 16
	}

	psection->GetWritableSharedSectionData().updataSectionBlocks();
	m_Dirty = true;
	return ret;
}

bool Chunk::setBlockData(int x, int y, int z, int data)
{
	Section *psection = getSectionByY(y);
	if (psection == NULL || psection->GetSharedSectionData().hasAllocated() == false)
	{
		return false;
	}

	int sy = y%SECTION_BLOCK_DIM;
	Block pBlock = psection->getBlock(x, sy, z);

	if(pBlock.getData() == data) return false;

	pBlock.setData(data);
	psection->setBlock(x, sy, z, pBlock);
	m_Dirty = true;

	BlockMaterial *mtl = g_BlockMtlMgr.getMaterial(pBlock.getResID());
	if(mtl && mtl->hasContainer())
	{
		WorldContainer *container = m_pWorld->getContainerMgr()->getContainer(m_Origin+WCoord(x,y,z));
		if(container)
		{
			container->updateContainingBlockInfo();
			container->m_BlockData = data;
		}
	}
	const BlockDef* blockDef = GetDefManagerProxy()->getBlockDef(pBlock.getResID());
	if (blockDef && blockDef->PhyCollide == 6)
	{
		WCoord abspos = WCoord(x, y, z) + m_Origin;
		m_pWorld->markBlockForPhyUpdate(abspos - WCoord(1, 1, 1), abspos + WCoord(1, 1, 1));
	}

	if (BLOCK_PLANTSPACE_RAINBOWGRASS == pBlock.getResID())
	{
		GetISandboxActorSubsystem()->GenerateRainbowGrassMaterial(m_pWorld, psection, mtl, this, x, sy, z);
	}


	return true;
}

bool Chunk::setBlockDateEx(int x, int y, int z, int dataEx)
{
	Section* psection = getSectionByY(y);
	if (psection == NULL || psection->GetSharedSectionData().hasAllocated() == false)
	{
		return false;
	}

	int sy = y % SECTION_BLOCK_DIM;
	Block pBlock = psection->getBlock(x, sy, z);
	if (pBlock.getDataEx() == dataEx) return false;

	pBlock.setDataEx(dataEx);
	psection->setBlock(x, sy, z, pBlock);

	return true;
}

const BiomeDef *Chunk::getBiome(int x, int z) const
{
	return GetSharedChunkData().getBiome(x, z);
}

const BiomeDef *Chunk::getAirLandBiome(int x, int z) const
{
	return GetSharedChunkData().getAirLandBiome(x, z);
}


Ecosystem *Chunk::getBiomeGen(int x, int z)
{
	return m_pWorld->getChunkProvider()->getBiomeManager()->getEcosystem(getBiomeID(x,z));
}

Block Chunk::getBlock(int x, int y, int z) const
{
	assert(x >= 0 && x < CHUNK_BLOCK_X);
	assert(z >= 0 && z < CHUNK_BLOCK_Z);

	if (y < 0 || y >= CHUNK_BLOCK_Y)
		return m_EmptyBlock;

	int i = y >> 4;
	Section* psection = m_Sections[i];
	if (!psection || psection->isEmpty()) return m_EmptyBlock;
	else return psection->getBlock(x, y & 15, z);
}

IClientItem *Chunk::findNearestItem(const WCoord &center, int itemid)
{
	int mindist = Rainbow::MAX_INT;
	IClientItem*pItem = NULL;
	for(int i=0; i<CHUNK_SECTION_DIM; i++)
	{
		Section *psection = m_Sections[i];
		if(psection)
		{
			for(size_t i=0; i<psection->m_Actors.size(); i++)
			{
				if (psection->m_Actors[i]->getObjType() == OBJ_TYPE_DROPITEM)
				{
					IClientItem* item = dynamic_cast<IClientItem*>(psection->m_Actors[i]);
					if (item && psection->m_Actors[i]->GetItemId() == itemid)
					{
						int dist = center.squareDistanceTo(psection->m_Actors[i]->getPosition());
						if (dist < mindist)
						{
							pItem = item;
							mindist = dist;
						}
					}
				}
			}
		}
	}

	return pItem;
}

bool Chunk::isEdgeChunk() const
{
	int startChunkX = m_pWorld->getChunkProvider()->getStartChunkX();
	int endChunkX = m_pWorld->getChunkProvider()->getEndChunkX();
	int startChunkZ = m_pWorld->getChunkProvider()->getStartChunkZ();
	int endChunkZ = m_pWorld->getChunkProvider()->getEndChunkZ();
	WCoord index = BlockDivSection(m_Origin);
	if (index.x <= startChunkX || index.x >= endChunkX || index.z <= startChunkX || index.z >= endChunkX)
		return true;
	return false;
}

bool Chunk::NeighbourChunkHasData()
{
	for (int i = 0; i < 9; ++i)
	{
		if (m_NeighbourChunks[i] == nullptr)
			return false;
	}
	return true;
}

void Chunk::resetRelightChecks()
{
	m_RelightCheckCount = 0;
}

void Chunk::updateRelightChecks()
{
	for(int i = 0; i < 8; ++i)
	{
		if(m_RelightCheckCount >= 4096)
		{
			return;
		}

		int isect = m_RelightCheckCount % 16;
		int offsetx = m_RelightCheckCount / 16 % 16;
		int offsetz = m_RelightCheckCount / 256;
		m_RelightCheckCount++;

		int wx = m_Origin.x + offsetx;
		int wz = m_Origin.z + offsetz;

		for(int offsety = 0; offsety < 16; ++offsety)
		{
			int wy = (isect << 4) + offsety;
			Section *psection = getIthSection(isect);

			if(psection->isEmpty() && (offsety==0 || offsety==15 || offsetx==0 || offsetx==15 || offsetz==0 || offsetz==15)
				|| !psection->isEmpty() && psection->getBlockID(offsetx, offsety, offsetz) == 0)
			{
				WCoord curblock(wx, wy, wz);
				for(int dir=0; dir<6; dir++)
				{
					WCoord ng = NeighborCoord(curblock, dir);
					int blockid = m_pWorld->getBlockID(ng);
					if(GetDefManagerProxy()->getBlockDef(blockid)->LightSrc > 0)
					{
						m_pWorld->blockLightingChange(ng);
					}
				}

				m_pWorld->blockLightingChange(curblock);
			}
		}
	}
}

int Chunk::calBlockNum(int blockid)
{
	int maxy = getTopFilledSegment() + CHUNK_BLOCK_Y-1;

	int count = 0;
	for(int y=0; y<maxy; y++)
	{
		for(int x=0; x<CHUNK_BLOCK_X; x++)
		{
			for(int z=0; z<CHUNK_BLOCK_Z; z++)
			{
				if(getBlockID(x, y, z) == blockid) count++;
			}
		}
	}
	return count;
}

inline unsigned short SearchBlockIndex(int x, int y, int z)
{
	int i = y*256 + z*16 + x;
	assert(i>=0 && i<65536);

	return (unsigned short)i;
}

SearchBlocks *Chunk::findSearchBlockIndex(int x, int y, int z, int blockid, int &offset)
{
	offset = -1;
	unsigned short index = SearchBlockIndex(x,y,z);
	for(size_t i=0; i<m_SearchBlocks.size(); i++)
	{
		SearchBlocks *sb = m_SearchBlocks[i];
		if(sb->m_BlockID == blockid)
		{
			for(size_t j=0; j<sb->m_PosIndices.size(); j++)
			{
				if(sb->m_PosIndices[j] == index)
				{
					offset = j;
					break;
				}
			}
			return sb;
		}
	}
	return NULL;
}

void Chunk::addSearchBlock(int x, int y, int z, int blockid)
{
	unsigned short index = SearchBlockIndex(x,y,z);
	int offset;
	SearchBlocks *sb = findSearchBlockIndex(x, y, z, blockid, offset);

	if(sb)
	{
		if(offset < 0) sb->m_PosIndices.push_back(index);
		return;
	}
	else
	{
		sb = ENG_NEW(SearchBlocks)();
		sb->m_BlockID = blockid;
		sb->m_PosIndices.push_back(index);
		m_SearchBlocks.push_back(sb);
	}
}

void Chunk::removeSearchBlock(int x, int y, int z, int blockid)
{
	int offset;
	SearchBlocks *sb = findSearchBlockIndex(x, y, z, blockid, offset);

	if(sb && offset>=0)
	{
		assert(sb->m_PosIndices[offset] == SearchBlockIndex(x,y,z));
		sb->m_PosIndices[offset] = sb->m_PosIndices.back();
		sb->m_PosIndices.resize(sb->m_PosIndices.size()-1);
	}
}

SearchBlocks *Chunk::getSearchBlocks(int blockid)
{
	for(size_t i=0; i<m_SearchBlocks.size(); i++)
	{
		SearchBlocks *sb = m_SearchBlocks[i];
		if(sb->m_BlockID == blockid) return sb;
	}
	return NULL;
}

void Chunk::SetChunkSpecialPos(const std::vector<TerrSpecialPos>& datas, const std::vector<ChunkSpecialBiomeData>& biomes, const std::vector<ChunkSpecialBiomeData>& chunkBiomes)
{
	m_specialPos = datas;
	m_specialBiome = biomes;
	m_chunkSpecialBiome = chunkBiomes;
	// ???boss
	/*auto funcSetVolcanoBossPos = [&](const WCoord& pos) -> void {
		int x = pos.x % SECTION_BLOCK_DIM;
		if (x < 0)
			x += SECTION_BLOCK_DIM;
		int z = pos.z % SECTION_BLOCK_DIM;
		if (z < 0)
			z += SECTION_BLOCK_DIM;
		int activeY = CHUNK_BLOCK_Y / 2;
		for (int y = CHUNK_BLOCK_Y / 2 - 1; y > 0; y--)
		{
			Section* psect = getIthSection(y / SECTION_BLOCK_DIM);
			if (!psect)
				return;

			int blockid = psect->getBlockID(x, y % SECTION_BLOCK_DIM, z);
			if (blockid == BLOCK_AIR)
			{
				activeY = y;
			}
			else
			{
				//Section* psect2 = getIthSection(activeY / SECTION_BLOCK_DIM);
				//psect2->setBlock(x, activeY % SECTION_BLOCK_DIM, z, BLOCK_GRASS);
				BossPos boss_pos;
				boss_pos.pos = WCoord(pos.x, activeY, pos.z); // ????????
				boss_pos.pWorld = m_pWorld;
				GetSandBoxManager().DoEvent(SandBoxMgrEventID::EVENT_VOLCANO_APPEAR, 0, 0, (char*)&boss_pos, sizeof(boss_pos));
				break;
			}
		}
	};

	bool bossFlag = false;
	for (auto iter = datas.begin(); iter != datas.end(); iter++)
	{
		if (iter->_type == BiomeSpecialPosType_VolcanoBoss)
		{
			// ???boss
			if (!bossFlag)
			{
				bossFlag = true;
				funcSetVolcanoBossPos(iter->_pos);
			}
		}
	}*/
}

void Chunk::setUpdateSkyLight()
{
	m_isGapLightingUpdated = true;
	for (int x = 0; x < 16; x++)
	{
		for (int z = 0; z < 16; z++)
		{
			m_updateSkylightColumns[z * 16 + x] = true;
		}
	}
}

void Chunk::waitBuildMeshCompleted()
{
	if (m_ChunkRenderer) 
	{
		m_ChunkRenderer->WaitForJobCompleted();
	}

	for (int i = 0; i < CHUNK_SECTION_DIM; ++i)
	{
		if (m_Sections[i] && m_Sections[i]->GetRenderSection())
		{
			m_Sections[i]->GetRenderSection()->WaitForJobCompleted();
		}
	}
}

bool Chunk::IsVaild()
{
	if (!m_SharedChunkData)
		return false;

	return true;
}

void Chunk::FreeRenderSection()
{
	if (m_ChunkRenderer != nullptr) 
	{
		m_ChunkRenderer->BeginDestroy(true);
		m_ChunkRenderer = nullptr;
	}

	for (int i = 0; i < CHUNK_SECTION_DIM; i++)
	{
		if (m_Sections[i])
		{
			m_Sections[i]->FreeRenderSection();
		}
	}
}

void Chunk::CreateRenderSection()
{
	for (int i = 0; i < CHUNK_SECTION_DIM; i++)
	{
		if (m_Sections[i])
		{
			m_Sections[i]->CreateRenderSection();
		}
	}
}

void Chunk::SetHide(bool value)
{
	if (m_ChunkRenderer != nullptr) 
	{
		if (m_ChunkRenderer->IsHide() != value) 
		{
			m_ChunkRenderer->SetHide(value);
			for (int i = 0; i < CHUNK_SECTION_DIM; i++)
			{
				if (m_Sections[i] == nullptr) continue;
				m_Sections[i]->UpdateActorsCull();
			}
		}
	}
	else 
	{
		for (int i = 0; i < CHUNK_SECTION_DIM; i++)
		{
			if (m_Sections[i] != nullptr)
			{
				m_Sections[i]->SetHide(value);
				m_Sections[i]->UpdateActorsCull();
			}
		}
	}

	for (size_t i = 0; i < m_ChunkContainers.size(); i++)
	{
		if (m_ChunkContainers[i]->getObjType() == OBJ_TYPE_MOD_CONTAINER || m_ChunkContainers[i]->getObjType() == OBJ_TYPE_MOD_CONTAINER_TRANSFER)
		{
			GetISandboxActorSubsystem()->ModShowWithChunk(m_ChunkContainers[i], value);
		}
	}
}

bool Chunk::IsHide()
{
	if (m_ChunkRenderer != nullptr) 
	{
		return m_ChunkRenderer->IsHide();
	}
	else 
	{
		bool isHide = false;
		for (int i = 0; i < CHUNK_SECTION_DIM; i++) 
		{
			if (m_Sections[i] != nullptr)
			{
				isHide = m_Sections[i]->IsHide();
				break;
			}
		}
		return isHide;
	}
	return false;
}

int Chunk::genMesh(const WCoord &viewpos)
{
	for (int i = 0; i < 16; i++)
	{
		Section *psection = getIthSection(i);
		psection->setMeshInvalid(true);
	}
	return 0;
}

void Chunk::exportChunksToObj(Chunk** chunks, size_t nchunk, const char* dir, int mode, const WCoord& offset)
{
	ChunkRenderer::ExportChunksToObj(chunks, nchunk, dir, mode, offset);
}

void Chunk::exportStudioMapChunksToObj(World* pworld, Chunk** chunks, size_t nchunk, const char* dir, int mode, const WCoord& origin, const int& size)
{
	ChunkRenderer::ExportStudioMapChunksToObj(pworld, chunks, nchunk, dir, mode, origin, size);
}

//bool Chunk::loadFromBuffer_sb(const void* data, const int len)
//{
//	if (m_sceneChunk) {
//		std::map<long long, MNSandbox::SandboxNode*> allActors;
//		getAllActors(allActors);
//		return m_sceneChunk->Unserialize(data, len, allActors);
//	}
//	return false;
//}
//
//void Chunk::getAllActors(std::map<long long, MNSandbox::SandboxNode*>& out) const
//{
//	out.clear();
//	for (int i = 0; i < CHUNK_SECTION_DIM; i++)
//	{
//		Section* psection = getIthSection(i);
//		if (psection == NULL) continue;
//
//		std::vector<IClientActor*>::iterator iter = psection->m_Actors.begin();
//		while (iter != psection->m_Actors.end()){
//			if (*iter) {
//				out[(*iter)->GetCompatibleSaveID()] = *iter;
//			}
//			iter++;
//		}
//	}
//
//	return;
//}


//void Chunk::LoadNodes(std::vector<MNSandbox::AutoRef<MNSandbox::SandboxNode>>& nodes, std::vector<MNSandbox::SandboxNodeID>& parentids)
//{
//	if (m_sceneChunk) {
//		m_sceneChunk->LoadNodes(nodes, parentids);
//	}
//}

void Chunk::recalculateLight()
{
	// 第一次遍历，生成整个chunk的 xz 平面的最高地面高度表
	int heightMap[CHUNK_BLOCK_X][CHUNK_BLOCK_Z];
	for (int x = 0; x < CHUNK_BLOCK_X; x++)
	{
		for (int z = 0; z < CHUNK_BLOCK_Z; z++)
		{
			int topY = -1;
			for (int y = CHUNK_BLOCK_Y - 1; y >= 0; y--)
			{
				if (getBlockID(x, y, z) != 0)
				{
					topY = y;
					break;
				}
			}
			heightMap[x][z] = topY;
		}
	}

	// 第二次遍历，按现有逻辑修复最高平面光照值为0的情况，并记录修复列作为扩散种子
	bool seedFixed[CHUNK_BLOCK_X][CHUNK_BLOCK_Z];
	for (int sx = 0; sx < CHUNK_BLOCK_X; ++sx)
		for (int sz = 0; sz < CHUNK_BLOCK_Z; ++sz)
			seedFixed[sx][sz] = false;
	for (int x = 0; x < CHUNK_BLOCK_X; x++)
	{
		for (int z = 0; z < CHUNK_BLOCK_Z; z++)
		{
			int topY = heightMap[x][z];
			if (topY < 0) continue;

			int lightY = topY + 1;
			bool fixedTopLightThisColumn = false;
			if (lightY < CHUNK_BLOCK_Y)
			{
				BlockLight currentLight = getBlockLight(x, lightY, z);
				// 只处理天空光 (lttype=0)
				if (currentLight.getLight(0) == 0 && m_pWorld->hasSky())
				{
					fixedTopLightThisColumn = true;
					setBlockLight(0, x, lightY, z, 15);
					for (int downY = lightY; downY > topY; downY--)
					{
						Section* psection = m_Sections[downY >> 4];
						if (psection)
						{
							psection->setLightDirty(x, downY & 15, z, 0);
						}
					}
				}
			}

			if (fixedTopLightThisColumn)
				seedFixed[x][z] = true;
		}
	}

	// 从所有修复列作为种子，进行xz平面上的BFS扩散，直到chunk边界
	if (m_pWorld->hasSky())
	{
		bool visited[CHUNK_BLOCK_X][CHUNK_BLOCK_Z];
		for (int vx = 0; vx < CHUNK_BLOCK_X; ++vx)
			for (int vz = 0; vz < CHUNK_BLOCK_Z; ++vz)
				visited[vx][vz] = false;

		int qx[CHUNK_BLOCK_X * CHUNK_BLOCK_Z];
		int qz[CHUNK_BLOCK_X * CHUNK_BLOCK_Z];
		int qh = 0, qt = 0;

		// 入队所有种子
		for (int x = 0; x < CHUNK_BLOCK_X; ++x)
		{
			for (int z = 0; z < CHUNK_BLOCK_Z; ++z)
			{
				if (seedFixed[x][z])
				{
					visited[x][z] = true;
					qx[qt] = x; qz[qt] = z; ++qt;
				}
			}
		}

		static const int neighborDx[4] = { -1, 1, 0, 0 };
		static const int neighborDz[4] = { 0, 0, -1, 1 };

		while (qh < qt)
		{
			int cx = qx[qh];
			int cz = qz[qh];
			++qh;

			int curTopY = heightMap[cx][cz];
			if (curTopY >= 0)
			{
				int maxNeighborTopY = curTopY;
				for (int i = 0; i < 4; ++i)
				{
					int nx = cx + neighborDx[i];
					int nz = cz + neighborDz[i];
					if (nx < 0 || nx >= CHUNK_BLOCK_X || nz < 0 || nz >= CHUNK_BLOCK_Z) continue;
					if (heightMap[nx][nz] > maxNeighborTopY)
						maxNeighborTopY = heightMap[nx][nz];
				}

				if (maxNeighborTopY > curTopY)
				{
					int maxY = maxNeighborTopY;
					if (maxY >= CHUNK_BLOCK_Y) maxY = CHUNK_BLOCK_Y - 1;
					for (int y = curTopY + 1; y <= maxY; ++y)
					{
						if (getBlockLightOpacity(cx, y, cz) != 0) break;
						BlockLight curLt = getBlockLight(cx, y, cz);
						if (curLt.getLight(0) == 0)
						{
							setBlockLight(0, cx, y, cz, 15);
							Section* psection = m_Sections[y >> 4];
							if (psection)
							{
								psection->setLightDirty(cx, y & 15, cz, 0);
							}
						}
					}
				}
			}

			// 扩展到四周，直到边界
			for (int i = 0; i < 4; ++i)
			{
				int nx = cx + neighborDx[i];
				int nz = cz + neighborDz[i];
				if (nx < 0 || nx >= CHUNK_BLOCK_X || nz < 0 || nz >= CHUNK_BLOCK_Z) continue;
				if (!visited[nx][nz])
				{
					visited[nx][nz] = true;
					qx[qt] = nx; qz[qt] = nz; ++qt;
				}
			}
		}
	}
}

#ifdef BUILD_MINI_EDITOR_APP
void Chunk::ResetChunkData(GenTerrResult& ret)
{
	unsigned short* chunkdata = (unsigned short*)ret.chunkdata;
	unsigned char* biomes = ret.biomes;
	unsigned char* airlandsbiomes = ret.airlandbiomes;

	for (int y = 0; y < MAX_TERRGEN_Y; y++)
	{
		Section* psection = m_Sections[y / SECTION_BLOCK_DIM];
		int sy = y % SECTION_BLOCK_DIM;

		for (int z = 0; z < CHUNK_BLOCK_Z; z++)
		{
			for (int x = 0; x < CHUNK_BLOCK_X; x++)
			{
				int block = chunkdata[xyz2Index(x, y, z)];
				if (block != m_EmptyBlock.getAll())
				{
					psection->setBlock(x, sy, z, block);
					psection->GetWritableSharedSectionData().nonEmptyBlocks()++;

					BlockMaterial* pmtl = g_BlockMtlMgr.getMaterial(Block::toResID(block));
					if (pmtl && pmtl->GetToggleTickRandomly())
						psection->m_RandomTickBlocks++;

					psection->setMeshInvalid(true);
					psection->setPhyInvalid();
					psection->setNeedUpdateVisibility();

					if (y == MAX_TERRGEN_Y - 1 || chunkdata[xyz2Index(x, y + 1, z)] == 0)
					{
						psection->setLightDirty(x, sy, z, 0);
					}
				}
			}
		}
	}
	setBiomeID(CHUNK_BLOCK_X , CHUNK_BLOCK_Z, *biomes);
	if (airlandsbiomes != nullptr)
	{
		auto airLandPtr = m_SharedChunkData->getAirLandsBiomeIDPtr();
		if (airLandPtr == nullptr)
		{
			m_SharedChunkData->initAirLandsBiomeID();
		}
		memcpy(airLandPtr, airlandsbiomes, CHUNK_BLOCK_X * CHUNK_BLOCK_Z);
	}
}
#endif