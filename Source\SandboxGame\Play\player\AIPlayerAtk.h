#ifndef __AIPlayerAtk_H__
#define __AIPlayerAtk_H__

#include "AIBase.h"
#include "SandboxGame.h"

class PathEntity;
class AINpcPlayer;
class EXPORT_SANDBOXGAME AIPlayerAtk;
class AIPlayerAtk : public AIBase //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	AIPlayerAtk(AINpcPlayer *pActor,  bool trace, float speed);
	virtual ~AIPlayerAtk();
	virtual bool willRun();
	virtual bool continueRun();
	virtual void start();
	virtual void reset();
	virtual void update();

	virtual bool canInterruptInteract() { return true; }
	virtual bool canInterruptedByInteract() { return false; }
	virtual AI_MOTION_TYPE getMotionType() { return ATK_MELEE; }
	//tolua_end
private:
	bool atkDist(ClientActor *pActor);

private:
	bool m_trace;

	PathEntity *m_PathEntity;
	int m_TraceTimer;
	//速度系数
	float m_Speed;
	int m_AttackTick;
	int m_AttackDistance;
}; //tolua_exports

#endif
