#ifndef __AiNpcPlayerCsv_H__ 
#define __AiNpcPlayerCsv_H__ 1 
#include "AbsCsv.h" 
#include "LazySingleton.h" 
#include "DefDataTable.h" 
#include "defdata.h" 
#include "IWorldExports.h"

class EXPORT_IWORLD AiNpcPlayerCsv;
class AiNpcPlayerCsv : public AbsCsv {//tolua_exports 
public: 
	AiNpcPlayerCsv();

    ~AiNpcPlayerCsv(); 
    void onParse(MINIW::CSVParser& parser) override; 
    void onClear() override; 
    const char* getName() override; 
    const char* getClassName() override; 
	//tolua_begin
	int getNum();
	AiNpcPlayerDef *get(int id, bool takeplace=false);
	AiNpcPlayerDef *getOriginal(int id);
	const AiNpcPlayerDef *getByIndex(int index);
	//tolua_end
	DefDataTable<AiNpcPlayerDef> &getAiNpcPlayers();
	AiNpcPlayerDef* add(int id, AiNpcPlayerDef* templateDef);
private: 
	std::vector<AiNpcPlayerDef*> m_AiNpcPlayerTable;
	DefDataTable<AiNpcPlayerDef> m_AiNpcPlayers;
    DECLARE_LAZY_SINGLETON(AiNpcPlayerCsv) 
	friend class DefManager;
};//tolua_exports 
#endif//__AiNpcPlayerCsv_H__ 
