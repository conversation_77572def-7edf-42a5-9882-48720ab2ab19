#include "block_tickmgr.h"
#include "Components/Camera.h"
#include "ChunkGen_Normal.h"
#include "ChunkGen_Dummy.h"
#include "ChunkGen_AirIsland.h"
#include "ChunkGen_EarthCore.h"
#include "ChunkGen_PlanetSpace.h"
#include "ChunkGen_SOC.h"
#include "TerrainIDGenerator.h"
#include "blocks/BlockMaterialMgr.h"
#include "EffectManager.h"
#include "Environment.h"
#include "EcosysUnit_VoxelModel.h"
#include "IGameMode.h"
#include "IRecordInterface.h"
#include "MpActorManager.h"
#include "chunkio.h"
#include "SandboxSceneManager.h"
#include "IPlayerControl.h"
#include "IBackpack.h"
#include "ChunkViewer.h"
#include "WorldManager.h"
#include "ChunkGen_Customize.h"
#include "SceneEffectManager.h"
#include "BlockScene.h"
#include "ClientInfoProxy.h"
#include "Core/GameEngine.h"
#include "EcosysBigBuildBuilder.h"

#define SECTION_BLOCK_OPT

#include "ChunkGen_BasicStone.h"
#include "DynamicLightingManager.h"

#include "Platforms/PlatformInterface.h"
#include "ChunkGen_Empty.h"
#include "EcosysUnit_IsLandBuild.h"
#include "EcosysUnit_FishingVillageBuild.h"
#include "EcosysUnit_DesertVillage.h"
#include "MiniCraftRenderer.h"
#include "Vehicle/VehiclePhysics/OgreVehicleManager.h"
#include "Core/blocks/container_world.h"
#include "defdata.h"
#include "SandboxIdDef.h"
#include "IClientActor.h"
#include "DefManagerProxy.h"
#include "SandBoxManager.h"
#include "PlayManagerInterface.h"
#include "OgrePhysXManager.h"
#ifdef SANDBOX_CHUNK_STREAM_LOAD
#include "SandboxChunkIOMgr.h"
#endif

#if defined SANDBOX_DEV && defined SANDBOX_USE_PROFILE_LOGCOST
//#define SANDBOX_USE_PROFILE_CHUNKSYNCLOAD
#endif
#ifdef SANDBOX_USE_PROFILE_CHUNKSYNCLOAD
static int s_loadChunkIdx = 0;
#endif

#include "Gizmo/GizmoManager.h"
#include "Gizmo/GizmoRenderer.h"
#include "Gizmo/GizmoUtil.h"
#include "CameraManager.h"
#include "GameCamera.h"
#include "Input/InputManager.h"
#if defined(USE_OPTICK) && defined(WIN32)
//gcc不同版本，不一定有这个头文件，所以只处理win32
#include< chrono >
#endif
#include "chunk.h"
#include "BlockGeom.h"
#include "EcosysUnit_City.h"
#include "EcosysUnit_RoadBuild.h"

using namespace Rainbow;
using namespace MINIW;
using namespace MNSandbox;
//class ContactModifyCallback : public PxContactModifyCallback
//{
//public:
//	ContactModifyCallback() : PxContactModifyCallback()
//	{
//
//	}
//	~ContactModifyCallback() {}
//
//	void onContactModify(PxContactModifyPair* const pairs, UInt32 count)
//	{
////		struct UIContactModifyTask : public Task 
////		{
////			UIContactModifyTask(){};
////			~UIContactModifyTask(){}
////			void run() {
////				if (!GetWorldManagerPtr())
////					return;	
////				for (int i = 0; i<(int)m_pActors.size(); i++)
////				{
////					if (GetWorldManagerPtr()->isActorExist(m_pActors[i]))
////					{
////						IClientActor *actor = static_cast<IClientActor*>(m_pActors[i]);
////						ClientActorProjectile *projectile = dynamic_cast<ClientActorProjectile*>(actor);
////						if (projectile)
////						{
////							projectile->playSoundByPhysCollision();
////						}
////						else
////						{
////							ActorBall *ball = dynamic_cast<ActorBall*>(actor);
////							if (ball)
////							{
////								ball->playSoundByPhysCollision();
////							}
////							else 
////							{
////								ActorBasketBall *ball = dynamic_cast<ActorBasketBall*>(actor);
////								if (ball)
////								{
////									ball->playSoundByPhysCollision();
////								}
////							}
////						}
////					}
////				}
////			}
////			std::vector<IClientActor*> m_pActors;
////		};
////		UIContactModifyTask *task = ENG_NEW(UIContactModifyTask)();
////		for (UInt32 i = 0; i < count; i++)
////		{
////#ifdef _DEBUG
////			//LOG_INFO("kekeke onCCDContactModify count:%d", count);
////#endif
////			const PxShape** shapes = pairs[i].shape;
////			for(UInt32 j=0; j < 2; j++)
////			{
////				const PxShape *shape = shapes[j];
////				if (shape)
////				{
////					if (shape->userData)
////					task->m_pActors.push_back(static_cast<IClientActor*>(shape->userData));
////				}
////			}
////		}
////		if (task->m_pActors.size())
////			UIThreadTask::addTask(task);
////		else
////			ENG_DELETE(task); 
//	}
//
//private:
//
//};
//ContactModifyCallback gContactModifyCallback;
//
//class SimulationEventCallback : public PxSimulationEventCallback
//{
//public:
//	SimulationEventCallback() : PxSimulationEventCallback()
//	{
//
//	}
//	~SimulationEventCallback() {}
//
//	void onContact(const PxContactPairHeader& pairHeader, const PxContactPair* pairs, UInt32 nbPairs)
//	{
//		for(UInt32 i=0; i < nbPairs; i++)
//		{
//			const PxContactPair& cp = pairs[i];
//
//			PxShape* const* shapes = cp.shapes;
//			for(UInt32 j=0; j < 2; j++)
//			{
//				const PxShape *shape = shapes[j];
//				if (shape)
//				{
//					/*LOG_INFO("onContact shape: %s",  shape->getName());*/
//					IClientActor *actor = static_cast<IClientActor*>(shape->userData);
//					ActorVehicleAssemble *vehicle = dynamic_cast<ActorVehicleAssemble*>(actor);
//					if (vehicle)
//					{
//						PxContactStreamIterator iter(cp.contactPatches, cp.contactPoints, cp.getInternalFaceIndices(), cp.patchCount, cp.contactCount);
//
//						const float* impulses = cp.contactImpulses;
//
//						UInt32 flippedContacts = (cp.flags & PxContactPairFlag::eINTERNAL_CONTACTS_ARE_FLIPPED);
//						UInt32 hasImpulses = (cp.flags & PxContactPairFlag::eINTERNAL_HAS_IMPULSES);
//						UInt32 nbContacts = 0;
//
//						while(iter.hasNextPatch())
//						{
//							iter.nextPatch();
//							while(iter.hasNextContact())
//							{
//								iter.nextContact();
//								Vector3f point = Vec3FromPx(iter.getContactPoint());
//								Vector3f impulse = hasImpulses ? Vec3FromPx(iter.getContactNormal()) * impulses[nbContacts] : Vector3f(0.f);
//
//								//LOG_INFO("contact point: %f %f %f ", point.x, point.y, point.z);
//
//								UInt32 internalFaceIndex0 = flippedContacts ?
//									iter.getFaceIndex1() : iter.getFaceIndex0();
//								UInt32 internalFaceIndex1 = flippedContacts ?
//									iter.getFaceIndex0() : iter.getFaceIndex1();
//								//TODO
//
//								//PxTransform trans = PxShapeExt::getGlobalPose(*shape, *shape->getActor());
//								//Rainbow::Vector3f pos = Rainbow::Vector3f(trans.p.x, trans.p.y, trans.p.z);
//								if (j == 0)
//									vehicle->onContact(point, impulse, shapes[1]);
//								else
//									vehicle->onContact(point, impulse, shapes[0]);
//
//								nbContacts++;
//							}
//						}
//					}
//				}
//			}
//
//		}
//	}
//
//	virtual void onTrigger(PxTriggerPair* pairs, UInt32 count){}
//	virtual void onConstraintBreak(PxConstraintInfo*, UInt32) {}
//	virtual void onWake(PxActor** , UInt32 ) {}
//	virtual void onSleep(PxActor** , UInt32 ){}
//	virtual void onAdvance(const PxRigidBody*const*, const PxTransform*, const UInt32) {}
//
//private:
//
//};

//SimulationEventCallback gSimulationEventCallback;
 /*
struct UISimulationFilterTask : public Task {
	UISimulationFilterTask();
	void run() {
		if (!GetWorldManagerPtr())
			return;	
		for (int i = 0; m_pActors0.size(); i++)
		{
			if (GetWorldManagerPtr()->isActorExist(m_pActors0[i]) && GetWorldManagerPtr()->isActorExist(m_pActors1[i]))
			{
				IClientPlayer *player = NULL;
				ActorVehicleAssemble *vehicle = NULL;
				Rainbow::Vector3f pos0,pos1;
				//Rainbow::Quaternionf quat0,quat1;
				PxTransform trans0,trans1;
				Rainbow::Vector3f dir;
				if (m_pActors0[i] && m_pActors1[i])
				{
					trans0 = PxShapeExt::getGlobalPose(*s0, *s0->getActor());
					pos0 = Rainbow::Vector3f(trans0.p.x, trans0.p.y, trans0.p.z);
					//quat0 = Rainbow::Quaternionf(trans0.q.x, trans0.q.y, trans0.q.z, trans0.q.w);

					trans1 = PxShapeExt::getGlobalPose(*s1, *s1->getActor());
					pos1 = Rainbow::Vector3f(trans1.p.x, trans1.p.y, trans1.p.z);
					//quat1 = Rainbow::Quaternionf(trans1.q.x, trans1.q.y, trans1.q.z, trans1.q.w);

					if ( (actor0->getObjType() == OBJ_TYPE_ROLE) && (actor1->getObjType() == OBJ_TYPE_VEHICLE) )
					{
						player = dynamic_cast<IClientPlayer*>(actor0);
						vehicle = dynamic_cast<ActorVehicleAssemble*>(actor1);

						dir = pos1 - pos0;
					}
					else if ( (actor0->getObjType() == OBJ_TYPE_VEHICLE) && (actor1->getObjType() == OBJ_TYPE_ROLE) )
					{				
						vehicle = dynamic_cast<ActorVehicleAssemble*>(actor0);
						player = dynamic_cast<IClientPlayer*>(actor1);

						dir = pos0 - pos1;
					}
				}

				if (player && vehicle)
				{
					if (player->getSpringMove())
					{//这里是用在判断被弹簧弹动时，碰到施力弹簧不进行加力处理（游戏逻辑已经给力了）。如果出现判断错误，就再加上载具上的碰撞点是否弹簧
						player->setSpringMove(false);
						return ;
					}

					dir.y = 0;
					Rainbow::Normalize(dir);

					//PlayerLocoMotion *locoPlayer = static_cast<PlayerLocoMotion *>(player->getLocoMotion());
					//Vector3f playerLinearVelocity = locoPlayer->getRoleController()->getPxController()->getActor()->getLinearVelocity();
					//float v0 = playerLinearVelocity.dot(Vector3f(dir.x, dir.y, dir.z));

					VehicleAssembleLocoMotion *locoVehicle = static_cast<VehicleAssembleLocoMotion *>(vehicle->getLocoMotion());
					if (locoVehicle->m_Car)
					{
						Vector3f vehicleLinearVelocity = locoVehicle->m_Car->getRigidDynamicActor()->getLinearVelocity();
						float m1 = locoVehicle->m_Car->getRigidDynamicActor()->getMass();
						float v1 = vehicleLinearVelocity.dot(Vector3f(dir.x, dir.y, dir.z));

						float x = m1 * v1 * GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig.collide_role_ratio;;
						Rainbow::Vector3f force = dir * x;
						player->setMotionChange(force);

					}
				}
			}
		}
	}
	std::vector<IClientActor*> m_pActors0;
	std::vector<IClientActor*> m_pActors1;
}; */
//class SimulationFilterCallback : public PxSimulationFilterCallback
//{
//public:
//	SimulationFilterCallback() : PxSimulationFilterCallback()
//	{
//
//	}
//	~SimulationFilterCallback() {}
//
//	virtual		PxFilterFlags	pairFound(UInt32 pairID,
//		PxFilterObjectAttributes attributes0, PxFilterData filterData0, const PxActor* a0, const PxShape* s0,
//		PxFilterObjectAttributes attributes1, PxFilterData filterData1, const PxActor* a1, const PxShape* s1,
//		PxPairFlags& pairFlags)
//	{
//		IClientActor* actor0 = NULL;
//		IClientActor* actor1 = NULL;
//		if (s0)
//			actor0 = static_cast<IClientActor*>(s0->userData);
//		if (s1)
//			actor1 = static_cast<IClientActor*>(s1->userData);
//
//		IClientPlayer* player = NULL;
//		ActorVehicleAssemble* vehicle = NULL;
//		Rainbow::Vector3f pos0, pos1;
//		//Rainbow::Quaternionf quat0,quat1;
//		PxTransform trans0, trans1;
//		Rainbow::Vector3f dir;
//		if (actor0 && actor1)
//		{
//			trans0 = PxShapeExt::getGlobalPose(*s0, *s0->getActor());
//			pos0 = Rainbow::Vector3f(trans0.p.x, trans0.p.y, trans0.p.z);
//			//quat0 = Rainbow::Quaternionf(trans0.q.x, trans0.q.y, trans0.q.z, trans0.q.w);
//
//			trans1 = PxShapeExt::getGlobalPose(*s1, *s1->getActor());
//			pos1 = Rainbow::Vector3f(trans1.p.x, trans1.p.y, trans1.p.z);
//			//quat1 = Rainbow::Quaternionf(trans1.q.x, trans1.q.y, trans1.q.z, trans1.q.w);
//
//			if ((actor0->getObjType() == OBJ_TYPE_ROLE) && (actor1->getObjType() == OBJ_TYPE_VEHICLE))
//			{
//				player = dynamic_cast<IClientPlayer*>(actor0);
//				vehicle = dynamic_cast<ActorVehicleAssemble*>(actor1);
//
//				dir = pos1 - pos0;
//			}
//			else if ((actor0->getObjType() == OBJ_TYPE_VEHICLE) && (actor1->getObjType() == OBJ_TYPE_ROLE))
//			{
//				vehicle = dynamic_cast<ActorVehicleAssemble*>(actor0);
//				player = dynamic_cast<IClientPlayer*>(actor1);
//
//				dir = pos0 - pos1;
//			}
//		}
//
//		if (player && vehicle)
//		{
//			if (player->getSpringMove())
//			{//这里是用在判断被弹簧弹动时，碰到施力弹簧不进行加力处理（游戏逻辑已经给力了）。如果出现判断错误，就再加上载具上的碰撞点是否弹簧
//				player->setSpringMove(false);
//				return PxFilterFlag::eSUPPRESS;
//			}
//
//			dir.y = 0;
//			dir  = MINIW::Normalize(dir);
//
//			//PlayerLocoMotion *locoPlayer = static_cast<PlayerLocoMotion *>(player->getLocoMotion());
//			//Vector3f playerLinearVelocity = locoPlayer->getRoleController()->getPxController()->getActor()->getLinearVelocity();
//			//float v0 = playerLinearVelocity.dot(Vector3f(dir.x, dir.y, dir.z));
//
//			VehicleAssembleLocoMotion* locoVehicle = static_cast<VehicleAssembleLocoMotion*>(vehicle->getLocoMotion());
//			if (locoVehicle->m_Car)
//			{
//				Component* body = locoVehicle->m_Car->GetBody();
//				Rigidbody* rigidbody = static_cast<Rigidbody*>(body);
//				Vector3f vehicleLinearVelocity = Vector3f::zero;
//				float mass = 0;
//				if (rigidbody)
//				{
//					vehicleLinearVelocity = rigidbody->GetVelocity();
//					mass = rigidbody->GetMass();
//				}
//				else
//				{
//					ArticulationBody* articulationBody = static_cast<ArticulationBody*>(body);
//					if (articulationBody)
//					{
//
//						vehicleLinearVelocity = articulationBody->GetVelocity();
//						mass = articulationBody->GetMass();
//					}
//				}
//				float v1 = DotProduct(vehicleLinearVelocity, dir);
//
//				float x = mass * v1 * GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig.collide_role_ratio;;
//				Rainbow::Vector3f force = dir * x;
//				player->setMotionChange(force);
//
//				//float v = PxAbs(v1)-PxAbs(v0);
//				//if (v > 0)
//				//{
//				//	float x = m1*v/1000;
//				//	Rainbow::Vector3f force = dir * -x;
//				//	player->setMotionChange(force);
//				//}
//			}
//		}
//
//		return PxFilterFlag::eSUPPRESS;
//	}
//
//	virtual void pairLost(UInt32 pairID, PxFilterObjectAttributes attributes0, PxFilterData filterData0, PxFilterObjectAttributes attributes1, PxFilterData filterData1, bool objectRemoved) {}
//	virtual bool statusChange(UInt32& pairID, PxPairFlags& pairFlags, PxFilterFlags& filterFlags) { return false; }
//
//private:
//
//};
//SimulationFilterCallback gSimulationFilterCallback;



const int CHUNK_FREE_DELAY_TICK = 200; //10秒

void World::addTempViewChunk(const CHUNK_INDEX index, ChunkViewerList* viewer) 
{
	if (m_tmpChunkIndex)
		m_tmpChunkIndex->addTempViewChunk(index, viewer);
}
ChunkViewerList* World::findTempChunkViewer(const ChunkIndex& index) const
{
	if (m_tmpChunkIndex)
		return m_tmpChunkIndex->findTempChunkViewer(index);
	return nullptr;
}

void World::removeTempChunkViewer(const ChunkIndex& index)
{
	if (m_tmpChunkIndex)
		m_tmpChunkIndex->removeTempChunkViewer(index);
}

void World::clearTempChunk()
{
	if (m_tmpChunkIndex)
		m_tmpChunkIndex->clearTempChunk();
}

void World::resetTempChunkCenter(int cx, int cz)
{
	if (m_tmpChunkIndex)
		m_tmpChunkIndex->resetCenter(cx, cz);
}

void World::clear()
{
	if (m_CurChunkProvider) m_CurChunkProvider->stopThread(); //子线程先关掉 demon
	// 销毁回调
	for (auto iter = m_bindWorldPtrs.begin(); iter != m_bindWorldPtrs.end(); iter++)
	{
		iter->second();
	}
	
	if (m_bEnableUpdateGodTemple)
	{
		m_bEnableUpdateGodTemple = false;
		BigBuildCreater::getInstance()->clear(this);
		BigBuildCreater::DestroyInstance();
	}
	
#ifdef BUILD_MINI_EDITOR_APP
	std::for_each(m_vecNTChunk.begin(), m_vecNTChunk.end(), [&](NTCHUNK& ntChunk)->void {
		if ((ntChunk.pProvider) && (ntChunk.pProvider != m_CurChunkProvider))
		{
			OGRE_DELETE(ntChunk.pProvider);
		}
		});
#endif
	GetEcosysUnitIsLandBuild().clear(this);
	GetEcosysUnitFishingVillageBuild().clear(this);
	GetEcosysUnitDesertVillage().clear(this);
	waitAllChunkBuildMeshCompleted();
	clearChunk();

	ENG_DELETE(m_SceneEffectMgr);

	ENG_DELETE(m_Environ);

	ENG_DELETE(m_CurChunkProvider);

	ENG_DELETE(m_ActorMgr);

	ENG_DELETE(m_ContainerMgr);

	ENG_DELETE(m_BedMgr);

	ENG_DELETE(m_WorkbenchMgr);

	ENG_DELETE(m_BlockTickMgr);

	ENG_DELETE(m_MpActorMgr);

	ENG_DELETE(m_EffectMgr);

	ENG_DELETE(m_BuildMgr);

	ENG_DELETE(m_CityMgr);

	ENG_DELETE(m_pUIMgr);
	//场景管理最后删除，effect析构时候会从场景管理中移除
#if	GIZMO_DRAW_ENGABLE
	GetGizmoManager().UnregisterGizmoDraw(m_GizmoDrawHandle);
#endif
	
	if (m_scene)
	{
		m_scene->EndPlay();
		MNSandbox::GetSceneManager().DestroyScene(m_scene);
		m_scene = nullptr;
	}
	

	
#ifndef IWORLD_SERVER_BUILD
	ENG_DELETE(m_WorldRender);
#endif

	ENG_DELETE(m_pMonsterSiegeMgr);

	char physname[64];
	sprintf(physname, "phys_%d_%p", m_CurMapID, this);
	MINIW::PhysXManager::GetInstance().DeletePhysXScene(std::string(physname));

	m_PhysScene = NULL;
}

void World::clearChunk(bool clearPhysActor/* =false */)
{
	for (size_t i = 0; i < m_ChunkArray.size(); i++)
	{
		if(clearPhysActor)
			m_ChunkArray[i]->clearPhysActor();
		auto pchunk_save = m_ChunkArray[i];
		int x = BlockDivSection(m_ChunkArray[i]->m_Origin.x);
		int z = BlockDivSection(m_ChunkArray[i]->m_Origin.z);
		CHUNK_INDEX index(x, z);
		ENG_DELETE(m_ChunkArray[i]);
		auto ele = m_ViewChunks.find(index);
		if (ele != m_ViewChunks.end() && ele->second)
		{
			ele->second->setChunk(NULL);
		}
		if (m_pChunkCache == pchunk_save)
		{
			m_pChunkCache = NULL;
		}
	}
	m_ChunkArray.clear();

	for (auto itr = m_ViewChunks.begin(); itr != m_ViewChunks.end(); ++itr)
	{
		ENG_DELETE(itr->second);
	}
	m_ViewChunks.clear();
	clearTempChunk();
}

ChunkGenerator *World::createChunkProvider(int mapid, ChunkIndex startchunk, ChunkIndex endchunk)
{
	ChunkGenerator *provider  = NULL;

	if(mapid == 1)
	{
		provider = ENG_NEW(ChunkGenEarthCore)(this, m_RandomSeed[0], m_RandomSeed[1], startchunk, endchunk);
	}
	else if(mapid == 2)
	{
		provider = ENG_NEW(ChunkGenPlanetSpace)(this,true, m_RandomSeed[0], m_RandomSeed[1], startchunk, endchunk);
	}
	else if (mapid >= 2000 && mapid <= 4000)
	{
		//provider = ENG_NEW(ChunkGenPlanetSpace)(this, true, m_RandomSeed[0], m_RandomSeed[1], startchunk, endchunk);
		provider = ENG_NEW(ChunkGenEmpty)(this, m_RandomSeed[0], m_RandomSeed[1], startchunk, endchunk);
	}
	else
	{

		if (HOMEGARDEN_ZONE == m_TerrainType)
		{
			ChunkIndex minPos(-1, -1), maxPos(-1, 0);
			provider = ENG_NEW(ChunkGenCustomize)(this, startchunk, endchunk, minPos, maxPos, m_unSpecialType);
		}
		else if(m_TerrainType == TERRAIN_FLAT || m_TerrainType == TERRAIN_HARDSAND_FLAT || m_TerrainType == TERRAIN_WATER_FLAT || m_TerrainType == TERRAIN_EMPTY_FLAT) 
		{
			provider = ENG_NEW(ChunkGenFlat)(this, startchunk, endchunk, (TERRAIN_TYPE)m_TerrainType);
		}
		else if(m_TerrainType == TERRAIN_NORMAL) {
			provider = ENG_NEW(ChunkGenNormal)(this, true, m_RandomSeed[0], m_RandomSeed[1], startchunk, endchunk);
		}
		else if(m_TerrainType == TERRAIN_SOC) {
			provider = ENG_NEW(ChunkGenSOC)(this, true, m_RandomSeed[0], m_RandomSeed[1], startchunk, endchunk);
		}
		else if(m_TerrainType == TERRAIN_LIMITSIZE)
		{
			provider = ENG_NEW(ChunkGenNormal)(this, true, m_RandomSeed[0], m_RandomSeed[1], startchunk, endchunk);
		}
		else if(m_TerrainType == TERRAIN_AIRISLAND)
		{
			provider = ENG_NEW(ChunkGenAirIsland)(this, m_RandomSeed[0], m_RandomSeed[1], startchunk, endchunk);
		}
		else if (m_TerrainType == TERRAIN_BASICSTONG)
		{
			provider = ENG_NEW(ChunkGenBasicStone)(this, startchunk, endchunk);
		}
		else if (m_TerrainType == TERRAIN_PLANTSPACE_STONE) {
			provider = ENG_NEW(ChunkGenBasicStone)(this, startchunk, endchunk, BLOCK_PLANTSPACE_STONE);
		}
		else
		{
			//assert(0);
			provider = ENG_NEW(ChunkGenNormal)(this, true, m_RandomSeed[0], m_RandomSeed[1], startchunk, endchunk);
		}
	}
	provider->startThread();
	return provider;
}
void World::createEditor(const WorldCreateData& wcdt, unsigned short mapid, int owneruin)
{
	m_RandomSeed[0] = wcdt.randseed1;
	m_RandomSeed[1] = wcdt.randseed2;
	m_CurMapID = mapid;
	m_unSpecialType = 0;
	m_CurMapOwnerUin = owneruin;
	m_TerrainType = wcdt.terrtype;
	ChunkIndex  startchunk(INT_MIN / 16, INT_MIN / 16), endchunk(INT_MAX / 16, INT_MAX / 16);
	
	if (wcdt.xtiles > 0) NumTiles2StartEnd(startchunk.x, endchunk.x, wcdt.xtiles);
	if (wcdt.ztiles > 0) NumTiles2StartEnd(startchunk.z, endchunk.z, wcdt.ztiles);

	m_CurChunkProvider = createChunkProvider(m_CurMapID, startchunk, endchunk);
	m_EmptyBlock.setAll(m_CurChunkProvider->getEmptyBlockID());

	m_ContainerMgr = ENG_NEW(WorldContainerMgr)(this);
	m_BlockTickMgr = ENG_NEW(BlockTickMgr)(this);
	m_WorldRender = ENG_NEW(WorldRenderer)(this)
		;
	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("World_Game_Init", SandboxContext(nullptr).SetData_Usertype<World>("world", this));
	if (result.IsExecSuccessed())
	{
		m_BuildMgr = result.GetData_Usertype<BuildMgrInterface>("buildmgrInterface");
		m_CityMgr = result.GetData_Usertype<CityMgrInterface>("citymgrInterface");
	}
	else
	{
		Assert(false);
	}

	/* 场景 */
	m_scene = MNSandbox::GetSceneManager().CreateScene<WorldScene>(this);
	m_scene->Init();
}
void World::create(const WorldCreateData &wcdt, unsigned short mapid, int owneruin, int specialtype, bool is_own)
{	
	reportGameObjectStatistics(this, true);

	//增加打印
#if PLATFORM_WIN
	WarningStringMsg(">>>>> World create! map_id:%I64d, cur_mapid:%hu, special_type:%d , is_own:%d,owner_uid:%d", m_OWID, mapid,specialtype,is_own, owneruin);
#else
	WarningStringMsg(">>>>> World create! map_id:%lld, cur_mapid:%hu, special_type:%d , is_own:%d,owner_uid:%d", m_OWID, mapid, specialtype, is_own, owneruin);
#endif

	m_RandomSeed[0] = wcdt.randseed1;
	m_RandomSeed[1] = wcdt.randseed2;
	m_CurMapID = mapid;
	m_unSpecialType = specialtype;
	m_CurMapOwnerUin = owneruin;
	m_TerrainType = wcdt.terrtype;
	
	int terrtype = -1;
	MNSandbox::GetGlobalEvent().Emit<int &,long long, int>("OWorldList_getTerrainTypeByFile", terrtype,getOWID(), m_unSpecialType);
	if(terrtype != -1){
		m_TerrainType = terrtype;
	}
	// add by cloud for soc-game

	if (m_TerrainType != TERRAIN_FLAT && m_TerrainType != TERRAIN_HARDSAND_FLAT && 
		wcdt.xtiles != 0 && wcdt.ztiles != 0 && wcdt.xtiles < 1000 && wcdt.ztiles < 1000)
		m_TerrainType = TERRAIN_SOC;
	//m_RandomSeed[0] = 117;
	//m_RandomSeed[1] = 2257;
	m_mapInfoRefreshCenter->Reset();

	TerrainIDGenerator::m_BiomeBits = wcdt.biomes; // 扩充biomes到64位

	ChunkIndex  startchunk(INT_MIN/16,INT_MIN/16), endchunk(INT_MAX/16,INT_MAX/16);
	if (m_TerrainType == TERRAIN_EMPTY_FLAT)
	{	
		//玩家设置在中心点位置,高度为第七层上方
		WCoord pos(Rainbow::Vector3f(8 * BLOCK_SIZE + BLOCK_HALFSIZE,7 * BLOCK_SIZE,8 * BLOCK_SIZE + BLOCK_HALFSIZE));
		auto actor = dynamic_cast<IClientActor*>(GetIPlayerControl());
		if (actor)
			actor->setPosition(pos);

		//设置第一个道具为星团云瓶
		if (GetWorldManagerPtr() && GetWorldManagerPtr()->getGameMode() != OWTYPE_GAMEMAKER_RUN)
		{

			if (GetWorldManagerPtr()->getIsEmptyFlatCreated())
			{
				if (GetIPlayerControl() && 
					GetIPlayerControl()->getIPlayerControlBackPack() &&
						!GetIPlayerControl()->getIPlayerControlBackPack()->getGridItem(GetIPlayerControl()->getIPlayerControlBackPack()->getShortcutStartIndex() + 1))
				{
					GetIPlayerControl()->getIPlayerControlBackPack()->removeItem(GetIPlayerControl()->getIPlayerControlBackPack()->getShortcutStartIndex(),1);
				}
			}
			else
			{
				if(GetIPlayerControl() && GetIPlayerControl()->getIPlayerControlBackPack())
					GetIPlayerControl()->getIPlayerControlBackPack()->setItem(11095, 1000 ,1);
			}
		}
	}

	//if (GetWorldManagerPtr()->getGameMode() == OWTYPE_GAMEMAKER || GetWorldManagerPtr()->getGameMode() == OWTYPE_GAMEMAKER_RUN)
	{
		if (wcdt.xtiles > 0) NumTiles2StartEnd(startchunk.x, endchunk.x, wcdt.xtiles);
		if (wcdt.ztiles > 0) NumTiles2StartEnd(startchunk.z, endchunk.z, wcdt.ztiles);
	}

	m_CurChunkProvider = createChunkProvider(m_CurMapID, startchunk, endchunk);
#ifdef BUILD_MINI_EDITOR_APP
	m_CurChunkProvider->InsertTChunkInfo(TCHUNKINFO(startchunk, endchunk, wcdt.terrtype, wcdt.biomes));
#endif //BUILD_MINI_EDITOR_APP
	m_EmptyBlock.setAll(m_CurChunkProvider->getEmptyBlockID());
	
#ifdef BUILD_MINI_EDITOR_APP
	InsertNTChunk(NTCHUNK(m_CurChunkProvider, startchunk, endchunk, wcdt.terrtype, wcdt.biomes));
#endif

	if (m_CurChunkProvider->hasSky())
	{
		BlockLight::s_EmptyBlockLightAboveGround = BlockLight(15, 0);
		BlockLight::s_EmptyBlockLightUnderGround = BlockLight(0, 0);
	}
	else
	{
		BlockLight::s_EmptyBlockLightAboveGround = BlockLight(0, 0);
		BlockLight::s_EmptyBlockLightUnderGround = BlockLight(0, 0);
	}

	m_ContainerMgr = ENG_NEW(WorldContainerMgr)(this);
	m_ChestMgr = ENG_NEW(ChestManager)(this);
	m_BedMgr = ENG_NEW(SocBedMgr)();
	m_WorkbenchMgr = ENG_NEW(SocWorkbenchMgr)();
	m_pUIMgr = ENG_NEW(WorldUIMgr)();
	//m_ActorMgr = ENG_NEW(ActorManager)(this);
	//ActorManager的new操作改到业务种
	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("ActorManager_New", SandboxContext(nullptr).SetData_Usertype("worldptr", this));
	m_ActorMgr = result.IsExecSuccessed() ? result.GetData_Usertype<ActorManagerInterface>("actormanager_ptr") : nullptr;
	if (m_ActorMgr)
	{
		m_ActorMgr->setMobGen((wcdt.flags & 1) == 0, (wcdt.flags & 2) == 0);
	}
	

	m_BlockTickMgr = ENG_NEW(BlockTickMgr)(this);
	m_MpActorMgr = ENG_NEW(MpActorManager)(this);

	result = SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("World_Game_Init", SandboxContext(nullptr).SetData_Usertype<World>("world", this));
	if (result.IsExecSuccessed())
	{
		m_BuildMgr = result.GetData_Usertype<BuildMgrInterface>("buildmgrInterface");
		m_CityMgr = result.GetData_Usertype<CityMgrInterface>("citymgrInterface");
	}
	else
	{
		Assert(false);
	}
#ifdef IWORLD_SERVER_BUILD

	//节约流量使用： 跟踪距离
	const char* dis_char_ = GetClientInfoProxy()->getEnterParam("track_distance");
	if (strlen(dis_char_) > 0)
	{
		int dis_ = atoi(dis_char_ );
		m_MpActorMgr->setTrackDistance(dis_);
	}

#endif

	m_EffectMgr = GetISandboxActorSubsystem()->CreateNewEffectManager(this);
	m_Environ = ENG_NEW(Environment)(this);

	// 初始化动态光照管理器
	m_DynamicLightingMgr = ENG_NEW(DynamicLightingManager)(this);

	// #ifndef IWORLD_SERVER_BUILD
	m_WorldRender = ENG_NEW(WorldRenderer)(this, is_own);
	// #else
	// m_WorldRender = NULL;
	// #endif

	m_SceneEffectMgr = ENG_NEW(SceneEffectManager)(this);

#if	GIZMO_DRAW_ENGABLE
	m_GizmoDrawHandle = GetGizmoManager().RegisterGizmoDraw<World>(&World::OnDrawGizmo, this);
#endif
	//物理场景
	char physname[64];
	sprintf(physname, "phys_%d_%p", m_CurMapID, this);
	if (MINIW::PhysXManager::GetInstance().CreatePhysXScene(std::string(physname)) && GetWorldManagerPtr() && GetWorldManagerPtr()->m_SurviveGameConfig)
	{
		MINIW::VehicleManagerParam vehicleManagerParam;
		vehicleManagerParam.mUpdateMode = GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig.update_mode;
		vehicleManagerParam.mStaticFrictionNowheel = GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig.staticFriction_no_wheel;
		vehicleManagerParam.mDynamicFrictionNowheel = GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig.dynamicFriction_no_wheel;
		vehicleManagerParam.mRestitutionNowheel = GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig.restitution_no_wheel;
		vehicleManagerParam.mStaticFriction = GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig.staticFriction;
		vehicleManagerParam.mDynamicFriction = GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig.dynamicFriction;
		vehicleManagerParam.mRestitution = GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig.restitution; 

		m_PhysScene = MINIW::PhysXManager::GetInstance().GetPhysXScene(std::string(physname));
		m_PhysScene->CreateScene(-980.0f, vehicleManagerParam, false);
	}
	else
	{
		 m_PhysScene = MINIW::PhysXManager::GetInstance().GetPhysXScene(std::string(physname));
	}
	m_PhysScene->SetWorld(this);
	//temp codeby:crane 物理暂时屏蔽掉
	/*
#ifdef BUILD_MINI_EDITOR_APP
	auto editorRunMode = MNSandbox::SandBoxCfg::GetInstancePtr()->getRun();
	if (editorRunMode == MNSandbox::RunMode::editor_edit)
	{
		m_PhysScene->SetGravity(0.0f);
	}
	else 
#endif
	{

		m_PhysScene->SetGravity(g_WorldMgr->m_SurviveGameConfig->ballconfig.physXscene_gravity);
	}
	*/

	/* 场景 */
	m_scene = MNSandbox::GetSceneManager().CreateScene<WorldScene>(this);
	m_scene->Init();

#ifndef DEDICATED_SERVER
	//不在这个世界时，将camera设为false by：Jeff
	if (m_WorldRender && !is_own)
	{
		m_WorldRender->GetCamera()->SetEnable(false);
		//m_WorldRender->GetFrontCamera()->SetEnable(false);
	}
#endif

	if (m_CurMapID == 0)
	{
		m_pMonsterSiegeMgr = GetISandboxActorSubsystem()->CreateSummonMonsterSiegeMgr(this);// ENG_NEW(SummonMonsterSiegeMgr)(this);
	}
}

void World::LoadWorldScene()
{
	if (m_scene)
	{
		m_scene->Load();
	}
		
}

void World::SaveWorldScene(bool sync)
{
	if (!Config::GetSingleton().IsNeedSave()) // 不需要存档，跳过
		return;

	SANDBOXPROFILING_FUNC("Save Sandbox Scene");
	if (m_scene)
		m_scene->Save(sync);
}

void World::ReloadWorldScene()
{
	if (m_scene)
		m_scene->Reload();
}

void World::setEasyGuideTipsNeverShow(bool bNever)
{
	m_isNeverShowEasyModeTips = bNever;
}

bool World::IsEasyGuideTipsNeverShow()
{
	return m_isNeverShowEasyModeTips;
}

void World::setRemoteMode(bool isClientMode)
{
	m_isRemote = isClientMode;
}

int World::getTopUncoverBlock(int x, int z)
{
	int y = m_CurChunkProvider->getSpawnMinY();
	Chunk *pchunk = getChunk(x, z);
	if(pchunk == NULL) return BLOCK_UNLOAD;

	int dx = x - pchunk->m_Origin.x;
	int dz = z - pchunk->m_Origin.z;

	while( (g_BlockMtlMgr.getMaterial(pchunk->getBlockID(dx,y+1,dz)) != NULL) && !g_BlockMtlMgr.getMaterial(pchunk->getBlockID(dx,y+1,dz))->isAir())
	{
		y++;
	}
	if(y > 255) y = 64;
	return pchunk->getBlockID(dx,y,dz);
}

int World::getTopUncoverBlockWithY(int x, int& y, int z)
{
    y = m_CurChunkProvider->getSpawnMinY();
    Chunk* pchunk = getChunk(x, z);
    if (pchunk == NULL) return BLOCK_UNLOAD;

    int dx = x - pchunk->m_Origin.x;
    int dz = z - pchunk->m_Origin.z;
	BlockDef* def = NULL;
	int blockid = 0;
	do
	{
		y++;
		blockid = pchunk->getBlockID(dx, y, dz);
		def = GetDefManagerProxy()->getBlockDef(blockid);
	} while (def != NULL && def->MoveCollide != 0 && y < 255);
	y--;
	if (y >= 254) return 0;
	return pchunk->getBlockID(dx, y, dz);
}
/**
 * @return true 客机, false 主机
*/
bool World::isRemoteMode() const
{
	return m_isRemote || GAMERECORD_INTERFACE_EXEC(isRecordPlaying(), false);
}
	
bool World::onServer()
{
#ifdef IWORLD_SERVER_BUILD
	return true;
#else
	return !m_isRemote && !GAMERECORD_INTERFACE_EXEC(isRecordPlaying(), false);
#endif
}

WCoord World::createSpawnPoint()
{
	ChunkRandGen randgen;
	randgen.setSeed64(getRandomSeed());

	return m_CurChunkProvider->createSpawnPoint(randgen);
}

WCoord World::MakeSpawnPoint()
{
    ChunkRandGen randgen;
    randgen.setSeed64(getRandomSeed());

    return m_CurChunkProvider->MakeSpawnPoint(randgen);
}

WCoord World::getPortalPoint()
{
	return m_PortalPoint;
}

static void CalPortalPos(World *pworld, int dir, const WCoord &pos, const WCoord &origin, int &mindist, WCoord &retpos, int &retdir)
{
	for(int i = dir; i < dir + 4; ++i)
	{
		int stepx = i % 2;
		int stepz = 1 - stepx;

		if(i % 4 >= 2)
		{
			stepx = -stepx;
			stepz = -stepz;
		}

		for(int a = 0; a < 3; ++a)
		{
			for(int b = 0; b < 4; ++b)
			{
				for(int y = -1; y < 4; ++y)
				{
					WCoord curblock = pos;
					curblock.x += (b - 1) * stepx + a * stepz;
					curblock.y += y;
					curblock.z += (b - 1) * stepz - a * stepx;

					if(y < 0 && !pworld->getBlockMaterial(curblock)->isSolid() || y >= 0 && !pworld->isAirBlock(curblock))
					{
						return;
					}
				}
			}
		}

		WCoord dp = pos - origin;
		int lsq = (int)dp.lengthSquared();

		if(mindist<0 || lsq<mindist)
		{
			retpos = pos;
			mindist = lsq;
			retdir = i % 4;
		}
	}
}

static void CalPortalPos2(World *pworld, int dir, const WCoord &pos, const WCoord &origin, int &mindist, WCoord &retpos, int &retdir)
{
	for(int i = dir; i < dir + 2; ++i)
	{
		int stepx = i % 2;
		int stepz = 1 - stepx;

		for(int a = 0; a < 4; ++a)
		{
			for(int y = -1; y < 4; ++y)
			{
				WCoord curpos = pos;
				curpos.x += (a - 1) * stepx;
				curpos.y += y;
				curpos.z += (a - 1) * stepz;

				if(y < 0 && !pworld->getBlockMaterial(curpos)->isSolid() || y >= 0 && !pworld->isAirBlock(curpos))
				{
					return;
				}
			}
		}

		WCoord dp = pos - origin;
		int lsq = (int)dp.lengthSquared();

		if(mindist<0 || lsq<mindist)
		{
			retpos = pos;
			mindist = lsq;
			retdir = i % 2;
		}
	}
}

void World::resetPortalPoint(const WCoord &blockpos)
{
	m_PortalPoint = blockpos;
}

void World::createPortal(const WCoord &blockpos)
{
	int checkrange = 16;
	WCoord retpos = blockpos;
	int retdir = 0;
	int dir = GenRandomInt(4);
	int mindist = -1;

	int miny = m_CurChunkProvider->getSpawnMinY();
	int maxy = m_CurChunkProvider->getActualHeight()/2;

	for(int x = blockpos.x - checkrange; x <= blockpos.x + checkrange; ++x)
	{
		for(int z = blockpos.z - checkrange; z <= blockpos.z + checkrange; ++z)
		{
			for(int y = maxy; y >= miny; --y)
			{
				if(isAirBlock(x,y,z))
				{
					while(y>miny && isAirBlock(x,y-1,z))
					{
						--y;
					}

					CalPortalPos(this, dir, WCoord(x,y,z), blockpos, mindist, retpos, retdir);
				}
			}
		}
	}

	if(mindist < 0)
	{
		for(int x = blockpos.x - checkrange; x <= blockpos.x + checkrange; ++x)
		{
			for(int z = blockpos.z - checkrange; z <= blockpos.z + checkrange; ++z)
			{
				for(int y = maxy; y >= miny; --y)
				{
					if(isAirBlock(x,y,z))
					{
						while(y>miny && isAirBlock(x,y-1,z))
						{
							--y;
						}

						CalPortalPos2(this, dir, WCoord(x,y,z), blockpos, mindist, retpos, retdir);
					}
				}
			}
		}
	}

	WCoord retpos_bak = retpos;
	int stepx = retdir % 2;
	int stepz = 1 - stepx;

	if(retdir % 4 >= 2)
	{
		stepx = -stepx;
		stepz = -stepz;
	}

	if(mindist < 0)
	{
		/*
		if(retpos.y < 70)
		{
			retpos.y = 70;
		}

		if(retpos.y > m_CurChunkProvider->getActualHeight() - 10)
		{
			retpos.y = m_CurChunkProvider->getActualHeight() - 10;
		}

		for(int a = -1; a <= 1; ++a)
		{
			for(int b = 1; b < 3; ++b)
			{
				for(int c = -1; c < 3; ++c)
				{
					WCoord pos = retpos_bak;
					pos.x += (b - 1) * stepx + a * stepz;
					pos.y += c;
					pos.z += (b - 1) * stepz - a * stepx;
					setBlockAll(pos, c<0 ? BLOCK_PORTAL_FRAME : 0, 0);
				}
			}
		}*/
	}

	syncLoadChunk(retpos_bak, 5);

	if(GetClientInfoProxy()->isGMMode())
	{
		ChunkRandGen randgen;
		MainWorldProxy worldProxy(this);
		m_CurChunkProvider->getModelGen("voxel/portal.vox")->addToWorld(&worldProxy, randgen, retpos_bak);
	}
	
	saveChunkRange(retpos_bak, 5);

	/*
	{
		for(int x = 0; x < 4; ++x)
		{
			for(int y = -1; y < 4; ++y)
			{
				WCoord pos = retpos_bak;
				pos.x += (x - 1) * stepx;
				pos.y += y;
				pos.z += (x - 1) * stepz;
				bool isframe = (x == 0 || x == 3 || y == -1 || y == 3);
				setBlockAll(pos, isframe ? BLOCK_PORTAL_FRAME : BLOCK_PORTAL, 0, 2);
			}
		}

		for(int x = 0; x < 4; ++x)
		{
			for(int y = -1; y < 4; ++y)
			{
				WCoord pos = retpos_bak;
				pos.x += (x - 1) * stepx;
				pos.y += y;
				pos.z += (x - 1) * stepz;
				notifyBlocksOfNeighborChange(pos, getBlockID(pos));
			}
		}
	}*/

	m_PortalPoint = retpos_bak + WCoord(0,1,0);
}

void MergeChunkLightingBox(WCoord &minpos, WCoord &maxpos, Chunk *pchunk, bool init)
{
	WCoord minpos2 = pchunk->m_Origin - WCoord(SUN_LIGHT-1, 0, SUN_LIGHT-1);
	WCoord maxpos2 = pchunk->m_Origin + WCoord(CHUNK_BLOCK_X+SUN_LIGHT-2, CHUNK_BLOCK_Y, CHUNK_BLOCK_Z+SUN_LIGHT-2);
	if(init)
	{
		minpos = minpos2;
		maxpos = maxpos2;
	}
	else
	{
		minpos = Min(minpos, minpos2);
		maxpos = Max(maxpos, maxpos2);
	}
}

bool World::addChunk(Chunk *pchunk)
{
	//unsigned int t1 = Rainbow::Timer::getSystemTick();

	assert(pchunk->m_MapID == m_CurMapID);
	
	int x = BlockDivSection(pchunk->m_Origin.x);
	int z = BlockDivSection(pchunk->m_Origin.z);
	CHUNK_INDEX index(x, z);

	auto ele = m_ViewChunks.find(index);
	if(ele == m_ViewChunks.end())
	{
		//LOG_INFO("addChunk-delete 1 (%d, %d)", index.x, index.z);
		ENG_DELETE(pchunk);
		return false;
	}

	ChunkViewerList *watcher = ele->second;
	//assert(watcher->getChunk() == NULL);
	if(watcher->getChunk())
	{
		//LOG_INFO("addChunk-delete 2 (%d, %d)", index.x, index.z);
		ENG_DELETE(pchunk);
		return false;
	}
	watcher->setChunk(pchunk);

	m_ChunkArray.push_back(pchunk);
	clearCacheBlock();

	//clear empty chunk phys
	std::map<ChunkIndex, Rainbow::RigidStaticActor *>::iterator iter = m_EmptyChunkPhys.find(index);
	if(iter != m_EmptyChunkPhys.end())
	{
		m_PhysScene->DeleteRigidActor(iter->second);
		m_EmptyChunkPhys.erase(iter);
	}
	auto wallIter = m_EmptyChunkPhysWall.begin();
	for (; wallIter != m_EmptyChunkPhysWall.end();)
	{
		CHUNK_INDEX wallIndex(CoordDivSection(wallIter->first.x), CoordDivSection(wallIter->first.z));
		if (wallIndex == index)
		{
			m_PhysScene->DeleteRigidActor(wallIter->second);
			wallIter = m_EmptyChunkPhysWall.erase(wallIter);
		}
		else
			wallIter++;
	}

	pchunk->onEnterWorld(this);
	if (m_WorldMgr && m_WorldMgr->GetHideAllChunks())
	{
		pchunk->SetHide(true);
	}
	jsonxx::Object resObj;
	resObj << "mapid" << getCurMapID();
	resObj << "cx" << index.x;
	resObj << "cz" << index.z;
	std::string jsonStr = resObj.json();
	GetSandBoxManagerPtr()->DoEvent(SandBoxMgrEventID::EVENT_NEW_CHUNK_LOADED, 0, 0, const_cast<char*>(jsonStr.c_str()), jsonStr.length());
	//unsigned int t2 = Rainbow::Timer::getSystemTick();
	//LOG_INFO("@ addChunk time = %d", t2 - t1);

	return true;
}

bool World::addChunkFromServer(Chunk *pchunk, ChunkViewer *player)
{
	assert(pchunk->m_MapID == m_CurMapID);

	int x = BlockDivSection(pchunk->m_Origin.x);
	int z = BlockDivSection(pchunk->m_Origin.z);
	CHUNK_INDEX index(x, z);

	assert(m_ViewChunks.find(index) == m_ViewChunks.end());

	ChunkViewerList *watcher = ENG_NEW(ChunkViewerList)();
#ifndef DEDICATED_SERVER
	m_WatcherLock.WriteLock();
#endif
	watcher->setChunk(pchunk);
	m_ViewChunks[index] = watcher;
	addTempViewChunk(index, watcher);
	//m_ViewChunks.insert(std::make_pair(index, watcher));
#ifndef DEDICATED_SERVER
	m_WatcherLock.WriteUnlock();
#endif

	m_ChunkArray.push_back(pchunk);
	clearCacheBlock();

	pchunk->onEnterWorld(this);

	return true;
}

static void SetChunksIntegraCounter(World *pworld, int cx1, int cz1, int cx2, int cz2, unsigned int counter)
{
	for(int z=cz1; z<=cz2; z++)
	{
		for(int x=cx1; x<=cx2; x++)
		{
			Chunk *pchunk = pworld->getChunk(ChunkIndex(x,z));
			if(pchunk) pchunk->m_SaveIntegralCounter = counter;
		}
	}
}
void World::addIntegralSave(int cx1, int cz1, int cx2, int cz2)
{
	m_SaveIntegralCounter++;
	if(m_SaveIntegralCounter == 0) m_SaveIntegralCounter = 1;

	for(int z=cz1; z<=cz2; z++)
	{
		for(int x=cx1; x<=cx2; x++)
		{
			Chunk *pchunk = getChunk(ChunkIndex(x,z));
			if(pchunk)
			{
				if(pchunk->m_SaveIntegralCounter > 0) //用这个counter以便跟相邻的一起存储
				{
					SetChunksIntegraCounter(this, cx1, cz1, cx2, cz2, pchunk->m_SaveIntegralCounter);
					return;
				}
				
				pchunk->m_SaveIntegralCounter = m_SaveIntegralCounter;
			}
		}
	}
}

unsigned int World::DoTaskAfterLoadedChunk(std::vector<ChunkIndex>& pIndexs, std::function<void(unsigned int)> pTask)
{
	return m_tickLoadedChunkToDo->DoTask(pIndexs, pTask);
}

void World::CancelTaskAfterLoadedChunk(unsigned int pId)
{
	m_tickLoadedChunkToDo->CancelTask(pId);
}

bool World::IsUGCEditMode()
{
	if (m_WorldMgr)
		return m_WorldMgr->isUGCEditMode();

	return false;
}

int World::saveChunks(bool saveall)
{
	if(GetWorldManagerPtr() == NULL) return 0;
	if(!GetWorldManagerPtr()->needSave(NEEDSAVE_CHUNKS)) return 0;

	unsigned int icounter = 0;
	int savednum = 0;

	unsigned int nchunk = m_ChunkArray.size();
	UInt64 tickStartTime = GetTimeMS();
	for(unsigned int i=0; i<nchunk; i++)
	{
		Chunk *pchunk = m_ChunkArray[m_SaveChunkStartIndex%nchunk];
		m_SaveChunkStartIndex++;
		if (pchunk->needSaveActor(saveall))
		{
			saveActor(pchunk);
		}
		if(pchunk->needSave(saveall))
		{
			if(!saveall && pchunk->m_SaveIntegralCounter > 0 && icounter==0)
			{
				icounter = pchunk->m_SaveIntegralCounter;
			}

			saveChunk(pchunk);
			pchunk->m_SaveIntegralCounter = 0;
			savednum++;
			if (!saveall && (savednum > 2 || GetTimeMS() - tickStartTime > 8)) break;
		}
	}

	if(icounter > 0) //需要整体保存的
	{
		for(size_t i=0; i<m_ChunkArray.size(); i++)
		{
			Chunk *pchunk = m_ChunkArray[i];
			if(pchunk->m_SaveIntegralCounter == icounter)
			{
				if(pchunk->m_Dirty)
				{
					saveChunk(pchunk);
					savednum++;
				}
				pchunk->m_SaveIntegralCounter = 0;
			}
		}
	}

	return savednum;
}

void World::saveChunkRange(const WCoord &blockpos, int range)
{
	if(!GetWorldManagerPtr()->needSave(NEEDSAVE_CHUNKS)) return;

	int minx = BlockDivSection(blockpos.x-range);
	int maxx = BlockDivSection(blockpos.x+range);
	int minz = BlockDivSection(blockpos.z-range);
	int maxz = BlockDivSection(blockpos.z+range);

	for(int z=minz; z<=maxz; z++)
	{
		for(int x=minx; x<=maxx; x++)
		{
			Chunk *pchunk = getChunkBySCoord(x, z);
			if(pchunk && pchunk->needSave(true)) saveChunk(pchunk);
		}
	}
}

void World::SetEngineCurrentScene()
{
	/*for (auto itr = m_ViewChunks.begin(); itr != m_ViewChunks.end(); ++itr)
	{
		if (itr->second->getChunk())
		{
			Chunk* pchunk = getChunk(itr->first);
			if (pchunk)
				pchunk->CreateRenderSection();
		}
	}*/

	this->m_WorldRender->SetEngineCurrentScene();
}

void World::FreeOldWorld()
{
	m_pChunkCache = NULL;
#define NEED_DESTROY_OLD_SCENE 0
#if NEED_DESTROY_OLD_SCENE

	// free old chunk
	FreeOldChunk();

	// free old effect and actor
	getEffectMgr()->FreeOldAllEffect();
	//getActorMgr()->FreeOldAllActors();

	// free old world render and scene
	auto r = getRender();
	ENG_DELETE(r);
	SetWorldRenderer(nullptr);

	g_BlockMtlMgr.clearOnEndGame();	
#else
	//FreeRenderSection();

	waitAllChunkBuildMeshCompleted();
#ifndef DEDICATED_SERVER
	getRender()->GetCamera()->SetEnable(false);
	//getRender()->GetFrontCamera()->SetEnable(false);
#endif
#endif
}

void World::FreeOldChunk()
{
	for (auto itr = m_ViewChunks.begin(); itr != m_ViewChunks.end(); ++itr)
	{
		if (itr->second->getChunk())
		{
			removeChunk(itr->first);
		}
	}
	m_ViewChunks.clear();
}

void World::FreeRenderSection()
{
	for (auto itr = m_ViewChunks.begin(); itr != m_ViewChunks.end(); ++itr)
	{
		if (itr->second->getChunk())
		{
			Chunk* pchunk = getChunk(itr->first);
			if (pchunk)
				pchunk->FreeRenderSection();
		}
	}
}

void World::saveChunk(Chunk *pchunk, bool savecache)
{
	if (!GetWorldManagerPtr() || !GetWorldManagerPtr()->m_ChunkIOMgr)  return;
	CHUNKSAVEDB *savedb = pchunk->saveDBBuffer();
	if(savedb)
	{
		ChunkIOCmd cmd;
		memset(&cmd, 0, sizeof(cmd));
		cmd.cx = BlockDivSection(pchunk->m_Origin.x);
		cmd.cz = BlockDivSection(pchunk->m_Origin.z);
		cmd.data = savedb;
		cmd.datalen = 0;
		cmd.mapid = getCurMapID();

#ifdef DEDICATED_SERVER
		if (NORMAL_WORLD == m_unSpecialType)
		{
			if (GetZmqMgrInterface() && GetZmqMgrInterface()->IsNeedSaveChunk())
			{
				int uin = GetClientInfoProxy()->getUin();
#if CHUNK_COMPRESS_INIOTHREAD
				//压缩失败
				CHUNKSAVEDB* dbnew = GetWorldManagerPtr()->m_ChunkIOMgr->GenCompressChunkBlob(savedb);
				if (!dbnew)
				{
					pchunk->m_hasNoSaveData = false;
					pchunk->m_Dirty = false;
					pchunk->m_LastSaveTick = m_CurWorldTick;
					free(cmd.data);
					return;
				}
				int ret = GetZmqMgrInterface()->SaveChunkToDataServer(uin, m_OWID, cmd.mapid, cmd.cx, cmd.cz, dbnew, cmd.datalen);
				free(dbnew);
				if (ret == 0)
				{
					pchunk->m_hasNoSaveData = false;
					pchunk->m_Dirty = false;
					pchunk->m_LastSaveTick = m_CurWorldTick;
					free(cmd.data);
					return;
				}
#else

				int ret = GetZmqMgrInterface()->SaveChunkToDataServer(uin, m_OWID, cmd.mapid, cmd.cx, cmd.cz, cmd.data, cmd.datalen);
				if (ret == 0)
				{
					pchunk->m_hasNoSaveData = false;
					pchunk->m_Dirty = false;
					pchunk->m_LastSaveTick = m_CurWorldTick;
					free(savedb);
					return;
				}
#endif

			}
		}
#endif

		if (savecache)
		{
			cmd.cmdtype = CIOCMD_SAVE_HOST_CACHE_CHUNK;
		}
		else
		{
			cmd.cmdtype = CIOCMD_SAVECHUNK;
			if (HOME_GARDEN_WORLD == m_unSpecialType)
			{
				//1--HOMELAND_REGION_TYPE_SELF_BUILD 自建区域
				SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().
					Emit("Homeland_IsChunkType", SandboxContext(nullptr).
						SetData_Number("type", 1).
						SetData_Number("chunkx", cmd.cx).
						SetData_Number("chunkz", cmd.cz));
				if (result.IsSuccessed())
				{
					cmd.ismustloadcustomize = false;
				}
				else
				{
					//比较严重的内存泄露，这里需要free掉
					free(savedb);
					return;
				}
				cmd.cmdtype = CIOCMD_SAVECHUNK_HOMEGARDEN;
			}
			else if (HOME_GARDEN_CUSTOMIZE_WORLD == m_unSpecialType)
			{
				strcpy(cmd.path, "singlesave");
			}
		}
		GetWorldManagerPtr()->m_ChunkIOMgr->pushCmd(cmd);
	}
	pchunk->m_hasNoSaveData = false;
	pchunk->m_Dirty = false;
	pchunk->m_LastSaveTick = m_CurWorldTick;
}

void World::saveActor(Chunk* pchunk, bool savecache)
{
	if (!GetWorldManagerPtr() || !GetWorldManagerPtr()->m_ChunkIOMgr)  return;

	int length;
	void* buff = pchunk->GetActorFBS(length);

	ChunkIOCmd cmd;
	memset(&cmd, 0, sizeof(cmd));
	cmd.cx = BlockDivSection(pchunk->m_Origin.x);
	cmd.cz = BlockDivSection(pchunk->m_Origin.z);
	cmd.data = buff;
	cmd.datalen = length;
	cmd.mapid = getCurMapID();

#ifdef DEDICATED_SERVER
	if (NORMAL_WORLD == m_unSpecialType)
	{
		if (GetZmqMgrInterface() && GetZmqMgrInterface()->IsNeedSaveChunk())
		{
			int uin = GetClientInfoProxy()->getUin();
			int ret = GetZmqMgrInterface()->SaveActorToDataServer(uin, m_OWID, cmd.mapid, cmd.cx, cmd.cz, cmd.data, cmd.datalen);
			if (ret == 0)
			{
				pchunk->m_LastActorSaveTick = m_CurWorldTick;
				pchunk->m_ActorDirty = false;
			}
			free(cmd.data);
			return;
		}
	}
#endif

	if (savecache) {
		cmd.cmdtype = CIOCMD_SAVE_HOST_CACHE_CHUNK_ACTOR;
	}
	else {
		cmd.cmdtype = CIOCMD_SAVECHUNK_ACTOR;
		if (HOME_GARDEN_WORLD == m_unSpecialType)
		{
			//1--HOMELAND_REGION_TYPE_SELF_BUILD 自建区域
			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().
				Emit("Homeland_IsChunkType", SandboxContext(nullptr).
					SetData_Number("type", 1).
					SetData_Number("chunkx", cmd.cx).
					SetData_Number("chunkz", cmd.cz));
			if (result.IsSuccessed())
			{
				cmd.cmdtype = CIOCMD_SAVECHUNK_HOMEGARDEN_ACTOR;//自建区才需要保存
			}
			else
			{
				free(cmd.data);
				return;
			}
		}
	}

	GetWorldManagerPtr()->m_ChunkIOMgr->pushCmd(cmd);
	pchunk->m_LastActorSaveTick = m_CurWorldTick;
	pchunk->m_ActorDirty = false;
}


static void CheckChunkLightSource(World *pworld, Chunk *pchunk, std::vector<int>&lightsrc)
{
	for(size_t i=0; i<lightsrc.size(); i++)
	{
		int v = lightsrc[i];
		int y = v >> 8;
		int z = (v >> 4) & 15;
		int x = v & 15;

		WCoord blockpos = pchunk->m_Origin + WCoord(x,y,z);
		pworld->blockLightingChange(1, blockpos);
	}
}

bool World::doOnePopulate(int chunkx, int chunkz)
{
	Chunk *pchunk = getChunkBySCoord(chunkx, chunkz);
	if(pchunk && !pchunk->m_Populated && chunkExist(chunkx+1,chunkz) && chunkExist(chunkx,chunkz+1) && chunkExist(chunkx+1,chunkz+1))
	{
		m_CurChunkProvider->initEcosysData(chunkx, chunkz);
		pchunk->m_Populated = true;
		pchunk->m_Dirty = true;
		if(!isRemoteMode() && GetWorldManagerPtr()->isGameMakerRunMode())
		{
			GetWorldManagerPtr()->m_RuleMgr->CallEventScript(WES_ON_CHUNKGEN, (void *)long(chunkx), (void *)long(chunkz));
		}
		return true;
	}
	else return false;
}

void World::populateChunk(Chunk *pchunk)
{
	int chunkx = BlockDivSection(pchunk->m_Origin.x);
	int chunkz = BlockDivSection(pchunk->m_Origin.z);

	int count = 0;

	if(doOnePopulate(chunkx,   chunkz)) count++;
	if(doOnePopulate(chunkx-1, chunkz)) count++;
	if(doOnePopulate(chunkx,   chunkz-1)) count++;
	if(doOnePopulate(chunkx-1, chunkz-1)) count++;

	for(int z=-1; z<=1; z++)
	{
		for(int x=-1; x<=1; x++)
		{
			Chunk *pcur = getChunkBySCoord(chunkx+x, chunkz+z);
			if(pcur==NULL || pcur->m_SuppendLightSrc.empty()) continue;

			bool cancallight = true;
			for(int z1=-1; z1<=1; z1++)
			{
				for(int x1=-1; x1<=1; x1++)
				{
					if(!chunkExist(chunkx+x+x1, chunkz+z+z1))
					{
						cancallight = false;
						break;
					}
				}
			}

			if(cancallight)
			{
				CheckChunkLightSource(this, pcur, pcur->m_SuppendLightSrc);
				pcur->m_SuppendLightSrc.clear();
			}
		}
	}

	if (GetIPlayerControl())
	{
		CHUNK_INDEX index(chunkx, chunkz);
		GetIPlayerControl()->OnLoadChunk(index);
		getChunkProvider()->OnGenChunk(index);

		if (isSOCCreateMap())
		{
			float percent = getChunkProvider()->GetGenPercent();
			if (percent > 0.99)
			{
				GetEcosysUnitCityBuild().onWorldLoaded(this);
			}
		}
	}
}
//#define USE_SECTION_MESH_PHYS
#ifdef USE_SECTION_MESH_PHYS
void World::updateSectionPhysics(int sx, int sy, int sz, bool checkMeshCount /* = false */, int emptyWallY/* =-1 */, bool needCreateEmptyChunkPhys/* =true */)
{
    OPTICK_CATEGORY(OPTICK_FUNC, Optick::Category::Physics);
	Chunk *pchunk = getChunkBySCoord(sx, sz);
	if(pchunk == NULL)
	{
		ChunkIndex ci(sx,sz);
		std::map<CHUNK_INDEX, Rainbow::RigidStaticActor *>::iterator iter = m_EmptyChunkPhys.find(ci);
		if(iter != m_EmptyChunkPhys.end()) return;

		WCoord center(sx*SECTION_SIZE+SECTION_SIZE/2, 8*SECTION_SIZE, sz*SECTION_SIZE+SECTION_SIZE/2);
		WCoord halfexten(SECTION_SIZE/2, 16*SECTION_SIZE, SECTION_SIZE/2);
		Rainbow::RigidStaticActor *rigidactor = m_PhysScene->AddRigidStaticActor(center.toVector3(), Rainbow::Quaternionf::identity, halfexten.toVector3(), 0.8f, 0.8f, 0.8f);

		m_EmptyChunkPhys[ci] = rigidactor;
		return;
	}
	if(sy<0 || sy>=CHUNK_SECTION_DIM) return;

	Section *psection = pchunk->getIthSection(sy);
	if(!psection->m_PhyMeshInvalid) return;

	if(psection->m_PhysActor)
	{
		m_PhysScene->DeleteRigidActor(psection->m_PhysActor);
		psection->m_PhysActor = NULL;
	}

	if(psection->m_PhysActor == NULL)
	{
		WCoord p1(sx*SECTION_SIZE, 0, sz*SECTION_SIZE);
		std::vector<Rainbow::Vector3f>verts;
		std::vector<unsigned short>indices;
		psection->createPhysMesh(verts, indices);

		if(indices.size() > 0)
			psection->m_PhysActor = m_PhysScene->AddRigidStaticActor(p1.toVector3(), Rainbow::Quaternionf::identity, &verts[0], verts.size(), &indices[0], indices.size()/3, false, GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.section_static_friciton, GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.section_dynamic_friciton, GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.section_restitution);
	}

	psection->m_PhyMeshInvalid = false;
}
#else
void World::updateSectionPhysics(int sx, int sy, int sz, bool checkMeshCount /* = false */, int emptyWallY/* =-1 */, bool needCreateEmptyChunkPhys/* =true */)
{
    OPTICK_CATEGORY(OPTICK_FUNC, Optick::Category::Physics);
	// 检查地图是否开启 玩家物理动态刚体
	bool bPlayerRigidBody = false;
	if (!MNSandbox::Config::GetSingleton().IsSandboxMode())
	{
		if (g_WorldMgr && g_WorldMgr->m_RuleMgr)
		{
			float val = 0.f;
			int optid = 0;
			g_WorldMgr->m_RuleMgr->getRuleOptionID(GAMEMAKER_RULE::GMRULE_PLAYERPHYSTYPE, optid, val);
			if (optid == 2)
			{
				bPlayerRigidBody = true;
			}
		}
	}

	Chunk *pchunk = getChunkBySCoord(sx, sz);
	if (pchunk == NULL)
	{
		if (!needCreateEmptyChunkPhys)
			return;

		if (emptyWallY == -1)
		{
			ChunkIndex ci(sx, sz);
			std::map<CHUNK_INDEX, Rainbow::RigidStaticActor*>::iterator iter = m_EmptyChunkPhys.find(ci);
			if (iter != m_EmptyChunkPhys.end()) return;

			if (m_PhysScene)
			{
#ifdef IWORLD_SERVER_BUILD			
				if (!getScene())
					return;
#endif			
				/*WCoord center(sx*SECTION_SIZE+SECTION_SIZE/2, 8*SECTION_SIZE, sz*SECTION_SIZE+SECTION_SIZE/2);
				WCoord halfexten(SECTION_SIZE/2, 16*SECTION_SIZE, SECTION_SIZE/2);*/
				WCoord center(sx * SECTION_SIZE + SECTION_SIZE / 2, BLOCK_SIZE / 2, sz * SECTION_SIZE + SECTION_SIZE / 2);
				WCoord halfexten(SECTION_SIZE / 2, 1 * BLOCK_SIZE, SECTION_SIZE / 2);
				Rainbow::RigidStaticActor* rigidactor = m_PhysScene->AddRigidStaticActor(center.toVector3(), Rainbow::Quaternionf::identity, halfexten.toVector3(), 0.8f, 0.8f, 0.8f);
				getScene()->AddGameObject(rigidactor->GetGameObject());
				//Rainbow::RigidStaticActor *rigidactor = m_PhysScene->AddRigidStaticActor(center.toVector3(), Rainbow::Quaternionf::identity, halfexten.toVector3(), 0.5f, 0.5f, 0.2f);
				m_EmptyChunkPhys[ci] = rigidactor;
			}
		}
		else
		{
			int offset[4][2] = { {0,-1}, {0, 1},{-1, 0}, {1, 0} };
			for (int i = 0; i < 4; i++)
			{
				int newSX = sx + offset[i][0];
				int newSZ = sz + offset[i][1];
				Chunk* pNearbyChunk = getChunkBySCoord(newSX, newSZ);
				if (pNearbyChunk)
				{
					int posx = newSX * SECTION_SIZE;
					int posz = newSZ * SECTION_SIZE;
					int halfextendx, halfextendz;
					int posy = (emptyWallY + 0.5) * SECTION_SIZE;

					if (offset[i][0] == 0)
					{
						posx = posx + SECTION_SIZE / 2;

						if (offset[i][1] == -1)
							posz = posz + SECTION_SIZE;
						posz = posz - offset[i][1] * BLOCK_SIZE / 2;

						halfextendx = SECTION_SIZE / 2;
						halfextendz = BLOCK_SIZE / 2;

					}
					else
					{
						if (offset[i][0] == -1)
							posx = posx + SECTION_SIZE;
						posx = posx - offset[i][0] * BLOCK_SIZE / 2;

						posz = posz + SECTION_SIZE / 2;

						halfextendx = BLOCK_SIZE / 2;
						halfextendz = SECTION_SIZE / 2;
					}

					createPhysWallByEmptyChunk(posx, posy, posz, halfextendx, SECTION_SIZE / 2, halfextendz);
					return;
				}
			}
		}
		
		return;
	}
	if(sy<0 || sy>=CHUNK_SECTION_DIM) return;
	ChunkIndex ci(sx,sz);
	std::map<CHUNK_INDEX, Rainbow::RigidStaticActor *>::iterator iter = m_EmptyChunkPhys.find(ci);
	if(iter != m_EmptyChunkPhys.end())
	{
		m_PhysScene->DeleteRigidActor(iter->second);
		m_EmptyChunkPhys.erase(iter);
	}

	Section *psection = pchunk->getIthSection(sy);
	if (checkMeshCount && (psection->m_PhyMeshCount <= 0))
	{
		return;
	}
	if(!psection->m_PhyMeshInvalid) return;
	if(psection->m_PhysActor)
	{
		m_PhysScene->DeleteRigidActor(psection->m_PhysActor);
		psection->m_PhysActor = NULL;
	}
	if (psection->m_TriggerActor)
	{
		m_PhysScene->DeleteRigidActor(psection->m_TriggerActor);
		psection->m_TriggerActor = nullptr;
	}
	if (psection->m_TriangleActor)
	{
		m_PhysScene->DeleteRigidActor(psection->m_TriangleActor);
		psection->m_TriangleActor = nullptr;
	}

	if(psection->m_PhysActor == NULL)
	{
		WCoord p1(sx*SECTION_SIZE, sy*SECTION_SIZE, sz*SECTION_SIZE);
		std::vector<Rainbow::Vector3f>blocks;
		std::vector<Rainbow::Vector3f>extens;
		std::vector<Rainbow::Vector3f> triggers;
		std::vector<Rainbow::Vector3f> triggerExtens;
		std::vector<int> triggerBlockId;
		std::vector<TriangleBlockPhyData> triangleBlocks;
		//CollisionDetect coldetect;
		//coldetect.reset();

#ifdef SECTION_BLOCK_OPT
		char colbits[SECTION_BLOCK_DIM][SECTION_BLOCK_DIM][SECTION_BLOCK_DIM];
		memset(&colbits[0][0][0], 0, SECTION_BLOCK_DIM*SECTION_BLOCK_DIM*SECTION_BLOCK_DIM);

		char colbits_4x4[SECTION_BLOCK_DIM*4][SECTION_BLOCK_DIM*4][SECTION_BLOCK_DIM*4];
		memset(&colbits_4x4[0][0][0], 0, SECTION_BLOCK_DIM*4*SECTION_BLOCK_DIM*4*SECTION_BLOCK_DIM*4);
		char colbits_4x4_check[4*4*4];
		memset(&colbits_4x4_check[0], 0, 4*4*4);
		bool havePhisicMeshBit = false;
		for(int y=0; y<SECTION_BLOCK_DIM; y++)
		{
			for(int z=0; z<SECTION_BLOCK_DIM; z++)
			{
				for(int x=0; x<SECTION_BLOCK_DIM; x++)
				{
					Block pblock = psection->getBlock(x, y, z);
					if(pblock.getResID() == 0) continue;
					const BlockDef* blockDef = GetDefManagerProxy()->getBlockDef(pblock.getResID());
					if (!blockDef) continue;
					if( blockDef && (blockDef->PhyCollide > 0))
					{
						if (blockDef->PhyCollide == 1)
						{
							colbits[y][z][x] = 1;
						}
						else if (blockDef->PhyCollide == 4)
						{
							triggers.push_back(Vector3f(x * BLOCK_SIZE + BLOCK_HALFSIZE, y * BLOCK_SIZE + BLOCK_HALFSIZE, z * BLOCK_SIZE + BLOCK_HALFSIZE));
							triggerExtens.push_back(Vector3f(BLOCK_HALFSIZE, BLOCK_HALFSIZE, BLOCK_HALFSIZE));
							triggerBlockId.push_back(blockDef->ID);
						}
						else if (blockDef->PhyCollide == 6)
						{
							// 							if (IsTriangleBlock(pblock->getResID()))
							{
								BlockMaterial* mtl = g_BlockMtlMgr.getMaterial(pblock.getResID());
								if (mtl)
								{
									TriangleBlockPhyData trData;
									WCoord posInSection(x, y, z);
									trData.triangleFaceCount = mtl->getPhisicMeshVerts(psection, posInSection, trData.blockVertList, trData.blockVertIdxList);
									if (0 < trData.triangleFaceCount && psection->getBlockData(x, y, z) < 15)
									{
										trData.pos = /*p1 + */posInSection * BLOCK_SIZE;
										triangleBlocks.push_back(trData);
									}
									else
									{
										dynamic_array<TriangleBlockPhyData> physDatas;
										physDatas.clear();
										mtl->getMultiPhisicMeshVerts(psection, posInSection, physDatas);
										for (int i = 0; i < physDatas.size(); i++)
										{
											triangleBlocks.push_back(physDatas[i]);
										}

										if(physDatas.size() == 0)
											colbits[y][z][x] = 1;
									}
								}
							}
						}
						else
						{
							BlockMaterial *mtl = g_BlockMtlMgr.getMaterial(pblock.getResID());
							if(mtl)
							{
								char * one_block_bit_4x4 = mtl->getPhisicMeshBit(psection, WCoord(x, y, z));
								if (one_block_bit_4x4)
								{
									for(int m=0; m<4; m++)
									{
										for(int n=0; n<4; n++)
										{
											for(int j=0; j<4; j++)
											{
												colbits_4x4[4*y + n][4*z + j][4*x + m] = one_block_bit_4x4[m*4*4 + n*4 + j];
											}
										}
									}
									colbits_4x4_check[(y/4)*16 + (z/4)*4 + x/4] = 1;
									havePhisicMeshBit = true;
								}
								else
									colbits[y][z][x] = 1;
							}
						}
					}
					else
					{
						if (blockDef->Type && ((bPlayerRigidBody && blockDef->Type == "custombasic") 
							|| blockDef->Type == "clitter" || blockDef->Type == "peristele"
							|| blockDef->Type == "mineralpile"))
						{
							BlockMaterial* mtl = g_BlockMtlMgr.getMaterial(pblock.getResID());
							if (mtl && mtl->defBlockMove())
							{
								WCoord posInSection(x, y, z);
								WCoord blockWorldPos = psection->getOrigin() + posInSection;
								int dir = getBlockData(blockWorldPos) % 4;
								WCoord minpos, maxpos;
								if (mtl->getGeom())
								{
									mtl->getGeom()->getBoundBox(minpos, maxpos, 0, 1.0f, dir);
									Rainbow::BoxBound box(minpos.toVector3(), maxpos.toVector3());
									Rainbow::Vector3f pos = posInSection.toVector3() * (float)BLOCK_SIZE;
									pos += box.getCenter();
									blocks.push_back(pos);
									extens.push_back(box.getExtension());
								}
							}
						}
					}
				}
			}
		}

		for(int y=0; y<SECTION_BLOCK_DIM; y++)
		{
			for(int z=0; z<SECTION_BLOCK_DIM; z++)
			{
				for(int x=0; x<SECTION_BLOCK_DIM; x++)
				{
					if(colbits[y][z][x] == 0) continue;
					int xw = 1;
					int yw = 1;
					int zw = 1;

					for(; x+xw<SECTION_BLOCK_DIM && colbits[y][z][x+xw]==1; xw++){}
					bool done = false;
					for(; z+zw<SECTION_BLOCK_DIM; zw++)
					{
						for(int k=0; k<xw; k++)
						{
							if(colbits[y][z+zw][x+k] == 0){done=true; break;}
						}
						if(done) break;
					}

					done = false;
					for(; y+yw<SECTION_BLOCK_DIM; yw++)
					{
						for(int m=0; m<zw; m++)
						{
							for(int k=0; k<xw; k++)
							{
								if(colbits[y+yw][z+m][x+k] == 0){done=true; break;}
							}
							if(done) break;
						}
						if(done) break;
					}

					WCoord tmp(x*BLOCK_SIZE+BLOCK_SIZE*xw/2, y*BLOCK_SIZE+BLOCK_SIZE*yw/2, z*BLOCK_SIZE+BLOCK_SIZE*zw/2);
					blocks.push_back(tmp.toVector3());
					extens.push_back(Rainbow::Vector3f(BLOCK_FSIZE*xw/2, BLOCK_FSIZE*yw/2, BLOCK_FSIZE*zw/2));

					for(int n=0; n<yw; n++)
					{
						for(int m=0; m<zw; m++)
						{
							for(int k=0; k<xw; k++)
							{
								colbits[y+n][z+m][x+k] = 0;
							}
						}
					}
				}
			}
		}

		if (havePhisicMeshBit)
		{
			for(int y0=0; y0<4; y0++)
			{
				for(int z0=0; z0<4; z0++)
				{
					for(int x0=0; x0<4; x0++)
					{
						if (!colbits_4x4_check[y0*4*4 + z0*4 + x0])
						{
							continue;
						}
						for(int y_=0; y_<SECTION_BLOCK_DIM; y_++)
						{
							for(int z_=0; z_<SECTION_BLOCK_DIM; z_++)
							{
								for(int x_=0; x_<SECTION_BLOCK_DIM; x_++)
								{
									int y = y0*SECTION_BLOCK_DIM + y_;
									int z = z0*SECTION_BLOCK_DIM + z_;
									int x = x0*SECTION_BLOCK_DIM + x_;
									if(colbits_4x4[y][z][x] == 0) continue;
									int xw = 1;
									int yw = 1;
									int zw = 1;

									for(; x+xw<SECTION_BLOCK_DIM*4 && colbits_4x4[y][z][x+xw]!=0; xw++){}
									bool done = false;
									for(; z+zw<SECTION_BLOCK_DIM*4; zw++)
									{
										for(int k=0; k<xw; k++)
										{
											if(colbits_4x4[y][z+zw][x+k] == 0){done=true; break;}
										}
										if(done) break;
									}

									done = false;
									for(; y+yw<SECTION_BLOCK_DIM*4; yw++)
									{
										for(int m=0; m<zw; m++)
										{
											for(int k=0; k<xw; k++)
											{
												if(colbits_4x4[y+yw][z+m][x+k] == 0){done=true; break;}
											}
											if(done) break;
										}
										if(done) break;
									}
									WCoord tmp(x*BLOCK_SIZE/4+BLOCK_SIZE/4*xw/2, y*BLOCK_SIZE/4+BLOCK_SIZE/4*yw/2, z*BLOCK_SIZE/4+BLOCK_SIZE/4*zw/2);
									blocks.push_back(tmp.toVector3());
									extens.push_back(Rainbow::Vector3f(BLOCK_FSIZE/4*xw/2, BLOCK_FSIZE/4*yw/2, BLOCK_FSIZE/4*zw/2));

									for(int n=0; n<yw; n++)
									{
										for(int m=0; m<zw; m++)
										{
											for(int k=0; k<xw; k++)
											{
												colbits_4x4[y+n][z+m][x+k] = 0;
											}
										}
									}
								}
							}
						}
					}
				}
			}
		}
#else
		for(int y=0; y<SECTION_BLOCK_DIM; y++)
		{
			for(int z=0; z<SECTION_BLOCK_DIM; z++)
			{
				for(int x=0; x<SECTION_BLOCK_DIM; x++)
				{
					Block pblock = psection->getBlock(x, y, z);
					if(pblock.getResID() == 0) continue;

					BlockMaterial *mtl = g_BlockMtlMgr.getMaterial(pblock.getResID());
					if(mtl->defBlockMove())
					{
						WCoord tmp(x*BLOCK_SIZE+BLOCK_SIZE/2, y*BLOCK_SIZE+BLOCK_SIZE/2, z*BLOCK_SIZE+BLOCK_SIZE/2);
						blocks.push_back(tmp.toVector3());
						extens.push_back(Rainbow::Vector3f(BLOCK_FSIZE/2, BLOCK_FSIZE/2, BLOCK_FSIZE/2));
					}

					//mtl->createCollideData(&coldetect, this, )
				}
			}
		}
#endif
		if(blocks.size() > 0)
		{
			psection->m_PhysActor = m_PhysScene->AddRigidStaticActor(p1.toVector3(), Rainbow::Quaternionf::identity, &blocks[0], &extens[0], blocks.size(), GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.section_static_friciton, GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.section_dynamic_friciton, GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.section_restitution, false);
			if (psection->m_PhysActor && getScene())
			{
				getScene()->AddGameObject(psection->m_PhysActor->GetGameObject());
			}
		}
		if (triggers.size() > 0)
		{
			psection->m_TriggerActor = m_PhysScene->AddRigidStaticActor(p1.toVector3(), Rainbow::Quaternionf::identity, &triggers[0], &triggerExtens[0], triggers.size(), GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.section_static_friciton, GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.section_dynamic_friciton, GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.section_restitution, true, &triggerBlockId[0]);
			if (psection->m_TriggerActor && getScene())
			{
				getScene()->AddGameObject(psection->m_TriggerActor->GetGameObject());
			}
		}
		if (triangleBlocks.size() > 0)
		{
			TriangleBlockPhyData trData;
			for (auto subItem : triangleBlocks)
			{
				std::vector<int> indexlist;
				for (int n = 0; n < subItem.blockVertList.size(); n++)
				{
					int index = -1;
					Vector3f vpos = subItem.blockVertList[n] + subItem.pos.toVector3();
					for (int o = 0; o < trData.blockVertList.size(); o++)
					{
						if (vpos == trData.blockVertList[o])
						{
							index = o;
							break;
						}
					}
					if (-1 == index)
					{
						index = trData.blockVertList.size();
						trData.blockVertList.push_back(vpos);
					}
					indexlist.push_back(index);
				}
				for (int n = 0; n < subItem.blockVertIdxList.size(); n++)
				{
					trData.blockVertIdxList.push_back(indexlist[subItem.blockVertIdxList[n]]);
				}
				trData.triangleFaceCount += subItem.triangleFaceCount;
			}
			psection->m_TriangleActor = m_PhysScene->AddRigidStaticActor(p1.toVector3(), Rainbow::Quaternionf::identity, trData.blockVertList.data(), trData.blockVertList.size(), trData.blockVertIdxList.data(), trData.blockVertIdxList.size(), false, g_WorldMgr->m_SurviveGameConfig->ballconfig.section_static_friciton, g_WorldMgr->m_SurviveGameConfig->ballconfig.section_dynamic_friciton, g_WorldMgr->m_SurviveGameConfig->ballconfig.section_restitution);
			if (psection->m_TriangleActor && getScene())
			{
				getScene()->AddGameObject(psection->m_TriangleActor->GetGameObject());
			}
		}
		
	}

	psection->m_PhyMeshInvalid = false;
}
#endif


void World::createPhysWallByEmptyChunk(int posx, int posy, int posz, int halfextendx, int halfextendy, int halfextendz)
{

	WCoord center(posx, posy, posz);
	std::map<WCoord, Rainbow::RigidStaticActor*>::iterator iter = m_EmptyChunkPhysWall.find(center);
	if (iter != m_EmptyChunkPhysWall.end()) return;

	if (m_PhysScene)
	{
#ifdef IWORLD_SERVER_BUILD			
		if (!getScene())
			return;
#endif			
		WCoord halfextend(halfextendx, halfextendy, halfextendz);
		Rainbow::RigidStaticActor* rigidactor = m_PhysScene->AddRigidStaticActor(center.toVector3(), Rainbow::Quaternionf::identity, halfextend.toVector3(), 0.8f, 0.8f, 0.8f);
		getScene()->AddGameObject(rigidactor->GetGameObject());
		m_EmptyChunkPhysWall[center] = rigidactor;
	}
}

void World::updateEnterSectionPhysics(int sx, int sy, int sz)
{
	Chunk *pchunk = getChunkBySCoord(sx, sz);
	if(pchunk == NULL)
	{
		return;
	}
	if(sy<0 || sy>=CHUNK_SECTION_DIM) return;

	Section *psection = pchunk->getIthSection(sy);
	if (psection)
	psection->m_PhyMeshCount++;
}

void World::updateLeaveSectionPhysics(int sx, int sy, int sz)
{
    OPTICK_CATEGORY(OPTICK_FUNC, Optick::Category::Physics);
	Chunk *pchunk = getChunkBySCoord(sx, sz);
	if(pchunk == NULL)
	{
		return;
	}
	if(sy<0 || sy>=CHUNK_SECTION_DIM) return;

	Section *psection = pchunk->getIthSection(sy);
	if (!psection)
		return;
	if (psection->m_PhyMeshCount > 0)
	{
		psection->m_PhyMeshCount--;
	}

	if (m_PhysScene && (psection->m_PhyMeshCount == 0))
	{
		if (psection->m_PhysActor)
		{
			m_PhysScene->DeleteRigidActor(psection->m_PhysActor);
			psection->m_PhysActor = NULL;
		}
		if (psection->m_TriggerActor)
		{
			m_PhysScene->DeleteRigidActor(psection->m_TriggerActor);
			psection->m_TriggerActor = nullptr;
		}
		if (psection->m_TriangleActor)
		{
			m_PhysScene->DeleteRigidActor(psection->m_TriangleActor);
			psection->m_TriangleActor = nullptr;
		}
		psection->m_PhyMeshInvalid = true;
	}
}	

bool World::removeChunk(ChunkIndex index)
{
	Chunk *pchunk = getChunk(index);
#ifndef DEDICATED_SERVER
	m_WatcherLock.WriteLock();
#endif
	//LOG_INFO("remove chunk: (%d, %d)", index.x, index.z);
	auto ele = m_ViewChunks.find(index);
	if (ele != m_ViewChunks.end())
	{
		ENG_DELETE(ele->second);
		m_ViewChunks.erase(ele);
		removeTempChunkViewer(index);
	}
#ifndef DEDICATED_SERVER
	m_WatcherLock.WriteUnlock();
#endif

	bool saved = false;
	if(pchunk)
	{
#ifdef BUILD_MINI_EDITOR_APP
		std::for_each(m_vecNewTerrainChunk.begin(), m_vecNewTerrainChunk.end(), [&](PAIR_CHUNK& var)->void {
			if (var.second == pchunk)
			{
				var.second = nullptr;
				return;
			}
			});
#endif

		clearCacheBlock();
		m_ChunkArray.erase(std::remove(m_ChunkArray.begin(), m_ChunkArray.end(), pchunk), m_ChunkArray.end());
		if(pchunk->needSave(true))
		{
			saved = true;
			if (GetWorldManagerPtr()->needSave(NEEDSAVE_CHUNKS))
				saveChunk(pchunk);
			else
				saveChunk(pchunk, true);
		}

		if (pchunk->needSaveActor(true))//生物独立存档
		{
			saved = true;
			if (GetWorldManagerPtr()->needSave(NEEDSAVE_CHUNKS))
				saveActor(pchunk);
			else
				saveActor(pchunk, true);
		}

		auto pchunk_save = pchunk;
		pchunk->onLeaveWorld();
		ENG_DELETE(pchunk);
		if (m_pChunkCache == pchunk_save)
		{
			m_pChunkCache = NULL;
		}
	}

	return saved;
}

bool World::setChunkRendererDisplay(ChunkIndex index, bool value)
{

	Chunk* pChunk = getChunk(index);
	if (pChunk != nullptr) 
	{
		pChunk->SetHide(value);
		return true;
	}
	return false;
}

ChunkViewerList* World::getWatchers(const CHUNK_INDEX& chunkIndex) const
{
#ifndef DEDICATED_SERVER
	ReadWriteSpinLock::AutoReadLock locker(m_WatcherLock);
#endif

	ChunkViewerList* ret = findTempChunkViewer(chunkIndex);
	if (ret) { return ret; }

	auto ele = m_ViewChunks.find(chunkIndex);
	if (ele != m_ViewChunks.end())
	{
		return ele->second;
	}
	return nullptr;
}

ChunkViewerList* World::getWatchersRaw(const CHUNK_INDEX& chunkIndex) const
{
	auto ele = m_ViewChunks.find(chunkIndex);
	if (ele != m_ViewChunks.end())
	{
		return ele->second;
	}
	return nullptr;
}

bool World::tryLoadChunk(CHUNK_INDEX chunkindex, ChunkViewer* player, bool isFast)
{
	assert(!isRemoteMode());

	auto itr = m_ViewChunks.find(chunkindex);
	if (itr != m_ViewChunks.end())
	{
		ChunkViewerList *watcher = itr->second;
		if (player) watcher->addViewer(player);
		auto emptyIt = m_EmptyChunkWatchers.find(chunkindex);
		if (emptyIt != m_EmptyChunkWatchers.end())
		{
			m_EmptyChunkWatchers.erase(emptyIt);
		}
		Chunk* chunk = watcher->getChunk();

		if (chunk != nullptr) 
		{
			if (m_WorldMgr)
			{
				if (m_WorldMgr->GetHideAllChunks())
				{
					bool isHide = !Rainbow::GetMiniCraftRenderer().IsOnViewRange(chunkindex.x, chunkindex.z); //!watcher->OnViewRange(chunkindex);
					chunk->SetHide(true);
				}
				else
				{
					bool isHide = !Rainbow::GetMiniCraftRenderer().IsOnViewRange(chunkindex.x, chunkindex.z); //!watcher->OnViewRange(chunkindex);
					chunk->SetHide(isHide);
				}
				
			}
			//bool beforeHide = !watcher->OnViewRange(chunkindex);;
			//if (!isHide) 
			//{
			//	bool onRange = Rainbow::GetMiniCraftRenderer().IsOnViewRange(chunkindex.x, chunkindex.z);
			//	if (!onRange) isHide = true;
			//	//ErrorStringMsg("tryLoadChunk x:%d, z:%d, isHide:%d, onViewRange:%d", chunkindex.x, chunkindex.z, isHide, onRange);
			//}
			//ErrorStringMsg("tryLoadChunk x:%d, z:%d, isHide:%d, beforeHide:%d", chunkindex.x, chunkindex.z, isHide, beforeHide);
			//if (!isHide) 
			//{
				
			//	if (!isOnRange) isHide = true;
			//}
			
		}

		return true;  // chunk ready
	}


	//LOG_INFO("tryLoadChunk (%d, %d)", chunkindex.x, chunkindex.z);
	ChunkViewerList *watcher = ENG_NEW( ChunkViewerList );
#ifndef DEDICATED_SERVER
	m_WatcherLock.WriteLock();
#endif
	m_ViewChunks[chunkindex] = watcher;
	addTempViewChunk(chunkindex, watcher);

#ifndef DEDICATED_SERVER
	m_WatcherLock.WriteUnlock();
#endif

	if (player) watcher->addViewer(player);
	else
		m_EmptyChunkWatchers[chunkindex] = m_CurWorldTick;


	ChunkIOCmd cmd;
	memset(&cmd, 0, sizeof(cmd));
	if (HOME_GARDEN_WORLD == (WorldPlayingType)m_unSpecialType)
	{
		cmd.cmdtype = CIOCMD_LOADCHUNK_HOMEGARDEN;
		cmd.mapid = m_CurMapID;
		cmd.cx = chunkindex.x;
		cmd.cz = chunkindex.z;

		//vector<int> list;
		//1--HOMELAND_REGION_TYPE_SELF_BUILD 自建区域
		SandboxResult homeresult = SandboxEventDispatcherManager::GetGlobalInstance().
			Emit("Homeland_IsChunkType", SandboxContext(nullptr)
				.SetData_Number("type", 1)
				.SetData_Number("chunkx", cmd.cx)
				.SetData_Number("chunkz", cmd.cz)
			);

		if (homeresult.IsSuccessed())
		{
			cmd.ismustloadcustomize = false;
			vector<int> list;
			SandboxResult homeresult = SandboxEventDispatcherManager::GetGlobalInstance().
				Emit("Homeland_GetRegionList", SandboxContext(nullptr)
					.SetData_Userdata("lists", "userdata", &list)
					.SetData_Number("chunkx", cmd.cx)
					.SetData_Number("chunkz", cmd.cz)
				);
			memset(cmd.path, 0, 128);
			for (unsigned int i = 0; i < list.size(); i++)
			{
				sprintf(cmd.path, "%s/%d", cmd.path, list[i]);
				sprintf(cmd.defaultpath, "%s/%d", cmd.defaultpath, 1);
			}
			if (1 < list.size())
			{
				cmd.customizeid = list[1];
				cmd.chunkExData = (list[0] << 8);
				//LOG_INFO("get home chunk self build level 1 data posx = %d, posz = %d, type id = %d, style id = %d", cmd.cx, cmd.cz, list[0], list[1]);
			}
			else
			{
				//LOG_INFO("get home chunk self build data posx = %d, posy = %d", cmd.cx, cmd.cz);
			}
		}
		else
		{
			vector<int> list;//list的结构 第一个元素为chunk的类型（风格、节目区域等），第二个元素为外观id，第三个元素（暂时只有农场牧场有，代表等级）
			SandboxResult homeresult = SandboxEventDispatcherManager::GetGlobalInstance().
				Emit("Homeland_GetRegionList", SandboxContext(nullptr)
					.SetData_Userdata("lists", "userdata", &list)
					.SetData_Number("chunkx", cmd.cx)
					.SetData_Number("chunkz", cmd.cz)
				);
			memset(cmd.path, 0, 128);
			for (unsigned int i = 0; i < list.size(); i++)
			{
				sprintf(cmd.path, "%s/%d", cmd.path, list[i]);
				sprintf(cmd.defaultpath, "%s/%d", cmd.defaultpath, 1);
			}
			cmd.ismustloadcustomize = true;
			if (1 < list.size())
			{
				cmd.customizeid = list[1];
				cmd.chunkExData = (list[0] << 8);
				if (3 == list.size())
				{
					cmd.chunkExData |= list[2];
					//LOG_INFO("get home customize chunk posx = %d, posy = %d, type id = %d, style id = %d, level = %d", cmd.cx, cmd.cz, list[0], list[1], list[2]);
				}
				else
				{
					//LOG_INFO("get home customize chunk posx = %d, posy = %d, type id = %d, style id = %d", cmd.cx, cmd.cz, list[0], list[1]);
				}
			}
			else
			{
				//LOG_INFO("get home customize chunk posx = %d, posy = %d fail", cmd.cx, cmd.cz);
			}
		}
	}
	else
	{
		cmd.cmdtype = CIOCMD_LOADCHUNK;
		cmd.mapid = m_CurMapID;
		cmd.cx = chunkindex.x;
		cmd.cz = chunkindex.z;
#if defined(IWORLD_SERVER_BUILD) && !STUDIO_SERVER
		if (GetZmqMgrInterface() && GetZmqMgrInterface()->IsNeedSaveChunk())
		{
			int uin = GetClientInfoProxy()->getUin();
			int ret = GetZmqMgrInterface()->LoadChunkFromDataServer(uin, m_OWID, cmd.mapid, cmd.cx, cmd.cz);
			if (ret == 0)
				return false;
		}
#endif
	}
	if (GetChunkIOMgr() != NULL) {
		cmd.isFast = isFast;
		GetChunkIOMgr()->pushCmd(cmd);

#ifdef SANDBOX_CHUNK_STREAM_LOAD
		if (SandboxNode::StreamEnableGameSet && cmd.cmdtype == CIOCMD_LOADCHUNK) {
			auto sbiomgr = GetChunkIOMgr()->GetSandboxChunkIOMgr();// ->Try
			if (sbiomgr) {
				sbiomgr->TryLoadChunk(cmd.mapid, cmd.cx, cmd.cz, isFast);
			}
		}
#endif

	}

	return false;
}

bool World::tryLoadChunkSync(CHUNK_INDEX chunkindex, ChunkViewer* player)
{
	assert(!isRemoteMode());

	auto itr = m_ViewChunks.find(chunkindex);
	if (itr != m_ViewChunks.end())
	{
		ChunkViewerList* watcher = itr->second;
		if (player) watcher->addViewer(player);
		Chunk* chunk = watcher->getChunk();
		if (chunk != nullptr)
		{
			chunk->SetHide(!watcher->OnViewRange(chunkindex));
		}
		removeUnloadChunk(chunkindex);
		//auto emptyIt = m_EmptyChunkWatchers.find(chunkindex);
		//if (emptyIt != m_EmptyChunkWatchers.end())
		//{
		//	m_EmptyChunkWatchers.erase(emptyIt);
		//}
		return true;  // chunk ready
	}


	//LOG_INFO("tryLoadChunk (%d, %d)", chunkindex.x, chunkindex.z);
	ChunkViewerList* watcher = ENG_NEW(ChunkViewerList);
#ifndef DEDICATED_SERVER
	m_WatcherLock.WriteLock();
#endif
	m_ViewChunks[chunkindex] = watcher;
	addTempViewChunk(chunkindex, watcher);

#ifndef DEDICATED_SERVER
	m_WatcherLock.WriteUnlock();
#endif

	if (player) watcher->addViewer(player);
	else
		m_EmptyChunkWatchers[chunkindex] = m_CurWorldTick;
	


	if (GetChunkIOMgr() != NULL) {
		GetChunkIOMgr()->SyncLoadChunk(this, chunkindex.x, chunkindex.z);
	}

	return false;
}

void World::unloadChunk(CHUNK_INDEX chunkindex, ChunkViewer *player)
{
	assert(!isRemoteMode());

	auto ele = m_ViewChunks.find(chunkindex);
	if(ele != m_ViewChunks.end())
	{
		ChunkViewerList *watcher = ele->second;

		watcher->removeViewer(player);
		if(watcher->isEmpty())
		{
			m_EmptyChunkWatchers[chunkindex] = m_CurWorldTick;
		}
	}
}

void World::removeUnloadChunk(const CHUNK_INDEX& chunkindex)
{
	auto emptyIt = m_EmptyChunkWatchers.find(chunkindex);
	if (emptyIt != m_EmptyChunkWatchers.end())
	{
		m_EmptyChunkWatchers.erase(emptyIt);
	}
}

bool World::isPrepareRemoveChunk(const CHUNK_INDEX& chunkindex)
{
	auto iter = m_ViewChunks.find(chunkindex);
	if (iter == m_ViewChunks.end())
	{
		return true;
	}

	auto emptyIt = m_EmptyChunkWatchers.find(chunkindex);
	if (emptyIt != m_EmptyChunkWatchers.end())
	{
		return true;
	}

	return false;
}

void World::appendHideChunkList(CHUNK_INDEX chunkindex, ChunkViewer* player)
{
	//auto ele = m_ViewChunks.find(chunkindex);
	//if (ele != m_ViewChunks.end()) 
	//{
	//	ChunkViewerList* watcher = ele->second;
	//	watcher->removeViewer(player);
	//	if (watcher->isEmpty()) 
	//	{
	//		m_HideEmptyChunkWatchers[chunkindex] = m_CurWorldTick;
	//	}
	//}
}

bool World::syncLoadChunk(int x, int z, ChunkViewer* player)
{
	assert(!isRemoteMode());
	if(m_CurChunkProvider == NULL) return false;
	if(!m_CurChunkProvider->canProvideChunk(x,z)) return false;

#ifdef SANDBOX_USE_PROFILE_CHUNKSYNCLOAD
	MNSandbox::Profile::ProfileCostPrint cost(ToString("Sync LoadChunk(", x, ",", z, ") [", s_loadChunkIdx++, "] :"));
#endif

	tryLoadChunk(x, z, player, true);

	while (getChunkBySCoord(x, z) == NULL)
	{
		if (GetWorldManagerPtr() != NULL) {
			if (GetWorldManagerPtr()->m_ChunkIOMgr != NULL) {
				GetWorldManagerPtr()->m_ChunkIOMgr->tick();

				if (getChunkBySCoord(x, z))  //避免不必要的阻塞 demon
				{
					break;
				}
			}
		}

		m_CurChunkProvider->check();
		if (getChunkBySCoord(x, z))  //避免不必要的阻塞 demon
		{
			break;
		}
		MINIW::ThreadSleep(5);  //不管是IO还是地形生成 5 ms 肯定是要的，减少线程竞争 demon
	}
	GetPixelMapMgrInterface()->OnChunkChange(x, z);
	return true;
}

void World::syncLoadChunk(const WCoord &blockpos, int range)
{
	int minx = BlockDivSection(blockpos.x-range);
	int maxx = BlockDivSection(blockpos.x+range);
	int minz = BlockDivSection(blockpos.z-range);
	int maxz = BlockDivSection(blockpos.z+range);

	for(int z=minz; z<=maxz; z++)
	{
		for(int x=minx; x<=maxx; x++)
		{
			syncLoadChunk(x, z);
		}
	}
}

IClientActor *World::findNearestActor(WCoord pos, int findRange, int objtype, int id /* = 0 */)
{
	std::vector<CHUNK_INDEX>indices;
	WCoord center = CoordDivBlock(pos);
	ChunkViewer::makeViewChunks(indices, BlockDivSection(center.x), BlockDivSection(center.z), findRange);

	IClientActor *findactor = NULL;
	for (size_t i = 0; i < indices.size(); i++)
	{
		Chunk *pchunk = getChunk(indices[i]);
		if (pchunk == NULL) continue;

		int mindist = Rainbow::MAX_INT;
		int maxy = CHUNK_SECTION_DIM;
		for (int y = 0; y < maxy; y++)
		{
			Section *psection = pchunk->getIthSection(y);
			size_t nactor = psection->m_Actors.size();
			if (nactor == 0) continue;

			for (size_t j = 0; j < nactor; j++)
			{
				IClientActor *actor = psection->m_Actors[j];
				if (actor->getObjType() == objtype)
				{
					int dist = pos.squareDistanceTo(actor->getPosition());
					if (dist < mindist)
					{
						mindist = dist;
						findactor = actor;
					}
				}
			}
		}

		if (findactor) break;
	}

	return findactor;
}

void World::findNearActors(std::vector<IClientActor*>& result, WCoord pos, int range, int objType)
{
	std::vector<CHUNK_INDEX>indices;
	WCoord center = CoordDivBlock(pos);
	ChunkViewer::makeViewChunks(indices, BlockDivSection(center.x), BlockDivSection(center.z), range);
	for (size_t i = 0; i < indices.size(); i++)
	{
		Chunk* pchunk = getChunk(indices[i]);
		if (pchunk == NULL) continue;
		int maxy = CHUNK_SECTION_DIM;
		for (int y = 0; y < maxy; y++)
		{
			Section* psection = pchunk->getIthSection(y);
			if (!psection)
				continue;
			size_t nactor = psection->m_Actors.size();
			if (nactor == 0) continue;
			for (size_t j = 0; j < nactor; j++)
			{
				IClientActor* actor = psection->m_Actors[j];
				if (actor && actor->getObjType() == objType)
				{
					result.push_back(actor);
				}
			}
		}
	}
}

void World::findAllNearActors(std::vector<IClientActor*>& result, WCoord pos, int range)
{
	if (range <= 0)
	{
		return;
	}
	std::vector<CHUNK_INDEX>indices;
	WCoord center = CoordDivBlock(pos);
	int chunkDis = 0;
	chunkDis = ((range - 1) / SECTION_BLOCK_DIM) + 1;
	ChunkViewer::makeViewChunks(indices, BlockDivSection(center.x), BlockDivSection(center.z), chunkDis);
	for (size_t i = 0; i < indices.size(); i++)
	{
		Chunk* pchunk = getChunk(indices[i]);
		if (pchunk == NULL) continue;
		int maxy = CHUNK_SECTION_DIM;
		for (int y = 0; y < maxy; y++)
		{
			Section* psection = pchunk->getIthSection(y);
			if (!psection)
				continue;
			size_t nactor = psection->m_Actors.size();
			if (nactor == 0) continue;
			Rainbow::Vector3f dis;
			WCoord actorPos;
			for (size_t j = 0; j < nactor; j++)
			{
				IClientActor* actor = psection->m_Actors[j];
				if (actor != NULL)
				{
					//这里用的方块坐标,没有使用世界坐标
					actorPos = CoordDivBlock(actor->getPosition());
					dis.x = center.x - actorPos.x;
					dis.y = center.y - actorPos.y;
					dis.z = center.z - actorPos.z;
					if (dis.Length() <= range)
					{
						result.push_back(actor);
					}
				}
			}
		}
	}
}

void World::resetGenMesh(const WCoord &pos)
{
	for (size_t i = 0; i < m_ChunkArray.size(); i++)
	{
		//m_ChunkArray[i]->genMesh(pos); 太卡了，要均开处理
		if (m_ChunkArray[i]->m_delayResetGenMesh == 0)
		{
			m_ChunkArray[i]->m_delayResetGenMesh = (abs(m_ChunkArray[i]->m_Origin.x - pos.x / 100) + abs(m_ChunkArray[i]->m_Origin.z - pos.z / 100))*2 + 20;
		}
	}
}

void World::OnDrawGizmo()
{
	if (m_DrawGizmo) {
		if (m_ActorMgr) m_ActorMgr->OnDrawGizmo();
		if (m_EffectMgr)m_EffectMgr->OnDrawGizmo();
	}

#if defined(USE_OPTICK) && defined(WIN32)
	//对比测试下两个算法的效率
	if (GetInputManager().GetKey(SDLK_KP_MULTIPLY))
	{
		for (int range = 0; range < 32; range++) {
			std::vector<IClientActor*> actors1;
			std::vector<IClientActor*> actors2;
			std::chrono::steady_clock::time_point start1, end1, start2, end2;
			auto aabb = GetIPlayerControl()->GetIAABB();
			aabb.Expand(float(range) * SECTION_SIZE);
			start1 = std::chrono::high_resolution_clock::now();
			getActorsInBox_Octree(actors1, aabb, 0, true);
			end1 = std::chrono::high_resolution_clock::now();
			auto duration1 = std::chrono::duration_cast<std::chrono::duration<float, std::milli>>(end1 - start1);

			CollideAABB box;
			dynamic_cast<IClientActor*>(GetIPlayerControl())->getCollideBox(box);
			box.expand(float(range) * SECTION_SIZE, float(range) * SECTION_SIZE, float(range) * SECTION_SIZE);
			start2 = std::chrono::high_resolution_clock::now();
			getActorsInBox(actors2, box, 0, true);
			end2 = std::chrono::high_resolution_clock::now();
			auto duration2 = std::chrono::duration_cast<std::chrono::duration<float, std::milli>>(end2 - start2);

			WarningStringMsg("getActorsInBox[range:%d]: octree[%d] %.2fms, grid[%d]:%.2fms",
				range,
				actors1.size(), duration1.count(),
				actors2.size(), duration2.count());
		}
	}
#endif//(USE_OPTICK) && defined(WIN32)
}