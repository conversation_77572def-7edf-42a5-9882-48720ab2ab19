#include "RuneDef.h"
#include "DefManagerProxy.h"
#include "ActorTypes.h"
#include "blocks/container.h"
#include "proto_common.h"
#include "proto_common.pb.h"

using namespace google::protobuf;
using namespace game::common;

	GridRuneItemData::GridRuneItemData():rune_id(0),rune_val0(0.0f),rune_val1(0.0f),item_id(0){};

	GridRuneItemData::GridRuneItemData(int id, float val0, float val1, int itemid):rune_id(id),rune_val0(val0),rune_val1(val1),item_id(itemid){};

	GridRuneItemData::GridRuneItemData(const GridRuneItemData& rval){
		rune_id = rval.rune_id;
		rune_val0 = rval.rune_val0;
		rune_val1 = rval.rune_val1;
		item_id   = rval.item_id;
	}
	GridRuneItemData& GridRuneItemData::operator = ( const GridRuneItemData& rval ){
		rune_id   = rval.rune_id;
		rune_val0 = rval.rune_val0;
		rune_val1 = rval.rune_val1;
		item_id   = rval.item_id;
		return *this;
	}
	void GridRuneItemData::resetGridRuneItemData(){
		rune_id   = 0;
		rune_val0 = 0.0f;
		rune_val1 = 0.0f;
		item_id   = 0;
	}

	const RuneDef* GridRuneItemData::getRuneDef(int rune_id){
		return GetDefManagerProxy()->getRuneDef(rune_id);
	}

	const RuneDef* GridRuneItemData::getRuneDef()const {
		return GetDefManagerProxy()->getRuneDef(rune_id);
	}

	bool GridRuneItemData::isDurationType() const {
		return ENCHANT_DURABLE == getEnchantType();
	}

	int GridRuneItemData::getEnchantType() const {
		const RuneDef* def = getRuneDef();
		if(def)
			return def->EnchantType;
		return ENCHANT_NULL;
	}

	int GridRuneItemData::getRuneId()const{
		return rune_id;
	}
	float GridRuneItemData::getRuneVal0()const{
		return rune_val0;
	}
	float GridRuneItemData::getRuneVal1()const{
		return rune_val1;
	}
	int GridRuneItemData::getRuneItemId()const{
		return item_id;
	}
//BackPackGrid�ﱣ���������������


	bool GridRuneData::setOneGridRuneItem(const GridRuneItemData &one, int index/* = -1*/){//����һ��������Ϣ
		if(index > rune_num)
			return false;
		int setIndex = index < 0 ? rune_num : index;
		rune_data[setIndex] = one;
		return true;
	}
	bool GridRuneData::addOneGridRuneItem(const GridRuneItemData &one){//���һ��������Ϣ
		if(rune_num >= MAX_ITEM_RUNES)
			return false;
		if(setOneGridRuneItem(one)){
			rune_num++;
			return true;
		}
		return false;
	}

	bool GridRuneData::removeOneGridRuneItem(int id){//ɾ��һ��ָ��id�ķ���  id = RuneID*100 + level
		int index = findIndex(id);
		if(index == -1)
			return false;
		rune_num--;
		if( index < rune_num){
			memmove(&rune_data[index], &rune_data[index+1], (rune_num-index)*sizeof(GridRuneItemData));
		}
		return true;
	}
	int GridRuneData::getRuneNum()const{
		return rune_num;
	}
	GridRuneData::GridRuneData(){
		reset();
	}

	GridRuneData::GridRuneData(const GridRuneData& data){
		setdata(data);
	}

	GridRuneData& GridRuneData::operator = ( const GridRuneData& data ){
		setdata(data);
		return *this;
	}

	void GridRuneData::reset(){
		rune_num = 0;
		for (int i = 0; i <MAX_ITEM_RUNES; i++){
			rune_data[i].resetGridRuneItemData();
		}
	}

	void GridRuneData::setdata(const GridRuneData& data){
		rune_num = data.rune_num;
		for (int i = 0; i <MAX_ITEM_RUNES; i++){
			rune_data[i] = data.rune_data[i];
		}
	}

	bool GridRuneData::clear(){//������з���
		reset();
		return true;
	}

	int GridRuneData::getDuration() const{//��ȡ���� �;üӳɰٷֱ�
		for(int i=0; i<rune_num && i < MAX_ITEM_RUNES; i++){
			if(rune_data[i].getEnchantType() == ENCHANT_DURABLE)
				return rune_data[i].getRuneVal0();
		}
		return 0;
	}

	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::ItemGridRune>>> GridRuneData::save(flatbuffers::FlatBufferBuilder &builder)const{
		std::vector<flatbuffers::Offset<FBSave::ItemGridRune>> datas;
		for (int i = 0; i < rune_num && i < MAX_ITEM_RUNES; ++i)
		{
			datas.push_back(FBSave::CreateItemGridRune(builder, rune_data[i].rune_id, rune_data[i].rune_val0, rune_data[i].rune_val1, rune_data[i].item_id ));
		}
		return builder.CreateVector(datas);
	}


	void GridRuneData::load(const flatbuffers::Vector<flatbuffers::Offset<FBSave::ItemGridRune>> *src){
		if(src == nullptr){
			reset();
			return;
		}

		rune_num = src->size();
		for(int i=0; i<rune_num && i < MAX_ITEM_RUNES; i++)
		{
			rune_data[i].rune_id = src->Get(i)->runeid();
			rune_data[i].rune_val0 = src->Get(i)->runeval0();
			rune_data[i].rune_val1 = src->Get(i)->runeval1();
			rune_data[i].item_id = src->Get(i)->itemid();
		}
	}

	void GridRuneData::load(const RepeatedPtrField<PB_ItemRune> &src, int size){
		rune_num = size;
		for(int i=0; i<rune_num && i < MAX_ITEM_RUNES; i++)
		{

			rune_data[i].rune_id = src.Get(i).runeid();
			rune_data[i].rune_val0 = src.Get(i).runeval0();
			rune_data[i].rune_val1 = src.Get(i).runeval1();
			rune_data[i].item_id = src.Get(i).itemid();
		}
	}
	void GridRuneData::save(RepeatedPtrField<PB_ItemRune> *dest)const{
		for (int i = 0; i< rune_num; i++)
		{
			auto item = dest->Add();
			item->set_runeid(rune_data[i].rune_id);
			item->set_runeval0(rune_data[i].rune_val0);
			item->set_runeval1(rune_data[i].rune_val1);
			item->set_itemid(rune_data[i].item_id);
		}
	}

	const RuneDef * GridRuneData::findDef(int type) const{
		for(int i=0; i<rune_num; i++){
			if(rune_data[i].getEnchantType() == type)
				return rune_data[i].getRuneDef();
		}
		return NULL;
	}
	const RuneDef * GridRuneData::findDefByIndex(int index) const{
		if(index < 0 || index >= rune_num)
			return NULL;
		return rune_data[index].getRuneDef();
	}

	int GridRuneData::findIndex(int id)const {
		for(int i=0; i<rune_num; i++){
			if ( rune_data[i].rune_id == id )
				return i;
		}
		return -1;
	}

	const GridRuneItemData& GridRuneData::getItemByIndex(int i)const{
		assert(i >= 0 && i < rune_num);
		return rune_data[i];
	}

	int GridRuneData::isConflick(int newid, int exceptIndex/* = -1*/){//����Ŀid �Ƿ��� ����ӵĳ�ͻ
		if(rune_num <= 0)
			return 0;//����ͻ

		int new_kind = newid / 100;
		const RuneDef* def = GridRuneItemData::getRuneDef(newid);
		for (int i = 0; i < rune_num; i++){
			if(i != exceptIndex){
				int cur_kind = rune_data[i].rune_id / 100;
				const RuneDef* curDef = rune_data[i].getRuneDef();
				if(def->ConflictID == cur_kind || curDef->ConflictID == new_kind)
					return -1;
				if(new_kind == cur_kind)//ͬ��Ŀ��ֻ����Ƕһ��
					return -2;
			}
		}
		return 0;
	}

	void GridRuneData::save(jsonxx::Object &obj)
	{
		if (rune_num)
		{
			obj<<"data"<<jsonxx::Array();
			jsonxx::Array &obj_ = (jsonxx::Array &)obj.get<jsonxx::Array>("data");
			for (int i = 0; i < rune_num && i < MAX_ITEM_RUNES; ++i)
			{
				jsonxx::Value *value = new jsonxx::Value;
				value->array_value_ = new jsonxx::Array;
				value->type_ = jsonxx::Value::ARRAY_;
				(*value->array_value_)<<rune_data[i].rune_id<<rune_data[i].rune_val0<<rune_data[i].rune_val1<<rune_data[i].item_id;
				obj_.import(value);
				value->release();
			}
		}
	}

	void GridRuneData::load(const jsonxx::Object &obj)
	{
		if (obj.has<jsonxx::Array>("data"))
		{
			const jsonxx::Array &grid = obj.get<jsonxx::Array>("data");
			rune_num = grid.size();
			for(int i=0; i<rune_num && i < MAX_ITEM_RUNES; i++)
			{
				if (grid.has<jsonxx::Array>(i))
				{
					const jsonxx::Array &rune_data_ = grid.get<jsonxx::Array>(i);
					if (rune_data_.size() == 4)
					{
						rune_data[i].rune_id = rune_data_.get<jsonxx::Number>(0);
						rune_data[i].rune_val0 = rune_data_.get<jsonxx::Number>(1);
						rune_data[i].rune_val1 = rune_data_.get<jsonxx::Number>(2);
						rune_data[i].item_id = rune_data_.get<jsonxx::Number>(3);
					}
				}
				else
				{
					break;
				}
			}

		}
		else
		{
			reset();
			return;
		}
	}
//��BackPackGrid֮�俽�� �踴�Ƶ�ȫ����Ϣ

	GridCopyData::GridCopyData():resid(0),num(0),duration(-1),toughness(-1),enchantnum(0),enchants(0),tunestonenum(0),tunestones(0),
		userdata(0),userdata_str(""),runedata(NULL), componentsPtr(NULL), maxduration(-1), m_nDataEX(0)
	{
		effects.clear();
		gridix = -1;
		m_KVS.clear();
	}
	GridCopyData::GridCopyData(int id, int n):resid(id),num(n),duration(-1),toughness(-1),enchantnum(0),enchants(0), tunestonenum(0), tunestones(0),
		userdata(0),userdata_str(""),runedata(NULL), componentsPtr(NULL), maxduration(-1), m_nDataEX(0)
	{
		gridix = -1;
		m_KVS.clear();
	}
	GridCopyData::GridCopyData(int id_, int num_, int duration_, int toughness_, int enchantnum_, const int* enchants_, int tunestonenum_, const int* tunestones_,
		void* userdata_, const char* userdata_str_, const GridRuneData* runedata_, int maxduration_)
		:resid(id_),num(num_),duration(duration_),toughness(toughness_),enchantnum(enchantnum_),enchants(enchants_), tunestonenum(tunestonenum_),
			tunestones(tunestones_), userdata(userdata_),userdata_str(userdata_str_),runedata(runedata_), maxduration(maxduration_), m_nDataEX(0)
	{
		gridix = -1;
		componentsPtr = NULL;
		m_KVS.clear();
	}

	GridCopyData::GridCopyData(const BackPackGrid* data)
		:resid(data->getItemID())
		,num(data->getNum())
		,duration(data->getDuration())
		,toughness(data->getToughness())
		,enchantnum(data->getNumEnchant())
		,enchants(data->getEnchants())
		,tunestonenum(0)
		,tunestones(0)
		,userdata(data->userdata)
		,userdata_str(data->userdata_str.c_str())
		,runedata(&data->getRuneData())
		,maxduration(data->getMaxDuration())
	{
		effects = data->m_effects;
		gridix = data->getIndex();
		componentsPtr = data->getDataComponentsPtr();
		m_KVS = data->m_KVS;
		m_nDataEX = data->m_nDataEX;
	}


	GridCopyData::GridCopyData(const GridCopyData &rhs){
		setdata(rhs);
	}

	GridCopyData& GridCopyData::operator = ( const GridCopyData& data ){
		setdata(data);
		return *this;
	}

	void GridCopyData::setdata(const GridCopyData& rhs){
		resid = rhs.resid;
		num = rhs.num;
		duration = rhs.duration;
		toughness = rhs.toughness;
		enchantnum = rhs.enchantnum;
		enchants = rhs.enchants;
		tunestonenum = rhs.tunestonenum;
		tunestones = rhs.tunestones;
		userdata = rhs.userdata;
		userdata_str = rhs.userdata_str;
		runedata = rhs.runedata;
		effects = rhs.effects;
		gridix = rhs.gridix;
		componentsPtr = rhs.componentsPtr;
		maxduration = rhs.maxduration;
		m_KVS = rhs.m_KVS;
		m_nDataEX = rhs.m_nDataEX;
	}
