#ifndef __RUNE_DEF_H__
#define __RUNE_DEF_H__

//#include "proto_define.h"
#include "FlatSaveCommon_generated.h"
#include "coreMisc.h"
//#include "itemdata/GridDataComponent.h"
namespace google {
	namespace protobuf {
        template <typename Element>
		class RepeatedPtrField;
	}
}

namespace game {
	namespace common {
		class PB_ItemRune;
	}
}

#define BIT(x) (1 << x)

#define BIND_RUNE_EVENT_FN(fn) std::bind(&fn, this, std::placeholders::_1)

namespace jsonxx
{
	class Object;
}

//tolua_begin
//符文石等级
enum RuneStoneLevel
{
	RuneStoneLevelNone = 0,
	RuneStoneLow    = 1,//初级
	RuneStoneMiddle = 2,//中级
	RuneStoneHigh   = 3,//高级
};
//符文石分类
enum RuneStoneCategory
{
	RuneStoneCategoryNone = 0,
	RuneStoneAttack     = 1,//攻击符文石
	RuneStoneDefense    = 2,//防御符文石
	RuneStoneEfficiency = 4,//效率符文石
};

enum RuneOperateType
{
	RuneOperateAuth  = 1,//鉴定
	RuneOperateMerge = 2,//合成
	RuneOperateInlayAdd = 3,//镶嵌追加
	RuneOperateInlayReplace = 4,//镶嵌替换
	RuneOperateGenerateAddInlay = 5, //合成并且镶嵌
	RuneOperateGenerateReplaceInlay = 6, // 合成并且替换
};

//符文矿石
#define RUNE_ORE_BEGIN 453
#define RUNE_ORE_END 455
//未鉴定符文石
#define RUNE_UN_AUTH_BEGIN 11603
#define RUNE_UN_AUTH_END 11611
//已经鉴定的符文石
#define RUNE_AUTHED_BEGIN 11618
#define RUNE_AUTHED_END 11626
//符文鉴定石
#define RUNE_AUTHENTICATE 11612
#define RUNE_AUTHED_ACCURATE 11653
//453:攻击  454:防御 455:效率    符文矿石
inline bool isRuneOre(int itemid)
{
	return itemid >= RUNE_ORE_BEGIN && itemid <= RUNE_ORE_END;
}

inline bool isRuneStoneUnAuth(int itemid)//未鉴定符文石
{
	return itemid >= RUNE_UN_AUTH_BEGIN && itemid <= RUNE_UN_AUTH_END;
}

inline bool isRuneStoneAuthed(int itemid)//已鉴定符文石
{
	return itemid >= RUNE_AUTHED_BEGIN && itemid <= RUNE_AUTHED_END || itemid == RUNE_AUTHED_ACCURATE;
}

//转换 未鉴定符文石头id -> 对应的已鉴定
inline int translateUnAuth2Authed(int itemid)
{
    if (isRuneStoneAuthed(itemid)) {
        return itemid;
    }
	if(isRuneStoneUnAuth(itemid)){
		return itemid - RUNE_UN_AUTH_BEGIN + RUNE_AUTHED_BEGIN;
	}
	return 0;
}

// 万能激活石转换，根据tuneStone类型随机出对应的已鉴定符文石
inline int translateAuthenticate2Authed(int tuneStone)
{
    int runeId = 0;
    int r = GenRandomInt(1, 3);
    switch (tuneStone)
    {
    case 1:
        runeId = RUNE_AUTHED_BEGIN + 2;
        break;
    case 2:
        runeId = RUNE_AUTHED_BEGIN + 5;
        break;
    case 4:
        runeId = RUNE_AUTHED_BEGIN + 8;
        break;
    case 3:
        if (r < 2) {
            runeId = RUNE_AUTHED_BEGIN + 2;
        } else {
            runeId = RUNE_AUTHED_BEGIN + 5;
        }
        break;
    case 5:
        if (r < 2) {
            runeId = RUNE_AUTHED_BEGIN + 2;
        } else {
            runeId = RUNE_AUTHED_BEGIN + 8;
        }
        break;
    case 6:
        if (r < 2) {
            runeId = RUNE_AUTHED_BEGIN + 5;
        } else {
            runeId = RUNE_AUTHED_BEGIN + 8;
        }
        break;
    case 7:
        if (r < 2) {
            runeId = RUNE_AUTHED_BEGIN + 2;
        } else if (r < 3) {
            runeId = RUNE_AUTHED_BEGIN + 5;
        } else {
            runeId = RUNE_AUTHED_BEGIN + 8;
        }
        break;
    default:
        runeId = 0;
        break;
    }

    return runeId;
}

//符文石
inline bool isRuneStone(int itemid)
{
	return isRuneStoneUnAuth(itemid) || isRuneStoneAuthed(itemid);
}
//符文鉴定石
inline bool isRuneAuthenticate(int itemid)
{
	return itemid == RUNE_AUTHENTICATE;
}


//根据已鉴定的符文种类 和 等级 -> 对应的itemID
inline int getItemIDByRuneLevelAndCategory(int level, int category)
{
	int tmp = (int)(log((float)category)/log(2.0f));// 2,1,0
	return RUNE_AUTHED_BEGIN + tmp * 3 + level - 1;
}

//根据未鉴定的符文种类 和 等级 -> 对应的itemID
inline int getUnAuthItemIDByRuneLevelAndCategory(int level, int category)
{
	int tmp = (int)(log((float)category)/log(2.0f));// 2,1,0
	return RUNE_UN_AUTH_BEGIN + tmp * 3 + level - 1;
}

//根据道具的等级 获取道具 可镶嵌的符文个数
inline int getNumOfItemCanAddRune(int stuffType){//0木1皮2石3链4铁5金6钻
	//铁质1个槽，钛金3个槽，钻石5个槽
	if(stuffType < 4)
		return 0;
	return (stuffType - 4)*2 + 1;
}

//初级:1 中级:2 高级:3
inline int getRuneStoneLevel(int itemid)
{
	if (isRuneStoneUnAuth(itemid)) {
		return ((itemid - RUNE_UN_AUTH_BEGIN) % 3) + 1;
	}
	else if (itemid== RUNE_AUTHED_ACCURATE) {
		return RuneStoneHigh;
	}
	else if (isRuneStoneAuthed(itemid)) {
		return ((itemid - RUNE_AUTHED_BEGIN) % 3) + 1;
	}
	else if (isRuneAuthenticate(itemid)) {
		int count=GenRandomInt(0, 100);//万能符文石改为随机出等级
		if (count < 30) return RuneStoneLow;
		else if (count > 70) return RuneStoneMiddle;
		return RuneStoneHigh;
	}
	return RuneStoneLevelNone;
}
//攻击:1 防御:2  效率:4

inline int getRuneStoneCategory(int itemid)
{
	if (isRuneOre(itemid)) {//符文矿石
		return (int)pow(2.0f, itemid - RUNE_ORE_BEGIN);
	}
	else if (isRuneStoneUnAuth(itemid)) {
		return (int)pow(2.0f, (int)((itemid - RUNE_UN_AUTH_BEGIN) / 3));
	}
	else if (itemid == RUNE_AUTHED_ACCURATE) {
		return RuneStoneAttack + RuneStoneDefense + RuneStoneEfficiency;
	}
	else if (isRuneStoneAuthed(itemid)) {
		return (int)pow(2.0f, (int)((itemid - RUNE_AUTHED_BEGIN) / 3));
	}
	else if (isRuneAuthenticate(itemid)) {
		return RuneStoneAttack + RuneStoneDefense + RuneStoneEfficiency;
	}
	return RuneStoneCategoryNone;
}


inline std::string getHandRuneEffectName(int itemid)
{
	int level = getRuneStoneLevel(itemid);
	const char* levelStr = NULL;
	if(level == RuneStoneHigh)
		levelStr = "high";
	else if(level == RuneStoneMiddle)
		levelStr = "medium";
	else
		levelStr = "low";

	int category = getRuneStoneCategory(itemid);
	const char* categoryStr = NULL;
	if(category == RuneStoneAttack)
		categoryStr = "attack";
	else if(category == RuneStoneDefense)
		categoryStr = "defence";
	else
		categoryStr = "efficiency";

	char nameStr[32]  = {0};//是否自己的地图 （1自己的地图 0别人的地图）
	memset(nameStr, 0, sizeof(nameStr));
	sprintf(nameStr, "%s%s", levelStr, categoryStr );
	return nameStr;
}

//一个装备 最多镶嵌的符文个数
#define MAX_ITEM_RUNES  5
//符文容器 最大格子数(镶嵌页面5个格子, 合成页面3个格子)
#define RUNE_INLAY_CONTAINER_SIZE 5
#define RUNE_MERGE_CONTAINER_SIZE 3
#define RUNE_AUTH_CONTAINER_SIZE 1

//tolua_end

struct RuneDef;
class BackPackGrid;

//单条符文数据  
class EXPORT_SANDBOXENGINE GridRuneItemData;
class GridRuneItemData //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	int   rune_id;//ID*100 + level
	float rune_val0;
	float rune_val1;
	int   item_id;//需要知道镶嵌的是哪种符文石--根据rune_id确定不了

	GridRuneItemData();
	GridRuneItemData(int id, float val0, float val1, int itemid);
	GridRuneItemData(const GridRuneItemData& rval);
	//tolua_end
	GridRuneItemData& operator = ( const GridRuneItemData& rval );
	//tolua_begin
	void resetGridRuneItemData();
	const RuneDef* getRuneDef()const ;
	int getRuneId()const;
	float getRuneVal0()const;
	float getRuneVal1()const;
	int getRuneItemId()const;
	static const RuneDef* getRuneDef(int rune_id);

	bool isDurationType() const ;
	int getEnchantType() const ;
	//tolua_end
}; //tolua_exports

//BackPackGrid里保存的整个符文数据
class EXPORT_SANDBOXENGINE GridRuneData;
class GridRuneData //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	bool setOneGridRuneItem(const GridRuneItemData &one, int index = -1);
	bool addOneGridRuneItem(const GridRuneItemData &one);//添加一条符文信息


	bool removeOneGridRuneItem(int id);//删除一条指定id的符文  id = RuneID*100 + level


	GridRuneData();
	GridRuneData(const GridRuneData& data);
	//tolua_end
	GridRuneData& operator = ( const GridRuneData& data );
	//tolua_begin
	void reset();
	void setdata(const GridRuneData& data);

	bool clear();//清除所有符文


	int getDuration() const;//获取符文 耐久加成百分比


	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::ItemGridRune>>> save(flatbuffers::FlatBufferBuilder &builder)const;
	void load(const flatbuffers::Vector<flatbuffers::Offset<FBSave::ItemGridRune>> *src);

	void load(const google::protobuf::RepeatedPtrField<game::common::PB_ItemRune> &src, int size);
	void save(google::protobuf::RepeatedPtrField<game::common::PB_ItemRune> *dest)const;
	//新存储接口
	void save(jsonxx::Object &obj);
	void load(const jsonxx::Object &obj);

	const RuneDef * findDef(int type) const;
	const RuneDef * findDefByIndex(int index) const;

	int findIndex(int id)const;
	const GridRuneItemData& getItemByIndex(int i)const;
	int isConflick(int newid, int exceptIndex = -1);//新条目id 是否与 已添加的冲突
	int getRuneNum()const;
	//tolua_end
private:

	int rune_num;//符文条目条数
	GridRuneItemData rune_data[MAX_ITEM_RUNES];
}; //tolua_exports

struct GridEffect
{
	std::string effectname;
	float scale;
	GridEffect()
	{
		effectname = "";
		scale = 0;
	}
	void operator=(const GridEffect& rhs)
	{
		effectname = rhs.effectname;
		scale = rhs.scale;
	}
};
//从BackPackGrid之间拷贝 需复制的全部信息
struct EXPORT_SANDBOXENGINE GridCopyData;
class GridDataComponent;
struct GridCopyData{ //tolua_exports
	//tolua_begin
	int resid;
	int num;
	int duration;
	int maxduration;
	int toughness;
	int enchantnum;
	int tunestonenum;
	void *userdata;
	//const char *userdata_str;
	// lua中使用指针传值会获取不到，改为string code_by:liya
	std::string userdata_str;
	const GridRuneData* runedata;
	const std::vector<GridDataComponent*>* componentsPtr;
	//tolua_end

	const int* enchants;
	const int* tunestones;
	std::map<std::string, GridEffect> effects;
	std::map<std::string, int> m_KVS;
	int gridix;
	int m_nDataEX;

	//tolua_begin
	GridCopyData();
	GridCopyData(int id, int n);
	GridCopyData(int id_, int num_, int duration_, int toughness_, int enchantnum_, const int* enchants_, int tunestonenum_, const int* tunestones_,
		void* userdata_, const char* userdata_str_, const GridRuneData* runedata_, int maxduration_ = 0);

	GridCopyData(const BackPackGrid* data);

	GridCopyData(const GridCopyData &rhs);

	void setdata(const GridCopyData& rhs);
	//tolua_end

	GridCopyData& operator = ( const GridCopyData& data );
}; //tolua_exports


#endif//__RUNE_DEF_H__