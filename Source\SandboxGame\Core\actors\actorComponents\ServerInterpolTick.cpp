#include "ServerInterpolTick.h"
#include "SandboxEventDispatcherManager.h"
#include "ClientActor.h"
#include "ActorLocoMotion.h"
#include "IClientGameManagerInterface.h"
#include "SandboxSchedulerManager.h"
#include "world.h"
#include "BlockMaterial.h"

using namespace MNSandbox;


IMPLEMENT_COMPONENTCLASS(ServerInterpolTick)

ServerInterpolTick::ServerInterpolTick()
	: m_ServerYaw(0), m_ServerPitch(0), m_ServerInterpolTicks(0)
{
}

void ServerInterpolTick::OnTick()
{
	//assert(m_owner != nullptr);
	if(m_ServerInterpolTicks > 0)
	{
		if (!GetOwner()) return ;
		ClientActor* m_owner = GetOwner()->ToCast<ClientActor>();
		if (!m_owner) return ;
		ActorLocoMotion* locoMotion = m_owner->getLocoMotion();
		if (locoMotion == nullptr)
		{
			return;
		}

		//locoMotion->m_RotateYaw     += WrapAngleTo180(m_ServerYaw   - locoMotion->m_RotateYaw)/m_ServerInterpolTicks;
		//locoMotion->m_RotationPitch += WrapAngleTo180(m_ServerPitch - locoMotion->m_RotationPitch)/m_ServerInterpolTicks;
		//上面的插值写法有问题，也没必要搞什么插值，直接赋值效果就很OK了
		locoMotion->m_RotateYaw = WrapAngleTo180(m_ServerYaw);
		locoMotion->m_RotationPitch = WrapAngleTo180(m_ServerPitch);

		WCoord targetPos = locoMotion->m_Position;
		targetPos += (m_ServerPos - locoMotion->m_Position) / m_ServerInterpolTicks;
		m_ServerInterpolTicks--;
		if (!m_ServerInterpolTicks)
		{
			targetPos = m_ServerPos;
		}

		WCoord blockpos = targetPos / BLOCK_SIZE;
		auto pmtl = m_owner->getWorld()->getBlockMaterial(blockpos);
		if (pmtl && pmtl->getBlockResID() > 0 && pmtl->getBlockMoveCollide() == 1)
		{
			targetPos.y = (blockpos.y + 1) * BLOCK_SIZE;
		}
		locoMotion->setPosition(targetPos.x, targetPos.y, targetPos.z);
	}
}

void ServerInterpolTick::moveToPosition(const WCoord &pos, float yaw, float pitch, int interpol_ticks)
{
	int ticks = interpol_ticks;
	auto curGame = GetIClientGameManagerInterface()->getICurGame(GameType::MpGameSurvive);
	if (!GetOwner()) return ;
	ClientActor* m_owner = GetOwner()->ToCast<ClientActor>();
	if (!m_owner) return ;
	if (curGame)
	{
		//MpGameSurvive *game = dynamic_cast<MpGameSurvive *>(curGame);
		//if (game)
	//	{
			ticks += curGame->getNetDelayTick();
		//}
	}
	/* flag 用来标记此次移动协议是否包含了位移/角度
	 * 主要为解决前次位移协议未移动完成时(m_ServerPitch未走完), 后续位移如果不带位置会使用当前位置作为目标, 导致最终显示无法到达主机位置
	 * 2022.07.12 by huanglin
	 */
	int flag = m_owner->getNetMoveFlag();
	if (!m_ServerInterpolTicks || (flag & MF_POSITION))
		m_ServerPos   = pos;
	if (!m_ServerInterpolTicks || (flag & MF_YAW_PITCH)){
		m_ServerYaw   = yaw;
		m_ServerPitch = pitch;
	}
	m_ServerInterpolTicks = ticks;
}

void ServerInterpolTick::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	owner->Event().SubscribeEvent("moveToPosition", this, (SandboxClassCallback)&ServerInterpolTick::OnMoveToPosition, "ServerInterpolTick_1");

	Super::OnEnterOwner(owner);

	BindOnTick();
}

void ServerInterpolTick::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	UnBindOnTick();
	SchedulerMgr().DestroySchedulerByName("ServerInterpolTick_1");
	Super::OnLeaveOwner(owner);
}
bool ServerInterpolTick::isMoving()
{
	return m_ServerInterpolTicks > 0;
}
///////////////////////////////////////////////////////////////////////////////////

SandboxResult SANDBOXAPI ServerInterpolTick::OnMoveToPosition(SandboxContext context)
{
	//取消插值
	if (context.HasKey("cancel") && context.GetData_Bool("cancel"))
	{
		if (m_ServerInterpolTicks > 0)
		{
			m_ServerYaw = 0;
			m_ServerPitch = 0;
			m_ServerInterpolTicks = 0;
		}
		return SandboxResult(this);
	}

	moveToPosition(context.GetData_UserObject<WCoord>("pos")
		, (float)context.GetData_Number("yaw")
		, (float)context.GetData_Number("pitch")
		, (int)context.GetData_Number("interpol_ticks")
	);
	return SandboxResult(this);
}
