/*
* 事件、网络协议相关
*/
#include "SandboxActorSubsystem.h"
#include "SandBoxManager.h"
#include "ClientActorArrow.h"
#include "ActorExpOrb.h"
#include "ActorFallingSand.h"
#include "ActorFlyingBlock.h"
#include "ActorTrainCar.h"
#include "ActorFireBall.h"
#include "ActorEarthCoreMan.h"
#include "ActorBomb.h"
#include "ActorDragon.h"
#include "ActorBoat.h"
#include "ClientActorThrowable.h"
#include "ActorDungeonEye.h"
#include "ActorFirework.h"
#include "ActorNpc.h"
#include "ActorRiverLantern.h"
#include "ActorMechaUnit.h"
#include "ActorChassis.h"
#include "ActorVillager.h"
#include "ActorSkinNpc.h"
#include "ActorIslandBusinessman.h"
#include "ClientAquaticComponent.h"
#include "ClientPipelineActor.h"
#include "ActorSnowHare.h"
#include "ActorFallingGravel.h"
#include "ClientActorNewArrow.h"
#include "ClientActorEgg.h"
#include "ClientActorSnowBall.h"
#include "ActorBall.h"
#include "ActorBasketBall.h"
#include "ActorRocket.h"
#include "ActorHalfGiant.h"
#include "ClientActorHook.h"
#include "ClientActorAttract.h"
#include "ClientActorImpulse.h"
#include "ClientActorBlockLaser.h"
#include "ClientActorLaser.h"
#include "ActorVehicleAssemble.h"
#include "ActorDragonMount.h"
#include "ActorShapeShiftHorse.h"
#include "VehicleContainerActioner.h"
#include "ActorMoonMount.h"
#include "ActorDouDuMount.h"
#include "ActorPumpkinHorse.h"
#include "ActorSavagePriest.h"
#include "ActorFlySnakeGod.h"
#include "ActorSandMan.h"
#include "ActorPackHorse.h"
#include "ActorPatrolMob.h"
#include "ClientVacantBoss.h"
#include "ActorIceBall.h"
#include "ActorFrostWyrm.h"
#include "ActorSandworm.h"
#include "ActorStorageBoxHorse.h"
#include "ActorDesertBusinessman.h"
#include "ActorDesertVillager.h"
#include "ActorFishingVillager.h"
#include "ActorFisherMan.h"
#include "ActorSeaSpiritGuarding.h"
#include "ActorDriftBottle.h"
#include "ActorPirateShip.h"
#include "ActorCrab.h"
#include "ActorHippocampus.h"
#include "ClientActorPirateChest.h"
#include "ClientActorThornBall.h"
#include "ClientActorCoconut.h"
#include "ActorPushSnowBall.h"
#include "ActorSnowMan.h"
#include "ActorYak.h"
#include "ClientActorIcicle.h"
#include "ActorPortal.h"
#include "ActorVortex.h"
#include "ActorTravelingTrader.h"
#include "ActorPlayerCorpse.h"
#include "ActorMobCorpse.h"
#include "ActorCubeChest.h"
#include "ActorAirPlane.h"
#include "FurnaceContainer.h"
#include "FurnaceContainerArray.h"
#include "container_world_lua.h"
#include "container_mobspawner.h"
#include "container_funnel.h"
#include "container_emitter.h"
#include "container_horse_egg.h"
#include "container_dragoncup.h"
#include "container_itemexpo.h"
#include "container_mecha.h"
#include "container_lightmushroom.h"
#include "container_sensor.h"
#include "container_railknot.h"
#include "container_alientotem.h"
#include "container_interpreterunit.h"
#include "container_regionreplicator.h"
#include "container_buildblueprint.h"	
#include "container_onequarterblock.h"
#include "container_giantscallops.h"
#include "container_gianttotem.h"
#include "container_collider.h"
#include "container_modelcraft.h"
#include "container_transfer.h"
#include "container_bookEditorTable.h"
#include "container_actormodel.h"
#include "container_wheel.h"
#include "container_fullycustommodel.h"
#include "container_actioner.h"
#include "container_tombstone.h"
#include "container_importmodel.h"
#include "container_altar.h"
#include "container_ostrich_egg.h"
#include "container_honorFrame.h"
#include "container_fusioncage.h"
#include "container_pot.h"
#include "container_keypedestal.h"
#include "container_starstationtransfercabin.h"
#include "container_RevivalStatue.h"
#include "container_feedtrough.h"
#include "container_peristele.h"
#include "container_keyEffect.h"
#include "container_miniclub.h"
#include "container_populusleaf.h"
#include "container_venom.h"
#include "container_fishframe.h"
#include "container_poseidonstatue.h"
#include "container_smalltorch.h"
#include "container_colorpalette.h"
#include "container_coconut.h"
#include "container_driverseat_model.h"
#include "container_stake.h"
#include "container_solidsand.h"
#include "container_glowstick.h"
#include "container_seaplant.h"

#include "container_detectionpipe.h"
#include "container_collectingpipe.h"
#include "container_keydoor.h"
#include "container_stretchForzen.h"
#include "container_thick.h"
#include "container_icecrystalshroom.h"
#include "container_villageflagbuilding.h"
#include "container_villagetotemice.h"
#include "container_villageflagice.h"
#include "container_schoolfence.h"
#include "container_electric_ray.h"
#include "container_animal_egg.h"
#include "container_arrowsigns.h"
#include "container_weaponrack.h"
#include "container_manualEmitter.h"
#include "container_cliptrap.h"
#include "container_voidDragonFlower.h"
#include "container_voidBellFlower.h"
#include "container_deathJar.h"
#include "ContainerStove.h"
#include "container_newpot.h"
#include "container_dummy.h"
#include "container_starstationcargo.h"
#include "container_hydarm.h"
#include "container_stone_core.h"
#include "container_talkingstatue.h"
#include "container_polaroid.h"
#include "container_worldselectmobspawner.h"
#include "container_modtransferconsole.h"
#include "container_computer.h"
#include "container_monstersummoner.h"
#include "container_territory.h"
#include "container_socdoor.h"
#include "container_socautodoor.h"
#include "container_decomposition.h"
#include "container_socworkbench.h"
#include "container_erosion.h"
#include "container_erosion_storage.h"
#include "container_torch.h"
#include "container_research.h"
#include "EffectComponent.h"

#include "DangerNightManager.h"
#include "WaterPressureManager.h"
#include "TemperatureManager.h"
#include "RadiationManager.h"
#include "PixelMapMgr.h"
#include "AvatarEffectMgr.h"
#include "SMediaPlayerMgr.h"
#ifdef DEDICATED_SERVER
#include "DataHubService.h"
#endif // DEDICATED_SERVER
#include "ThermalSpringManager.h"
#include "DesertTradeCaravanMgr.h"
#include "StarStationTransferMgr.h"
#include "UgcAssetMgr.h"
#include "WeatherManager.h"
#include "ClientActorFuncWrapper.h"
#include "ClientActorHelper.h"
#include "ThornBallComponent.h"
#include "RiddenComponent.h"
#include "DropItemComponent.h"
#include "TemperatureComponent.h"
#include "DissolveComponent.h"
#include "ModPackMgr.h"
#include "BuildMgr.h"
#include "CityMgr.h"
#include "BiomeModConfig.h"
#include "UgcEcosysBuild.h"
#include "container_sandboxGame.h"
#include "DynamicContainer.h"

using namespace MNSandbox;

bool SandboxActorSubsystem::CreateModuleEvent()
{
	//new出对应的ActorManager对象
	SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("ActorManager_New");
	m_eventCallbacks["ActorManager_New"] = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("ActorManager_New", nullptr,
		[&](SandboxContext context) -> MNSandbox::SandboxResult {
			World* worldptr = context.GetData_Usertype<World>("worldptr");
			ActorManager* actormgr = ENG_NEW(ActorManager)(worldptr);
			return SandboxResult(nullptr, true).SetData_Usertype<ActorManagerInterface>("actormanager_ptr", actormgr);
		});
	SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("ClientMob_New");
	m_eventCallbacks["ClientMob_New"] = SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("ClientMob_New", nullptr,
		[&](SandboxContext context) -> SandboxResult {
			ClientMob* mob = ENG_NEW(ClientMob)();
			return SandboxResult(nullptr, true).SetData_Usertype<IClientActor>("clientmob_ptr", mob);
		});

	SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("GetPixelMapMgr");
	m_eventCallbacks["GetPixelMapMgr"] = SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("GetPixelMapMgr", nullptr,
		[&](SandboxContext context) -> SandboxResult {
			PixelMapMgr::GetInstancePtr();
			return SandboxResult(nullptr, true);
		});
	SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("GetAvatarEffectMgr");
	m_eventCallbacks["GetAvatarEffectMgr"] = SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("GetAvatarEffectMgr", nullptr,
		[&](SandboxContext context) -> SandboxResult {
			AvatarEffectMgr::GetInstance();
			return SandboxResult(nullptr, true);
		});
#ifndef DEDICATED_SERVER
	SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("GetSMediaPlayerMgr");
	m_eventCallbacks["GetSMediaPlayerMgr"] = SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("GetSMediaPlayerMgr", nullptr,
		[&](SandboxContext context) -> SandboxResult {
			SMediaPlayerMgr::GetSingleton();
			return SandboxResult(nullptr, true);
		});
#endif	
#ifdef DEDICATED_SERVER
	SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("GetDataHubService");
	m_eventCallbacks["GetDataHubService"] = SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("GetDataHubService", nullptr,
		[&](SandboxContext context) -> SandboxResult {
			DataHubService::GetInstance();
			return SandboxResult(nullptr, true);
		});
#endif // DEDICATED_SERVER

	SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("GetThermalSpringMgr");
	m_eventCallbacks["GetThermalSpringMgr"] = SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("GetThermalSpringMgr", nullptr,
		[&](SandboxContext context) -> SandboxResult {
			GetThermalSpringMgr();
			return SandboxResult(nullptr, true);
		});

	SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("GetDesertTradeCaravanMgr");
	m_eventCallbacks["GetDesertTradeCaravanMgr"] = SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("GetDesertTradeCaravanMgr", nullptr,
		[&](SandboxContext context) -> SandboxResult {
			GetDesertTradeCaravanMgr();
			return SandboxResult(nullptr, true);
		});

	SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("GetStarStationTransferMgr");
	m_eventCallbacks["GetStarStationTransferMgr"] = SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("GetStarStationTransferMgr", nullptr,
		[&](SandboxContext context) -> SandboxResult {
			GetStarStationTransferMgr();
			return SandboxResult(nullptr, true);
		});

	SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("GetUgcAssetMgr");
	m_eventCallbacks["GetUgcAssetMgr"] = SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("GetUgcAssetMgr", nullptr,
		[&](SandboxContext context) -> SandboxResult {
			//如果UgcAssetMgr单例为空，此处确保new一个新的
			if (UgcAssetMgr::GetInstancePtr() == nullptr)
			{
				ENG_NEW(UgcAssetMgr)();
			}
			return SandboxResult(nullptr, true);
		});

	SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("GetCityConfig");
	m_eventCallbacks["GetCityConfig"] = SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("GetCityConfig", nullptr,
		[&](SandboxContext context) -> SandboxResult {
			//如果CityConfig单例为空，此处确保new一个新的
			if (CityConfig::getSingletonPtr() == nullptr)
			{
				ENG_NEW(CityConfig)();
			}
			return SandboxResult(nullptr, true);
		});
	SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("GetModBiomeConfig");
	m_eventCallbacks["GetModBiomeConfig"] = SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("GetModBiomeConfig", nullptr,
		[&](SandboxContext context) -> SandboxResult {
			//如果ModBiomeConfig单例为空，此处确保new一个新的
			if (BiomeModConfig::getSingletonPtr() == nullptr)
			{
				ENG_NEW(BiomeModConfig)();
			}
			return SandboxResult(nullptr, true);
		});
	SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("GetUgcEcosysBuild");
	m_eventCallbacks["GetUgcEcosysBuild"] = SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("GetUgcEcosysBuild", nullptr,
		[&](SandboxContext context) -> SandboxResult {
			//如果UgcEcosysBuild单例为空，此处确保new一个新的
			if (UgcEcosysBuild::getSingletonPtr() == nullptr)
			{
				ENG_NEW(UgcEcosysBuild)();
			}
			return SandboxResult(nullptr, true);
		});
	SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("GetModPackMgr");
	m_eventCallbacks["GetModPackMgr"] = SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("GetModPackMgr", nullptr,
		[&](SandboxContext context) -> SandboxResult {

			ModPackMgr::GetInstancePtr();

			return SandboxResult(nullptr, true);
		});

	SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("WorldManager_Game_Init");
	m_eventCallbacks["WorldManager_Game_Init"] = SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("WorldManager_Game_Init", nullptr,
		[&](SandboxContext context) -> SandboxResult {
			ClientActor::ResetObjId();
			WaterPressureManagerInterface* waterPressMgr = ENG_NEW(WaterPressureManager);
			TemperatureManagerInterface* temperatureMgr = ENG_NEW(TemperatureManager);
			RadiationManagerInterface* radiationMgr = ENG_NEW(RadiationManager);

			return SandboxResult(nullptr, true).SetData_Usertype<WaterPressureManagerInterface>("waterInterface", waterPressMgr).
				SetData_Usertype<TemperatureManagerInterface>("temperatureInterface", temperatureMgr).
				SetData_Usertype<RadiationManagerInterface>("radiationInterface", radiationMgr);
		});

	SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("WorldManager_DangerNight_Create");
	m_eventCallbacks["WorldManager_DangerNight_Create"] = SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("WorldManager_DangerNight_Create", nullptr,
		[&](SandboxContext context) -> SandboxResult {

			DangerNightManager::create();

			return SandboxResult(nullptr, true);
		});

	SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("World_Game_Init");
	m_eventCallbacks["World_Game_Init"] = SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("World_Game_Init", nullptr,
		[&](SandboxContext context) -> SandboxResult {

			World* world = context.GetData_Usertype<World>("world");
			BuildMgrInterface* buildmgrInterface = ENG_NEW(BuildMgr)(world);
			CityMgrInterface* citymgrInterface = ENG_NEW(CityMgr)(world);

			return SandboxResult(nullptr, true).SetData_Usertype<BuildMgrInterface>("buildmgrInterface", buildmgrInterface).
				SetData_Usertype<CityMgrInterface>("citymgrInterface", citymgrInterface);
		});
	/*SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("ActorBody_clearEquipItems");
	m_eventCallbacks["ActorBody_clearEquipItems"] = SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("ActorBody_clearEquipItems", nullptr,
		[&](SandboxContext context) -> SandboxResult
		{
			Rainbow::Model* model = context.GetData_Usertype<Rainbow::Model>("model");
			ActorBody::clearEquipItems(model);

			return SandboxResult(nullptr, true);
		});*/
	SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("ChunkSave_CreateActor");
	m_eventCallbacks["ChunkSave_CreateActor"] = SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("ChunkSave_CreateActor", nullptr,
		[&](SandboxContext context) -> SandboxResult
		{
			MNSandbox::SandboxNode** actorptrptr = context.GetData_Usertype<MNSandbox::SandboxNode*>("actorptrptr");
			//如果这里不为空，就证明其他事件已经先new出了一个ClientActor对象
			//若要执意继续执行自己的逻辑，那就记得要delete掉老的对象
			if (nullptr != (*actorptrptr))
			{
				//这里可能前面有重复的actortype了 检查确认一下
				Assert(false);
				return SandboxResult(nullptr, true);
			}
			FBSave::SectionActorUnion actortype = (FBSave::SectionActorUnion)(context.GetData_Number("actortype"));
			switch (actortype)
			{
			case FBSave::SectionActorUnion_ActorMob:
				(*actorptrptr) = ClientMob::NewLonelyObject();//SANDBOX_NEW(ClientMob);
				break;
			case FBSave::SectionActorUnion_ActorArrow:
				(*actorptrptr) = SANDBOX_NEW(ClientActorArrow);
				break;
			case FBSave::SectionActorUnion_ActorNewArrow:
				(*actorptrptr) = SANDBOX_NEW(ClientActorNewArrow);
				break;
			case FBSave::SectionActorUnion_ActorEgg:
				(*actorptrptr) = SANDBOX_NEW(ClientActorEgg);
				break;
			case FBSave::SectionActorUnion_ActorSnowBall:
				(*actorptrptr) = SANDBOX_NEW(ClientActorSnowBall);
				break;
			case FBSave::SectionActorUnion_ActorProjectile:
				(*actorptrptr) = SANDBOX_NEW(ClientActorProjectile);
				break;
			case FBSave::SectionActorUnion_ActorExpOrb:
				(*actorptrptr) = SANDBOX_NEW(::ActorExpOrb);
				break;
			case FBSave::SectionActorUnion_ActorFallSand:
				(*actorptrptr) = SANDBOX_NEW(ActorFallingSand);
				break;
			case FBSave::SectionActorUnion_ActorFlyBlock:
				(*actorptrptr) = SANDBOX_NEW(ActorFlyingBlock);
				break;
			case FBSave::SectionActorUnion_ActorItem:
				(*actorptrptr) = SANDBOX_NEW(ClientItem);
				break;
			case FBSave::SectionActorUnion_ActorMinecartEmpty:
				(*actorptrptr) = SANDBOX_NEW(::ActorTrainCar);
				break;
			case FBSave::SectionActorUnion_ActorLargeFireball:
				(*actorptrptr) = SANDBOX_NEW(ActorLargeFireBall);
				break;
			case FBSave::SectionActorUnion_ActorLavaBall:
				(*actorptrptr) = SANDBOX_NEW(::ActorLavaBall);
				break;
			case FBSave::SectionActorUnion_ActorChaosBall:
				(*actorptrptr) = SANDBOX_NEW(::ActorChaosBall);
				break;
			case FBSave::SectionActorUnion_ActorEnderman:
				(*actorptrptr) = SANDBOX_NEW(::ActorEarthCoreMan);
				break;
			case FBSave::SectionActorUnion_ActorTNT:
				(*actorptrptr) = SANDBOX_NEW(::ActorBomb);
				break;
			case FBSave::SectionActorUnion_ActorEnderEye:
				(*actorptrptr) = SANDBOX_NEW(::ActorDungeonEye);
				break;
			case FBSave::SectionActorUnion_ActorThrowable:
				(*actorptrptr) = SANDBOX_NEW(::ClientActorThrowable);
				break;
			case FBSave::SectionActorUnion_ActorDragon:
				(*actorptrptr) = SANDBOX_NEW(::ActorDragon);
				break;
			case FBSave::SectionActorUnion_ActorTrader:
				(*actorptrptr) = SANDBOX_NEW(::ActorTrader);
				break;
			case FBSave::SectionActorUnion_ActorFirework:
				(*actorptrptr) = SANDBOX_NEW(::ActorFirework);
				break;
			case FBSave::SectionActorUnion_ActorBoat:
				(*actorptrptr) = SANDBOX_NEW(::ActorBoat);
				break;
			case FBSave::SectionActorUnion_ActorHorse:
				(*actorptrptr) = SANDBOX_NEW(::ActorHorse);
				break;
			case FBSave::SectionActorUnion_ActorNpc:
				(*actorptrptr) = SANDBOX_NEW(::ActorNpc);
				break;
			case FBSave::SectionActorUnion_ActorAquaticMob:
				// (*actorptrptr) = SANDBOX_NEW(::ClientAquaticMob);
			{
				ClientMob* mob = SANDBOX_NEW(::ClientMob);
				mob->cacheClientAquaticComponent(mob->CreateComponent<ClientAquaticComponent>("ClientAquaticComponent"));
	
				(*actorptrptr) = mob;
				break;
			}
			case FBSave::SectionActorUnion_ActorRiverLantern:
				(*actorptrptr) = SANDBOX_NEW(::ActorRiverLantern);
				break;
			case FBSave::SectionActorUnion_ActorMechaUnit:
				(*actorptrptr) = SANDBOX_NEW(::ActorMechaUnit);
				break;
			case FBSave::SectionActorUnion_ActorFallGravel:
				(*actorptrptr) = SANDBOX_NEW(::ActorFallingGravel);
				break;
			case FBSave::SectionActorUnion_ActorFlyMob:
				//(*actorptrptr) = SANDBOX_NEW(::ClientFlyMob);
			{
				ClientMob* mob = SANDBOX_NEW(::ClientMob);
				mob->cacheClientFlyComponent(mob->CreateComponent<ClientFlyComponent>("ClientFlyComponent"));
				(*actorptrptr) = mob;
				break;
			}
			case FBSave::SectionActorUnion_ActorThrowBlock:
				(*actorptrptr) = SANDBOX_NEW(::ActorThrowBlock);
				break;
			case FBSave::SectionActorUnion_ActorBall:
				(*actorptrptr) = SANDBOX_NEW(::ActorBall);
				break;
			case FBSave::SectionActorUnion_ActorRocket:
				(*actorptrptr) = SANDBOX_NEW(::ActorRocket);
				break;
			case FBSave::SectionActorUnion_ActorHalfGiant:
				(*actorptrptr) = SANDBOX_NEW(::ActorHalfGiant);
				break;
			case FBSave::SectionActorUnion_ActorGhost:
				(*actorptrptr) = SANDBOX_NEW(::ActorGhost);
				break;
			case FBSave::SectionActorUnion_ActorGiant:
				(*actorptrptr) = SANDBOX_NEW(::ActorGiant);
				break;
			case FBSave::SectionActorUnion_ActorHook:
				(*actorptrptr) = SANDBOX_NEW(::ClientActorHook);
				break;
			case FBSave::SectionActorUnion_ActorLaser:
				(*actorptrptr) = SANDBOX_NEW(::ClientActorLaser);
				break;
			case FBSave::SectionActorUnion_ActorAttract:
				(*actorptrptr) = SANDBOX_NEW(::ClientActorAttract);
				break;
			case FBSave::SectionActorUnion_ActorImpulse:
				(*actorptrptr) = SANDBOX_NEW(::ClientActorImpulse);
				break;
			case FBSave::SectionActorUnion_ActorBlockLaser:
				(*actorptrptr) = SANDBOX_NEW(::ClientActorBlockLaser);
				break;
			case FBSave::SectionActorUnion_ActorVehicleAssemble:
				(*actorptrptr) = SANDBOX_NEW(::ActorVehicleAssemble);
				break;
			case FBSave::SectionActorUnion_ActorChassis:
				(*actorptrptr) = SANDBOX_NEW(::ActorChassis);
				break;
			case FBSave::SectionActorUnion_ActorBasketBall:
				(*actorptrptr) = SANDBOX_NEW(::ActorBasketBall);
				break;
			case FBSave::SectionActorUnion_ActorShapeShiftHorse:
				(*actorptrptr) = SANDBOX_NEW(::ActorShapeShiftHorse);
				break;
			case FBSave::SectionActorUnion_ActorDragonMount:
				(*actorptrptr) = SANDBOX_NEW(::ActorDragonMount);
				break;
			case FBSave::SectionActorUnion_ActorVillager:
				(*actorptrptr) = SANDBOX_NEW(::ActorVillager);
				break;
			case FBSave::SectionActorUnion_ActorSouvenir:
				(*actorptrptr) = SANDBOX_NEW(::ClientSouvenir);
				break;
			case FBSave::SectionActorUnion_ActorMoonMount:
				(*actorptrptr) = SANDBOX_NEW(::ActorMoonMount);
				break;
			case FBSave::SectionActorUnion_ActorSkinNpc:
				(*actorptrptr) = SANDBOX_NEW(::ActorSkinNpc);
				break;
			case FBSave::SectionActorUnion_ActorDouDuHorse:
				(*actorptrptr) = SANDBOX_NEW(ActorDouDuMount);
				break;
			case FBSave::SectionActorUnion_ActorSavagePriest:
				(*actorptrptr) = SANDBOX_NEW(::ActorSavagePriest);
				break;
			case FBSave::SectionActorUnion_ActorFlySnakeGod:
				(*actorptrptr) = SANDBOX_NEW(::ActorFlySnakeGod);
				break;
			case FBSave::SectionActorUnion_ActorPoisonBall:
				(*actorptrptr) = SANDBOX_NEW(::ActorPoisonBall);
				break;
			case FBSave::SectionActorUnion_ActorVacantBoss:
				(*actorptrptr) = SANDBOX_NEW(ClientVacantBoss);
				break;
			case FBSave::SectionActorUnion_ActorPumpkinHorse:
				(*actorptrptr) = SANDBOX_NEW(::ActorPumpkinHorse);
				break;
			case FBSave::SectionActorUnion_ActorTrixenieMob:
				(*actorptrptr) = SANDBOX_NEW(ClientTrixenieMob);
				break;
			case FBSave::SectionActorUnion_ActorFrostWyrm:
				(*actorptrptr) = SANDBOX_NEW(::ActorFrostWyrm);
				break;
			case FBSave::SectionActorUnion_ActorIceBall_Normal:
				(*actorptrptr) = SANDBOX_NEW(::ActorIceBall_Normal);
				break;
			case FBSave::SectionActorUnion_ActorSandworm:
				(*actorptrptr) = SANDBOX_NEW(::ActorSandworm);
				break;
			case FBSave::SectionActorUnion_ActorSandMan:
				(*actorptrptr) = SANDBOX_NEW(::ActorSandMan);
				break;
			case FBSave::SectionActorUnion_ActorPackHorse:
				(*actorptrptr) = SANDBOX_NEW(::ActorPackHorse);
				break;
			case FBSave::SectionActorUnion_ActorStorageBoxHorse:
				(*actorptrptr) = SANDBOX_NEW(::ActorStorageBoxHorse);
				break;
			case FBSave::SectionActorUnion_ActorDesertBusInessman:
				(*actorptrptr) = SANDBOX_NEW(::ActorDesertBusInessMan);
				break;
			case FBSave::SectionActorUnion_ActorDesertBusInessmanGuard:
				(*actorptrptr) = SANDBOX_NEW(::ActorDesertBusInessManGuard);
				break;
			case FBSave::SectionActorUnion_ActorDesertVillger:
				(*actorptrptr) = SANDBOX_NEW(::ActorDesertVillager);
				break;
			case FBSave::SectionActorUnion_ActorFishingVillager:
				(*actorptrptr) = SANDBOX_NEW(::ActorFishingVillager);
				break;
			case FBSave::SectionActorUnion_ActorFisherMan:
				(*actorptrptr) = SANDBOX_NEW(::ActorFisherMan);
				break;
			case FBSave::SectionActorUnion_ActorSeaSpiritGuarding:
				(*actorptrptr) = SANDBOX_NEW(::ActorSeaSpiritGuarding);
				break;
			case FBSave::SectionActorUnion_ActorPatrolMob:
				(*actorptrptr) = SANDBOX_NEW(::ActorPatrolMob);
				break;
			case FBSave::SectionActorUnion_ActorIslandBusInessman:
				(*actorptrptr) = SANDBOX_NEW(::ActorIslandBusInessMan);
				break;
			case FBSave::SectionActorUnion_ActorDriftBottle:
				(*actorptrptr) = SANDBOX_NEW(::ActorDriftBottle);
				break;
			case FBSave::SectionActorUnion_ActorThornBall:
				(*actorptrptr) = SANDBOX_NEW(::ClientActorThornBall);
				break;
			case FBSave::SectionActorUnion_ActorPirateShip:
				(*actorptrptr) = SANDBOX_NEW(::ActorPirateShip);
				break;
			case FBSave::SectionActorUnion_ActorPirateChest:
				(*actorptrptr) = SANDBOX_NEW(::ClientActorPirateChest);
				break;
			case FBSave::SectionActorUnion_ActorCoconut:
				(*actorptrptr) = SANDBOX_NEW(::ClientActorCoconut);
				break;
			case FBSave::SectionActorUnion_ActorCrab:
				(*actorptrptr) = SANDBOX_NEW(::ActorCrab);
				break;
			case FBSave::SectionActorUnion_ActorHippocampus:
				(*actorptrptr) = SANDBOX_NEW(::ActorHippocampus);
				break;
			case FBSave::SectionActorUnion_ActorHippocampusHorse:
				(*actorptrptr) = SANDBOX_NEW(::ActorHippocampusHorse);
				break;
			case FBSave::SectionActorUnion_ActorPipeline:
				(*actorptrptr) = SANDBOX_NEW(::ClientPipleLineActor);
				break;
			case FBSave::SectionActorUnion_ActorYak:
				(*actorptrptr) = SANDBOX_NEW(::ActorYak);
				break;
			case FBSave::SectionActorUnion_ActorPushSnowBall:
				(*actorptrptr) = SANDBOX_NEW(::ActorPushSnowBall);
				break;
			case FBSave::SectionActorUnion_ActorSnowMan:
				(*actorptrptr) = SANDBOX_NEW(::ActorSnowMan);
				break;
			case FBSave::SectionActorUnion_ActorIcicle:
				(*actorptrptr) = SANDBOX_NEW(::ClientActorIcicle);
				break;
			case FBSave::SectionActorUnion_ActorSnowHare:
				(*actorptrptr) = SANDBOX_NEW(::ActorSnowHare);
				break;
			case FBSave::SectionActorUnion_ActorPortal:
				(*actorptrptr) = SANDBOX_NEW(::ActorPortal);
				break;
			case FBSave::SectionActorUnion_ActorObj:
				(*actorptrptr) = SANDBOX_NEW(::ClientActor, true);
				break;
			case FBSave::SectionActorUnion_ActorVortex:
				(*actorptrptr) = SANDBOX_NEW(::ActorVortex);
				break;
			case FBSave::SectionActorUnion_ActorTravelingTrader:
				(*actorptrptr) = SANDBOX_NEW(::ActorTravelingTrader);
				break;
			case  FBSave::SectionActorUnion_ActorPlayerCorpse:
				(*actorptrptr) = SANDBOX_NEW(::ActorPlayerCorpse);
				break;
			case  FBSave::SectionActorUnion_ActorMobCorpse:
				(*actorptrptr) = SANDBOX_NEW(::ActorMobCorpse);
				break;
			case FBSave::SectionActorUnion_ActorCubeChest:
				(*actorptrptr) = SANDBOX_NEW(::ActorCubeChest);
				break;
			case FBSave::SectionActorUnion_ActorAirPlane:
				(*actorptrptr) = SANDBOX_NEW(::ActorAirPlane);
				break;
			default:
				break;
			}

			return SandboxResult(nullptr, true);
		});

	SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("SandboxGame_CreateContainer");
	m_eventCallbacks["SandboxGame_CreateContainer"] = SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("SandboxGame_CreateContainer", nullptr, (SandboxFunctionCallback)[&](SandboxContext context) -> SandboxResult {

		FBSave::ContainerUnion containertype = (FBSave::ContainerUnion)(context.GetData_Number("containertype"));

		int pSubTypeLua = context.GetData_Number("pSubTypeLua");

		WorldContainer* container = nullptr;
		switch (containertype)
		{
		case FBSave::ContainerUnion_ContainerFurnace:
			container = SANDBOX_NEW(FurnaceContainer);
			break;
		case FBSave::ContainerUnion_ContainerFurnaceOxy:
			container = SANDBOX_NEW(WorldFurnaceOxy);
			break;
		case FBSave::ContainerUnion_ContainerPiston:
			container = SANDBOX_NEW(WorldContainerHydarm);
			break;
		case FBSave::ContainerUnion_ContainerStorage:
			container = SANDBOX_NEW(WorldStorageBox);
			break;
		case FBSave::ContainerUnion_ContainerDynamicBox:
			container = SANDBOX_NEW(DynamicContainer);
			break;
		case FBSave::ContainerUnion_ContainerString:
			container = SANDBOX_NEW(WorldStringContainer);
			break;
		case FBSave::ContainerUnion_ContainerValue:
			container = SANDBOX_NEW(WorldValueContainer);
			break;
		case FBSave::ContainerUnion_ContainerMobSpawner:
			container = SANDBOX_NEW(WorldMobSpawner);
			break;
		case FBSave::ContainerUnion_ContainerSigns:
			container = SANDBOX_NEW(WorldSignsContainer);
			break;
		case FBSave::ContainerUnion_ContainerFunnel:
			container = SANDBOX_NEW(WorldFunnelContainer);
			break;
		case FBSave::ContainerUnion_ContainerEmitter:
			container = SANDBOX_NEW(WorldEmitterContainer);
			break;
		case FBSave::ContainerUnion_ContainerEffect:
			container = SANDBOX_NEW(WorldEffectContainer);
			break;
		case FBSave::ContainerUnion_ContainerHorseEgg:
			container = SANDBOX_NEW(::ContainerHorseEgg);
			break;
		case FBSave::ContainerUnion_ContainerDragonCup:
			container = SANDBOX_NEW(::ContainerDragonCup);
			break;
		case FBSave::ContainerUnion_ContainerItemExpo:
			container = SANDBOX_NEW(::ContainerItemExpo);
			break;
		case FBSave::ContainerUnion_ContainerMecha:
			container = SANDBOX_NEW(::ContainerMecha);
			break;
		case FBSave::ContainerUnion_ContainerSensor:
			container = SANDBOX_NEW(::WorldSensorContainer);
			break;
		case FBSave::ContainerUnion_ContainerCollider:
			container = SANDBOX_NEW(::WorldColliderContainer);
			break;
		case FBSave::ContainerUnion_ContainerRailKnot:
			container = SANDBOX_NEW(::ContainerRailKnot);
			break;
		case FBSave::ContainerUnion_ContainerAlienTotem:
			container = SANDBOX_NEW(::WorldAlienTotemContainer);
			break;
		case FBSave::ContainerUnion_ContainerRadioUnit:
			container = SANDBOX_NEW(::RadioUnitContainer);
			break;
		case FBSave::ContainerUnion_ContainerInterpreterUnit:
			container = SANDBOX_NEW(::InterpreterUnitContainer);
			break;
		case FBSave::ContainerUnion_ContainerStoragePassword:
			container = SANDBOX_NEW(::WorldStorageBoxPassword);
			break;
		case FBSave::ContainerUnion_ContainerStoneCore:
			container = SANDBOX_NEW(::BlockStoneCoreContainer);
			break;
		case FBSave::ContainerUnion_ContainerGiantTotem:
			container = SANDBOX_NEW(::WorldGiantTotemContainer);
			break;
		case FBSave::ContainerUnion_ContainerBlueprint:
			container = SANDBOX_NEW(::WorldBlueprint);
			break;
		case FBSave::ContainerUnion_ContainerRegionReplicator:
			container = SANDBOX_NEW(::ContainerRegionReplicator);
			break;
		case FBSave::ContainerUnion_ContainerBuildBluePrint:
			container = SANDBOX_NEW(::ContainerBuildBluePrint);
			break;
		case FBSave::ContainerUnion_ContainerMeasureDistance:
			container = SANDBOX_NEW(::WorldMeasureDistance);
			break;
		case FBSave::ContainerUnion_ContainerCustomModel:
			container = SANDBOX_NEW(::ContainerModelCraft);
			break;
		case FBSave::ContainerUnion_ContainerSelectMobSpawner:
			container = SANDBOX_NEW(::WorldSelectMobSpawner);
			break;
		case FBSave::ContainerUnion_ContainerTransfer:
			container = SANDBOX_NEW(::WorldTransferContainer);
			break;
		case FBSave::ContainerUnion_ContainerBookEditorTable:
			container = SANDBOX_NEW(WorldBookEditorTableContainer);
			break;
		case FBSave::ContainerUnion_ContainerBookCabinet:
			container = SANDBOX_NEW(::WorldBookCabinet);
			break;
		case FBSave::ContainerUnion_ContainerActorModel:
			container = SANDBOX_NEW(::ContainerActorModel);
			break;
		case FBSave::ContainerUnion_ContainerWheel:
			container = SANDBOX_NEW(::ContainerWheel);
			break;
		case FBSave::ContainerUnion_ContainerWorkshop:
			container = SANDBOX_NEW(::ContainerWorkshop);
			break;
		case FBSave::ContainerUnion_ContainerFullyCustomModel:
			container = SANDBOX_NEW(::ContainerFullyCustomModel);
			break;
		case FBSave::ContainerUnion_ContainerActioner:
			container = SANDBOX_NEW(::ContainerActioner);
			break;
		case FBSave::ContainerUnion_ContainerVehicleActioner:
			container = SANDBOX_NEW(::VehicleContainerActioner);
			break;
		case FBSave::ContainerUnion_ContainerDriverSeat:
			container = SANDBOX_NEW(::ContainerDriverSeat);
			break;
		case FBSave::ContainerUnion_ContainerArmPrismatic:
			container = SANDBOX_NEW(::ContainerArmPrismatic);
			break;
		case FBSave::ContainerUnion_ContainerSensorValue:
			container = SANDBOX_NEW(::WorldValueSensorContainer);
			break;
		case FBSave::ContainerUnion_ContainerBonFire:
			container = SANDBOX_NEW(::WorldBonFire);
			break;
		case FBSave::ContainerUnion_ContainerTombStone:
			container = SANDBOX_NEW(::TombStoneContainer);
			break;
		case FBSave::ContainerUnion_ContainerBed:
			container = SANDBOX_NEW(::WorldBed);
			break;
		case FBSave::ContainerUnion_ContainerVillageTotem:
			container = SANDBOX_NEW(::WorldVillageTotem);
			break;
		case FBSave::ContainerUnion_ContainerVillagerFlag:
			container = SANDBOX_NEW(::WorldVillagerFlag);
			break;
		case FBSave::ContainerUnion_ContainerImportModel:
			container = SANDBOX_NEW(::ContainerImportModel);
			break;
		case FBSave::ContainerUnion_ContainerAltarTbl:
			container = SANDBOX_NEW(AltarContainer);
			break;
		case FBSave::ContainerUnion_ContainerOneQuarterBlock:
			container = SANDBOX_NEW(::ContainerOneQuarterBlock);
			break;
		case FBSave::ContainerUnion_ContainerBlockLogo:
			container = SANDBOX_NEW(::HonorFrameContainer);
			break;
		case FBSave::ContainerUnion_ContainerPot:
			container = SANDBOX_NEW(::WorldPot);
			break;
		case FBSave::ContainerUnion_ContainerBlockKeyPedestal:
			container = SANDBOX_NEW(::KeyPedestalContainer);
			break;
		case FBSave::ContainerUnion_ContainerStarStationTransferConsole:
			container = SANDBOX_NEW(::WorldStarStationTransferConsoleContainer);
			break;
		case FBSave::ContainerUnion_ContainerStarStationTransferCabin:
			container = SANDBOX_NEW(::WorldStarStationTransferCabinContainer);
			break;
		case FBSave::ContainerUnion_ContainerRevivalStatue:
			container = SANDBOX_NEW(::ContainerRevivalStatue);
			break;
		case FBSave::ContainerUnion_ContainerFeedTrough:
			container = SANDBOX_NEW(::FeedTroughContainer);
			break;
		case FBSave::ContainerUnion_ContainerPeristele:
			container = SANDBOX_NEW(::PeristeleContainer);
			break;
		case FBSave::ContainerUnion_ContainerCoagulation:
			container = SANDBOX_NEW(::CoagualationContainer);
			break;
		case FBSave::ContainerUnion_ContainerKeyEffect:
			container = SANDBOX_NEW(::KeyEffectContainer);
			break;
		case FBSave::ContainerUnion_ContainerMiniClub:
			container = SANDBOX_NEW(::WorldMiniClubContainer);
			break;
		case FBSave::ContainerUnion_ContainerCanvas:
			container = SANDBOX_NEW(::WorldCanvas);
			break;
		case FBSave::ContainerUnion_ContainerPopulusLeaf:
			container = SANDBOX_NEW(::PopulusLeafContainer);
			break;
		case FBSave::ContainerUnion_ContainerOstrichEgg:
			container = SANDBOX_NEW(::ContainerOstrichEgg);
			break;
		case FBSave::ContainerUnion_ContainerByLua:
			container = WorldContainerLua::createLuaContainerFactory(pSubTypeLua, false);
			break;
		case FBSave::ContainerUnion_ContainerVenom:
			container = SANDBOX_NEW(::WorldVenomContainer);
			break;
		case FBSave::ContainerUnion_ContainerPoseidonStatue:
			container = SANDBOX_NEW(::PoseidonStatueContainer);
			break;
		case FBSave::ContainerUnion_ContainerModel:
			container = SANDBOX_NEW(::ContainerModel);
			break;
		case FBSave::ContainerUnion_ContainerDriverSeatModel:
			container = SANDBOX_NEW(::ContainerDriverSeatModel);
			break;
		case FBSave::ContainerUnion_ContainerGiantScallops:
			container = SANDBOX_NEW(::GiantScallopsContainer);
			break;
		case FBSave::ContainerUnion_ContainerFishFrame:
			container = SANDBOX_NEW(::FishFrameContainer);
			break;
		case FBSave::ContainerUnion_ContainerSmallTorch:
			container = SANDBOX_NEW(::SmallTorchContainer);
			break;
		case FBSave::ContainerUnion_ContainerShells:
			container = SANDBOX_NEW(::WorldBells);
			break;
		case FBSave::ContainerUnion_ContainerColorPalette:
			container = SANDBOX_NEW(::WorldColorPaletteContainer);
			break;
		case FBSave::ContainerUnion_ContainerCoconut:
			container = SANDBOX_NEW(::CoconutContainer);
			break;
		case FBSave::ContainerUnion_ContainerSolidSand:
			container = SANDBOX_NEW(::SolidSandContainer);
			break;
		case FBSave::ContainerUnion_ContainerStake:
			container = SANDBOX_NEW(::BlockStakeContainer);
			break;
		case FBSave::ContainerUnion_ContainerGlowStick:
			container = SANDBOX_NEW(::GlowStickBlockContainer);
			break;
		case FBSave::ContainerUnion_ContainerSeaPlant:
			container = SANDBOX_NEW(::SeaPlantContainer);
			break;
		case FBSave::ContainerUnion_ContainerLightMushroom:
			container = SANDBOX_NEW(::ContainerLightMushroom);
			break;
		case FBSave::ContainerUnion_ContainerElectricBaseSave:
			container = SANDBOX_NEW(ContainerElectricElement);
			break;
		case FBSave::ContainerUnion_ContainerElectricSplitterSave:
			container = SANDBOX_NEW(ContainerElectricSplitter);
			break;
		case FBSave::ContainerUnion_ContainerElectricDelaySave:
			container = SANDBOX_NEW(ContainerElectricDelay);
			break;
		case FBSave::ContainerUnion_ContainerElectricResisterSave:
			container = SANDBOX_NEW(ContainerElectricResister);
			break;
		case FBSave::ContainerUnion_ContainerElectricCounterSave:
			container = SANDBOX_NEW(ContainerElectricCounter);
			break;
		case FBSave::ContainerUnion_ContainerPiplineSave:
			container = SANDBOX_NEW(ContainerPipeline);
			break;
		case FBSave::ContainerUnion_ContainerKeyDoor:
			container = SANDBOX_NEW(KeyDoorContainer);
			break;
		case FBSave::ContainerUnion_ContainerCollectingPipe:
			container = SANDBOX_NEW(CollectingPipeContainer);
			break;
		case FBSave::ContainerUnion_ContainerDetectionPipeSave:
			container = SANDBOX_NEW(ContainerDetectionPipeline);
			break;
		case FBSave::ContainerUnion_ContainerShellBed:
			container = SANDBOX_NEW(WorldShellbed);
			break;
		case FBSave::ContainerUnion_ContainerStretchForzen:
			container = SANDBOX_NEW(::WorldBlockStretchForzenContainer);
			break;
		case FBSave::ContainerUnion_ContainerThick:
			container = SANDBOX_NEW(::ContainerThick);
			break;
		case FBSave::ContainerUnion_ContainerIceCrystalShroom:
			container = SANDBOX_NEW(::ContainerIceCrystalShroom);
			break;
		case FBSave::ContainerUnion_ContainerVillageTotemIce:
			container = SANDBOX_NEW(::VillageTotemIceContainer);
			break;
		case FBSave::ContainerUnion_ContainerVillageFlagBuilding:
			container = SANDBOX_NEW(::VillageFlagBuildingContainer);
			break;
		case FBSave::ContainerUnion_ContainerVillageFlagIce:
			container = SANDBOX_NEW(::VillageFlagIceContainer);
			break;
		case FBSave::ContainerUnion_ContainerSchoolFence:
			container = SANDBOX_NEW(::SchoolFenceContainer);
			break;
		case FBSave::ContainerUnion_ContainerElectricRay:
			container = SANDBOX_NEW(::ContainerElectricRay);
			break;
		case FBSave::ContainerUnion_ContainerAnimalEgg:
			container = SANDBOX_NEW(::AnimalEggContainer);
			break;
		case FBSave::ContainerUnion_ContainerArrowSigns:
			container = SANDBOX_NEW(::WorldArrowSignsContainer);
			break;
		case FBSave::ContainerUnion_ContainerWeaponRack:
			container = SANDBOX_NEW(::ContainerWeaponRack);
			break;
		case FBSave::ContainerUnion_ContainerMobBodyModel:
			container = SANDBOX_NEW(::ModleMobContainer);
			break;
		case FBSave::ContainerUnion_ContainerPlayerBodyModel:
			container = SANDBOX_NEW(::ModlePlayerContainer);
			break;
		case FBSave::ContainerUnion_ContainerManualEmitter:
			container = SANDBOX_NEW(::ContainerManualEmitter);
			break;
		case FBSave::ContainerUnion_ContainerClipTrap:
			container = SANDBOX_NEW(::ContainerClipTrap);
			break;
		case FBSave::ContainerUnion_ContainerFusionCage:
			container = SANDBOX_NEW(::ContainerFusionCage);
			break;
		case FBSave::ContainerUnion_ContainerDragonFlower:
			container = SANDBOX_NEW(::DragonFlowerContainer);
			break;
		case FBSave::ContainerUnion_ContainerBellFlower:
			container = SANDBOX_NEW(::BellFlowerContainer);
			break;
		case FBSave::ContainerUnion_ContainerDeathJar:
			container = SANDBOX_NEW(::ContainerDeathJar);
			break;
		case FBSave::ContainerUnion_ContainerStove:
			container = SANDBOX_NEW(::WorldStove);
			break;
		case FBSave::ContainerUnion_ContainerNewPot:
			container = SANDBOX_NEW(::WorldNewPot);
			break;
		case FBSave::ContainerUnion_ContainerDummy:
			container = SANDBOX_NEW(::DummyContainer);
			break;
		case FBSave::ContainerUnion_ContainerStarStationCargo:
			container = SANDBOX_NEW(::WorldStarStationCargoContainer);
			break;
		case FBSave::ContainerUnion_ContainerMod:
			container = SANDBOX_NEW(::ContainerMod);
			break;
		case FBSave::ContainerUnion_ContainerComputer:
			container = SANDBOX_NEW(::ContainerComputer);
			break;
		case FBSave::ContainerUnion_ContainerMonsterSummoner:
			container = SANDBOX_NEW(::ContainerMonsterSummoner);
			break;
		case FBSave::ContainerUnion_ContainerTalkingStatue:
			container = SANDBOX_NEW(::ContainerTalkingStatue);
			break;
		case FBSave::ContainerUnion_ContainerPolaroidFrame:
			container = SANDBOX_NEW(::ContainerPolaroidFrame);
			break;
		case FBSave::ContainerUnion_ContainerModTransfer:
			container = SANDBOX_NEW(::ModTransferConsoleContainer);
			break;
		case FBSave::ContainerUnion_ContainerFurnaceArray:
			container = SANDBOX_NEW(::FurnaceContainerArray);
			break;
		case FBSave::ContainerUnion_ContainerTerritory:
			container = SANDBOX_NEW(::TerritoryContainer);
			break;
		case FBSave::ContainerUnion_ContainerSocDoor:
			container = SANDBOX_NEW(::SocDoorContainer);
			break;
		case FBSave::ContainerUnion_ContainerAutoSocDoor:
			container = SANDBOX_NEW(::SocAutoDoorContainer);
			break;
		case FBSave::ContainerUnion_ContainerDecomposition:
			container = SANDBOX_NEW(::ContainerDecomposition);
			break;
		case FBSave::ContainerUnion_ContainerErosion:
			container = SANDBOX_NEW(::ErosionContainer);
			break;
		case FBSave::ContainerUnion_ContainerWaterStorage:
			container = SANDBOX_NEW(::containerWaterStorage);
			break;
		case FBSave::ContainerUnion_ContainerSocWorkbench:
			container = SANDBOX_NEW(::ContainerSocWorkbench);
			break;
		case FBSave::ContainerUnion_ContainerArchitecture:
			container = SANDBOX_NEW(::containerArchitecture);
			break;
		case FBSave::ContainerUnion_ContainerErosionStorage:
			container = SANDBOX_NEW(::ErosionStorageBox);
			break;
		case FBSave::ContainerUnion_ContainerResearch:
			container = SANDBOX_NEW(ContainerResearch);
			break;
		case FBSave::ContainerUnion_ContainerLinkMachine:
			container = SANDBOX_NEW(::containerLinkMachine);
			break;
		case FBSave::ContainerUnion_ContainerTorch:
			container = SANDBOX_NEW(::TorchContainer);
			break;
		default:
			break;
		}
		if (container)
		{
			return SandboxResult(nullptr, true).SetData_Userdata("WorldContainer", "worldcontainer", container);
		}
		return SandboxResult(nullptr, false);
		});

	SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("WorldManager_Destructor");
	m_eventCallbacks["WorldManager_Destructor"] = SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("WorldManager_Destructor", nullptr,
		[&](SandboxContext context) -> SandboxResult {

			CityConfig::Destroy();
			g_ModPackMgr->OnLeaveWorld();
			BiomeModConfig::Destroy();
			UgcEcosysBuild::Destroy();
			return SandboxResult(nullptr, true);
		});
	return true;
}

/*
	消息发送第三种方式
*/
void SandboxActorSubsystem::OnExecute(const char* eventname, void* context, int nLen, unsigned long long userdata)
{
	if (!g_WorldMgr) return;
	jsonxx::Object* obj = (jsonxx::Object*)context;

	auto iter = m_funcMap.find(eventname);
	if (iter != m_funcMap.end())
	{
		iter->second(obj);
		return;
	}

	if (0 == strcmp(eventname, "PB_ACTOR_VILLAGER_INFO"))
	{
		long long objid = 0;
		int profession = 0;
		if (obj->has<jsonxx::String>("objid"))
		{
			std::string objidstring = obj->get<jsonxx::String>("objid");
			//#if OGRE_PLATFORM == OGRE_PLATFORM_ANDROID
			//			objid = atoll(objidstring.c_str());
			//#else
			//			objid = std::stoll(objidstring.c_str());
			//#endif
			std::stringstream sstr;
			sstr << objidstring;
			if (!(sstr >> objid))
				return;
		}
		if (obj->has<jsonxx::Number>("profession"))
		{
			profession = (int)obj->get<jsonxx::Number>("profession");
		}
		ClientMob* mob = static_cast<ClientMob*>(g_WorldMgr->findMobByWID(objid));
		::ActorVillager* villager = dynamic_cast<::ActorVillager*>(mob);
		if (villager)
		{
			villager->setProfession(profession);
			//客机收到停止播放 音乐特效
			//野人伙伴会围绕火把跳舞，跳舞过程中转职会有特效残留
			auto effectComponent = villager->getEffectComponent();
			if (effectComponent)
			{
				effectComponent->stopBodyEffect("mob_3200_music");
				effectComponent->stopBodyEffect(BODYFX_AI_SLEEP);
			}
		}
	}
	else if (0 == strcmp(eventname, "PB_PART_WEATHER_HC"))
	{
		if (g_WorldMgr->getWorld(0) && g_WorldMgr->getWorld(0)->getWeatherMgr())
		{
			g_WorldMgr->getWorld(0)->getWeatherMgr()->syncPartWeather(context, nLen, userdata);
		}
	}
	else if (0 == strcmp(eventname, "PB_GROUP_WEATHER_HC"))
	{
		if (g_WorldMgr->getWorld(0) && g_WorldMgr->getWorld(0)->getWeatherMgr())
		{
			g_WorldMgr->getWorld(0)->getWeatherMgr()->syncBiomeGroupWeather(context, nLen, userdata);
		}
	}
	else if (0 == strcmp(eventname, "PB_CREATE_BLOCK_CH"))
	{
		if (obj->has<jsonxx::Number>("uin"))
		{
			ClientPlayer* player = static_cast<ClientPlayer*>(g_WorldMgr->getPlayerByUin((int)obj->get<jsonxx::Number>("uin")));
			if (player && player->getWorld())
			{
				WCoord pos = WCoord((int)obj->get<jsonxx::Number>("posX"), (int)obj->get<jsonxx::Number>("posY"), (int)obj->get<jsonxx::Number>("posZ"));
				player->getWorld()->setBlockAll(pos, (int)obj->get<jsonxx::Number>("blockid"), (int)obj->get<jsonxx::Number>("blockdata"));
			}
		}
	}
	else if (0 == strcmp(eventname, "PB_ACTOR_SANDWORM_SHOW"))
	{
		long long objid = 0;
		bool v = true;
		if (obj->has<jsonxx::String>("objid"))
		{
			std::string objidstring = obj->get<jsonxx::String>("objid");
			std::stringstream sstr;
			sstr << objidstring;
			if (!(sstr >> objid))
				return;
		}
		if (obj->has<jsonxx::Boolean>("isShow"))
		{
			v = obj->get<jsonxx::Boolean>("isShow");
		}
		ClientMob* mob = static_cast<ClientMob*>(g_WorldMgr->findMobByWID(objid));
		::ActorSandworm* sandworm = dynamic_cast<::ActorSandworm*>(mob);
		if (sandworm)
		{
			if (sandworm->getBody())
			{
				sandworm->getBody()->show(v);
			}
		}
	}
	else if (0 == strcmp(eventname, "PB_ACTOR_SANDWORM_CAN_MOVE"))
	{
		long long objid = 0;
		bool canmove = true;
		if (obj->has<jsonxx::String>("objid"))
		{
			std::string objidstring = obj->get<jsonxx::String>("objid");
			std::stringstream sstr;
			sstr << objidstring;
			if (!(sstr >> objid))
				return;
		}
		if (obj->has<jsonxx::Boolean>("canmove"))
		{
			canmove = obj->get<jsonxx::Boolean>("canmove");
		}
		ClientMob* mob = static_cast<ClientMob*>(g_WorldMgr->findMobByWID(objid));
		::ActorSandworm* sandworm = dynamic_cast<::ActorSandworm*>(mob);
		if (sandworm)
		{
			auto functionWrapper = sandworm->getFuncWrapper();
			if (functionWrapper)
			{
				functionWrapper->setCanMove(canmove);
			}
		}
	}
	else if (0 == strcmp(eventname, "PB_ACTOR_SCASLE"))
	{
		long long objid = 0;
		int scale = 1;
		if (obj->has<jsonxx::String>("objid"))
		{
			std::string objidstring = obj->get<jsonxx::String>("objid");
			std::stringstream sstr;
			sstr << objidstring;
			if (!(sstr >> objid))
				return;
		}
		if (obj->has<jsonxx::Number>("scale"))
		{
			scale = (int)obj->get<jsonxx::Number>("scale");
		}
		ClientActor* actor = static_cast<ClientActor*>(g_WorldMgr->findActorByWID(objid));
		if (actor && actor->getBody())
		{
			//actor->getBody()->setScale(scale);
			if (actor->isPlayer())
			{
				actor->getBody()->setScale(scale);
			}
			else
			{
				actor->setCustomScale(scale);
			}
		}
	}
	else if (0 == strcmp(eventname, "PB_ACTOR_SANDWORM_NIBBLE_PLAYER"))
	{
		long long objid = 0;
		long long objid2 = 0;
		bool isFly = false;
		if (obj->has<jsonxx::String>("objid"))
		{
			std::string objidstring = obj->get<jsonxx::String>("objid");
			std::stringstream sstr;
			sstr << objidstring;
			if (!(sstr >> objid))
				return;
		}
		if (obj->has<jsonxx::String>("objid2"))
		{
			std::string objidstring = obj->get<jsonxx::String>("objid2");
			std::stringstream sstr;
			sstr << objidstring;
			if (!(sstr >> objid2))
				return;
		}
		if (obj->has<jsonxx::Boolean>("isFly"))
		{
			isFly = obj->get<jsonxx::Boolean>("isFly");
		}
		ClientActor* actor = static_cast<ClientActor*>(g_WorldMgr->findActorByWID(objid));
		if (actor)
		{
			ClientMob* mob = static_cast<ClientMob*>(g_WorldMgr->findMobByWID(objid2));
			::ActorSandworm* sandworm = dynamic_cast<::ActorSandworm*>(mob);
			if (sandworm && sandworm->getBody())
			{
				WCoord bindPos = sandworm->getBody()->getBindPointPos(500);
				actor->setPosition(bindPos);
				actor->getLocoMotion()->m_Motion.y = 0;
			}
			if (actor->getFlying())
			{
				actor->setFlying(false);
			}
		}
	}
	else if (0 == strcmp(eventname, "MOB_SCORPION_HIDE_TICK_CH"))
	{
		long long objid = 0;
		if (obj->has<jsonxx::String>("objid"))
		{
			std::string objidstring = obj->get<jsonxx::String>("objid");
			std::stringstream sstr;
			sstr << objidstring;
			if (!(sstr >> objid))
				return;
		}
		ClientMob* mob = static_cast<ClientMob*>(g_WorldMgr->findMobByWID(objid));
		if (mob)
		{
			int index = 0;
			if (mob->getIsHideStatus())
			{
				index = 1;
			}
			jsonxx::Object object;
			object << "objid" << mob->getObjId();
			object << "playAnim" << index;
			SandBoxManager::getSingleton().sendToTrackingPlayers(mob, "MOB_SCORPION_HIDE_CHANGE", object.bin(), object.binLen());
		}
	}
	else if (0 == strcmp(eventname, "PB_ACTOR_CREATE_THORNBALL"))
	{
		long long objid = 0;
		int anchorId = 0;
		Rainbow::Vector3f pos = Rainbow::Vector3f(0, 0, 0);
		if (obj->has<jsonxx::String>("objid"))
		{
			std::string objidstring = obj->get<jsonxx::String>("objid");
			std::stringstream sstr;
			sstr << objidstring;
			if (!(sstr >> objid))
				return;
		}
		if (obj->has<jsonxx::Number>("anchorId"))
		{
			anchorId = (int)obj->get<jsonxx::Number>("anchorId");
		}
		if (obj->has<jsonxx::Array>("pos"))
		{
			pos = Vec3ToVector3Json(obj->get<jsonxx::Array>("pos"));
		}
		ClientActor* actor = static_cast<ClientActor*>(g_WorldMgr->findActorByWID(objid));
		if (actor == nullptr || !actor->getBody())
		{
			return;
		}
		auto thornComponent = actor->sureThornBallComponent();
		if (thornComponent == nullptr)
		{
			return;
		}
		thornComponent->setThornAnchorId(anchorId, pos);
		actor->getBody()->createThornBallMeshModel(anchorId, pos);
	}
	else if (0 == strcmp(eventname, "PB_ACTOR_CREATE_THORNBALL2"))
	{
		//todo
	}
	else if (0 == strcmp(eventname, "PB_ATTR_SHAPE_SHIFT_RIGHT_CLICK_CH"))
	{
		if (obj->has<jsonxx::Number>("uin"))
		{
			int uin = obj->get<jsonxx::Number>("uin");
			auto player = static_cast<ClientPlayer*>(g_WorldMgr->getPlayerByUin(uin));
			if (player)
			{
				player->doAttrShapeShiftRightClick();
			}
		}
	}
	else if (0 == strcmp(eventname, "PB_ACTOR_REBOUNDS_ATTACK_UP"))
	{
		long long objid = 0;
		float atkpoints = 0.0f;
		if (obj->has<jsonxx::String>("objid"))
		{
			std::string objidstring = obj->get<jsonxx::String>("objid");
			std::stringstream sstr;
			sstr << objidstring;
			if (!(sstr >> objid))
				return;
		}
		if (obj->has<jsonxx::Number>("atkpoints"))
		{
			atkpoints = (float)obj->get<jsonxx::Number>("atkpoints");
		}
		ClientActor* actor = g_WorldMgr->findActorByWID(objid) ? g_WorldMgr->findActorByWID(objid)->GetActor() : nullptr;
		if (actor == nullptr) {
			return;
		}
		auto thornComponent = actor->sureThornBallComponent();
		if (thornComponent != nullptr)
		{
			thornComponent->reboundsAttackedUp(atkpoints);
		}
	}
	else if (0 == strcmp(eventname, "PB_ACTOR_REBOUNDS_ATTACK_ROUND"))
	{
		long long objid = 0;
		float atkpoints = 0.0f;
		int dir = 0;
		bool impactInjured = false;
		if (obj->has<jsonxx::String>("objid"))
		{
			std::string objidstring = obj->get<jsonxx::String>("objid");
			std::stringstream sstr;
			sstr << objidstring;
			if (!(sstr >> objid))
				return;
		}
		if (obj->has<jsonxx::Number>("dir"))
		{
			dir = (int)obj->get<jsonxx::Number>("dir");
		}
		if (obj->has<jsonxx::Number>("atkpoints"))
		{
			atkpoints = (float)obj->get<jsonxx::Number>("atkpoints");
		}
		if (obj->has<jsonxx::Boolean>("impactInjured"))
		{
			impactInjured = (bool)obj->get<jsonxx::Boolean>("impactInjured");
		}
		ClientActor* actor = g_WorldMgr->findActorByWID(objid) ? g_WorldMgr->findActorByWID(objid)->GetActor() : nullptr;
		if (actor == nullptr) {
			return;
		}
		auto thornComponent = actor->sureThornBallComponent();
		if (thornComponent != nullptr)
		{
			thornComponent->reboundsAttackedRound(atkpoints, dir, impactInjured);
		}
	}
	else if (0 == strcmp(eventname, "PB_CH_NOTICE_ATTACKED_UP"))
	{
		long long objid = 0;
		float atkpoints = 0.0f;
		if (obj->has<jsonxx::String>("objid"))
		{
			std::string objidstring = obj->get<jsonxx::String>("objid");
			std::stringstream sstr;
			sstr << objidstring;
			if (!(sstr >> objid))
				return;
		}
		if (obj->has<jsonxx::Number>("atkpoints"))
		{
			atkpoints = (float)obj->get<jsonxx::Number>("atkpoints");
		}
		ClientActor* actor = g_WorldMgr->findActorByWID(objid) ? g_WorldMgr->findActorByWID(objid)->GetActor() : nullptr;
		if (actor == nullptr) {
			return;
		}
		auto thornComponent = actor->sureThornBallComponent();
		if (thornComponent != nullptr)
		{
			thornComponent->reboundsAttackedUp(atkpoints);
		}
	}
	else if (0 == strcmp(eventname, "PB_CH_NOTICE_ATTACKED_ROUND"))
	{
		long long objid = 0;
		float atkpoints = 0.0f;
		int dir = 0;
		if (obj->has<jsonxx::String>("objid"))
		{
			std::string objidstring = obj->get<jsonxx::String>("objid");
			std::stringstream sstr;
			sstr << objidstring;
			if (!(sstr >> objid))
				return;
		}
		if (obj->has<jsonxx::Number>("atkpoints"))
		{
			atkpoints = (float)obj->get<jsonxx::Number>("atkpoints");
		}
		if (obj->has<jsonxx::Number>("dir"))
		{
			dir = (int)obj->get<jsonxx::Number>("dir");
		}
		ClientActor* actor = g_WorldMgr->findActorByWID(objid) ? g_WorldMgr->findActorByWID(objid)->GetActor() : nullptr;
		if (actor == nullptr) {
			return;
		}
		auto thornComponent = actor->sureThornBallComponent();
		if (thornComponent != nullptr)
		{
			thornComponent->reboundsAttackedRound(atkpoints, dir);
		}
	}
	else if (0 == strcmp(eventname, "PB_REMOVE_SAWTOOTH_THORNB"))
	{
		long long objid = 0;
		int num = 0;
		if (obj->has<jsonxx::String>("objid"))
		{
			std::string objidstring = obj->get<jsonxx::String>("objid");
			std::stringstream sstr;
			sstr << objidstring;
			if (!(sstr >> objid))
				return;
		}
		if (obj->has<jsonxx::Number>("num"))
		{
			num = (int)obj->get<jsonxx::Number>("num");
		}
		ClientActor* actor = g_WorldMgr->findActorByWID(objid) ? g_WorldMgr->findActorByWID(objid)->GetActor() : nullptr;
		if (actor == nullptr)
		{
			return;
		}
		auto thornComponent = actor->sureThornBallComponent();
		if (thornComponent != nullptr)
		{
			thornComponent->removeThornBallModel(num);
		}
	}
	else if (0 == strcmp(eventname, "PB_CH_NOTICE_REMOVE_SAWTOOTH_THORNBA"))
	{
		long long objid = 0;
		int num = 0;
		if (obj->has<jsonxx::String>("objid"))
		{
			std::string objidstring = obj->get<jsonxx::String>("objid");
			std::stringstream sstr;
			sstr << objidstring;
			if (!(sstr >> objid))
				return;
		}
		if (obj->has<jsonxx::Number>("num"))
		{
			num = (int)obj->get<jsonxx::Number>("num");
		}
		ClientPlayer* actor = dynamic_cast<ClientPlayer*>(g_WorldMgr->findActorByWID(objid));
		if (actor == nullptr)
		{
			return;
		}
		auto thornComponent = actor->sureThornBallComponent();
		if (thornComponent != nullptr)
		{
			thornComponent->dropThornBall(1);
			thornComponent->removeThornBallModel(num);
		}
	}
	else if (0 == strcmp(eventname, "PB_ACTOR_CH_DROP"))
	{
		long long objid = 0;
		int num = 0;
		if (obj->has<jsonxx::String>("objid"))
		{
			std::string objidstring = obj->get<jsonxx::String>("objid");
			std::stringstream sstr;
			sstr << objidstring;
			if (!(sstr >> objid))
				return;
		}
		if (obj->has<jsonxx::Number>("num"))
		{
			num = (int)obj->get<jsonxx::Number>("num");
		}
		IClientActor* actor = dynamic_cast<IClientActor*>(g_WorldMgr->findActorByWID(objid));
		if (actor == nullptr)
		{
			return;
		}
		auto dropComponent = actor->GetComponent<DropItemComponent>();
		if (dropComponent)
			dropComponent->dropItem(ITEM_THORNBALL, num);
	}
	else if (0 == strcmp(eventname, "PB_DESTORY_BLOCK_CH"))
	{
		if (obj->has<jsonxx::Number>("uin"))
		{
			int uin = obj->get<jsonxx::Number>("uin");
			auto player = static_cast<ClientPlayer*>(g_WorldMgr->getPlayerByUin(uin));
			if (player)
			{
				int x = obj->get<jsonxx::Number>("posX");
				int y = obj->get<jsonxx::Number>("posY");
				int z = obj->get<jsonxx::Number>("posZ");
				if (player->getWorld()->getBlockID(x, y, z) == obj->get<jsonxx::Number>("blockid"))
					player->getWorld()->destroyBlock(x, y, z, obj->get<jsonxx::Number>("dropItem"));
			}
		}
	}
	else if (0 == strcmp(eventname, "PB_WATER_PRESSURE_CH"))
	{
		if (obj->has<jsonxx::Number>("uin"))
		{
			int uin = obj->get<jsonxx::Number>("uin");
			auto player = static_cast<ClientPlayer*>(g_WorldMgr->getPlayerByUin(uin));
			if (player && player->getPlayerAttrib())
			{
				player->getPlayerAttrib()->setWaterPressure(obj->get<jsonxx::Number>("waterPress"));
				player->getPlayerAttrib()->setLowerBodyWaterPressuer(obj->get<jsonxx::Number>("lowerWaterPress"));
			}
		}
	}
	else if (0 == strcmp(eventname, "PB_COCONUT_HIT"))
	{
		long long objid = 0;
		bool hit = false;
		if (obj->has<jsonxx::String>("objid"))
		{
			std::string objidstring = obj->get<jsonxx::String>("objid");
			std::stringstream sstr;
			sstr << objidstring;
			if (!(sstr >> objid))
				return;
		}
		if (obj->has<jsonxx::Boolean>("hit"))
		{
			hit = (bool)obj->get<jsonxx::Boolean>("hit");
		}
		ClientPlayer* actor = dynamic_cast<ClientPlayer*>(g_WorldMgr->findActorByWID(objid));
		if (actor == nullptr)
		{
			return;
		}
		actor->setCoconutHit(hit);
	}
	else if (0 == strcmp(eventname, "PB_COCONUT_SKIP_NIGHT"))
	{
		long long objid = 0;
		bool night = false;
		if (obj->has<jsonxx::String>("objid"))
		{
			std::string objidstring = obj->get<jsonxx::String>("objid");
			std::stringstream sstr;
			sstr << objidstring;
			if (!(sstr >> objid))
				return;
		}
		if (obj->has<jsonxx::Boolean>("night"))
		{
			night = (bool)obj->get<jsonxx::Boolean>("night");
		}
		ClientPlayer* actor = dynamic_cast<ClientPlayer*>(g_WorldMgr->findActorByWID(objid));
		if (actor == nullptr)
		{
			return;
		}
		actor->setCoconutSkipNight(night);
	}
	else if (0 == strcmp(eventname, "PB_ATTR_SHAPE_SHIFT_SYNC"))
	{
		if (g_pPlayerCtrl && g_pPlayerCtrl->isAttrShapeShift())
		{
			if (obj->has<jsonxx::Number>("male"))
			{
				PlayerAttrib* attr = g_pPlayerCtrl->getPlayerAttrib();
				char path[256];
				int id = obj->get<jsonxx::Number>("male");
				if (id == 0)
				{
					sprintf(path, "entity/%s/male.png", attr->m_AttrShapeShiftDef->Model.c_str());
				}
				else
					sprintf(path, "entity/%s/male%d.png", attr->m_AttrShapeShiftDef->Model.c_str(), id);
				if (g_pPlayerCtrl->getBody())
					g_pPlayerCtrl->getBody()->changeBodyTex(path, "rtexbody");
			}
		}
	}
	else if (0 == strcmp(eventname, "PB_ACTOR_SHARK_BITE_PLAYER_MOVE"))
	{
		long long objid = 0;
		long long objid2 = 0;
		int  data = 0;
		if (obj->has<jsonxx::String>("objid"))
		{
			std::string objidstring = obj->get<jsonxx::String>("objid");
			std::stringstream sstr;
			sstr << objidstring;
			if (!(sstr >> objid))
				return;
		}
		if (obj->has<jsonxx::String>("objid2"))
		{
			std::string objidstring = obj->get<jsonxx::String>("objid2");
			std::stringstream sstr;
			sstr << objidstring;
			if (!(sstr >> objid2))
				return;
		}
		if (obj->has<jsonxx::Number>("data"))
		{
			data = obj->get<jsonxx::Number>("data");
		}
		ClientMob* mob = static_cast<ClientMob*>(g_WorldMgr->findMobByWID(objid));
		if (mob)
		{
			mob->setData(data);
			ClientActor* actor = static_cast<ClientActor*>(g_WorldMgr->findActorByWID(objid2));
			if (actor != NULL) //bugfix
			{
				if (actor->isPlayer())
				{
					ClientPlayer* cPlayer = dynamic_cast<ClientPlayer*>(actor);
					ClientActor* actor1 = nullptr;
					RiddenComponent* riddenCom = cPlayer->getRiddenComponent();
					if (riddenCom)
					{
						actor1 = riddenCom->getRidingActor();
					}
					if (actor1)
					{
						actor1->AttackFromSharkBite(mob);
					}
					else
					{
						actor->AttackFromSharkBite(mob);
					}
				}
				else
				{
					actor->AttackFromSharkBite(mob);
				}
			}
		}
	}
	else if (0 == strcmp(eventname, "PB_CRAB_INFO_SYNC"))
	{
		if (obj->has<jsonxx::Number>("uin"))
		{
			ClientPlayer* player = static_cast<ClientPlayer*>(g_WorldMgr->getPlayerByUin((int)obj->get<jsonxx::Number>("uin")));
			if (player && player->getWorld())
			{
				long long objid = 0;
				bool night = false;
				if (obj->has<jsonxx::String>("objid"))
				{
					std::string objidstring = obj->get<jsonxx::String>("objid");
					std::stringstream sstr;
					sstr << objidstring;
					if (!(sstr >> objid))
						return;
				}
				player->setCrabClamp(objid);
			}
		}
	}
	else if (0 == strcmp(eventname, "PB_CRAB_CLICKCOUNT_RESET"))
	{
		if (obj->has<jsonxx::Number>("uin"))
		{
			ClientPlayer* player = static_cast<ClientPlayer*>(g_WorldMgr->getPlayerByUin((int)obj->get<jsonxx::Number>("uin")));
			if (player && player->getWorld())
			{
				player->m_crabClickCount = 6;
				player->addCrabClick();
			}
		}
	}
	else if (0 == strcmp(eventname, "PB_HIPPOCAMPUS_REFRESHMODEL"))
	{
		long long objid = 0;
		int modelNum = 0;
		if (obj->has<jsonxx::String>("objid"))
		{
			std::string objidstring = obj->get<jsonxx::String>("objid");
			std::stringstream sstr;
			sstr << objidstring;
			if (!(sstr >> objid))
				return;
		}
		if (obj->has<jsonxx::Number>("modelNum"))
		{
			modelNum = (int)obj->get<jsonxx::Number>("modelNum");
		}
		::ActorHippocampus* actor = dynamic_cast<::ActorHippocampus*>(g_WorldMgr->findActorByWID(objid));
		if (actor == nullptr)
		{
			return;
		}

		actor->setCurModel(modelNum);
	}
	else if (0 == strcmp(eventname, "PB_HIPPOCAMPUS_CHANGECOLOR"))
	{
		long long objid = 0;
		int blockid = 0;
		if (obj->has<jsonxx::String>("objid"))
		{
			std::string objidstring = obj->get<jsonxx::String>("objid");
			std::stringstream sstr;
			sstr << objidstring;
			if (!(sstr >> objid))
				return;
		}
		if (obj->has<jsonxx::Number>("blockid"))
		{
			blockid = (int)obj->get<jsonxx::Number>("blockid");
		}
		::ActorHippocampus* actor = dynamic_cast<::ActorHippocampus*>(g_WorldMgr->findActorByWID(objid));
		if (actor == nullptr)
		{
			return;
		}

		actor->changeColor(blockid);
	}
	else if (0 == strcmp(eventname, "PB_BACKPACKGRID_DRUATION_HC"))
	{
		if (g_pPlayerCtrl && g_pPlayerCtrl->getBackPack())
		{
			int index = obj->get<jsonxx::Number>("index");
			auto grid = g_pPlayerCtrl->getBackPack()->index2Grid(index);
			if (grid)
			{
				int dur = obj->get<jsonxx::Number>("Duration");
				grid->setDuration(dur);
				//GameEventQue::GetInstance().postBackpackChange(index);
				MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
					SetData_Number("grid_index", index);
				if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
					MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_CHANGE", sandboxContext);
				}
			}
		}
	}
	else if (0 == strcmp(eventname, "PB_GUNLOGIC_USE_WaterCanoonSkill"))
	{
		if (obj->has<jsonxx::Number>("uin"))
		{
			IClientPlayer* player = g_WorldMgr->getPlayerByUin((int)obj->get<jsonxx::Number>("uin"));
			if (player)
				player->doWaterCanoonSkill();
		}
	}
	else if (0 == strcmp(eventname, "PB_ACTOR_SNOWMAN_PART_SHOW"))
	{
		long long objid = 0;
		int partIndex = 0;
		bool isShow = false;
		if (obj->has<jsonxx::String>("objid"))
		{
			std::string objidstring = obj->get<jsonxx::String>("objid");
			std::stringstream sstr;
			sstr << objidstring;
			if (!(sstr >> objid))
				return;
		}
		if (obj->has<jsonxx::Number>("partIndex"))
		{
			partIndex = (int)obj->get<jsonxx::Number>("partIndex");
		}
		if (obj->has<jsonxx::Boolean>("show"))
		{
			isShow = (bool)obj->get<jsonxx::Boolean>("show");
		}
		::ActorSnowMan* actor = dynamic_cast<::ActorSnowMan*>(g_WorldMgr->findActorByWID(objid));
		if (actor && actor->getBody())
		{
			char partName[64];
			sprintf(partName, "part%d", partIndex);
			actor->getBody()->showSkin(partName, isShow);
		}
	}
	else if (0 == strcmp(eventname, "PB_MOB_PART_SHOW"))
	{
		long long objid = 0;
		const char* partName = "";
		bool isShow = false;
		if (obj->has<jsonxx::String>("objid"))
		{
			std::string objidstring = obj->get<jsonxx::String>("objid");
			std::stringstream sstr;
			sstr << objidstring;
			if (!(sstr >> objid))
				return;
		}
		if (obj->has<jsonxx::String>("partName"))
		{
			partName = obj->get<jsonxx::String>("partName").c_str();
		}
		if (obj->has<jsonxx::Boolean>("show"))
		{
			isShow = (bool)obj->get<jsonxx::Boolean>("show");
		}
		ClientMob* mob = dynamic_cast<ClientMob*>(g_WorldMgr->findActorByWID(objid));
		if (mob)
		{
			mob->showSkin(partName, isShow);
		}
	}
	else if (0 == strcmp(eventname, "PB_PLAYER_SHAKE_CH"))
	{
		int uin = 0;
		if (obj->has<jsonxx::Number>("Uin"))
		{
			uin = (int)obj->get<jsonxx::Number>("Uin");
		}
		ClientPlayer* player = static_cast<ClientPlayer*>(g_WorldMgr->getPlayerByUin(uin));
		if (player)
		{
			auto temperatureComponent = player->getTemperatureComponent();
			if (temperatureComponent) temperatureComponent->ShakeImpactDuration();
		}
	}
	else if (0 == strcmp(eventname, "PB_ACTOR_DISSOLVE_COMPONENT_OPEN_HC"))
	{
		long long objid = 0;
		bool dissolveOpen = false;
		float dissolveRate = 0.2f;
		float dissolveType = 0.8f;
		if (obj->has<jsonxx::String>("objid"))
		{
			std::string objidstring = obj->get<jsonxx::String>("objid");
			std::stringstream sstr;
			sstr << objidstring;
			if (!(sstr >> objid))
				return;
		}
		if (obj->has<jsonxx::Boolean>("dissolveOpen"))
		{
			dissolveOpen = (bool)obj->get<jsonxx::Boolean>("dissolveOpen");
		}
		if (obj->has<jsonxx::Number>("dissolveRate"))
		{
			dissolveRate = (float)obj->get<jsonxx::Number>("dissolveRate");
		}
		if (obj->has<jsonxx::Number>("dissolveType"))
		{
			dissolveType = (float)obj->get<jsonxx::Number>("dissolveType");
		}

		ClientActor* actor = static_cast<ClientActor*>(g_WorldMgr->findActorByWID(objid));
		if (!actor)
			return;

		auto dissolveComp = actor->GetComponent<DissolveComponent>();
		if (!dissolveComp)
			return;

		if (dissolveOpen)
		{
			dissolveComp->setDissolveIntensityRate(dissolveRate);
			dissolveComp->setDissolveType(dissolveType);
		}
		dissolveComp->openDissolve(dissolveOpen);
	}
	else if (0 == strcmp(eventname, "PB_COOKBOOKINFO_HC"))
	{
		int uin = 0;
		if (obj->has<jsonxx::Number>("uin"))
		{
			uin = (int)obj->get<jsonxx::Number>("uin");
		}
		int id = 0;
		if (obj->has<jsonxx::Number>("cookbookid"))
		{
			id = (int)obj->get<jsonxx::Number>("cookbookid");
		}
		int state = 0;
		if (obj->has<jsonxx::Number>("state"))
		{
			state = (int)obj->get<jsonxx::Number>("state");
		}
		if (uin > 0 && id > 0 && state > 0)
		{
			g_WorldMgr->AddCookBook(uin, id, state);
		}
	}
	else if (0 == strcmp(eventname, "PB_STOVETAKE_CH"))
	{
		int uin = 0;
		if (obj->has<jsonxx::Number>("uin"))
		{
			uin = (int)obj->get<jsonxx::Number>("uin");
		}
		WCoord pos;
		if (obj->has<jsonxx::Number>("x"))
		{
			pos.x = (int)obj->get<jsonxx::Number>("x");
		}
		if (obj->has<jsonxx::Number>("y"))
		{
			pos.y = (int)obj->get<jsonxx::Number>("y");
		}
		if (obj->has<jsonxx::Number>("z"))
		{
			pos.z = (int)obj->get<jsonxx::Number>("z");
		}
		int taketype = 0;
		if (obj->has<jsonxx::Number>("taketype"))
		{
			taketype = (int)obj->get<jsonxx::Number>("taketype");
		}
		ClientPlayer* player = static_cast<ClientPlayer*>(g_WorldMgr->getPlayerByUin(uin));
		if (player)
		{
			World* world = player->getWorld();
			if (world && world->getContainerMgr())
			{
				WorldStove* stove = dynamic_cast<WorldStove*>(world->getContainerMgr()->getContainer(pos.x, pos.y, pos.z));
				if (stove)
				{
					switch (taketype)
					{
					case 1: //放燃料到
					{
						int index = 0;
						if (obj->has<jsonxx::Number>("index"))
						{
							index = (int)obj->get<jsonxx::Number>("index");
						}
						stove->addFurnace(player, index);
					}
					break;
					case 2: //取出燃料
					{
						stove->takeFurnace(player);
					}
					break;
					case 3: //取出生成物
					{
						stove->takeResult(player);

						break;
					default:
						break;
					}
					}

				}
			}
		}

	}
}

#pragma region ProtocolFeild
void SandboxActorSubsystem::DealSkillPlayAnimHC(jsonxx::Object* obj)
{
	long long objid = 0;
	if (obj->has<jsonxx::String>("objid"))
	{
		std::string objidstring = obj->get<jsonxx::String>("objid");
		std::stringstream sstr;
		sstr << objidstring;
		sstr >> objid;
	}
	int tpsanimid = 0;
	if (obj->has<jsonxx::Number>("tpsanimid"))
	{
		tpsanimid = (int)obj->get<jsonxx::Number>("tpsanimid");
	}
	int fpsanimid = 0;
	if (obj->has<jsonxx::Number>("fpsanimid"))
	{
		fpsanimid = (int)obj->get<jsonxx::Number>("fpsanimid");
	}

	int loop = -1;
	if (obj->has<jsonxx::Number>("loop"))
	{
		loop = (int)obj->get<jsonxx::Number>("loop");
	}
	int playLayer = -1;
	if (obj->has<jsonxx::Number>("playLayer"))
	{
		playLayer = (int)obj->get<jsonxx::Number>("playLayer");
	}

	IClientActor* actor = g_WorldMgr->findActorByWID(objid);
	if (actor)
	{
		actor->GetActor()->skillPlayAnim(tpsanimid, fpsanimid, loop, playLayer);
	}
}
/*PB_SKILLSTOPANIM_HC*/
void SandboxActorSubsystem::DealSkillStopAnimHC(jsonxx::Object* obj)
{
	long long objid = 0;
	if (obj->has<jsonxx::String>("objid"))
	{
		std::string objidstring = obj->get<jsonxx::String>("objid");
		std::stringstream sstr;
		sstr << objidstring;
		sstr >> objid;
	}
	int tpsanimid = 0;
	if (obj->has<jsonxx::Number>("tpsanimid"))
	{
		tpsanimid = (int)obj->get<jsonxx::Number>("tpsanimid");
	}
	int fpsanimid = 0;
	if (obj->has<jsonxx::Number>("fpsanimid"))
	{
		fpsanimid = (int)obj->get<jsonxx::Number>("fpsanimid");
	}

	bool isReset = false;
	if (obj->has<jsonxx::Boolean>("isReset"))
	{
		isReset = obj->get<jsonxx::Boolean>("isReset");
	}

	IClientActor* actor = g_WorldMgr->findActorByWID(objid);
	if (actor)
	{
		actor->GetActor()->skillStopAnim(tpsanimid, fpsanimid, isReset);
	}
}
/*PB_SKILLPLAYBODYEFFECT_HC*/
void SandboxActorSubsystem::DealSkillPlayBodyEffectHC(jsonxx::Object* obj)
{
	long long objid = 0;
	if (obj->has<jsonxx::String>("objid"))
	{
		std::string objidstring = obj->get<jsonxx::String>("objid");
		std::stringstream sstr;
		sstr << objidstring;
		sstr >> objid;
	}
	std::string path = "";
	if (obj->has<jsonxx::String>("path"))
	{
		path = obj->get<jsonxx::String>("path");
	}
	int loopPlayTime = 0;
	if (obj->has<jsonxx::Number>("loopPlayTime"))
	{
		loopPlayTime = (int)obj->get<jsonxx::Number>("loopPlayTime");
	}
	Vector3f OffsetPosition;
	if (obj->has<jsonxx::Object>("OffsetPosition"))
	{
		jsonxx::Object offsetpos = obj->get<jsonxx::Object>("OffsetPosition");
		OffsetPosition.x = (float)offsetpos.get<jsonxx::Number>("x");
		OffsetPosition.y = (float)offsetpos.get<jsonxx::Number>("y");
		OffsetPosition.z = (float)offsetpos.get<jsonxx::Number>("z");
	}
	Vector3f Rote;
	if (obj->has<jsonxx::Object>("rote"))
	{
		jsonxx::Object rote = obj->get<jsonxx::Object>("rote");
		Rote.x = (float)rote.get<jsonxx::Number>("x");
		Rote.y = (float)rote.get<jsonxx::Number>("y");
		Rote.z = (float)rote.get<jsonxx::Number>("z");
	}
	Vector3f Scale;
	if (obj->has<jsonxx::Object>("scale"))
	{
		jsonxx::Object scale = obj->get<jsonxx::Object>("scale");
		Scale.x = (float)scale.get<jsonxx::Number>("x");
		Scale.y = (float)scale.get<jsonxx::Number>("y");
		Scale.z = (float)scale.get<jsonxx::Number>("z");
	}

	bool isLoop = false;
	if (obj->has<jsonxx::Boolean>("isLoop"))
	{
		isLoop = obj->get<jsonxx::Boolean>("isLoop");
	}
	int motionclass;
	if (obj->has<jsonxx::Number>("motionclass"))
	{
		motionclass = obj->get<jsonxx::Number>("motionclass");
	}
	IClientActor* actor = g_WorldMgr->findActorByWID(objid);
	if (actor)
	{
		actor->GetActor()->skillPlayBodyEffect(path.c_str(), loopPlayTime, OffsetPosition, Rote, Scale, isLoop, motionclass);
	}
}

void SandboxActorSubsystem::DealSkillStopBodyEffectHC(jsonxx::Object* obj)
{
	long long objid = 0;
	if (obj->has<jsonxx::String>("objid"))
	{
		std::string objidstring = obj->get<jsonxx::String>("objid");
		std::stringstream sstr;
		sstr << objidstring;
		sstr >> objid;
	}
	std::string path = "";
	if (obj->has<jsonxx::String>("path"))
	{
		path = obj->get<jsonxx::String>("path");
	}
	IClientActor* actor = g_WorldMgr->findActorByWID(objid);
	if (actor)
	{
		actor->GetActor()->skillStopBodyEffect(path.c_str());
	}
}


//PB_SKILLWORLDPLAYBODYEFFECT_HC
void SandboxActorSubsystem::DealSkillWorldPlayEffectHC(jsonxx::Object* obj)
{
	int uin = 0;
	if (obj->has<jsonxx::Number>("uin"))
	{
		uin = (int)obj->get<jsonxx::Number>("uin");
	}
	jsonxx::Object context;
	WCoord pos;
	float yaw, pitch, roll;
	yaw = pitch = roll = 0;
	bool isLoop = false;
	int ptime = 0;
	std::string  path;
	Rainbow::Vector3f scale;
	if (obj->has<jsonxx::Object>("context"))
	{
		context = obj->get<jsonxx::Object>("context");
		if (context.has<jsonxx::String>("path"))
		{
			path = context.get<jsonxx::String>("path");
		}
		if (context.has<jsonxx::Number>("ptime"))
		{
			ptime = (int)context.get<jsonxx::Number>("ptime");
		}
		if (context.has<jsonxx::Boolean>("isLoop"))
		{
			isLoop = context.get<jsonxx::Boolean>("isLoop");
		}
		if (context.has<jsonxx::Number>("x"))
		{
			pos.x = (int)context.get<jsonxx::Number>("x");
		}
		if (context.has<jsonxx::Number>("y"))
		{
			pos.y = (int)context.get<jsonxx::Number>("y");
		}
		if (context.has<jsonxx::Number>("z"))
		{
			pos.z = (int)context.get<jsonxx::Number>("z");
		}
		if (context.has<jsonxx::Number>("yaw"))
		{
			yaw = (float)context.get<jsonxx::Number>("yaw");
		}
		if (context.has<jsonxx::Number>("pitch"))
		{
			pitch = (float)context.get<jsonxx::Number>("pitch");
		}
		if (context.has<jsonxx::Number>("roll"))
		{
			roll = (float)context.get<jsonxx::Number>("roll");
		}
		if (context.has<jsonxx::Number>("sx"))
		{
			scale.x = (float)context.get<jsonxx::Number>("sx");
		}
		if (context.has<jsonxx::Number>("sy"))
		{
			scale.y = (float)context.get<jsonxx::Number>("sy");
		}
		if (context.has<jsonxx::Number>("sz"))
		{
			scale.z = (float)context.get<jsonxx::Number>("sz");
		}
	}

	IClientPlayer* player = g_WorldMgr->getPlayerByUin(uin);
	if (player && player->GetPlayer()->getWorld())
	{
		player->GetPlayer()->getWorld()->skillPlayParticleEffect(pos, scale, path.c_str(), yaw, pitch, roll, ptime, isLoop);
	}
}
/*PB_Accumulator_HC*/
void SandboxActorSubsystem::DealAccumulatorHC(jsonxx::Object* obj)
{
	int uin = 0;
	if (obj->has<jsonxx::Number>("uin"))
	{
		uin = (int)obj->get<jsonxx::Number>("uin");
	}
	float progress = 0;
	if (obj->has<jsonxx::Number>("progress"))
	{
		progress = (float)obj->get<jsonxx::Number>("progress");
	}
	IClientPlayer* player = g_WorldMgr->getPlayerByUin(uin);
	if (player && player->GetPlayer()->getWorld())
	{
		player->GetPlayer()->setAccumulatorState(progress);
	}
}
//PB_SKILLPLAYTOOLANIM_HC
void SandboxActorSubsystem::DealSkillPlayToolAnimHC(jsonxx::Object* obj)
{
	long long objid = 0;
	if (obj->has<jsonxx::String>("objid"))
	{
		std::string objidstring = obj->get<jsonxx::String>("objid");
		std::stringstream sstr;
		sstr << objidstring;
		sstr >> objid;
	}
	int animid = 0;
	if (obj->has<jsonxx::Number>("animid"))
	{
		animid = (int)obj->get<jsonxx::Number>("animid");
	}
	IClientActor* actor = g_WorldMgr->findActorByWID(objid);
	if (actor)
	{
		actor->GetActor()->skillPlayToolAnim(animid);
	}
}
//	PB_SKILLSTOPTOOLANIM_HC
void SandboxActorSubsystem::DealSkillStopToolAnimHC(jsonxx::Object* obj)
{
	long long objid = 0;
	if (obj->has<jsonxx::String>("objid"))
	{
		std::string objidstring = obj->get<jsonxx::String>("objid");
		std::stringstream sstr;
		sstr << objidstring;
		sstr >> objid;
	}
	int animid = 0;
	if (obj->has<jsonxx::Number>("animid"))
	{
		animid = (int)obj->get<jsonxx::Number>("animid");
	}
	IClientActor* actor = g_WorldMgr->findActorByWID(objid);
	if (actor)
	{
		actor->GetActor()->skillStopToolAnim(animid);
	}
}

void SandboxActorSubsystem::DealSkillSetChargeMoveHC(jsonxx::Object* obj)
{
	int uin = 0;
	if (obj->has<jsonxx::Number>("uin"))
	{
		uin = (int)obj->get<jsonxx::Number>("uin");
	}
	float chargemove = 0;
	if (obj->has<jsonxx::Number>("chargemove"))
	{
		chargemove = (float)obj->get<jsonxx::Number>("chargemove");
	}
	IClientPlayer* player = g_WorldMgr->getPlayerByUin(uin);
	if (player && player->GetPlayer()->getWorld())
	{
		player->GetPlayer()->skillSetChargeMove(chargemove);
	}
}

void SandboxActorSubsystem::DealSkillMovePos(jsonxx::Object* obj)
{
	long long objid = 0;
	if (obj->has<jsonxx::String>("objid"))
	{
		std::string objidstring = obj->get<jsonxx::String>("objid");
		std::stringstream sstr;
		sstr << objidstring;
		sstr >> objid;
	}
	int x = 0;
	if (obj->has<jsonxx::Number>("x"))
	{
		x = (int)obj->get<jsonxx::Number>("x");
	}
	int y = 0;
	if (obj->has<jsonxx::Number>("y"))
	{
		y = (int)obj->get<jsonxx::Number>("y");
	}

	int z = 0;
	if (obj->has<jsonxx::Number>("z"))
	{
		z = (int)obj->get<jsonxx::Number>("z");
	}
	IClientActor* actor = g_WorldMgr->findActorByWID(objid);
	if (actor)
	{
		ActorLocoMotion* locomotion = actor->GetActor()->getLocoMotion();
		if (locomotion)
		{
			WCoord pos;/*= WCoord(x, y, z);*/
			pos.x = x;
			pos.y = y;
			pos.z = z;
			locomotion->gotoPosition(pos);
			auto RidComp = actor->GetActor()->getRiddenComponent();
			if (RidComp && RidComp->isRiding())
			{
				auto ridingActor = RidComp->getRidingActor();
				if (ridingActor && ridingActor->getObjType() != OBJ_TYPE_MINECART)
				{
					ridingActor->getLocoMotion()->gotoPosition(pos);
				}
			}
		}

	}

}

void SandboxActorSubsystem::DealSkillCamera(jsonxx::Object* obj)
{
	int  uin = 0;
	if (obj->has<jsonxx::Number>("uin"))
	{
		uin = (int)obj->get<jsonxx::Number>("uin");
	}
	float val = 0.0f;
	if (obj->has<jsonxx::Number>("val"))
	{
		val = (float)obj->get<jsonxx::Number>("val");
	}
	bool  isreset = false;
	if (obj->has<jsonxx::Boolean>("isReset"))
	{
		isreset = obj->get<jsonxx::Boolean>("isReset");
	}
	IClientPlayer* player = g_WorldMgr->getPlayerByUin(uin);
	if (player && player->GetPlayer()->getWorld())
	{
		PlayerControl* playerControl = dynamic_cast<PlayerControl*>(player);
		if (playerControl)
		{
			GameCamera* camera = playerControl->getCamera();
			if (camera)
			{
				if (isreset)
				{
					camera->disableZoom();
				}
				else
				{
					camera->setZoomInOut(camera->getFov() - val);
				}
			}
		}

	}

}

void SandboxActorSubsystem::DealSkillSetOBJInfo(jsonxx::Object* obj)
{
	long long objid = 0;
	if (obj->has<jsonxx::String>("objid"))
	{
		std::string objidstring = obj->get<jsonxx::String>("objid");
		std::stringstream sstr;
		sstr << objidstring;
		sstr >> objid;
	}
	IClientActor* actor = g_WorldMgr->findActorByWID(objid);
	if (actor)
	{
		int x = 0;
		if (obj->has<jsonxx::Number>("x"))
		{
			x = (int)obj->get<jsonxx::Number>("x");
		}
		int y = 0;
		if (obj->has<jsonxx::Number>("y"))
		{
			y = (int)obj->get<jsonxx::Number>("y");
		}

		int z = 0;
		if (obj->has<jsonxx::Number>("z"))
		{
			z = (int)obj->get<jsonxx::Number>("z");
		}

		float sx = 0;
		if (obj->has<jsonxx::Number>("sx"))
		{
			sx = (float)obj->get<jsonxx::Number>("sx");
		}
		float sy = 0;
		if (obj->has<jsonxx::Number>("sy"))
		{
			sy = (float)obj->get<jsonxx::Number>("sy");
		}

		float sz = 0;
		if (obj->has<jsonxx::Number>("sz"))
		{
			sz = (float)obj->get<jsonxx::Number>("sz");
		}

		float rx = 0;
		if (obj->has<jsonxx::Number>("rx"))
		{
			rx = (float)obj->get<jsonxx::Number>("rx");
		}
		float ry = 0;
		if (obj->has<jsonxx::Number>("ry"))
		{
			ry = (float)obj->get<jsonxx::Number>("ry");
		}

		float rz = 0;
		if (obj->has<jsonxx::Number>("rz"))
		{
			rz = (float)obj->get<jsonxx::Number>("rz");
		}

		actor->GetActor()->setPosition(WCoord(x, y, z));
		actor->GetActor()->SetLocalScale(Rainbow::Vector3f(sx, sy, sz));
		actor->GetActor()->SetLocalRotationEuler(rx, ry, rz);
	}
}

void SandboxActorSubsystem::PlayWeaponAnimHC(jsonxx::Object* obj)
{
	long long objid = 0;
	if (obj->has<jsonxx::String>("objid"))
	{
		std::string objidstring = obj->get<jsonxx::String>("objid");
		std::stringstream sstr;
		sstr << objidstring;
		sstr >> objid;
	}
	int animid = 0;
	if (obj->has<jsonxx::Number>("animid"))
	{
		animid = (int)obj->get<jsonxx::Number>("animid");
	}
	int loop = 0;
	if (obj->has<jsonxx::Number>("loop"))
	{
		loop = (int)obj->get<jsonxx::Number>("loop");
	}
	float speed = 1.f;
	if (obj->has<jsonxx::Number>("speed"))
	{
		speed = (float)obj->get<jsonxx::Number>("speed");
	}

	IClientActor* actor = g_WorldMgr->findActorByWID(objid);
	if (actor && actor->GetActor()->getBody())
	{
		actor->GetActor()->getBody()->playWeaponAnim(animid, loop, speed);
	}
}

void SandboxActorSubsystem::PlayWeaponAnimCH(jsonxx::Object* obj)
{
	long long objid = 0;
	if (obj->has<jsonxx::String>("objid"))
	{
		std::string objidstring = obj->get<jsonxx::String>("objid");
		std::stringstream sstr;
		sstr << objidstring;
		sstr >> objid;
	}
	int animid = 0;
	if (obj->has<jsonxx::Number>("animid"))
	{
		animid = (int)obj->get<jsonxx::Number>("animid");
	}
	int loop = 0;
	if (obj->has<jsonxx::Number>("loop"))
	{
		loop = (int)obj->get<jsonxx::Number>("loop");
	}
	float speed = 1.f;
	if (obj->has<jsonxx::Number>("speed"))
	{
		speed = (float)obj->get<jsonxx::Number>("speed");
	}

	IClientActor* actor = g_WorldMgr->findActorByWID(objid);
	if (actor)
	{
		actor->GetActor()->PlayWeaponAnim(animid, loop, speed);
	}
}

void SandboxActorSubsystem::StopWeaponAnimHC(jsonxx::Object* obj)
{
	long long objid = 0;
	if (obj->has<jsonxx::String>("objid"))
	{
		std::string objidstring = obj->get<jsonxx::String>("objid");
		std::stringstream sstr;
		sstr << objidstring;
		sstr >> objid;
	}
	int animid = 0;
	if (obj->has<jsonxx::Number>("animid"))
	{
		animid = (int)obj->get<jsonxx::Number>("animid");
	}

	IClientActor* actor = g_WorldMgr->findActorByWID(objid);
	if (actor && actor->GetActor()->getBody())
	{
		actor->GetActor()->getBody()->stopWeaponAnim(animid);
	}
}

void SandboxActorSubsystem::StopWeaponAnimCH(jsonxx::Object* obj)
{
	long long objid = 0;
	if (obj->has<jsonxx::String>("objid"))
	{
		std::string objidstring = obj->get<jsonxx::String>("objid");
		std::stringstream sstr;
		sstr << objidstring;
		sstr >> objid;
	}
	int animid = 0;
	if (obj->has<jsonxx::Number>("animid"))
	{
		animid = (int)obj->get<jsonxx::Number>("animid");
	}

	IClientActor* actor = g_WorldMgr->findActorByWID(objid);
	if (actor)
	{
		actor->GetActor()->StopWeaponAnim(animid);
	}
}

void SandboxActorSubsystem::PlayWeaponMotionHC(jsonxx::Object* obj)
{
	long long objid = 0;
	if (obj->has<jsonxx::String>("objid"))
	{
		std::string objidstring = obj->get<jsonxx::String>("objid");
		std::stringstream sstr;
		sstr << objidstring;
		sstr >> objid;
	}
	std::string name;
	if (obj->has<jsonxx::String>("name"))
	{
		name = obj->get<jsonxx::String>("name");
	}
	bool reset = false;
	if (obj->has<jsonxx::Boolean>("reset"))
	{
		reset = obj->get<jsonxx::Boolean>("reset");
	}
	int mclass = 0;
	if (obj->has<jsonxx::Number>("mclass"))
	{
		mclass = (int)obj->get<jsonxx::Number>("mclass");
	}
	float scale = 1.f;
	if (obj->has<jsonxx::Number>("scale"))
	{
		scale = (float)obj->get<jsonxx::Number>("scale");
	}

	IClientActor* actor = g_WorldMgr->findActorByWID(objid);
	if (actor && actor->GetActor()->getBody())
	{
		actor->GetActor()->getBody()->playWeaponMotion(name.c_str(), reset, mclass, scale);
	}
}

void SandboxActorSubsystem::PlayWeaponMotionCH(jsonxx::Object* obj)
{
	long long objid = 0;
	if (obj->has<jsonxx::String>("objid"))
	{
		std::string objidstring = obj->get<jsonxx::String>("objid");
		std::stringstream sstr;
		sstr << objidstring;
		sstr >> objid;
	}
	std::string name;
	if (obj->has<jsonxx::String>("name"))
	{
		name = obj->get<jsonxx::String>("name");
	}
	bool reset = false;
	if (obj->has<jsonxx::Boolean>("reset"))
	{
		reset = obj->get<jsonxx::Boolean>("reset");
	}
	int mclass = 0;
	if (obj->has<jsonxx::Number>("mclass"))
	{
		mclass = (int)obj->get<jsonxx::Number>("mclass");
	}
	float scale = 1.f;
	if (obj->has<jsonxx::Number>("scale"))
	{
		scale = (float)obj->get<jsonxx::Number>("scale");
	}

	IClientActor* actor = g_WorldMgr->findActorByWID(objid);
	if (actor)
	{
		actor->GetActor()->PlayWeaponMotion(name.c_str(), reset, mclass, scale);
	}
}

void SandboxActorSubsystem::StopWeaponMotionHC(jsonxx::Object* obj)
{
	long long objid = 0;
	if (obj->has<jsonxx::String>("objid"))
	{
		std::string objidstring = obj->get<jsonxx::String>("objid");
		std::stringstream sstr;
		sstr << objidstring;
		sstr >> objid;
	}
	int mclass = 0;
	if (obj->has<jsonxx::Number>("mclass"))
	{
		mclass = (int)obj->get<jsonxx::Number>("mclass");
	}
	IClientActor* actor = g_WorldMgr->findActorByWID(objid);
	if (actor && actor->GetActor()->getBody())
	{
		actor->GetActor()->getBody()->stopWeaponMotion(mclass);
	}
}

void SandboxActorSubsystem::StopWeaponMotionCH(jsonxx::Object* obj)
{
	long long objid = 0;
	if (obj->has<jsonxx::String>("objid"))
	{
		std::string objidstring = obj->get<jsonxx::String>("objid");
		std::stringstream sstr;
		sstr << objidstring;
		sstr >> objid;
	}
	int mclass = 0;
	if (obj->has<jsonxx::Number>("mclass"))
	{
		mclass = (int)obj->get<jsonxx::Number>("mclass");
	}
	IClientActor* actor = g_WorldMgr->findActorByWID(objid);
	if (actor)
	{
		actor->GetActor()->StopWeaponMotion(mclass);
	}
}
void SandboxActorSubsystem::SetLocoMotionTypeHC(jsonxx::Object* obj)
{
	long long objid = 0;
	if (obj->has<jsonxx::String>("objid"))
	{
		std::string objidstring = obj->get<jsonxx::String>("objid");
		std::stringstream sstr;
		sstr << objidstring;
		sstr >> objid;
	}
	int locotype = 0;
	if (obj->has<jsonxx::Number>("locotype"))
	{
		locotype = (int)obj->get<jsonxx::Number>("locotype");
	}
	bool isno = false;
	if (obj->has<jsonxx::Boolean>("isno"))
	{
		isno = (bool)obj->get<jsonxx::Boolean>("isno");
	}

	IClientActor* actor = g_WorldMgr->findActorByWID(objid);
	if (actor)
	{
		actor->GetActor()->SetLocoMotionType(locotype, isno);
	}
}
#pragma endregion
