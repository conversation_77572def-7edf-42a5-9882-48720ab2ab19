
#include "AITargetHurtee.h"
#include "ClientActor.h"
#include "ClientMob.h"
#include "ClientPlayer.h"
#include "ActorBoss.h"
#include "ToAttackTargetComponent.h"
#include "LivingAttrib.h"
#include "ActorBody.h"
#include "ActorVision.h"
#include "navigationpath.h"

AITargetHurtee::AITargetHurtee(ClientMob *pActor, bool <PERSON>elp, bool BossExclude)
	: m_LastBeAtkTimer(0), m_CallHelp(CallHelp), m_BossExclude(BossExclude), AITarget(pActor, false)
{
	setMutexBits(1);
	m_CheckSight = true;
}

bool AITargetHurtee::willRun()
{
	WCoord pos = m_pMobActor->getPosition();
	//����home��Χ��������
	if (!m_pMobActor->isInHomeDist(pos.x, pos.y, pos.z))
	{
		m_pMobActor->setNeedRetHome(true);
		return false;
	}

	if (m_LastBeAtkTimer != m_pMobActor->getBeHurtTimer() && isSuitableTarget(m_pMobActor->getBeHurtTarget(),true))
	{
		return true;
	}
	LivingAttrib* pLivingAttrib = dynamic_cast<LivingAttrib*>(m_pMobActor->getAttrib());
	if (nullptr != pLivingAttrib)
	{
		if (pLivingAttrib->getBuffEffectBankInfo(BuffAttrType::BUFFATTRT_FORBID_OPERATE))
		{
			return false;
		}
	}
	return false;
}

void AITargetHurtee::start()
{
	ActorLiving *target = dynamic_cast<ActorLiving *>(m_pMobActor->getBeHurtTarget());
	if (target == NULL)
		return;

	// �Ƿ��ų�boss
	if (m_BossExclude)
	{
		if (dynamic_cast<ActorBoss*>(target) != nullptr)
			return;
	}

	/*WCoord pos = m_pMobActor->getPosition();
	WCoord targetpos = target->getLocoMotion()->getPosition();
	WCoord vec = targetpos - m_pMobActor->getLocoMotion()->getPosition();

	float dist = vec.length();
	if (m_pMobActor->getViewDist() < dist)
	{
		//m_pMobActor->getNavigator()->tryMoveTo(targetpos.x, targetpos.y, targetpos.z, 1);
		m_pMobActor->getNavigator()->tryMoveTo(200, 700, 2900, 1);
	}
	else*/
	{
		auto targetComponent = m_pMobActor->getToAttackTargetComponent();
		if (targetComponent)
		{
			targetComponent->setTarget(target);
			m_pMobActor->setNeedRetHome(false);
		}
		m_LastBeAtkTimer = m_pMobActor->getBeHurtTimer();
	}

	//�ٻ���Χ�Ĺ���һ���𹥻�
	if (m_CallHelp)
	{
		CollideAABB box;
		m_pMobActor->getCollideBox(box);
		box.expand(1000, 1000, 1000);

		std::vector<IClientActor *>actors;
		m_pMobActor->getWorld()->getActorsOfTypeInBox(actors, box, OBJ_TYPE_MONSTER, m_pMobActor->getDefID());

		for(size_t i=0; i<actors.size(); i++)
		{
			ClientMob *pMob = dynamic_cast<ClientMob *>(actors[i]);
			if(pMob && pMob!=m_pMobActor && !pMob->isDead())
			{
				auto targetComponent = pMob->getToAttackTargetComponent();
				if (targetComponent && !targetComponent->hasTarget() && !pMob->isSameTeam(target))
				{
					targetComponent->setTarget(target);
				}
			}
		}
	}

	AITarget::start();
}

bool AITargetHurtee::continueRun()
{
	WCoord pos = m_pMobActor->getPosition();
	//����home��Χ��������
	if (!m_pMobActor->isInHomeDist(pos.x, pos.y, pos.z))
	{
		m_pMobActor->setBeHurtTarget(NULL);
		auto targetComponent = m_pMobActor->getToAttackTargetComponent();
		if (targetComponent)
		{
			targetComponent->setTarget(NULL);
			m_pMobActor->setNeedRetHome(true);
		}
		m_pMobActor->getNavigator()->clearPathEntity();
		return false;
	}

	ClientActor* target = NULL;
	auto targetComponent = m_pMobActor->getToAttackTargetComponent();
	if (targetComponent)
	{
		target = targetComponent->getTarget();
	}
	if (NULL == target)
	{
		return false;
	}
	if (target->isDead() || target->isInvulnerable(m_pMobActor))
	{
		m_pMobActor->setNeedRetHome(true);
		return false;
	}

	if (m_pMobActor->getDefID() == 3824 || m_pMobActor->getDefID() == 3829)
	{
		if (m_pMobActor->getIsHideStatus() && m_pMobActor->getBody())
		{
			m_pMobActor->getBody()->show(true, true);
			m_pMobActor->setIsHideStatus(false);
		}
	}

	WCoord targetpos = target->getLocoMotion()->getPosition();
	WCoord vec = targetpos - m_pMobActor->getLocoMotion()->getPosition();

	float dist = vec.length();
	//if (m_pMobActor->getTraceDist() < dist)
	if (m_pMobActor->getViewDist() < dist)
	{
		m_pMobActor->setNeedRetHome(true);
		return false;
	}
	else
	{
		if (m_CheckSight)
		{
			if (m_pMobActor->getVision()->canSeeInAICache(target))
			{
				m_CheckSightCount = 0;
			}
			else if (++m_CheckSightCount > 60) //60stick ���3�� �������ͷ���
			{
				m_pMobActor->setNeedRetHome(true);
				return false;
			}
		}

		return true;
	}
}

void AITargetHurtee::update()
{
	return;
}