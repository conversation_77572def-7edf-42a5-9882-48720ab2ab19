// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_FLATSAVECOMMON_FBSAVE_H_
#define FLATBUFFERS_GENERATED_FLATSAVECOMMON_FBSAVE_H_

#include "flatbuffers/flatbuffers.h"

namespace FBSave {

struct Coord3;

struct Vec2;

struct Vec3;

struct BoxBound;

struct Quat4;

struct Mat3x3f;

struct Mat4x4f;

struct SawtoothInfo;

struct ActorCommon;

struct ActorAttInfo;

struct ItemGridRune;

struct KVData;

struct ItemGrid;

struct ItemIndexGrid;

struct ActorBuff;

struct AttribMod;

struct Achievement;

struct AvatarModelData;

struct BoneModelData;

struct VehicleBlockLine;

struct UgcVertexData;

struct KeyValueData;

enum Color {
  Color_Red = 0,
  Color_Green = 1,
  Color_Blue = 2,
  Color_MIN = Color_Red,
  Color_MAX = Color_Blue
};

inline const char **EnumNamesColor() {
  static const char *names[] = {
    "Red",
    "Green",
    "Blue",
    nullptr
  };
  return names;
}

inline const char *EnumNameColor(Color e) {
  const size_t index = static_cast<int>(e);
  return EnumNamesColor()[index];
}

MANUALLY_ALIGNED_STRUCT(4) Coord3 FLATBUFFERS_FINAL_CLASS {
 private:
  int32_t x_;
  int32_t y_;
  int32_t z_;

 public:
  Coord3() {
    memset(this, 0, sizeof(Coord3));
  }
  Coord3(const Coord3 &_o) {
    memcpy(this, &_o, sizeof(Coord3));
  }
  Coord3(int32_t _x, int32_t _y, int32_t _z)
      : x_(flatbuffers::EndianScalar(_x)),
        y_(flatbuffers::EndianScalar(_y)),
        z_(flatbuffers::EndianScalar(_z)) {
  }
  int32_t x() const {
    return flatbuffers::EndianScalar(x_);
  }
  int32_t y() const {
    return flatbuffers::EndianScalar(y_);
  }
  int32_t z() const {
    return flatbuffers::EndianScalar(z_);
  }
};
STRUCT_END(Coord3, 12);

MANUALLY_ALIGNED_STRUCT(4) Vec2 FLATBUFFERS_FINAL_CLASS {
 private:
  float x_;
  float y_;

 public:
  Vec2() {
    memset(this, 0, sizeof(Vec2));
  }
  Vec2(const Vec2 &_o) {
    memcpy(this, &_o, sizeof(Vec2));
  }
  Vec2(float _x, float _y)
      : x_(flatbuffers::EndianScalar(_x)),
        y_(flatbuffers::EndianScalar(_y)) {
  }
  float x() const {
    return flatbuffers::EndianScalar(x_);
  }
  float y() const {
    return flatbuffers::EndianScalar(y_);
  }
};
STRUCT_END(Vec2, 8);

MANUALLY_ALIGNED_STRUCT(4) Vec3 FLATBUFFERS_FINAL_CLASS {
 private:
  float x_;
  float y_;
  float z_;

 public:
  Vec3() {
    memset(this, 0, sizeof(Vec3));
  }
  Vec3(const Vec3 &_o) {
    memcpy(this, &_o, sizeof(Vec3));
  }
  Vec3(float _x, float _y, float _z)
      : x_(flatbuffers::EndianScalar(_x)),
        y_(flatbuffers::EndianScalar(_y)),
        z_(flatbuffers::EndianScalar(_z)) {
  }
  float x() const {
    return flatbuffers::EndianScalar(x_);
  }
  float y() const {
    return flatbuffers::EndianScalar(y_);
  }
  float z() const {
    return flatbuffers::EndianScalar(z_);
  }
};
STRUCT_END(Vec3, 12);

MANUALLY_ALIGNED_STRUCT(4) Quat4 FLATBUFFERS_FINAL_CLASS {
 private:
  float x_;
  float y_;
  float z_;
  float w_;

 public:
  Quat4() {
    memset(this, 0, sizeof(Quat4));
  }
  Quat4(const Quat4 &_o) {
    memcpy(this, &_o, sizeof(Quat4));
  }
  Quat4(float _x, float _y, float _z, float _w)
      : x_(flatbuffers::EndianScalar(_x)),
        y_(flatbuffers::EndianScalar(_y)),
        z_(flatbuffers::EndianScalar(_z)),
        w_(flatbuffers::EndianScalar(_w)) {
  }
  float x() const {
    return flatbuffers::EndianScalar(x_);
  }
  float y() const {
    return flatbuffers::EndianScalar(y_);
  }
  float z() const {
    return flatbuffers::EndianScalar(z_);
  }
  float w() const {
    return flatbuffers::EndianScalar(w_);
  }
};
STRUCT_END(Quat4, 16);

MANUALLY_ALIGNED_STRUCT(4) Mat3x3f FLATBUFFERS_FINAL_CLASS {
 private:
  float _11_;
  float _12_;
  float _13_;
  float _21_;
  float _22_;
  float _23_;
  float _31_;
  float _32_;
  float _33_;

 public:
  Mat3x3f() {
    memset(this, 0, sizeof(Mat3x3f));
  }
  Mat3x3f(const Mat3x3f &_o) {
    memcpy(this, &_o, sizeof(Mat3x3f));
  }
  Mat3x3f(float __11, float __12, float __13, float __21, float __22, float __23, float __31, float __32, float __33)
      : _11_(flatbuffers::EndianScalar(__11)),
        _12_(flatbuffers::EndianScalar(__12)),
        _13_(flatbuffers::EndianScalar(__13)),
        _21_(flatbuffers::EndianScalar(__21)),
        _22_(flatbuffers::EndianScalar(__22)),
        _23_(flatbuffers::EndianScalar(__23)),
        _31_(flatbuffers::EndianScalar(__31)),
        _32_(flatbuffers::EndianScalar(__32)),
        _33_(flatbuffers::EndianScalar(__33)) {
  }
  float _11() const {
    return flatbuffers::EndianScalar(_11_);
  }
  float _12() const {
    return flatbuffers::EndianScalar(_12_);
  }
  float _13() const {
    return flatbuffers::EndianScalar(_13_);
  }
  float _21() const {
    return flatbuffers::EndianScalar(_21_);
  }
  float _22() const {
    return flatbuffers::EndianScalar(_22_);
  }
  float _23() const {
    return flatbuffers::EndianScalar(_23_);
  }
  float _31() const {
    return flatbuffers::EndianScalar(_31_);
  }
  float _32() const {
    return flatbuffers::EndianScalar(_32_);
  }
  float _33() const {
    return flatbuffers::EndianScalar(_33_);
  }
};
STRUCT_END(Mat3x3f, 36);

MANUALLY_ALIGNED_STRUCT(4) Mat4x4f FLATBUFFERS_FINAL_CLASS {
 private:
  float _11_;
  float _12_;
  float _13_;
  float _14_;
  float _21_;
  float _22_;
  float _23_;
  float _24_;
  float _31_;
  float _32_;
  float _33_;
  float _34_;
  float _41_;
  float _42_;
  float _43_;
  float _44_;

 public:
  Mat4x4f() {
    memset(this, 0, sizeof(Mat4x4f));
  }
  Mat4x4f(const Mat4x4f &_o) {
    memcpy(this, &_o, sizeof(Mat4x4f));
  }
  Mat4x4f(float __11, float __12, float __13, float __14, float __21, float __22, float __23, float __24, float __31, float __32, float __33, float __34, float __41, float __42, float __43, float __44)
      : _11_(flatbuffers::EndianScalar(__11)),
        _12_(flatbuffers::EndianScalar(__12)),
        _13_(flatbuffers::EndianScalar(__13)),
        _14_(flatbuffers::EndianScalar(__14)),
        _21_(flatbuffers::EndianScalar(__21)),
        _22_(flatbuffers::EndianScalar(__22)),
        _23_(flatbuffers::EndianScalar(__23)),
        _24_(flatbuffers::EndianScalar(__24)),
        _31_(flatbuffers::EndianScalar(__31)),
        _32_(flatbuffers::EndianScalar(__32)),
        _33_(flatbuffers::EndianScalar(__33)),
        _34_(flatbuffers::EndianScalar(__34)),
        _41_(flatbuffers::EndianScalar(__41)),
        _42_(flatbuffers::EndianScalar(__42)),
        _43_(flatbuffers::EndianScalar(__43)),
        _44_(flatbuffers::EndianScalar(__44)) {
  }
  float _11() const {
    return flatbuffers::EndianScalar(_11_);
  }
  float _12() const {
    return flatbuffers::EndianScalar(_12_);
  }
  float _13() const {
    return flatbuffers::EndianScalar(_13_);
  }
  float _14() const {
    return flatbuffers::EndianScalar(_14_);
  }
  float _21() const {
    return flatbuffers::EndianScalar(_21_);
  }
  float _22() const {
    return flatbuffers::EndianScalar(_22_);
  }
  float _23() const {
    return flatbuffers::EndianScalar(_23_);
  }
  float _24() const {
    return flatbuffers::EndianScalar(_24_);
  }
  float _31() const {
    return flatbuffers::EndianScalar(_31_);
  }
  float _32() const {
    return flatbuffers::EndianScalar(_32_);
  }
  float _33() const {
    return flatbuffers::EndianScalar(_33_);
  }
  float _34() const {
    return flatbuffers::EndianScalar(_34_);
  }
  float _41() const {
    return flatbuffers::EndianScalar(_41_);
  }
  float _42() const {
    return flatbuffers::EndianScalar(_42_);
  }
  float _43() const {
    return flatbuffers::EndianScalar(_43_);
  }
  float _44() const {
    return flatbuffers::EndianScalar(_44_);
  }
};
STRUCT_END(Mat4x4f, 64);

MANUALLY_ALIGNED_STRUCT(4) ActorBuff FLATBUFFERS_FINAL_CLASS {
 private:
  uint32_t buffid_;
  uint16_t bufflv_;
  int16_t padding0__;
  int32_t ticks_;

 public:
  ActorBuff() {
    memset(this, 0, sizeof(ActorBuff));
  }
  ActorBuff(const ActorBuff &_o) {
    memcpy(this, &_o, sizeof(ActorBuff));
  }
  ActorBuff(uint32_t _buffid, uint16_t _bufflv, int32_t _ticks)
      : buffid_(flatbuffers::EndianScalar(_buffid)),
        bufflv_(flatbuffers::EndianScalar(_bufflv)),
        padding0__(0),
        ticks_(flatbuffers::EndianScalar(_ticks)) {
    (void)padding0__;
  }
  uint32_t buffid() const {
    return flatbuffers::EndianScalar(buffid_);
  }
  uint16_t bufflv() const {
    return flatbuffers::EndianScalar(bufflv_);
  }
  int32_t ticks() const {
    return flatbuffers::EndianScalar(ticks_);
  }
};
STRUCT_END(ActorBuff, 12);

MANUALLY_ALIGNED_STRUCT(4) AttribMod FLATBUFFERS_FINAL_CLASS {
 private:
  int32_t attr_;
  float val_;

 public:
  AttribMod() {
    memset(this, 0, sizeof(AttribMod));
  }
  AttribMod(const AttribMod &_o) {
    memcpy(this, &_o, sizeof(AttribMod));
  }
  AttribMod(int32_t _attr, float _val)
      : attr_(flatbuffers::EndianScalar(_attr)),
        val_(flatbuffers::EndianScalar(_val)) {
  }
  int32_t attr() const {
    return flatbuffers::EndianScalar(attr_);
  }
  float val() const {
    return flatbuffers::EndianScalar(val_);
  }
};
STRUCT_END(AttribMod, 8);

struct BoxBound FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_MIN = 4,
    VT_MAX = 6
  };
  const Vec3 *min() const {
    return GetStruct<const Vec3 *>(VT_MIN);
  }
  const Vec3 *max() const {
    return GetStruct<const Vec3 *>(VT_MAX);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<Vec3>(verifier, VT_MIN) &&
           VerifyField<Vec3>(verifier, VT_MAX) &&
           verifier.EndTable();
  }
};

struct BoxBoundBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_min(const Vec3 *min) {
    fbb_.AddStruct(BoxBound::VT_MIN, min);
  }
  void add_max(const Vec3 *max) {
    fbb_.AddStruct(BoxBound::VT_MAX, max);
  }
  BoxBoundBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  BoxBoundBuilder &operator=(const BoxBoundBuilder &);
  flatbuffers::Offset<BoxBound> Finish() {
    const auto end = fbb_.EndTable(start_, 2);
    auto o = flatbuffers::Offset<BoxBound>(end);
    return o;
  }
};

inline flatbuffers::Offset<BoxBound> CreateBoxBound(
    flatbuffers::FlatBufferBuilder &_fbb,
    const Vec3 *min = 0,
    const Vec3 *max = 0) {
  BoxBoundBuilder builder_(_fbb);
  builder_.add_max(max);
  builder_.add_min(min);
  return builder_.Finish();
}

struct SawtoothInfo FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_SAWTOOTHID = 4,
    VT_POS = 6
  };
  uint32_t sawtoothid() const {
    return GetField<uint32_t>(VT_SAWTOOTHID, 0);
  }
  const Vec3 *pos() const {
    return GetStruct<const Vec3 *>(VT_POS);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_SAWTOOTHID) &&
           VerifyField<Vec3>(verifier, VT_POS) &&
           verifier.EndTable();
  }
};

struct SawtoothInfoBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_sawtoothid(uint32_t sawtoothid) {
    fbb_.AddElement<uint32_t>(SawtoothInfo::VT_SAWTOOTHID, sawtoothid, 0);
  }
  void add_pos(const Vec3 *pos) {
    fbb_.AddStruct(SawtoothInfo::VT_POS, pos);
  }
  SawtoothInfoBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  SawtoothInfoBuilder &operator=(const SawtoothInfoBuilder &);
  flatbuffers::Offset<SawtoothInfo> Finish() {
    const auto end = fbb_.EndTable(start_, 2);
    auto o = flatbuffers::Offset<SawtoothInfo>(end);
    return o;
  }
};

inline flatbuffers::Offset<SawtoothInfo> CreateSawtoothInfo(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t sawtoothid = 0,
    const Vec3 *pos = 0) {
  SawtoothInfoBuilder builder_(_fbb);
  builder_.add_pos(pos);
  builder_.add_sawtoothid(sawtoothid);
  return builder_.Finish();
}

struct ActorCommon FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_WID = 4,
    VT_POS = 6,
    VT_MOTION = 8,
    VT_YAW = 10,
    VT_PITCH = 12,
    VT_FALLDIST = 14,
    VT_FLAGS = 16,
    VT_FLAGSEX = 18,
    VT_LIVETICKS = 20,
    VT_ATTINFO = 22,
    VT_MASTEROBJID = 24,
    VT_SAWTOOTH = 26,
    VT_SANDBOXNODES = 28,
    VT_FIRSTSEARCH = 30
  };
  uint64_t wid() const {
    return GetField<uint64_t>(VT_WID, 0);
  }
  const Coord3 *pos() const {
    return GetStruct<const Coord3 *>(VT_POS);
  }
  const Vec3 *motion() const {
    return GetStruct<const Vec3 *>(VT_MOTION);
  }
  float yaw() const {
    return GetField<float>(VT_YAW, 0.0f);
  }
  float pitch() const {
    return GetField<float>(VT_PITCH, 0.0f);
  }
  float falldist() const {
    return GetField<float>(VT_FALLDIST, 0.0f);
  }
  uint32_t flags() const {
    return GetField<uint32_t>(VT_FLAGS, 0);
  }
  uint32_t flagsex() const {
    return GetField<uint32_t>(VT_FLAGSEX, 0);
  }
  uint32_t liveticks() const {
    return GetField<uint32_t>(VT_LIVETICKS, 0);
  }
  const ActorAttInfo *attinfo() const {
    return GetPointer<const ActorAttInfo *>(VT_ATTINFO);
  }
  uint64_t masterobjid() const {
    return GetField<uint64_t>(VT_MASTEROBJID, 0);
  }
  const flatbuffers::Vector<flatbuffers::Offset<SawtoothInfo>> *sawtooth() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<SawtoothInfo>> *>(VT_SAWTOOTH);
  }
  const flatbuffers::String *sandboxnodes() const {
    return GetPointer<const flatbuffers::String *>(VT_SANDBOXNODES);
  }
  int8_t firstsearch() const {
    return GetField<int8_t>(VT_FIRSTSEARCH, 0);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint64_t>(verifier, VT_WID) &&
           VerifyField<Coord3>(verifier, VT_POS) &&
           VerifyField<Vec3>(verifier, VT_MOTION) &&
           VerifyField<float>(verifier, VT_YAW) &&
           VerifyField<float>(verifier, VT_PITCH) &&
           VerifyField<float>(verifier, VT_FALLDIST) &&
           VerifyField<uint32_t>(verifier, VT_FLAGS) &&
           VerifyField<uint32_t>(verifier, VT_FLAGSEX) &&
           VerifyField<uint32_t>(verifier, VT_LIVETICKS) &&
           VerifyField<flatbuffers::uoffset_t>(verifier, VT_ATTINFO) &&
           verifier.VerifyTable(attinfo()) &&
           VerifyField<uint64_t>(verifier, VT_MASTEROBJID) &&
           VerifyField<flatbuffers::uoffset_t>(verifier, VT_SAWTOOTH) &&
           verifier.Verify(sawtooth()) &&
           verifier.VerifyVectorOfTables(sawtooth()) &&
           VerifyField<flatbuffers::uoffset_t>(verifier, VT_SANDBOXNODES) &&
           verifier.Verify(sandboxnodes()) &&
           VerifyField<int8_t>(verifier, VT_FIRSTSEARCH) &&
           verifier.EndTable();
  }
};

struct ActorCommonBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_wid(uint64_t wid) {
    fbb_.AddElement<uint64_t>(ActorCommon::VT_WID, wid, 0);
  }
  void add_pos(const Coord3 *pos) {
    fbb_.AddStruct(ActorCommon::VT_POS, pos);
  }
  void add_motion(const Vec3 *motion) {
    fbb_.AddStruct(ActorCommon::VT_MOTION, motion);
  }
  void add_yaw(float yaw) {
    fbb_.AddElement<float>(ActorCommon::VT_YAW, yaw, 0.0f);
  }
  void add_pitch(float pitch) {
    fbb_.AddElement<float>(ActorCommon::VT_PITCH, pitch, 0.0f);
  }
  void add_falldist(float falldist) {
    fbb_.AddElement<float>(ActorCommon::VT_FALLDIST, falldist, 0.0f);
  }
  void add_flags(uint32_t flags) {
    fbb_.AddElement<uint32_t>(ActorCommon::VT_FLAGS, flags, 0);
  }
  void add_flagsex(uint32_t flagsex) {
    fbb_.AddElement<uint32_t>(ActorCommon::VT_FLAGSEX, flagsex, 0);
  }
  void add_liveticks(uint32_t liveticks) {
    fbb_.AddElement<uint32_t>(ActorCommon::VT_LIVETICKS, liveticks, 0);
  }
  void add_attinfo(flatbuffers::Offset<ActorAttInfo> attinfo) {
    fbb_.AddOffset(ActorCommon::VT_ATTINFO, attinfo);
  }
  void add_masterobjid(uint64_t masterobjid) {
    fbb_.AddElement<uint64_t>(ActorCommon::VT_MASTEROBJID, masterobjid, 0);
  }
  void add_sawtooth(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<SawtoothInfo>>> sawtooth) {
    fbb_.AddOffset(ActorCommon::VT_SAWTOOTH, sawtooth);
  }
  void add_sandboxnodes(flatbuffers::Offset<flatbuffers::String> sandboxnodes) {
    fbb_.AddOffset(ActorCommon::VT_SANDBOXNODES, sandboxnodes);
  }
  void add_firstsearch(int8_t firstsearch) {
    fbb_.AddElement<int8_t>(ActorCommon::VT_FIRSTSEARCH, firstsearch, 0);
  }
  ActorCommonBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ActorCommonBuilder &operator=(const ActorCommonBuilder &);
  flatbuffers::Offset<ActorCommon> Finish() {
    const auto end = fbb_.EndTable(start_, 14);
    auto o = flatbuffers::Offset<ActorCommon>(end);
    return o;
  }
};

inline flatbuffers::Offset<ActorCommon> CreateActorCommon(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint64_t wid = 0,
    const Coord3 *pos = 0,
    const Vec3 *motion = 0,
    float yaw = 0.0f,
    float pitch = 0.0f,
    float falldist = 0.0f,
    uint32_t flags = 0,
    uint32_t flagsex = 0,
    uint32_t liveticks = 0,
    flatbuffers::Offset<ActorAttInfo> attinfo = 0,
    uint64_t masterobjid = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<SawtoothInfo>>> sawtooth = 0,
    flatbuffers::Offset<flatbuffers::String> sandboxnodes = 0,
    int8_t firstsearch = 0) {
  ActorCommonBuilder builder_(_fbb);
  builder_.add_masterobjid(masterobjid);
  builder_.add_wid(wid);
  builder_.add_sandboxnodes(sandboxnodes);
  builder_.add_sawtooth(sawtooth);
  builder_.add_attinfo(attinfo);
  builder_.add_liveticks(liveticks);
  builder_.add_flagsex(flagsex);
  builder_.add_flags(flags);
  builder_.add_falldist(falldist);
  builder_.add_pitch(pitch);
  builder_.add_yaw(yaw);
  builder_.add_motion(motion);
  builder_.add_pos(pos);
  builder_.add_firstsearch(firstsearch);
  return builder_.Finish();
}

inline flatbuffers::Offset<ActorCommon> CreateActorCommonDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint64_t wid = 0,
    const Coord3 *pos = 0,
    const Vec3 *motion = 0,
    float yaw = 0.0f,
    float pitch = 0.0f,
    float falldist = 0.0f,
    uint32_t flags = 0,
    uint32_t flagsex = 0,
    uint32_t liveticks = 0,
    flatbuffers::Offset<ActorAttInfo> attinfo = 0,
    uint64_t masterobjid = 0,
    const std::vector<flatbuffers::Offset<SawtoothInfo>> *sawtooth = nullptr,
    const char *sandboxnodes = nullptr,
    int8_t firstsearch = 0) {
  return FBSave::CreateActorCommon(
      _fbb,
      wid,
      pos,
      motion,
      yaw,
      pitch,
      falldist,
      flags,
      flagsex,
      liveticks,
      attinfo,
      masterobjid,
      sawtooth ? _fbb.CreateVector<flatbuffers::Offset<SawtoothInfo>>(*sawtooth) : 0,
      sandboxnodes ? _fbb.CreateString(sandboxnodes) : 0,
      firstsearch);
}

struct ActorAttInfo FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_MAXHP = 4,
    VT_HPRECOVER = 6,
    VT_WALKSPEED = 8,
    VT_SWIMSPEED = 10,
    VT_JUMPPOWER = 12,
    VT_PUNCHATTACK = 14,
    VT_RANGEATTACK = 16,
    VT_PUNCHDEFENSE = 18,
    VT_RANGEDEFENSE = 20,
    VT_DODGE = 22,
    VT_ATTACKTYPE = 24,
    VT_IMMUNETYPE = 26,
    VT_SETTINGATT = 28,
    VT_UNSIGHTED = 30
  };
  float maxhp() const {
    return GetField<float>(VT_MAXHP, -1.0f);
  }
  float hprecover() const {
    return GetField<float>(VT_HPRECOVER, -1.0f);
  }
  float walkspeed() const {
    return GetField<float>(VT_WALKSPEED, -1.0f);
  }
  float swimspeed() const {
    return GetField<float>(VT_SWIMSPEED, -1.0f);
  }
  float jumppower() const {
    return GetField<float>(VT_JUMPPOWER, -1.0f);
  }
  float punchattack() const {
    return GetField<float>(VT_PUNCHATTACK, -1.0f);
  }
  float rangeattack() const {
    return GetField<float>(VT_RANGEATTACK, -1.0f);
  }
  float punchdefense() const {
    return GetField<float>(VT_PUNCHDEFENSE, -1.0f);
  }
  float rangedefense() const {
    return GetField<float>(VT_RANGEDEFENSE, -1.0f);
  }
  int32_t dodge() const {
    return GetField<int32_t>(VT_DODGE, -1);
  }
  int32_t attacktype() const {
    return GetField<int32_t>(VT_ATTACKTYPE, -1);
  }
  int32_t immunetype() const {
    return GetField<int32_t>(VT_IMMUNETYPE, 0);
  }
  uint32_t settingatt() const {
    return GetField<uint32_t>(VT_SETTINGATT, 1023);
  }
  int8_t unsighted() const {
    return GetField<int8_t>(VT_UNSIGHTED, 0);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<float>(verifier, VT_MAXHP) &&
           VerifyField<float>(verifier, VT_HPRECOVER) &&
           VerifyField<float>(verifier, VT_WALKSPEED) &&
           VerifyField<float>(verifier, VT_SWIMSPEED) &&
           VerifyField<float>(verifier, VT_JUMPPOWER) &&
           VerifyField<float>(verifier, VT_PUNCHATTACK) &&
           VerifyField<float>(verifier, VT_RANGEATTACK) &&
           VerifyField<float>(verifier, VT_PUNCHDEFENSE) &&
           VerifyField<float>(verifier, VT_RANGEDEFENSE) &&
           VerifyField<int32_t>(verifier, VT_DODGE) &&
           VerifyField<int32_t>(verifier, VT_ATTACKTYPE) &&
           VerifyField<int32_t>(verifier, VT_IMMUNETYPE) &&
           VerifyField<uint32_t>(verifier, VT_SETTINGATT) &&
           VerifyField<int8_t>(verifier, VT_UNSIGHTED) &&
           verifier.EndTable();
  }
};

struct ActorAttInfoBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_maxhp(float maxhp) {
    fbb_.AddElement<float>(ActorAttInfo::VT_MAXHP, maxhp, -1.0f);
  }
  void add_hprecover(float hprecover) {
    fbb_.AddElement<float>(ActorAttInfo::VT_HPRECOVER, hprecover, -1.0f);
  }
  void add_walkspeed(float walkspeed) {
    fbb_.AddElement<float>(ActorAttInfo::VT_WALKSPEED, walkspeed, -1.0f);
  }
  void add_swimspeed(float swimspeed) {
    fbb_.AddElement<float>(ActorAttInfo::VT_SWIMSPEED, swimspeed, -1.0f);
  }
  void add_jumppower(float jumppower) {
    fbb_.AddElement<float>(ActorAttInfo::VT_JUMPPOWER, jumppower, -1.0f);
  }
  void add_punchattack(float punchattack) {
    fbb_.AddElement<float>(ActorAttInfo::VT_PUNCHATTACK, punchattack, -1.0f);
  }
  void add_rangeattack(float rangeattack) {
    fbb_.AddElement<float>(ActorAttInfo::VT_RANGEATTACK, rangeattack, -1.0f);
  }
  void add_punchdefense(float punchdefense) {
    fbb_.AddElement<float>(ActorAttInfo::VT_PUNCHDEFENSE, punchdefense, -1.0f);
  }
  void add_rangedefense(float rangedefense) {
    fbb_.AddElement<float>(ActorAttInfo::VT_RANGEDEFENSE, rangedefense, -1.0f);
  }
  void add_dodge(int32_t dodge) {
    fbb_.AddElement<int32_t>(ActorAttInfo::VT_DODGE, dodge, -1);
  }
  void add_attacktype(int32_t attacktype) {
    fbb_.AddElement<int32_t>(ActorAttInfo::VT_ATTACKTYPE, attacktype, -1);
  }
  void add_immunetype(int32_t immunetype) {
    fbb_.AddElement<int32_t>(ActorAttInfo::VT_IMMUNETYPE, immunetype, 0);
  }
  void add_settingatt(uint32_t settingatt) {
    fbb_.AddElement<uint32_t>(ActorAttInfo::VT_SETTINGATT, settingatt, 1023);
  }
  void add_unsighted(int8_t unsighted) {
    fbb_.AddElement<int8_t>(ActorAttInfo::VT_UNSIGHTED, unsighted, 0);
  }
  ActorAttInfoBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ActorAttInfoBuilder &operator=(const ActorAttInfoBuilder &);
  flatbuffers::Offset<ActorAttInfo> Finish() {
    const auto end = fbb_.EndTable(start_, 14);
    auto o = flatbuffers::Offset<ActorAttInfo>(end);
    return o;
  }
};

inline flatbuffers::Offset<ActorAttInfo> CreateActorAttInfo(
    flatbuffers::FlatBufferBuilder &_fbb,
    float maxhp = -1.0f,
    float hprecover = -1.0f,
    float walkspeed = -1.0f,
    float swimspeed = -1.0f,
    float jumppower = -1.0f,
    float punchattack = -1.0f,
    float rangeattack = -1.0f,
    float punchdefense = -1.0f,
    float rangedefense = -1.0f,
    int32_t dodge = -1,
    int32_t attacktype = -1,
    int32_t immunetype = 0,
    uint32_t settingatt = 1023,
    int8_t unsighted = 0) {
  ActorAttInfoBuilder builder_(_fbb);
  builder_.add_settingatt(settingatt);
  builder_.add_immunetype(immunetype);
  builder_.add_attacktype(attacktype);
  builder_.add_dodge(dodge);
  builder_.add_rangedefense(rangedefense);
  builder_.add_punchdefense(punchdefense);
  builder_.add_rangeattack(rangeattack);
  builder_.add_punchattack(punchattack);
  builder_.add_jumppower(jumppower);
  builder_.add_swimspeed(swimspeed);
  builder_.add_walkspeed(walkspeed);
  builder_.add_hprecover(hprecover);
  builder_.add_maxhp(maxhp);
  builder_.add_unsighted(unsighted);
  return builder_.Finish();
}

struct ItemGridRune FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_RUNEID = 4,
    VT_RUNEVAL0 = 6,
    VT_RUNEVAL1 = 8,
    VT_ITEMID = 10
  };
  int32_t runeid() const {
    return GetField<int32_t>(VT_RUNEID, 0);
  }
  float runeval0() const {
    return GetField<float>(VT_RUNEVAL0, 0.0f);
  }
  float runeval1() const {
    return GetField<float>(VT_RUNEVAL1, 0.0f);
  }
  int32_t itemid() const {
    return GetField<int32_t>(VT_ITEMID, 0);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_RUNEID) &&
           VerifyField<float>(verifier, VT_RUNEVAL0) &&
           VerifyField<float>(verifier, VT_RUNEVAL1) &&
           VerifyField<int32_t>(verifier, VT_ITEMID) &&
           verifier.EndTable();
  }
};

struct ItemGridRuneBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_runeid(int32_t runeid) {
    fbb_.AddElement<int32_t>(ItemGridRune::VT_RUNEID, runeid, 0);
  }
  void add_runeval0(float runeval0) {
    fbb_.AddElement<float>(ItemGridRune::VT_RUNEVAL0, runeval0, 0.0f);
  }
  void add_runeval1(float runeval1) {
    fbb_.AddElement<float>(ItemGridRune::VT_RUNEVAL1, runeval1, 0.0f);
  }
  void add_itemid(int32_t itemid) {
    fbb_.AddElement<int32_t>(ItemGridRune::VT_ITEMID, itemid, 0);
  }
  ItemGridRuneBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ItemGridRuneBuilder &operator=(const ItemGridRuneBuilder &);
  flatbuffers::Offset<ItemGridRune> Finish() {
    const auto end = fbb_.EndTable(start_, 4);
    auto o = flatbuffers::Offset<ItemGridRune>(end);
    return o;
  }
};

inline flatbuffers::Offset<ItemGridRune> CreateItemGridRune(
    flatbuffers::FlatBufferBuilder &_fbb,
    int32_t runeid = 0,
    float runeval0 = 0.0f,
    float runeval1 = 0.0f,
    int32_t itemid = 0) {
  ItemGridRuneBuilder builder_(_fbb);
  builder_.add_itemid(itemid);
  builder_.add_runeval1(runeval1);
  builder_.add_runeval0(runeval0);
  builder_.add_runeid(runeid);
  return builder_.Finish();
}

struct KVData FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_KEY = 4,
    VT_VAL = 6
  };
  const flatbuffers::String *key() const {
    return GetPointer<const flatbuffers::String *>(VT_KEY);
  }
  int32_t val() const {
    return GetField<int32_t>(VT_VAL, 0);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<flatbuffers::uoffset_t>(verifier, VT_KEY) &&
           verifier.Verify(key()) &&
           VerifyField<int32_t>(verifier, VT_VAL) &&
           verifier.EndTable();
  }
};

struct KVDataBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_key(flatbuffers::Offset<flatbuffers::String> key) {
    fbb_.AddOffset(KVData::VT_KEY, key);
  }
  void add_val(int32_t val) {
    fbb_.AddElement<int32_t>(KVData::VT_VAL, val, 0);
  }
  KVDataBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  KVDataBuilder &operator=(const KVDataBuilder &);
  flatbuffers::Offset<KVData> Finish() {
    const auto end = fbb_.EndTable(start_, 2);
    auto o = flatbuffers::Offset<KVData>(end);
    return o;
  }
};

inline flatbuffers::Offset<KVData> CreateKVData(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> key = 0,
    int32_t val = 0) {
  KVDataBuilder builder_(_fbb);
  builder_.add_val(val);
  builder_.add_key(key);
  return builder_.Finish();
}

inline flatbuffers::Offset<KVData> CreateKVDataDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *key = nullptr,
    int32_t val = 0) {
  return FBSave::CreateKVData(
      _fbb,
      key ? _fbb.CreateString(key) : 0,
      val);
}

struct ItemGrid FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_ITEMID = 4,
    VT_NUM = 6,
    VT_DURABLE = 8,
    VT_ENCHANTS = 10,
    VT_USERDATA = 12,
    VT_USERDATA_STR = 14,
    VT_SID_STR = 16,
    VT_USERDATAEX = 18,
    VT_RUNES = 20,
    VT_TOUGHNESS = 22,
    VT_DATACOMPONENTS = 24,
    VT_MAXDURABLE = 26,
    VT_KVS = 28,
    VT_DATAEX = 30
  };
  int32_t itemid() const {
    return GetField<int32_t>(VT_ITEMID, 0);
  }
  int16_t num() const {
    return GetField<int16_t>(VT_NUM, 0);
  }
  int16_t durable() const {
    return GetField<int16_t>(VT_DURABLE, 0);
  }
  const flatbuffers::Vector<int32_t> *enchants() const {
    return GetPointer<const flatbuffers::Vector<int32_t> *>(VT_ENCHANTS);
  }
  int16_t userdata() const {
    return GetField<int16_t>(VT_USERDATA, 0);
  }
  const flatbuffers::String *userdata_str() const {
    return GetPointer<const flatbuffers::String *>(VT_USERDATA_STR);
  }
  const flatbuffers::String *sid_str() const {
    return GetPointer<const flatbuffers::String *>(VT_SID_STR);
  }
  int32_t userdataEx() const {
    return GetField<int32_t>(VT_USERDATAEX, 0);
  }
  const flatbuffers::Vector<flatbuffers::Offset<ItemGridRune>> *runes() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<ItemGridRune>> *>(VT_RUNES);
  }
  int16_t toughness() const {
    return GetField<int16_t>(VT_TOUGHNESS, 0);
  }
  const flatbuffers::Vector<int8_t> *datacomponents() const {
    return GetPointer<const flatbuffers::Vector<int8_t> *>(VT_DATACOMPONENTS);
  }
  int16_t maxdurable() const {
    return GetField<int16_t>(VT_MAXDURABLE, 0);
  }
  const flatbuffers::Vector<flatbuffers::Offset<KVData>> *KVS() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<KVData>> *>(VT_KVS);
  }
  int32_t dataex() const {
    return GetField<int32_t>(VT_DATAEX, 0);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_ITEMID) &&
           VerifyField<int16_t>(verifier, VT_NUM) &&
           VerifyField<int16_t>(verifier, VT_DURABLE) &&
           VerifyField<flatbuffers::uoffset_t>(verifier, VT_ENCHANTS) &&
           verifier.Verify(enchants()) &&
           VerifyField<int16_t>(verifier, VT_USERDATA) &&
           VerifyField<flatbuffers::uoffset_t>(verifier, VT_USERDATA_STR) &&
           verifier.Verify(userdata_str()) &&
           VerifyField<flatbuffers::uoffset_t>(verifier, VT_SID_STR) &&
           verifier.Verify(sid_str()) &&
           VerifyField<int32_t>(verifier, VT_USERDATAEX) &&
           VerifyField<flatbuffers::uoffset_t>(verifier, VT_RUNES) &&
           verifier.Verify(runes()) &&
           verifier.VerifyVectorOfTables(runes()) &&
           VerifyField<int16_t>(verifier, VT_TOUGHNESS) &&
           VerifyField<flatbuffers::uoffset_t>(verifier, VT_DATACOMPONENTS) &&
           verifier.Verify(datacomponents()) &&
           VerifyField<int16_t>(verifier, VT_MAXDURABLE) &&
           VerifyField<flatbuffers::uoffset_t>(verifier, VT_KVS) &&
           verifier.Verify(KVS()) &&
           verifier.VerifyVectorOfTables(KVS()) &&
           VerifyField<int32_t>(verifier, VT_DATAEX) &&
           verifier.EndTable();
  }
};

struct ItemGridBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_itemid(int32_t itemid) {
    fbb_.AddElement<int32_t>(ItemGrid::VT_ITEMID, itemid, 0);
  }
  void add_num(int16_t num) {
    fbb_.AddElement<int16_t>(ItemGrid::VT_NUM, num, 0);
  }
  void add_durable(int16_t durable) {
    fbb_.AddElement<int16_t>(ItemGrid::VT_DURABLE, durable, 0);
  }
  void add_enchants(flatbuffers::Offset<flatbuffers::Vector<int32_t>> enchants) {
    fbb_.AddOffset(ItemGrid::VT_ENCHANTS, enchants);
  }
  void add_userdata(int16_t userdata) {
    fbb_.AddElement<int16_t>(ItemGrid::VT_USERDATA, userdata, 0);
  }
  void add_userdata_str(flatbuffers::Offset<flatbuffers::String> userdata_str) {
    fbb_.AddOffset(ItemGrid::VT_USERDATA_STR, userdata_str);
  }
  void add_sid_str(flatbuffers::Offset<flatbuffers::String> sid_str) {
    fbb_.AddOffset(ItemGrid::VT_SID_STR, sid_str);
  }
  void add_userdataEx(int32_t userdataEx) {
    fbb_.AddElement<int32_t>(ItemGrid::VT_USERDATAEX, userdataEx, 0);
  }
  void add_runes(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<ItemGridRune>>> runes) {
    fbb_.AddOffset(ItemGrid::VT_RUNES, runes);
  }
  void add_toughness(int16_t toughness) {
    fbb_.AddElement<int16_t>(ItemGrid::VT_TOUGHNESS, toughness, 0);
  }
  void add_datacomponents(flatbuffers::Offset<flatbuffers::Vector<int8_t>> datacomponents) {
    fbb_.AddOffset(ItemGrid::VT_DATACOMPONENTS, datacomponents);
  }
  void add_maxdurable(int16_t maxdurable) {
    fbb_.AddElement<int16_t>(ItemGrid::VT_MAXDURABLE, maxdurable, 0);
  }
  void add_KVS(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<KVData>>> KVS) {
    fbb_.AddOffset(ItemGrid::VT_KVS, KVS);
  }
  void add_dataex(int32_t dataex) {
    fbb_.AddElement<int32_t>(ItemGrid::VT_DATAEX, dataex, 0);
  }
  ItemGridBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ItemGridBuilder &operator=(const ItemGridBuilder &);
  flatbuffers::Offset<ItemGrid> Finish() {
    const auto end = fbb_.EndTable(start_, 14);
    auto o = flatbuffers::Offset<ItemGrid>(end);
    return o;
  }
};

inline flatbuffers::Offset<ItemGrid> CreateItemGrid(
    flatbuffers::FlatBufferBuilder &_fbb,
    int32_t itemid = 0,
    int16_t num = 0,
    int16_t durable = 0,
    flatbuffers::Offset<flatbuffers::Vector<int32_t>> enchants = 0,
    int16_t userdata = 0,
    flatbuffers::Offset<flatbuffers::String> userdata_str = 0,
    flatbuffers::Offset<flatbuffers::String> sid_str = 0,
    int32_t userdataEx = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<ItemGridRune>>> runes = 0,
    int16_t toughness = 0,
    flatbuffers::Offset<flatbuffers::Vector<int8_t>> datacomponents = 0,
    int16_t maxdurable = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<KVData>>> KVS = 0,
    int32_t dataex = 0) {
  ItemGridBuilder builder_(_fbb);
  builder_.add_dataex(dataex);
  builder_.add_KVS(KVS);
  builder_.add_datacomponents(datacomponents);
  builder_.add_runes(runes);
  builder_.add_userdataEx(userdataEx);
  builder_.add_sid_str(sid_str);
  builder_.add_userdata_str(userdata_str);
  builder_.add_enchants(enchants);
  builder_.add_itemid(itemid);
  builder_.add_maxdurable(maxdurable);
  builder_.add_toughness(toughness);
  builder_.add_userdata(userdata);
  builder_.add_durable(durable);
  builder_.add_num(num);
  return builder_.Finish();
}

inline flatbuffers::Offset<ItemGrid> CreateItemGridDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    int32_t itemid = 0,
    int16_t num = 0,
    int16_t durable = 0,
    const std::vector<int32_t> *enchants = nullptr,
    int16_t userdata = 0,
    const char *userdata_str = nullptr,
    const char *sid_str = nullptr,
    int32_t userdataEx = 0,
    const std::vector<flatbuffers::Offset<ItemGridRune>> *runes = nullptr,
    int16_t toughness = 0,
    const std::vector<int8_t> *datacomponents = nullptr,
    int16_t maxdurable = 0,
    const std::vector<flatbuffers::Offset<KVData>> *KVS = nullptr,
    int32_t dataex = 0) {
  return FBSave::CreateItemGrid(
      _fbb,
      itemid,
      num,
      durable,
      enchants ? _fbb.CreateVector<int32_t>(*enchants) : 0,
      userdata,
      userdata_str ? _fbb.CreateString(userdata_str) : 0,
      sid_str ? _fbb.CreateString(sid_str) : 0,
      userdataEx,
      runes ? _fbb.CreateVector<flatbuffers::Offset<ItemGridRune>>(*runes) : 0,
      toughness,
      datacomponents ? _fbb.CreateVector<int8_t>(*datacomponents) : 0,
      maxdurable,
      KVS ? _fbb.CreateVector<flatbuffers::Offset<KVData>>(*KVS) : 0,
      dataex);
}

struct ItemIndexGrid FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_INDEX = 4,
    VT_ITEMID = 6,
    VT_NUM = 8,
    VT_DURABLE = 10,
    VT_ENCHANTS = 12,
    VT_USERDATA = 14,
    VT_USERDATA_STR = 16,
    VT_SID_STR = 18,
    VT_USERDATAEX = 20,
    VT_RUNES = 22,
    VT_TOUGHNESS = 24,
    VT_DATACOMPONENTS = 26,
    VT_MAXDURABLE = 28,
    VT_KVS = 30,
    VT_DATAEX = 32
  };
  int32_t index() const {
    return GetField<int32_t>(VT_INDEX, 0);
  }
  int32_t itemid() const {
    return GetField<int32_t>(VT_ITEMID, 0);
  }
  int16_t num() const {
    return GetField<int16_t>(VT_NUM, 0);
  }
  int16_t durable() const {
    return GetField<int16_t>(VT_DURABLE, 0);
  }
  const flatbuffers::Vector<int32_t> *enchants() const {
    return GetPointer<const flatbuffers::Vector<int32_t> *>(VT_ENCHANTS);
  }
  int16_t userdata() const {
    return GetField<int16_t>(VT_USERDATA, 0);
  }
  const flatbuffers::String *userdata_str() const {
    return GetPointer<const flatbuffers::String *>(VT_USERDATA_STR);
  }
  const flatbuffers::String *sid_str() const {
    return GetPointer<const flatbuffers::String *>(VT_SID_STR);
  }
  int32_t userdataEx() const {
    return GetField<int32_t>(VT_USERDATAEX, 0);
  }
  const flatbuffers::Vector<flatbuffers::Offset<ItemGridRune>> *runes() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<ItemGridRune>> *>(VT_RUNES);
  }
  int16_t toughness() const {
    return GetField<int16_t>(VT_TOUGHNESS, 0);
  }
  const flatbuffers::Vector<int8_t> *datacomponents() const {
    return GetPointer<const flatbuffers::Vector<int8_t> *>(VT_DATACOMPONENTS);
  }
  int16_t maxdurable() const {
    return GetField<int16_t>(VT_MAXDURABLE, 0);
  }
  const flatbuffers::Vector<flatbuffers::Offset<KVData>> *KVS() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<KVData>> *>(VT_KVS);
  }
  int32_t dataex() const {
    return GetField<int32_t>(VT_DATAEX, 0);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_INDEX) &&
           VerifyField<int32_t>(verifier, VT_ITEMID) &&
           VerifyField<int16_t>(verifier, VT_NUM) &&
           VerifyField<int16_t>(verifier, VT_DURABLE) &&
           VerifyField<flatbuffers::uoffset_t>(verifier, VT_ENCHANTS) &&
           verifier.Verify(enchants()) &&
           VerifyField<int16_t>(verifier, VT_USERDATA) &&
           VerifyField<flatbuffers::uoffset_t>(verifier, VT_USERDATA_STR) &&
           verifier.Verify(userdata_str()) &&
           VerifyField<flatbuffers::uoffset_t>(verifier, VT_SID_STR) &&
           verifier.Verify(sid_str()) &&
           VerifyField<int32_t>(verifier, VT_USERDATAEX) &&
           VerifyField<flatbuffers::uoffset_t>(verifier, VT_RUNES) &&
           verifier.Verify(runes()) &&
           verifier.VerifyVectorOfTables(runes()) &&
           VerifyField<int16_t>(verifier, VT_TOUGHNESS) &&
           VerifyField<flatbuffers::uoffset_t>(verifier, VT_DATACOMPONENTS) &&
           verifier.Verify(datacomponents()) &&
           VerifyField<int16_t>(verifier, VT_MAXDURABLE) &&
           VerifyField<flatbuffers::uoffset_t>(verifier, VT_KVS) &&
           verifier.Verify(KVS()) &&
           verifier.VerifyVectorOfTables(KVS()) &&
           VerifyField<int32_t>(verifier, VT_DATAEX) &&
           verifier.EndTable();
  }
};

struct ItemIndexGridBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_index(int32_t index) {
    fbb_.AddElement<int32_t>(ItemIndexGrid::VT_INDEX, index, 0);
  }
  void add_itemid(int32_t itemid) {
    fbb_.AddElement<int32_t>(ItemIndexGrid::VT_ITEMID, itemid, 0);
  }
  void add_num(int16_t num) {
    fbb_.AddElement<int16_t>(ItemIndexGrid::VT_NUM, num, 0);
  }
  void add_durable(int16_t durable) {
    fbb_.AddElement<int16_t>(ItemIndexGrid::VT_DURABLE, durable, 0);
  }
  void add_enchants(flatbuffers::Offset<flatbuffers::Vector<int32_t>> enchants) {
    fbb_.AddOffset(ItemIndexGrid::VT_ENCHANTS, enchants);
  }
  void add_userdata(int16_t userdata) {
    fbb_.AddElement<int16_t>(ItemIndexGrid::VT_USERDATA, userdata, 0);
  }
  void add_userdata_str(flatbuffers::Offset<flatbuffers::String> userdata_str) {
    fbb_.AddOffset(ItemIndexGrid::VT_USERDATA_STR, userdata_str);
  }
  void add_sid_str(flatbuffers::Offset<flatbuffers::String> sid_str) {
    fbb_.AddOffset(ItemIndexGrid::VT_SID_STR, sid_str);
  }
  void add_userdataEx(int32_t userdataEx) {
    fbb_.AddElement<int32_t>(ItemIndexGrid::VT_USERDATAEX, userdataEx, 0);
  }
  void add_runes(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<ItemGridRune>>> runes) {
    fbb_.AddOffset(ItemIndexGrid::VT_RUNES, runes);
  }
  void add_toughness(int16_t toughness) {
    fbb_.AddElement<int16_t>(ItemIndexGrid::VT_TOUGHNESS, toughness, 0);
  }
  void add_datacomponents(flatbuffers::Offset<flatbuffers::Vector<int8_t>> datacomponents) {
    fbb_.AddOffset(ItemIndexGrid::VT_DATACOMPONENTS, datacomponents);
  }
  void add_maxdurable(int16_t maxdurable) {
    fbb_.AddElement<int16_t>(ItemIndexGrid::VT_MAXDURABLE, maxdurable, 0);
  }
  void add_KVS(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<KVData>>> KVS) {
    fbb_.AddOffset(ItemIndexGrid::VT_KVS, KVS);
  }
  void add_dataex(int32_t dataex) {
    fbb_.AddElement<int32_t>(ItemIndexGrid::VT_DATAEX, dataex, 0);
  }
  ItemIndexGridBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ItemIndexGridBuilder &operator=(const ItemIndexGridBuilder &);
  flatbuffers::Offset<ItemIndexGrid> Finish() {
    const auto end = fbb_.EndTable(start_, 15);
    auto o = flatbuffers::Offset<ItemIndexGrid>(end);
    return o;
  }
};

inline flatbuffers::Offset<ItemIndexGrid> CreateItemIndexGrid(
    flatbuffers::FlatBufferBuilder &_fbb,
    int32_t index = 0,
    int32_t itemid = 0,
    int16_t num = 0,
    int16_t durable = 0,
    flatbuffers::Offset<flatbuffers::Vector<int32_t>> enchants = 0,
    int16_t userdata = 0,
    flatbuffers::Offset<flatbuffers::String> userdata_str = 0,
    flatbuffers::Offset<flatbuffers::String> sid_str = 0,
    int32_t userdataEx = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<ItemGridRune>>> runes = 0,
    int16_t toughness = 0,
    flatbuffers::Offset<flatbuffers::Vector<int8_t>> datacomponents = 0,
    int16_t maxdurable = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<KVData>>> KVS = 0,
    int32_t dataex = 0) {
  ItemIndexGridBuilder builder_(_fbb);
  builder_.add_dataex(dataex);
  builder_.add_KVS(KVS);
  builder_.add_datacomponents(datacomponents);
  builder_.add_runes(runes);
  builder_.add_userdataEx(userdataEx);
  builder_.add_sid_str(sid_str);
  builder_.add_userdata_str(userdata_str);
  builder_.add_enchants(enchants);
  builder_.add_itemid(itemid);
  builder_.add_index(index);
  builder_.add_maxdurable(maxdurable);
  builder_.add_toughness(toughness);
  builder_.add_userdata(userdata);
  builder_.add_durable(durable);
  builder_.add_num(num);
  return builder_.Finish();
}

inline flatbuffers::Offset<ItemIndexGrid> CreateItemIndexGridDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    int32_t index = 0,
    int32_t itemid = 0,
    int16_t num = 0,
    int16_t durable = 0,
    const std::vector<int32_t> *enchants = nullptr,
    int16_t userdata = 0,
    const char *userdata_str = nullptr,
    const char *sid_str = nullptr,
    int32_t userdataEx = 0,
    const std::vector<flatbuffers::Offset<ItemGridRune>> *runes = nullptr,
    int16_t toughness = 0,
    const std::vector<int8_t> *datacomponents = nullptr,
    int16_t maxdurable = 0,
    const std::vector<flatbuffers::Offset<KVData>> *KVS = nullptr,
    int32_t dataex = 0) {
  return FBSave::CreateItemIndexGrid(
      _fbb,
      index,
      itemid,
      num,
      durable,
      enchants ? _fbb.CreateVector<int32_t>(*enchants) : 0,
      userdata,
      userdata_str ? _fbb.CreateString(userdata_str) : 0,
      sid_str ? _fbb.CreateString(sid_str) : 0,
      userdataEx,
      runes ? _fbb.CreateVector<flatbuffers::Offset<ItemGridRune>>(*runes) : 0,
      toughness,
      datacomponents ? _fbb.CreateVector<int8_t>(*datacomponents) : 0,
      maxdurable,
      KVS ? _fbb.CreateVector<flatbuffers::Offset<KVData>>(*KVS) : 0,
      dataex);
}

struct Achievement FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_ID = 4,
    VT_VAL = 6,
    VT_STATE = 8,
    VT_GETAWARD = 10,
    VT_COMPLETEYEAR = 12,
    VT_COMPLETEMONTH = 14,
    VT_COMPLETEDAY = 16
  };
  int32_t id() const {
    return GetField<int32_t>(VT_ID, 0);
  }
  int32_t val() const {
    return GetField<int32_t>(VT_VAL, 0);
  }
  int8_t state() const {
    return GetField<int8_t>(VT_STATE, 0);
  }
  int8_t getaward() const {
    return GetField<int8_t>(VT_GETAWARD, 0);
  }
  uint16_t completeyear() const {
    return GetField<uint16_t>(VT_COMPLETEYEAR, 0);
  }
  int8_t completemonth() const {
    return GetField<int8_t>(VT_COMPLETEMONTH, 0);
  }
  int8_t completeday() const {
    return GetField<int8_t>(VT_COMPLETEDAY, 0);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_ID) &&
           VerifyField<int32_t>(verifier, VT_VAL) &&
           VerifyField<int8_t>(verifier, VT_STATE) &&
           VerifyField<int8_t>(verifier, VT_GETAWARD) &&
           VerifyField<uint16_t>(verifier, VT_COMPLETEYEAR) &&
           VerifyField<int8_t>(verifier, VT_COMPLETEMONTH) &&
           VerifyField<int8_t>(verifier, VT_COMPLETEDAY) &&
           verifier.EndTable();
  }
};

struct AchievementBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_id(int32_t id) {
    fbb_.AddElement<int32_t>(Achievement::VT_ID, id, 0);
  }
  void add_val(int32_t val) {
    fbb_.AddElement<int32_t>(Achievement::VT_VAL, val, 0);
  }
  void add_state(int8_t state) {
    fbb_.AddElement<int8_t>(Achievement::VT_STATE, state, 0);
  }
  void add_getaward(int8_t getaward) {
    fbb_.AddElement<int8_t>(Achievement::VT_GETAWARD, getaward, 0);
  }
  void add_completeyear(uint16_t completeyear) {
    fbb_.AddElement<uint16_t>(Achievement::VT_COMPLETEYEAR, completeyear, 0);
  }
  void add_completemonth(int8_t completemonth) {
    fbb_.AddElement<int8_t>(Achievement::VT_COMPLETEMONTH, completemonth, 0);
  }
  void add_completeday(int8_t completeday) {
    fbb_.AddElement<int8_t>(Achievement::VT_COMPLETEDAY, completeday, 0);
  }
  AchievementBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  AchievementBuilder &operator=(const AchievementBuilder &);
  flatbuffers::Offset<Achievement> Finish() {
    const auto end = fbb_.EndTable(start_, 7);
    auto o = flatbuffers::Offset<Achievement>(end);
    return o;
  }
};

inline flatbuffers::Offset<Achievement> CreateAchievement(
    flatbuffers::FlatBufferBuilder &_fbb,
    int32_t id = 0,
    int32_t val = 0,
    int8_t state = 0,
    int8_t getaward = 0,
    uint16_t completeyear = 0,
    int8_t completemonth = 0,
    int8_t completeday = 0) {
  AchievementBuilder builder_(_fbb);
  builder_.add_val(val);
  builder_.add_id(id);
  builder_.add_completeyear(completeyear);
  builder_.add_completeday(completeday);
  builder_.add_completemonth(completemonth);
  builder_.add_getaward(getaward);
  builder_.add_state(state);
  return builder_.Finish();
}

struct AvatarModelData FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_MODELFILENAME = 4,
    VT_SCALE = 6,
    VT_YAW = 8,
    VT_PITCH = 10,
    VT_OFFSETPOS = 12,
    VT_ROLL = 14,
    VT_NEWROTATEMODE = 16,
    VT_SCALE3 = 18
  };
  const flatbuffers::String *modelfilename() const {
    return GetPointer<const flatbuffers::String *>(VT_MODELFILENAME);
  }
  float scale() const {
    return GetField<float>(VT_SCALE, 0.0f);
  }
  float yaw() const {
    return GetField<float>(VT_YAW, 0.0f);
  }
  float pitch() const {
    return GetField<float>(VT_PITCH, 0.0f);
  }
  const Coord3 *offsetpos() const {
    return GetStruct<const Coord3 *>(VT_OFFSETPOS);
  }
  float roll() const {
    return GetField<float>(VT_ROLL, 0.0f);
  }
  bool newrotatemode() const {
    return GetField<uint8_t>(VT_NEWROTATEMODE, 0) != 0;
  }
  const Vec3 *scale3() const {
    return GetStruct<const Vec3 *>(VT_SCALE3);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<flatbuffers::uoffset_t>(verifier, VT_MODELFILENAME) &&
           verifier.Verify(modelfilename()) &&
           VerifyField<float>(verifier, VT_SCALE) &&
           VerifyField<float>(verifier, VT_YAW) &&
           VerifyField<float>(verifier, VT_PITCH) &&
           VerifyField<Coord3>(verifier, VT_OFFSETPOS) &&
           VerifyField<float>(verifier, VT_ROLL) &&
           VerifyField<uint8_t>(verifier, VT_NEWROTATEMODE) &&
           VerifyField<Vec3>(verifier, VT_SCALE3) &&
           verifier.EndTable();
  }
};

struct AvatarModelDataBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_modelfilename(flatbuffers::Offset<flatbuffers::String> modelfilename) {
    fbb_.AddOffset(AvatarModelData::VT_MODELFILENAME, modelfilename);
  }
  void add_scale(float scale) {
    fbb_.AddElement<float>(AvatarModelData::VT_SCALE, scale, 0.0f);
  }
  void add_yaw(float yaw) {
    fbb_.AddElement<float>(AvatarModelData::VT_YAW, yaw, 0.0f);
  }
  void add_pitch(float pitch) {
    fbb_.AddElement<float>(AvatarModelData::VT_PITCH, pitch, 0.0f);
  }
  void add_offsetpos(const Coord3 *offsetpos) {
    fbb_.AddStruct(AvatarModelData::VT_OFFSETPOS, offsetpos);
  }
  void add_roll(float roll) {
    fbb_.AddElement<float>(AvatarModelData::VT_ROLL, roll, 0.0f);
  }
  void add_newrotatemode(bool newrotatemode) {
    fbb_.AddElement<uint8_t>(AvatarModelData::VT_NEWROTATEMODE, static_cast<uint8_t>(newrotatemode), 0);
  }
  void add_scale3(const Vec3 *scale3) {
    fbb_.AddStruct(AvatarModelData::VT_SCALE3, scale3);
  }
  AvatarModelDataBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  AvatarModelDataBuilder &operator=(const AvatarModelDataBuilder &);
  flatbuffers::Offset<AvatarModelData> Finish() {
    const auto end = fbb_.EndTable(start_, 8);
    auto o = flatbuffers::Offset<AvatarModelData>(end);
    return o;
  }
};

inline flatbuffers::Offset<AvatarModelData> CreateAvatarModelData(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> modelfilename = 0,
    float scale = 0.0f,
    float yaw = 0.0f,
    float pitch = 0.0f,
    const Coord3 *offsetpos = 0,
    float roll = 0.0f,
    bool newrotatemode = false,
    const Vec3 *scale3 = 0) {
  AvatarModelDataBuilder builder_(_fbb);
  builder_.add_scale3(scale3);
  builder_.add_roll(roll);
  builder_.add_offsetpos(offsetpos);
  builder_.add_pitch(pitch);
  builder_.add_yaw(yaw);
  builder_.add_scale(scale);
  builder_.add_modelfilename(modelfilename);
  builder_.add_newrotatemode(newrotatemode);
  return builder_.Finish();
}

inline flatbuffers::Offset<AvatarModelData> CreateAvatarModelDataDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *modelfilename = nullptr,
    float scale = 0.0f,
    float yaw = 0.0f,
    float pitch = 0.0f,
    const Coord3 *offsetpos = 0,
    float roll = 0.0f,
    bool newrotatemode = false,
    const Vec3 *scale3 = 0) {
  return FBSave::CreateAvatarModelData(
      _fbb,
      modelfilename ? _fbb.CreateString(modelfilename) : 0,
      scale,
      yaw,
      pitch,
      offsetpos,
      roll,
      newrotatemode,
      scale3);
}

struct BoneModelData FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_BONENAME = 4,
    VT_AVATARMODELS = 6
  };
  const flatbuffers::String *bonename() const {
    return GetPointer<const flatbuffers::String *>(VT_BONENAME);
  }
  const flatbuffers::Vector<flatbuffers::Offset<AvatarModelData>> *avatarmodels() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<AvatarModelData>> *>(VT_AVATARMODELS);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<flatbuffers::uoffset_t>(verifier, VT_BONENAME) &&
           verifier.Verify(bonename()) &&
           VerifyField<flatbuffers::uoffset_t>(verifier, VT_AVATARMODELS) &&
           verifier.Verify(avatarmodels()) &&
           verifier.VerifyVectorOfTables(avatarmodels()) &&
           verifier.EndTable();
  }
};

struct BoneModelDataBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_bonename(flatbuffers::Offset<flatbuffers::String> bonename) {
    fbb_.AddOffset(BoneModelData::VT_BONENAME, bonename);
  }
  void add_avatarmodels(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<AvatarModelData>>> avatarmodels) {
    fbb_.AddOffset(BoneModelData::VT_AVATARMODELS, avatarmodels);
  }
  BoneModelDataBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  BoneModelDataBuilder &operator=(const BoneModelDataBuilder &);
  flatbuffers::Offset<BoneModelData> Finish() {
    const auto end = fbb_.EndTable(start_, 2);
    auto o = flatbuffers::Offset<BoneModelData>(end);
    return o;
  }
};

inline flatbuffers::Offset<BoneModelData> CreateBoneModelData(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> bonename = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<AvatarModelData>>> avatarmodels = 0) {
  BoneModelDataBuilder builder_(_fbb);
  builder_.add_avatarmodels(avatarmodels);
  builder_.add_bonename(bonename);
  return builder_.Finish();
}

inline flatbuffers::Offset<BoneModelData> CreateBoneModelDataDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *bonename = nullptr,
    const std::vector<flatbuffers::Offset<AvatarModelData>> *avatarmodels = nullptr) {
  return FBSave::CreateBoneModelData(
      _fbb,
      bonename ? _fbb.CreateString(bonename) : 0,
      avatarmodels ? _fbb.CreateVector<flatbuffers::Offset<AvatarModelData>>(*avatarmodels) : 0);
}

struct VehicleBlockLine FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_FROM = 4,
    VT_TO = 6,
    VT_CANEDIT = 8
  };
  uint16_t from() const {
    return GetField<uint16_t>(VT_FROM, 0);
  }
  uint16_t to() const {
    return GetField<uint16_t>(VT_TO, 0);
  }
  int8_t canedit() const {
    return GetField<int8_t>(VT_CANEDIT, 0);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_FROM) &&
           VerifyField<uint16_t>(verifier, VT_TO) &&
           VerifyField<int8_t>(verifier, VT_CANEDIT) &&
           verifier.EndTable();
  }
};

struct VehicleBlockLineBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_from(uint16_t from) {
    fbb_.AddElement<uint16_t>(VehicleBlockLine::VT_FROM, from, 0);
  }
  void add_to(uint16_t to) {
    fbb_.AddElement<uint16_t>(VehicleBlockLine::VT_TO, to, 0);
  }
  void add_canedit(int8_t canedit) {
    fbb_.AddElement<int8_t>(VehicleBlockLine::VT_CANEDIT, canedit, 0);
  }
  VehicleBlockLineBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  VehicleBlockLineBuilder &operator=(const VehicleBlockLineBuilder &);
  flatbuffers::Offset<VehicleBlockLine> Finish() {
    const auto end = fbb_.EndTable(start_, 3);
    auto o = flatbuffers::Offset<VehicleBlockLine>(end);
    return o;
  }
};

inline flatbuffers::Offset<VehicleBlockLine> CreateVehicleBlockLine(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t from = 0,
    uint16_t to = 0,
    int8_t canedit = 0) {
  VehicleBlockLineBuilder builder_(_fbb);
  builder_.add_to(to);
  builder_.add_from(from);
  builder_.add_canedit(canedit);
  return builder_.Finish();
}

struct UgcVertexData FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_VERTS = 4,
    VT_INDEXES = 6
  };
  const flatbuffers::Vector<const Vec3 *> *verts() const {
    return GetPointer<const flatbuffers::Vector<const Vec3 *> *>(VT_VERTS);
  }
  const flatbuffers::Vector<uint16_t> *indexes() const {
    return GetPointer<const flatbuffers::Vector<uint16_t> *>(VT_INDEXES);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<flatbuffers::uoffset_t>(verifier, VT_VERTS) &&
           verifier.Verify(verts()) &&
           VerifyField<flatbuffers::uoffset_t>(verifier, VT_INDEXES) &&
           verifier.Verify(indexes()) &&
           verifier.EndTable();
  }
};

struct UgcVertexDataBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_verts(flatbuffers::Offset<flatbuffers::Vector<const Vec3 *>> verts) {
    fbb_.AddOffset(UgcVertexData::VT_VERTS, verts);
  }
  void add_indexes(flatbuffers::Offset<flatbuffers::Vector<uint16_t>> indexes) {
    fbb_.AddOffset(UgcVertexData::VT_INDEXES, indexes);
  }
  UgcVertexDataBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  UgcVertexDataBuilder &operator=(const UgcVertexDataBuilder &);
  flatbuffers::Offset<UgcVertexData> Finish() {
    const auto end = fbb_.EndTable(start_, 2);
    auto o = flatbuffers::Offset<UgcVertexData>(end);
    return o;
  }
};

inline flatbuffers::Offset<UgcVertexData> CreateUgcVertexData(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::Vector<const Vec3 *>> verts = 0,
    flatbuffers::Offset<flatbuffers::Vector<uint16_t>> indexes = 0) {
  UgcVertexDataBuilder builder_(_fbb);
  builder_.add_indexes(indexes);
  builder_.add_verts(verts);
  return builder_.Finish();
}

inline flatbuffers::Offset<UgcVertexData> CreateUgcVertexDataDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<const Vec3 *> *verts = nullptr,
    const std::vector<uint16_t> *indexes = nullptr) {
  return FBSave::CreateUgcVertexData(
      _fbb,
      verts ? _fbb.CreateVector<const Vec3 *>(*verts) : 0,
      indexes ? _fbb.CreateVector<uint16_t>(*indexes) : 0);
}

struct KeyValueData FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_KEY = 4,
    VT_VALUE = 6
  };
  const flatbuffers::String *key() const {
    return GetPointer<const flatbuffers::String *>(VT_KEY);
  }
  int16_t value() const {
    return GetField<int16_t>(VT_VALUE, 0);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<flatbuffers::uoffset_t>(verifier, VT_KEY) &&
           verifier.Verify(key()) &&
           VerifyField<int16_t>(verifier, VT_VALUE) &&
           verifier.EndTable();
  }
};

struct KeyValueDataBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_key(flatbuffers::Offset<flatbuffers::String> key) {
    fbb_.AddOffset(KeyValueData::VT_KEY, key);
  }
  void add_value(int16_t value) {
    fbb_.AddElement<int16_t>(KeyValueData::VT_VALUE, value, 0);
  }
  KeyValueDataBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  KeyValueDataBuilder &operator=(const KeyValueDataBuilder &);
  flatbuffers::Offset<KeyValueData> Finish() {
    const auto end = fbb_.EndTable(start_, 2);
    auto o = flatbuffers::Offset<KeyValueData>(end);
    return o;
  }
};

inline flatbuffers::Offset<KeyValueData> CreateKeyValueData(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> key = 0,
    int16_t value = 0) {
  KeyValueDataBuilder builder_(_fbb);
  builder_.add_key(key);
  builder_.add_value(value);
  return builder_.Finish();
}

inline flatbuffers::Offset<KeyValueData> CreateKeyValueDataDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *key = nullptr,
    int16_t value = 0) {
  return FBSave::CreateKeyValueData(
      _fbb,
      key ? _fbb.CreateString(key) : 0,
      value);
}

inline const FBSave::UgcVertexData *GetUgcVertexData(const void *buf) {
  return flatbuffers::GetRoot<FBSave::UgcVertexData>(buf);
}

inline bool VerifyUgcVertexDataBuffer(
    flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<FBSave::UgcVertexData>(nullptr);
}

inline void FinishUgcVertexDataBuffer(
    flatbuffers::FlatBufferBuilder &fbb,
    flatbuffers::Offset<FBSave::UgcVertexData> root) {
  fbb.Finish(root);
}

}  // namespace FBSave

#endif  // FLATBUFFERS_GENERATED_FLATSAVECOMMON_FBSAVE_H_
