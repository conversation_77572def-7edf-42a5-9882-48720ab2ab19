#pragma once
#include "AIBase.h"
class LivingLocoMotion;
class AINpcPlayer;

class AIPlayerFear :public AIBase //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	AIPlayerFear(AINpcPlayer* pActor, float speed, int range, int panicTick, int intervalTick);
	~AIPlayerFear();
	virtual bool willRun();
	void start();
	bool continueRun();
	void reset();
	virtual void update();

	virtual bool canInterruptedByInteract() { return false; }
	//tolua_end
private:
private:
	float m_speed;
	int m_range;
	int m_panicTick;
	int m_panicTickTotal;
	int m_intervalTick;
	int m_intervalTickTotal;

	WCoord m_validPos;
}; //tolua_exports