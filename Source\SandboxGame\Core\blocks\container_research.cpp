﻿#include "container_research.h"

#include "SandboxTimer.h"
#include "world.h"

#define RESEARCHTIME 10
#define ENDTIME 200

using namespace MNSandbox;

ContainerResearch::ContainerResearch():
    ErosionStorageBox(),
	researchtickcount(0)
{   
}

ContainerResearch::ContainerResearch(const WCoord& blockpos, const int blockId):
    ErosionStorageBox(blockpos, blockId),
	researchtickcount(0)
{

}

ContainerResearch::~ContainerResearch()
{

}

flatbuffers::Offset<FBSave::ChunkContainer> ContainerResearch::save(SAVE_BUFFER_BUILDER& builder)
{
	auto ErosionStorage = saveContainerErosionStorage(builder);
	auto actor = FBSave::CreateContainerResearch(builder, ErosionStorage);
	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerResearch, actor.Union());;
}

bool ContainerResearch::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerResearch*>(srcdata);
	return ErosionStorageBox::load((void *)src->basedata());
}

void ContainerResearch::enterWorld(World* pworld)
{
    ErosionStorageBox::enterWorld(pworld);

}

void ContainerResearch::leaveWorld()
{
    ErosionStorageBox::leaveWorld();
}

void ContainerResearch::updateTick()
{
	ErosionStorageBox::updateTick();

	int result_index = STORAGE_START_INDEX + 2;
	BackPackGrid* result_grid = index2Grid(result_index);
	if (result_grid->isEmpty()) return;

	if (researchtickcount++ < ENDTIME) {
		//1s写一次时间
		if (researchtickcount % 20 == 0) {
			std::string userdata = result_grid->getUserdataStr();
			jsonxx::Object old_json_data;
			old_json_data.parse(userdata);

			uint64_t start_time = 0; 
			uint64_t old_current_time = 0;
			//容错,大不了再走10s
			if (old_json_data.has<jsonxx::Number>("start_time") && old_json_data.has<jsonxx::Number>("current_time"))
			{
				start_time = (uint64_t)old_json_data.get<jsonxx::Number>("start_time");
				old_current_time = (uint64_t)old_json_data.get<jsonxx::Number>("current_time");
			}
			
			uint64_t current_time = old_current_time + 1;

			jsonxx::Object json_data;
			json_data << "start_time" << start_time;
			json_data << "current_time" << current_time;

			result_grid->setUserdataStr(json_data.json().c_str());
			afterChangeGrid(result_index);
		}
		return;
	}
	Process();
	researchtickcount = 0;
}

void ContainerResearch::dropItems()
{
    for (int i = 0; i < 2; i++)
    {
        dropOneItem(m_Grids[i]);
    }
}

void ContainerResearch::dropItems(WCoord BlockPos)
{
    dropItems();
}

void ContainerResearch::Process()
{
	int research_index = STORAGE_START_INDEX;
	int demand_index = STORAGE_START_INDEX + 1;
	int result_index = STORAGE_START_INDEX + 2;

	BackPackGrid* research_grid = index2Grid(research_index);
	int research_itemid = research_grid->getItemID();
	WorkbenchTechCsvDef* TechCsvDef = GetDefManagerProxy()->findItembenchTech(research_itemid);
	if (!TechCsvDef)
	{
		LOG_WARNING("not TechCsvDef");
		return;
	}

	//消耗
	removeItemByIndex(demand_index, TechCsvDef->_Cost.CostValue); //消耗晶石
	removeItemByIndex(research_index, 1);   //消耗研究物

	//生成 放到第二格子
	if (research_grid)
	{
		std::string itemid_str = std::to_string(TechCsvDef->ItemId);
		SetBackPackGrid(*research_grid, 2020119, 1, -1, -1, 0, 1, 0, itemid_str.c_str());
		afterChangeGrid(research_index);
	}

	BackPackGrid* result_grid = index2Grid(result_index);
	result_grid->clear();
	afterChangeGrid(result_index);
}

void ContainerResearch::StartResearch()
{
	int research_index = STORAGE_START_INDEX;
	int demand_index = STORAGE_START_INDEX + 1;
	int result_index = STORAGE_START_INDEX + 2;

	BackPackGrid* research_grid = index2Grid(research_index);
	BackPackGrid* demand_grid = index2Grid(demand_index);
	BackPackGrid* result_grid = index2Grid(result_index);
	//不是空说明在研究状态
	if (!result_grid->isEmpty()) 
	{
		LOG_WARNING("Research ....");
		return;
	}

	int research_itemid = research_grid->getItemID();
	WorkbenchTechCsvDef* TechCsvDef = GetDefManagerProxy()->findItembenchTech(research_itemid);
	if (!TechCsvDef)
	{
		LOG_WARNING("not TechCsvDef");
		return;
	}

	int demand_itemid = demand_grid->getItemID();
	if (demand_itemid != TechCsvDef->_Cost.CostItem)
	{
		LOG_WARNING("Cost.CostItem");
		return;
	}

	if (demand_grid->getNum() < TechCsvDef->_Cost.CostValue)
	{
		LOG_WARNING("demand_grid not num");
		return;
	}

	int sum = research_grid->getNum();
	//第三格子放一些信息
	if (result_grid)
	{
		std::chrono::milliseconds tem = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch());
		uint64_t current_time = tem.count() / 1000;

		jsonxx::Object json_data;
		json_data << "start_time" << current_time;
		json_data << "current_time" << current_time;

		SetBackPackGrid(*result_grid, 2020119, 1, -1, -1, 0, 1, 0, json_data.json().c_str());
		afterChangeGrid(result_index);
	}

	//多余的物品吐场景
	if (sum - 1 > 0)
	{
		LOG_WARNING("ContainerResearch sum - 1 > 0");
		//放场景
		BackPackGrid itemgrid;
		itemgrid.setItemDef(GetDefManagerProxy()->getItemDef(research_itemid));
		itemgrid.setNum(sum - 1);
		dropOneItem(itemgrid);
		removeItemByIndex(research_index, sum - 1);
	}

	researchtickcount = 0;
}

void ContainerResearch::StopResearch()
{

}