#include "GunUseComponent.h"
#include "ClientPlayer.h"
#include "world.h"
#include "SandboxGameDef.h"
#include "PlayerAttrib.h"
#include "backpack.h"
#include "SandboxIdDef.h"
#include "LuaInterfaceProxy.h"
#include "defdata.h"
#include "PlayerControl.h"
#include "GameCamera.h"
#include "ClientInfoProxy.h"
#include "PCControlLua.h"
#include "TouchControlLua.h"
#include "ActorLocoMotion.h"
#include "EffectExplosion.h"
#include "SandBoxManager.h"
#include "SandboxResult.h"
#include "SandboxCoreDriver.h"
#include "ProjectileFactory.h"
#include "EffectManager.h"
#include "WorldManager.h"
#include "Physics/IRaycast.h"
#include "OgrePhysXManager.h"
#include "OgreEntity.h"
#include "ActorBody.h"
#include "OgreModel.h"
#include "CameraModel.h"
#include "Math/Quaternionf.h"
#include "ModelItemMesh.h"
#include "AttackedComponent.h"
#include "ClientMob.h"
#include "InputInfo.h"
#include "ActionIdleStateGunAdvance.h"
#include "BlockMaterialMgr.h"
#include "Components/Camera.h"

using namespace MNSandbox;
using namespace Rainbow;

// 功能：检测以(xCenter,yCenter)为圆心、半径为radius的圆与屏幕矩形(x1,y1)-(x2,y2)是否相交
static bool checkOverlap(float radius, float xCenter, float yCenter, float x1, float y1, float x2, float y2) 
{
	// 求矩阵中心
	float xrc = 1.0 * (x2 + x1) / 2, yrc = 1.0 * (y1 + y2) / 2;
	// 矩形对角线半长向量
	float xhd = abs(x1 - xrc), yhd = abs(y1 - yrc);
	// 第1步：圆转换至以矩形中心为源点的第一象限，求出两点的距离向量
	float xdis = abs(xCenter - xrc), ydis = abs(yCenter - yrc);
	// 第2步：求圆心到矩阵的最短距离向量，负数的分量置为0
	float xmd = std::max(xdis - xhd, 0.0f), ymd = std::max(ydis - yhd, 0.0f);
	// 第3步：判断圆心到矩阵的最短距离是否小于圆半径
	return xmd * xmd + ymd * ymd <= radius * radius;
}

// 功能：更新屏幕AABB最小/最大点
static void clampV3f(Vector3f& screenMinPos, Vector3f& screenMaxPos, Vector3f& pos)
{
	if (pos.x > screenMaxPos.x)
	{
		screenMaxPos.x = pos.x;
	}
	if (pos.y > screenMaxPos.y)
	{
		screenMaxPos.y = pos.y;
	}

	if (pos.x < screenMinPos.x)
	{
		screenMinPos.x = pos.x;
	}
	if (pos.y < screenMinPos.y)
	{
		screenMinPos.y = pos.y;
	}
}

// 功能：将三维AABB八个角转换为屏幕坐标，得到其屏幕空间包围矩形
static void getMinMaxPos(Rainbow::Camera* camera, Vector3f& minPos, Vector3f& dim, Vector3f& screenMinPos, Vector3f& screenMaxPos)
{
	Vector3f pos1 = Vector3f(minPos.x, minPos.y, minPos.z);
	pos1 = camera->WorldToScreenPoint(pos1);
	screenMinPos = pos1;
	screenMaxPos = pos1;

	Vector3f pos2 = Vector3f(minPos.x + dim.x, minPos.y, minPos.z);
	pos2 = camera->WorldToScreenPoint(pos2);
	clampV3f(screenMinPos, screenMaxPos, pos2);

	Vector3f pos3 = Vector3f(minPos.x, minPos.y + dim.y, minPos.z);
	pos3 = camera->WorldToScreenPoint(pos3);
	clampV3f(screenMinPos, screenMaxPos, pos3);

	Vector3f pos4 = Vector3f(minPos.x + dim.x, minPos.y + dim.y, minPos.z);
	pos4 = camera->WorldToScreenPoint(pos4);
	clampV3f(screenMinPos, screenMaxPos, pos4);

	Vector3f pos5 = Vector3f(minPos.x, minPos.y, minPos.z + dim.z);
	pos5 = camera->WorldToScreenPoint(pos5);
	clampV3f(screenMinPos, screenMaxPos, pos5);

	Vector3f pos6 = Vector3f(minPos.x + dim.x, minPos.y, minPos.z + dim.z);
	pos6 = camera->WorldToScreenPoint(pos6);
	clampV3f(screenMinPos, screenMaxPos, pos6);

	Vector3f pos7 = Vector3f(minPos.x, minPos.y + dim.y, minPos.z + dim.z);
	pos7 = camera->WorldToScreenPoint(pos7);
	clampV3f(screenMinPos, screenMaxPos, pos7);

	Vector3f pos8 = Vector3f(minPos.x + dim.x, minPos.y + dim.y, minPos.z + dim.z);
	pos8 = camera->WorldToScreenPoint(pos8);
	clampV3f(screenMinPos, screenMaxPos, pos8);
}

IMPLEMENT_COMPONENTCLASS(GunUseComponent)

const CameraControlMode tpsCamerMode = CAMERA_TPS_BACK_SHOULDER/*CAMERA_TPS_BACK*/;

GunUseComponent::GunUseComponent():
	m_pBasicIK(nullptr),
	m_IsRegisterAfterAnimationProcessEvent(false),
	m_Host(nullptr),
	m_GunCoolDown(-1),
	m_GunDef(nullptr),
	m_ToolDef(nullptr),
	m_Magazine(-1),
	m_IsFire(false),
	m_IsReload(false),
	m_CurrentSpread(10),
	m_LastRotateX(0),
	m_NeedCameraRecovery(false),
	m_LastFov(0.0f),
	m_LastSensitivity(0),
	m_ReloadRecovery(false),
	m_LastViewMode(-1),
	m_CurrentJaw(0),
	m_CurrentPitch(0),
	m_nPulledGunId(-1),
	m_FireInterval(0)	
{
#if AlotmRecoilData
	if (!m_pTestCallback.IsValid())
	{
		MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("gunTestData");
		m_pTestCallback = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("gunTestData", nullptr, [&](MNSandbox::SandboxContext context) ->  MNSandbox::SandboxResult {

			/*
			m_RecoilSpeedThird = (float)context.GetData_Number("RecoilSpeedThird");
			m_MaxRecoilThird = (float)context.GetData_Number("MaxRecoilThird");
			m_RecoilRecoverySpeedThird = (float)context.GetData_Number("RecoilRecoverySpeedThird");

			m_RecoilSpeedThirdIK = (float)context.GetData_Number("RecoilSpeedThirdIK");
			m_MaxRecoilThirdIK = (float)context.GetData_Number("MaxRecoilThirdIK");

			m_vRightHandOffsetThird = Rainbow::Vector3f(context.GetData_Number("righthandoffsetxThird"), context.GetData_Number("righthandoffsetyThird"), context.GetData_Number("righthandoffsetzThird"));
			m_vRightPoleRelativeToEffectorThird = Rainbow::Vector3f(context.GetData_Number("rightPoleRelativeToEffectorxThird"), context.GetData_Number("rightPoleRelativeToEffectoryThird"), context.GetData_Number("rightPoleRelativeToEffectorzThird"));
			m_fRightWeightThird = (float)context.GetData_Number("fRightWeightThird");
			m_bRightStretchEnableThird = (bool)context.GetData_Bool("bRightStretchEnableThird");
			m_fRightStretchStartRatioThird = (float)context.GetData_Number("fRightStretchStartRatioThird");
			m_fRightStretchMaxRatioThird = (float)context.GetData_Number("fRightStretchMaxRatioThird");
			m_fRightSecondaryAxisWeightThird = (float)context.GetData_Number("fRightSecondaryAxisWeightThird");

			m_vLeftHandOffsetThird = Rainbow::Vector3f(context.GetData_Number("lefthandoffsetxThird"), context.GetData_Number("lefthandoffsetyThird"), context.GetData_Number("lefthandoffsetzThird"));
			m_vLeftPoleRelativeToEffectorThird = Rainbow::Vector3f(context.GetData_Number("leftPoleRelativeToEffectorxThird"), context.GetData_Number("leftPoleRelativeToEffectoryThird"), context.GetData_Number("leftPoleRelativeToEffectorzThird"));
			m_fLeftWeightThird = (float)context.GetData_Number("fLeftWeightThird");
			m_bLeftStretchEnableThird = (bool)context.GetData_Bool("bLeftStretchEnableThird");
			m_fLeftStretchStartRatioThird = (float)context.GetData_Number("fLeftStretchStartRatioThird");
			m_fLeftStretchMaxRatioThird = (float)context.GetData_Number("fLeftStretchMaxRatioThird");
			m_fLeftSecondaryAxisWeightThird = (float)context.GetData_Number("fLeftSecondaryAxisWeightThird");


			//m_RecoilSpeedFirst = (float)context.GetData_Number("RecoilSpeedFirst");
			//m_MaxRecoilFirst = (float)context.GetData_Number("m_MaxRecoilFirst");
			//m_RecoilRecoverySpeedFirst = (float)context.GetData_Number("RecoilRecoverySpeedFirst");
			m_vRightHandOffsetFirst = Rainbow::Vector3f(context.GetData_Number("righthandoffsetxFirst"), context.GetData_Number("righthandoffsetyFirst"), context.GetData_Number("righthandoffsetzFirst"));
			m_vRightPoleRelativeToEffectorFirst = Rainbow::Vector3f(context.GetData_Number("rightPoleRelativeToEffectorxFirst"), context.GetData_Number("rightPoleRelativeToEffectoryFirst"), context.GetData_Number("rightPoleRelativeToEffectorzFirst"));
			m_fRightWeightFirst = (float)context.GetData_Number("fRightWeightFirst");
			m_bRightStretchEnableFirst = (bool)context.GetData_Bool("bRightStretchEnableFirst");
			m_fRightStretchStartRatioFirst = (float)context.GetData_Number("fRightStretchStartRatioFirst");
			m_fRightStretchMaxRatioFirst = (float)context.GetData_Number("fRightStretchMaxRatioFirst");
			m_fRightSecondaryAxisWeightFirst = (float)context.GetData_Number("fRightSecondaryAxisWeightFirst");

			m_vLeftHandOffsetFirst = Rainbow::Vector3f(context.GetData_Number("lefthandoffsetxFirst"), context.GetData_Number("lefthandoffsetyFirst"), context.GetData_Number("lefthandoffsetzFirst"));
			m_vLeftPoleRelativeToEffectorFirst = Rainbow::Vector3f(context.GetData_Number("leftPoleRelativeToEffectorxFirst"), context.GetData_Number("leftPoleRelativeToEffectoryFirst"), context.GetData_Number("leftPoleRelativeToEffectorzFirst"));
			m_fLeftWeightFirst = (float)context.GetData_Number("fLeftWeightFirst");
			m_bLeftStretchEnableFirst = (bool)context.GetData_Bool("bLeftStretchEnableFirst");
			m_fLeftStretchStartRatioFirst = (float)context.GetData_Number("fLeftStretchStartRatioFirst");
			m_fLeftStretchMaxRatioFirst = (float)context.GetData_Number("fLeftStretchMaxRatioFirst");
			m_fLeftSecondaryAxisWeightFirst = (float)context.GetData_Number("fLeftSecondaryAxisWeightFirst");

			//float scalex = (float)context.GetData_Number("handmodelscalex");
			//float scaley = (float)context.GetData_Number("handmodelscaley");
			//float scalez = (float)context.GetData_Number("handmodelscalez");	
			//float px = (float)context.GetData_Number("handmodeloffsetx");
			//float py = (float)context.GetData_Number("handmodeloffsety");
			//float pz = (float)context.GetData_Number("handmodeloffsetz");
			//float rx = (float)context.GetData_Number("handmodelrotx");
			//float ry = (float)context.GetData_Number("handmodelroty");
			//float rz = (float)context.GetData_Number("handmodelrotz");

			float fov = (float)context.GetData_Number("cameraFov");

			if (m_Host)
			{
				PlayerControl* player = dynamic_cast<PlayerControl*>(m_Host);
				if (player)
				{
					player->setCameraConfigFov(fov);
					//auto cm = player->m_pCamera->getCameraModel();
					//if (cm)	
					//{
					//	cm->px = px;
					//	cm->py = py;
					//	cm->pz = pz;
					//	cm->hscx = scalex;
					//	cm->hscy = scaley;
					//	cm->hscz = scalez;
					//	cm->rx = rx;
					//	cm->ry = ry;
					//	cm->rz = rz;
					//}
				}
			*/
			m_nTmpAimAssistType = (int)context.GetData_Number("AimAssistType");
			m_bIsAimReduce = (bool)context.GetData_Bool("IsAimReduce");
			m_fSlowRaduisMax = (float)context.GetData_Number("SlowRaduisMax");
			m_fSlowRaduisMaxZoom = (float)context.GetData_Number("SlowRaduisMaxZoom");
			m_fSlowModulusPC = (float)context.GetData_Number("SlowModulusPC");
			m_fSlowModulusMobile = (float)context.GetData_Number("SlowModulusMobile");
			m_fRaduisMin = (float)context.GetData_Number("RaduisMin");
			m_fRaduisMinZoom = (float)context.GetData_Number("RaduisMinZoom");
			m_fAutoAimCameraSpeedPC = (float)context.GetData_Number("AutoAimCameraSpeedPC");
			m_fAutoAimCameraSpeedMobile = (float)context.GetData_Number("AutoAimCameraSpeedMobile");
			m_fAimRange = (float)context.GetData_Number("AimMaxRange");

			return MNSandbox::SandboxResult(nullptr, true);
			});
	}
#endif

	setComViewMode(tpsCamerMode);
}

GunUseComponent::~GunUseComponent()
{
#if AlotmRecoilData
	if (m_pTestCallback.IsValid())
	{
		MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Unsubscribe("gunTestData", m_pTestCallback);
	}
#endif
	unregisterThirdPersonEvent();
	ENG_DELETE(m_pBasicIK);
	m_Host = NULL;
}

void GunUseComponent::registerThirdPersonEvent()
{
	if (!m_pBasicIK)
	{
		m_pBasicIK = ENG_NEW(BasicIK)();
	}
	if (m_Host)
	{
		auto body = m_Host->getBody();
		if (body)
		{
			auto entity = body->getEntity();
			if (entity)
			{
				auto model = entity->GetMainModel();
				if (model)
				{
					auto skinnedSkeleton = model->GetskinnedSkeleton();
					if (skinnedSkeleton)
					{
						skinnedSkeleton->RegisterAfterAnimationProcessEvent<GunUseComponent>(&GunUseComponent::OnAnimatorPostEvent, this);
						//LogStringMsg("~~~~!!!!~~~~ registerThirdPersonEvent ThirdSkinnedSkeleton cviewmode == %d", m_cViewMode);
					}
				}
			}
		}
	}
}

void GunUseComponent::registerFirstPersonEvent()
{
	if (!m_pBasicIK)
	{
		m_pBasicIK = ENG_NEW(BasicIK)();
	}
	if (m_Host)
	{
		PlayerControl* player = dynamic_cast<PlayerControl*>(m_Host);
		if (player)
		{
			if (player->m_pCamera->getCameraModel() && player->m_pCamera->getCameraModel()->m_HandModel)
			{
				auto skinnedSkeleton = player->m_pCamera->getCameraModel()->m_HandModel->GetskinnedSkeleton();
				if (skinnedSkeleton)
				{
					skinnedSkeleton->RegisterAfterAnimationProcessEvent<GunUseComponent>(&GunUseComponent::OnAnimatorPostEvent, this);
				}
			}
			setComViewMode(player->getViewMode());
		}
	}
}

void GunUseComponent::unregisterThirdPersonEvent()
{
	if (m_Host)
	{
		auto body = m_Host->getBody();
		if (body)
		{
			auto entity = body->getEntity();
			if (entity)
			{
				auto model = entity->GetMainModel();
				if (model)
				{
					auto skinnedSkeleton = model->GetskinnedSkeleton();
					if (skinnedSkeleton)
					{
						skinnedSkeleton->UnRegisterAfterAnimationProcessEvent<GunUseComponent>(&GunUseComponent::OnAnimatorPostEvent, this);
					}
				}
			}
		}
	}
	if (m_pBasicIK)
	{
		m_pBasicIK->resetAllData();
	}
}

void GunUseComponent::unregisterFirstPersonEvent()
{
	if (m_Host)
	{
		PlayerControl* player = dynamic_cast<PlayerControl*>(m_Host);
		if (player)
		{
			if (player->m_pCamera->getCameraModel() && player->m_pCamera->getCameraModel()->m_HandModel)
			{
				auto skinnedSkeleton = player->m_pCamera->getCameraModel()->m_HandModel->GetskinnedSkeleton();
				if (skinnedSkeleton)
				{
					skinnedSkeleton->UnRegisterAfterAnimationProcessEvent<GunUseComponent>(&GunUseComponent::OnAnimatorPostEvent, this);
				}
			}
		}
	}
	if (m_pBasicIK)
	{
		m_pBasicIK->resetAllData();
	}
}

void GunUseComponent::OnAnimatorPostEvent(const Rainbow::EventContent* evt)
{
	// 基础检查：确保事件和用户数据有效
	if (!m_Host || !evt || evt->userData == NULL)
		return;

	// 如果不是开火状态且不需要复位，则直接返回
	if (!m_IsFire && !m_IsIKRecovery/* && !m_GunDef*/)
		return;

	// 获取骨骼系统
	Rainbow::SkinnedSkeleton* skinnedSkeleton = (Rainbow::SkinnedSkeleton*)evt->userData;
	if (!skinnedSkeleton)
		return;
	
	if (m_pBasicIK)
	{
		float weightfactor = 1.f;
		if (m_IsFire){
			m_IsFire = false;
			float recoilSpeed = getComViewMode() == tpsCamerMode ? m_RecoilSpeedThirdIK : m_RecoilSpeedFirst;
			if(recoilSpeed < 0.001f)
			{
				return;
			}
		}
		else if (m_IsIKRecovery)
		{
			float maxRecoil = getComViewMode() == tpsCamerMode ? m_MaxRecoilThirdIK : m_MaxRecoilFirst;
			weightfactor = maxRecoil > 0.001f ? m_RecoilTotalRotate / maxRecoil : 0.f;
		}
		if(getComViewMode() == tpsCamerMode)
		{
			Rainbow::Model* model = NULL;
			auto body = m_Host->getBody();
			if (body)
			{
				auto entity = body->getEntity();
				if (entity)
				{
					model = entity->GetMainModel();
				}
			}
			if (model && model->GetskinnedSkeleton() == skinnedSkeleton)
			{
				// 获取需要的骨骼节点
				auto spineNode = skinnedSkeleton->GetSkeletonNode(skinnedSkeleton->FindBoneId("B_Spine"));
				auto neckNode = skinnedSkeleton->GetSkeletonNode(skinnedSkeleton->FindBoneId("B_Neck"));
				auto headNode = skinnedSkeleton->GetSkeletonNode(skinnedSkeleton->FindBoneId("B_Head"));

				// 获取右手臂的骨骼链
				auto rightUpperArm = skinnedSkeleton->GetSkeletonNode(skinnedSkeleton->FindBoneId("B_R_UpperArm"));
				auto rightForearm = skinnedSkeleton->GetSkeletonNode(skinnedSkeleton->FindBoneId("B_R_Forearm"));
				auto rightHand = skinnedSkeleton->GetSkeletonNode(skinnedSkeleton->FindBoneId("B_R_Hand"));

				// 获取左手臂的骨骼链
				auto leftUpperArm = skinnedSkeleton->GetSkeletonNode(skinnedSkeleton->FindBoneId("B_L_UpperArm"));
				auto leftForearm = skinnedSkeleton->GetSkeletonNode(skinnedSkeleton->FindBoneId("B_L_Forearm"));
				auto leftHand = skinnedSkeleton->GetSkeletonNode(skinnedSkeleton->FindBoneId("B_L_Hand"));

				// 设置右手臂后坐力
				if (rightUpperArm && rightForearm && rightHand)
				{
					m_pBasicIK->resetAllData();
					m_pBasicIK->SetCachedSkeletonComp(skinnedSkeleton);
					auto handTrans = rightHand->GetWorldTransform();
					auto obj = Rainbow::GameObject::Create();
					auto transform = obj->GetTransform();
					// 减小动画幅度，只保留较小的上下移动
					transform->SetWorldPosition(m_vRightHandOffsetThird + handTrans.GetPosition());
					transform->SetWorldScale(Vector3f(1.0f));
					// 保持原有旋转，不添加额外旋转
					transform->SetWorldRotation(handTrans.GetRotation());

					BasicIKData rightdata;
					rightdata._boneA = rightUpperArm;
					rightdata._boneB = rightForearm;
					rightdata._boneC = rightHand;
					rightdata._effector = obj;
					// 减小极向量的影响，使手臂移动更加平滑
					rightdata._poleVectorRelativeToEffector = m_vRightPoleRelativeToEffectorThird;
					rightdata._weight = m_fRightWeightThird; // 减小权重使动作更柔和
					rightdata._bEnableStretch = m_bRightStretchEnableThird;
					rightdata._stretchStartRatio = m_fRightStretchStartRatioThird;
					rightdata._stretchMaxRatio = m_fRightStretchMaxRatioThird;
					// rightdata._primaryAxis = Rainbow::Vector3f(0, 0, 1);
					// rightdata._secondaryAxis = Rainbow::Vector3f(0, 1, 0);
					rightdata._secondaryAxisWeight = m_fRightSecondaryAxisWeightThird; // 进一步减小次旋转的影响

					m_pBasicIK->SetIDData(rightdata);
					m_pBasicIK->SampleInternal(weightfactor);
				}

				// 设置左手臂后坐力
				if (leftUpperArm && leftForearm && leftHand)
				{
					m_pBasicIK->resetAllData();
					m_pBasicIK->SetCachedSkeletonComp(skinnedSkeleton);
					auto handTrans = leftHand->GetWorldTransform();
					auto obj = Rainbow::GameObject::Create();
					auto transform = obj->GetTransform();
					// 对左手使用相同的减小幅度
					transform->SetWorldPosition(m_vLeftHandOffsetThird + handTrans.GetPosition());
					transform->SetWorldScale(Vector3f(1.0f));
					// 保持原有旋转
					transform->SetWorldRotation(handTrans.GetRotation());

					BasicIKData leftdata;
					leftdata._boneA = leftUpperArm;
					leftdata._boneB = leftForearm;
					leftdata._boneC = leftHand;
					leftdata._effector = obj;
					leftdata._weight = m_fLeftWeightThird; // 减小权重
					leftdata._bEnableStretch = m_bLeftStretchEnableThird;
					leftdata._stretchStartRatio = m_fLeftStretchStartRatioThird;
					leftdata._stretchMaxRatio = m_fLeftStretchMaxRatioThird;
					// leftdata._primaryAxis = Rainbow::Vector3f(0, 0, 1);
					// leftdata._secondaryAxis = Rainbow::Vector3f(0, 1, 0);
					// 使用相同的减小极向量影响
					leftdata._poleVectorRelativeToEffector = m_vLeftPoleRelativeToEffectorThird;
					leftdata._secondaryAxisWeight = m_fLeftSecondaryAxisWeightThird;

					m_pBasicIK->SetIDData(leftdata);
					m_pBasicIK->SampleInternal(weightfactor);
				}
			}
		}
		else if(getComViewMode() == CAMERA_FPS)
		{
			PlayerControl* player = dynamic_cast<PlayerControl*>(m_Host);
			if (player)
			{
				if (player->m_pCamera->getCameraModel() && player->m_pCamera->getCameraModel()->m_HandModel)
				{
					if (player->m_pCamera->getCameraModel()->m_HandModel->GetskinnedSkeleton() == skinnedSkeleton)
					{
						// 获取右手臂的骨骼链
						auto rightUpperArm = skinnedSkeleton->GetSkeletonNode(skinnedSkeleton->FindBoneId("B_R_UpperArm"));
						auto rightLowerArm = skinnedSkeleton->GetSkeletonNode(skinnedSkeleton->FindBoneId("B_R_Forearm"));
						auto rightHand = skinnedSkeleton->GetSkeletonNode(skinnedSkeleton->FindBoneId("B_R_Hand"));

						// 获取左手臂的骨骼链
						auto leftUpperArm = skinnedSkeleton->GetSkeletonNode(skinnedSkeleton->FindBoneId("B_L_UpperArm"));
						auto leftLowerArm = skinnedSkeleton->GetSkeletonNode(skinnedSkeleton->FindBoneId("B_L_Forearm"));
						auto leftHand = skinnedSkeleton->GetSkeletonNode(skinnedSkeleton->FindBoneId("B_L_Hand"));

						// 设置右手臂后坐力
						if (rightUpperArm && rightLowerArm && rightHand)
						{
							m_pBasicIK->resetAllData();
							m_pBasicIK->SetCachedSkeletonComp(skinnedSkeleton);
							auto handTrans = rightHand->GetWorldTransform();
							auto obj = Rainbow::GameObject::Create();
							auto transform = obj->GetTransform();
							// 减小动画幅度，只保留较小的上下移动
							transform->SetWorldPosition(m_vRightHandOffsetFirst + handTrans.GetPosition());
							transform->SetWorldScale(Vector3f(1.0f));
							// 保持原有旋转，不添加额外旋转
							transform->SetWorldRotation(handTrans.GetRotation());

							BasicIKData rightdata;
							rightdata._boneA = rightUpperArm;
							rightdata._boneB = rightLowerArm;
							rightdata._boneC = rightHand;
							rightdata._effector = obj;
							// 减小极向量的影响，使手臂移动更加平滑
							rightdata._poleVectorRelativeToEffector = m_vRightPoleRelativeToEffectorFirst;
							rightdata._weight = m_fRightWeightFirst; // 减小权重使动作更柔和
							rightdata._bEnableStretch = m_bRightStretchEnableFirst;
							rightdata._stretchStartRatio = m_fRightStretchStartRatioFirst;
							rightdata._stretchMaxRatio = m_fRightStretchMaxRatioFirst;
							// rightdata._primaryAxis = Rainbow::Vector3f(0, 0, 1);
							// rightdata._secondaryAxis = Rainbow::Vector3f(0, 1, 0);
							rightdata._secondaryAxisWeight = m_fRightSecondaryAxisWeightFirst; // 进一步减小次旋转的影响

							m_pBasicIK->SetIDData(rightdata);
							m_pBasicIK->SampleInternal(weightfactor);
						}

						// 设置左手臂后坐力
						if (leftUpperArm && leftLowerArm && leftHand)
						{
							m_pBasicIK->resetAllData();
							m_pBasicIK->SetCachedSkeletonComp(skinnedSkeleton);
							auto handTrans = leftHand->GetWorldTransform();
							auto obj = Rainbow::GameObject::Create();
							auto transform = obj->GetTransform();
							// 对左手使用相同的减小幅度
							transform->SetWorldPosition(m_vLeftHandOffsetFirst + handTrans.GetPosition());
							transform->SetWorldScale(Vector3f(1.0f));
							// 保持原有旋转
							transform->SetWorldRotation(handTrans.GetRotation());

							BasicIKData leftdata;
							leftdata._boneA = leftUpperArm;
							leftdata._boneB = leftLowerArm;
							leftdata._boneC = leftHand;
							leftdata._effector = obj;
							leftdata._weight = m_fLeftWeightFirst; // 减小权重
							leftdata._bEnableStretch = m_bLeftStretchEnableFirst;
							leftdata._stretchStartRatio = m_fLeftStretchStartRatioFirst;
							leftdata._stretchMaxRatio = m_fLeftStretchMaxRatioFirst;
							// leftdata._primaryAxis = Rainbow::Vector3f(0, 0, 1);
							// leftdata._secondaryAxis = Rainbow::Vector3f(0, 1, 0);
							// 使用相同的减小极向量影响
							leftdata._poleVectorRelativeToEffector = m_vLeftPoleRelativeToEffectorFirst;
							leftdata._secondaryAxisWeight = m_fLeftSecondaryAxisWeightFirst;

							m_pBasicIK->SetIDData(leftdata);
							m_pBasicIK->SampleInternal(weightfactor);
						}
					}
				}
			}
		}
	}

}

void GunUseComponent::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	m_Host = dynamic_cast<ClientPlayer*>(owner);
	if (m_Host) {
		m_Host->BindGunComponent(this);
	}
	Super::OnEnterOwner(owner);
}

void GunUseComponent::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	if (m_Host) {
		m_Host->clearGunUseUpdate();
		m_Host->BindGunComponent(NULL);
		m_Host = NULL;
	}
	Super::OnLeaveOwner(owner); 
}

void GunUseComponent::reset()
{
	if (g_pPlayerCtrl == m_Host)
	{
		setZoom(false);
		m_GunCoolDown = -1;
		m_Magazine = -1;
		m_GunDef = NULL;
		m_CurrentSpread = 10;
		m_NeedCameraRecovery = false;
		m_LastRotateX = 0;
		m_LastFov = 0.0f;
		m_LastSensitivity = 0;
		m_ReloadRecovery = false;
		m_LastViewMode = -1;
		m_FireInterval = 0;
		m_CurrentJaw = 0;
		m_CurrentPitch = 0;
	}

#ifndef IWORLD_SERVER_BUILD
	if(m_Host)
		m_Host->clearGunUseUpdate();
#endif
}

int GunUseComponent::getReloadTime()
{
	if (m_GunDef == nullptr) return 0;
	int baseReloadTime = m_GunDef->ReloadTime;

	float slotReloadTime = 0;
	BackPackGrid* itemgrid = m_Host->getPlayerAttrib()->getEquipGrid(EQUIP_WEAPON);
	if (itemgrid)
	{
		int slotcount = GetDefManagerProxy()->getGunSlotNum(m_GunDef->ID);
		for (int i = 0; i < slotcount; i++)
		{
			int slotItemId = itemgrid->getKVData("slot" + std::to_string(i + 1));
			if (slotItemId > 0)
			{
				const GunComponentDef* componentDef = GetDefManagerProxy()->getGunComponentDef(slotItemId);
				if (componentDef)
				{
					slotReloadTime += componentDef->AddReloadTime;  // 万分比
				}
			}
		}
	}
	return baseReloadTime + int(baseReloadTime * slotReloadTime);
}


int GunUseComponent::getMaxMagazines()
{
	if (m_GunDef == nullptr) return 0;

	int magazines = m_GunDef->Magazines;
	float slotMagazines = 0;

	BackPackGrid* itemgrid = m_Host->getPlayerAttrib()->getEquipGrid(EQUIP_WEAPON);
	if (itemgrid)
	{
		int slotcount = GetDefManagerProxy()->getGunSlotNum(m_GunDef->ID);
		for (int i = 0; i < slotcount; i++)
		{
			int slotItemId = itemgrid->getKVData("slot" + std::to_string(i + 1));
			if (slotItemId > 0)
			{
				const GunComponentDef* componentDef = GetDefManagerProxy()->getGunComponentDef(slotItemId);
				if (componentDef)
				{
					slotMagazines += componentDef->AddMagazines;  // 万分比
				}
			}
		}
	}
	return magazines + int(magazines * slotMagazines);
}

//获取枪当前子弹量
int GunUseComponent::getBulletNum()
{
	if (m_GunDef == NULL) return 0;
	int costItemId = m_GunDef->GetCostItemId();
	int costItemNum = m_Host->getBackPack()->getItemCountInNormalPack(costItemId);

	//一個压缩气罐能分解成多个子弹
	if (costItemId == BLOCK_OXYGEN_JAR_FULL)
	{
		return costItemNum * GetLuaInterfaceProxy().get_lua_const()->oxygen_pack_full_bullet_num;
	}

	return costItemNum;
}

//当前是否能够换子弹
bool GunUseComponent::canReload()
{
	int costItemId = m_GunDef->GetCostItemId();
	if (costItemId == BLOCK_OXYGEN_JAR_FULL)
	{
		return m_Magazine <= 0;
	}

	if (m_Host->IsCurToolBroken())
	{
		return false;
	}

	return true;
}

void GunUseComponent::setGunDef(const GunDef* def)
{
	m_GunDef = def;
	int toolid = def ? def->ID : 0;
	m_ToolDef = nullptr;
	if (toolid > 0)
		m_ToolDef = GetDefManagerProxy()->getToolDef(toolid);

	if (def)
	{
		MINIW::ScriptVM::game()->callFunction("SetGunMagazine", "ii", m_Magazine, getBulletNum());
		m_CurrentSpread = def->InitSpread;
		m_RecoilSpeedFirst = def->RecoilSpeedFirst;
		m_RecoilSpeedThird = def->RecoilSpeedThird;
		m_RecoilSpeedThirdIK = def->RecoilSpeedThirdIK;
		m_MaxRecoilFirst = def->MaxRecoilFirst;
		m_MaxRecoilThird = def->MaxRecoilThird;
		m_MaxRecoilThirdIK = def->MaxRecoilThirdIK;
		m_RecoilRecoverySpeedFirst = def->RecoilRecoverySpeedFirst;
		m_RecoilRecoverySpeedThird = def->RecoilRecoverySpeedThird;
	}
	else
	{
		MINIW::ScriptVM::game()->callFunction("SetGunMagazine", "ii", -1, -1);
		m_CurrentSpread = 10;
	}
}

bool GunUseComponent::isWaterCannon()
{
	if (m_GunDef && m_GunDef->ID == ITEM_WATER_CANNON)
	{
		return true;
	}

	return false;
}

void GunUseComponent::setComViewMode(char viewMode)
{
	if (m_cViewMode != viewMode)
	{
		m_cViewMode = viewMode;
	}
}

const GunDef* GunUseComponent::getGunDef()
{
	return m_GunDef;
}

void GunUseComponent::resetRecoil()
{
	PlayerControl* player = dynamic_cast<PlayerControl*>(m_Host);
	m_LastRotateX = player->m_pCamera->m_RotatePitch;
}

void GunUseComponent::setZoom(bool open)
{
	bool visible = false;

	//if (!m_GunDef) return;

	if (open && m_GunDef)
	{
		float aim = getSightAim();
		if (aim > 0 && !m_IsReload)
		{
			openTelescope(aim);
			visible = true;
		}
	}
	else
	{
		closeTelescope();

		m_LastFov = 0.0f;
		m_LastViewMode = -1;
		visible = false;

		PlayerControl* player = dynamic_cast<PlayerControl*>(m_Host);
		if (player)
		{
			player->m_PlayerAnimation->performIdle();
		}
	}
	MINIW::ScriptVM::game()->callFunction("SetSightingTelescopeFrame", "b", visible);
}

bool GunUseComponent::getZoom()
{
	return m_LastFov == 0 || m_IsReload ? false : true;
}

void GunUseComponent::openTelescope(float aim)
{
	PlayerControl* player = dynamic_cast<PlayerControl*>(m_Host);

	if (aim == 0.0f)
	{
		aim = getSightAim();
	}

	if (aim > 0 && !m_IsReload)// && player->m_ViewMode == 0)
	{
		aim = aim > 74.9999f ? 74.9999f : aim;

		if (GetClientInfoProxy()->isMobile())
		{
			if (m_LastSensitivity == 0)
			{
				m_LastSensitivity = TouchControlLua::GetInstance().getSensitivity();
			}
			TouchControlLua::GetInstance().setSensitivity2(m_LastSensitivity * (1 - aim / 75) * GetLuaInterfaceProxy().get_lua_const()->kaijing_sensitivity_xishu * 2.7f);
		}
		else
		{
			if (m_LastSensitivity == 0)
			{
				m_LastSensitivity = PCControlLua::GetInstance().getSensitivity();
			}
			PCControlLua::GetInstance().setSensitivity2(m_LastSensitivity * (1 - aim / 75) * GetLuaInterfaceProxy().get_lua_const()->kaijing_sensitivity_xishu);
		}

		if (m_LastViewMode == -1)
		{
			m_LastViewMode = player->m_ViewMode;
		}
		player->setViewMode(CAMERA_FPS);

		m_LastFov = player->m_pCamera->getFov();
		player->m_pCamera->setZoomInOut(75 - aim);
	}
}

void GunUseComponent::closeTelescope()
{
	PlayerControl* player = dynamic_cast<PlayerControl*>(m_Host);

	if (m_LastSensitivity != 0)
	{
		if (GetClientInfoProxy()->isMobile())
		{
			TouchControlLua::GetInstance().setSensitivity2(m_LastSensitivity);
		}
		else
		{
			PCControlLua::GetInstance().setSensitivity2(m_LastSensitivity);
		}
	}

	if (m_LastFov != 0)
	{
		player->m_pCamera->resetZoom();
	}

	if (m_LastViewMode != -1)
	{
		player->setViewMode(m_LastViewMode);
	}
}

float GunUseComponent::getSightAim()
{
	if (m_GunDef == nullptr) return 0;
	float aim = m_GunDef->Aim;

	BackPackGrid* itemgrid = m_Host->getPlayerAttrib()->getEquipGrid(EQUIP_WEAPON);
	if (itemgrid)
	{
		int slotcount = GetDefManagerProxy()->getGunSlotNum(m_GunDef->ID);
		for (int i = 0; i < slotcount; i++)
		{
			int slotItemId = itemgrid->getKVData("slot" + std::to_string(i + 1));
			if (slotItemId > 0)
			{
				const GunComponentDef* componentDef = GetDefManagerProxy()->getGunComponentDef(slotItemId);
				if (componentDef)
				{
					aim += componentDef->SightAim;
				}
			}
		}

	}
	return aim;
}

void GunUseComponent::onUpdate(float dtime)
{
	decreaseSpreadOnTime(dtime);
}

void GunUseComponent::OnUpdate(float dtime)
{
	if (m_GunDef)
	{
		//if (m_IsFire) m_IsFire = false;
		// 恢复后坐力导致准心上移
		float recoilRecoverySpeed = getComViewMode() == tpsCamerMode ? /*m_GunDef->RecoilRecoverySpeedThird : m_GunDef->RecoilRecoverySpeedFirst;*/m_RecoilRecoverySpeedThird : m_RecoilRecoverySpeedFirst;
		if (/*m_GunDef->RecoilRecoverySpeed*/recoilRecoverySpeed > 0.001 && (m_IsIKRecovery || m_NeedCameraRecovery))
		{
			float step = dtime * recoilRecoverySpeed/*m_GunDef->RecoilRecoverySpeed*/;
			if (m_IsIKRecovery)
			{

				m_RecoilTotalRotate -= step;
				if (m_RecoilTotalRotate <= 0.001)
				{
					m_RecoilTotalRotate = 0.f;
					m_IsIKRecovery = false;
				}
			}
			if (m_NeedCameraRecovery)
			{
				PlayerControl* player = dynamic_cast<PlayerControl*>(m_Host);
				float offset = abs(player->m_pCamera->m_RotatePitch - m_LastRotateX) / 90.0f;
				if (offset >= 0.001)
				{
					if (offset < step) step = offset;
					// LOG_INFO("dodo2222 before[step = %f, m_LastRotateX = %f, m_RotatePitch = %f]", step, m_LastRotateX, player->m_pCamera->m_RotatePitch);
					player->m_pCamera->rotate(0.0, step);
					// LOG_INFO("dodo2222 after[step = %f, m_LastRotateX = %f, m_RotatePitch = %f]", step, m_LastRotateX, player->m_pCamera->m_RotatePitch);
					float offset2 = abs(player->m_pCamera->m_RotatePitch - m_LastRotateX) / 90.0f;
					if (offset2 > offset || offset2 <= 0.001)
					{
						m_NeedCameraRecovery = false;
					}
				}
			}
		}
		bool isAimAssist = false;
		if (m_nAimAssistType == AIM_ASSIST_ALLTIME) isAimAssist = true;
		else if (m_nAimAssistType == AIM_ASSIST_IN_AIM_STATE)
		{
			if (m_Host)
			{
				//todo by charles xie 这里可以优化为使用同一个值。即（pc的右键，与 移动端的瞄准按钮同时设置m_InputInfo的一个值，比如都设置rightClick）
				if (GetClientInfoProxy()->isPC())
				{
					if (m_Host->m_InputInfo && m_Host->m_InputInfo->rightClick) isAimAssist = true;
				}
				else
				{
					PlayerControl* pPlayerCtrl = dynamic_cast<PlayerControl*>(m_Host);
					if (pPlayerCtrl && pPlayerCtrl->getTouchControl()->GetButtonDown(BUTTON_GUNAIM)) isAimAssist = true;
				}
			}
		}
		// 处理辅助瞄准
		if (isAimAssist) CheckAimAssist(dtime);
	}
}


bool GunUseComponent::doWaterCanoonSkill()
{
	if (!m_Host->getWorld()->isRemoteMode())
	{
		if (!isWaterCannon())  // 防止外挂利用, 必须手持水压炮 2023.10.07 by huanglin
			return false;
		WCoord pos = WCoord(m_Host->getPosition()) + WCoord(0, 10, 0);
		int exploreradius = GetLuaInterfaceProxy().get_lua_const()->air_ball_land_range_value_param;
		int atkpoint = 0;
		Rainbow::Vector3f euler;
		Rainbow::Vector3f lookdir = m_Host->getLocoMotion()->getLookDir();
		Direction2PitchYaw(&euler.y, &euler.x, lookdir);
		euler.z = 0;
		euler.y += 180;
		ExplosionAirBall explosion(m_Host->getWorld(), m_Host, pos, exploreradius, atkpoint, m_Host->isInWater(), euler);
		explosion.doExplosionA();
		explosion.doExplosionB();
	}
	else
	{
		jsonxx::Object context;
		context << "uin" << m_Host->getUin();
		SandBoxManager::getSingleton().sendToHost("PB_GUNLOGIC_USE_WaterCanoonSkill", context.bin(), context.binLen());
	}

	return true;
}

bool GunUseComponent::doShootJob()
{
	if (isWaterCannon() && !m_Host->isInWater())
	{
		return doWaterCanoonSkill();
	}
	else
	{
		return m_Host->useItem(m_GunDef->ID, PLAYEROP_STATUS_BEGIN);
	}
}

/**
 * @brief 射击后扩大准星
*/
void GunUseComponent::increaseSpreadOnFire()
{
	//spread的速度，后面最好配置在表格里面
	//float spreadSpeed = 0.25;
	if (m_GunDef && m_CurrentSpread < m_GunDef->MaxSpread)
	{
		m_CurrentSpread += (m_GunDef->MaxSpread - m_CurrentSpread) * m_GunDef->SpreadSpeed;
	}
}

/**
 * @brief 定时缩小准星
 * @param dtime 过去的时间 单位:秒
*/
void GunUseComponent::decreaseSpreadOnTime(float dtime)
{
	if (m_GunDef && m_CurrentSpread - m_GunDef->InitSpread > 1.1f)
	{
		//float lerpSpeed = 10.0f;
		//m_CurrentSpread = Lerp(m_CurrentSpread, m_GunDef->InitSpread, dtime * lerpSpeed);
		m_CurrentSpread -= dtime * m_GunDef->SpreadRecoverySpeed;
	}
}

int GunUseComponent::fireOnce(bool bAuto)
{
	if (m_GunDef == NULL)
	{
		m_GunDef = GetDefManagerProxy()->getGunDef(m_Host->getCurToolID());
	}

	if (m_Magazine > 0 || m_GunDef->NeedBullet == 2)
	{
		bool success = doShootJob();
		if (!success)
			return 2; // 不可使用
		if (m_GunDef->NeedBullet != 2)
			m_Magazine -= 1;
			
		MINIW::ScriptVM::game()->callFunction("SetGunMagazine", "ii", m_Magazine, getBulletNum());

		// useitem中扣除
		BackPackGrid* itemgrid = m_Host->getPlayerAttrib()->getEquipGrid(EQUIP_WEAPON);
		itemgrid->setUserDataInt(m_Magazine);

		PlayerControl* player = dynamic_cast<PlayerControl*>(m_Host);
		float recoilSpeed = getComViewMode() == tpsCamerMode ? m_RecoilSpeedThird : m_RecoilSpeedFirst;//m_RecoilSpeedThird : m_RecoilSpeedFirst;
		float maxRecoil = getComViewMode() == tpsCamerMode ? m_MaxRecoilThird : m_MaxRecoilFirst;//m_MaxRecoilThird : m_MaxRecoilFirst;
		// 后坐力导致准心上移
		if (/*m_GunDef->RecoilSpeed > 0.001*/ (recoilSpeed > 0.001f) && player)
		{
			if (m_RecoilTotalRotate < maxRecoil/*m_GunDef->MaxRecoil*/)
			{
				m_RecoilTotalRotate += recoilSpeed;/*m_GunDef->RecoilSpeed*/
				m_RecoilTotalRotate = m_RecoilTotalRotate > maxRecoil ? maxRecoil : m_RecoilTotalRotate;
			}
			m_IsIKRecovery = true;
			//if (getComViewMode() == CAMERA_FPS)
			{
		 		if (abs(player->m_pCamera->m_RotatePitch - m_LastRotateX) < maxRecoil/*m_GunDef->MaxRecoil*/)
		 		{
					player->m_pCamera->rotate(0.0, -recoilSpeed/*-m_GunDef->RecoilSpeed*/); 
		 		}
		 		m_NeedCameraRecovery = true;
			}
		}
		 
		setIsFire(true);
		// auto body = m_Host->getBody();
		// if (body) {
		// 	body->playAnim(SEQ_GUN_FIRE);
		// }
		return 0;
	}

	return 1;  // 无子弹
}

int GunUseComponent::getMagazine()
{
	return m_Magazine;
}

void GunUseComponent::setMagazine(int count)
{
	m_Magazine = count;
}

WCoord GunUseComponent::getMuzzlePos()
{
	PlayerControl* player = dynamic_cast<PlayerControl*>(m_Host);
	ModelItemMesh* weaponModel = dynamic_cast<ModelItemMesh*>(player->getBody()->getWeaponModel());
	if (getComViewMode() == CAMERA_FPS)
	{
		weaponModel = dynamic_cast<ModelItemMesh*>(player->m_CameraModel->m_ToolModel);
	}
	else
	{
		weaponModel = dynamic_cast<ModelItemMesh*>(player->getBody()->getWeaponModel());
	}
	auto pos = weaponModel->getEntity()->GetAnchorWorldPos(123);
	if (getComViewMode() == tpsCamerMode)
	{
		if (!m_Host->getSneaking()) pos.y = player->getEyePosition().y;
	}
	return WCoord(pos);
}

void GunUseComponent::setIsReload(bool isReload)
{
	m_IsReload = isReload;
	if (isReload)
	{
		if (m_LastFov != 0)
		{
			closeTelescope();
			m_ReloadRecovery = true;
		}
		m_IsFire = false;
	}
}

void GunUseComponent::doCostBulletItem(int bulletid, int num)
{
	if (m_GunDef && m_GunDef->NeedBullet == 1)
	{
		int costnum = num;
		//水压炮是换弹夹，一次只能消耗一个压缩气罐 BLOCK_OXYGEN_JAR_FULL
		if (isWaterCannon())
		{
			costnum = 1;
		}
		m_Host->getBackPack()->removeItemInNormalPack(bulletid, costnum);
		m_Host->consumeItemOnTrigger(bulletid, costnum);

		if (isWaterCannon())
		{
			m_Host->getBackPack()->addItem(BLOCK_OXYGEN_JAR_EMPTY, 1, 2);
		}
	}
}

void GunUseComponent::doReload(int count)
{
	if (m_GunDef == NULL)
	{
		m_GunDef = GetDefManagerProxy()->getGunDef(m_Host->getCurToolID());
	}

	if (m_GunDef == NULL) return;
	if (count == 0) return;
	
	if (count == -1)
		m_Magazine = getMaxMagazines();
	else
		m_Magazine = count;
	// SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("WeaponSkin_System_magazines", SandboxContext(nullptr)
	// 	.SetData_Number("itemid", m_GunDef->ID)
	// 	.SetData_Number("uin", m_Host ? m_Host->getUin() : 0)
	// );

	// if (result.IsExecSuccessed())
	// {
	// 	maxMagazines += (int)result.GetData_Number("nvalue");
	// }

	MINIW::ScriptVM::game()->callFunction("SetGunMagazine", "ii", m_Magazine, getBulletNum());
	
	BackPackGrid* itemgrid = m_Host->getPlayerAttrib()->getEquipGrid(EQUIP_WEAPON);
	itemgrid->setUserDataInt(m_Magazine);

	//m_Host->playSound(m_GunDef->ReloadSound, 1.0, 1.0f + (GenRandomFloat() - GenRandomFloat())*0.4f);
	setIsReload(false);

	if (m_ReloadRecovery)
	{
		openTelescope();
		m_ReloadRecovery = false;
	}
}

// 道具远程修改时，需要更新枪械弹夹
void GunUseComponent::updateMagazine()
{
	if (m_GunDef == NULL)
		return;
	BackPackGrid* itemgrid = m_Host->getPlayerAttrib()->getEquipGrid(EQUIP_WEAPON);
	if (!itemgrid)
		return;
	m_Magazine = itemgrid->getUserDataInt();
	MINIW::ScriptVM::game()->callFunction("SetGunMagazine", "ii", m_Magazine, getBulletNum());
}


int GunUseComponent::getGunSpread()
{
	float para = 1;
	if (m_Host != NULL) {
		if (m_Host->getLocoMotion() != NULL && !m_Host->getLocoMotion()->m_OnGround)
		{
			para = 1.3f;
		}
		if (m_Host->getSneaking())
		{
			para = 0.667f;
		}
	}
	int tmp = (int)(para * m_CurrentSpread);
	if (m_GunDef && (tmp > m_GunDef->MaxSpread))
	{
		return m_GunDef->MaxSpread;
	}
	return tmp;
}

float GunUseComponent::getGunSpeedRatio()
{
	if (m_GunDef == NULL)
	{
		m_GunDef = GetDefManagerProxy()->getGunDef(m_Host->getCurToolID());
	}

	float v = (float)(1 - (m_GunDef->Weight * 0.001 - 0.5) / (m_GunDef->Weight * 0.001 - 0.5 + 30));
	return getZoom() ? v * GetLuaInterfaceProxy().get_lua_const()->kaijing_yidong_xishu : v;
}

bool GunUseComponent::isFirstShoot()
{
	if (m_GunDef == NULL)
	{
		return false;
	}

	if (m_GunDef->GunType == 10107)	// 狙击枪永远不偏移（当成第一枪）
	{
		return true;
	}

	return abs(m_CurrentSpread - m_GunDef->InitSpread) > 1.1f ? false : true;
}

void GunUseComponent::setGunInfo(float spread, float jaw, float pitch, Rainbow::Vector3f pos)
{
	if (spread >= 0)
		m_CurrentSpread = spread;
	m_CurrentJaw = jaw;
	m_CurrentPitch = pitch;
	m_CurrentPos = pos;
}

bool GunUseComponent::canUseGun(int gunId, unsigned int useTick)
{
	auto itr = m_GunCanFireTime.find(gunId);
	if (itr == m_GunCanFireTime.end())
		return true;

	return useTick >= itr->second;
}

void GunUseComponent::setGunUse(int gunId, unsigned int useTick)
{
	m_GunCanFireTime[gunId] = useTick;
}

void GunUseComponent::doGunFire(int id)
{
	if (!m_Host || !m_Host->getWorld())
	{
		return;
	}
	WCoord offset = WCoord::zero;
#ifndef IWORLD_SERVER_BUILD
	// 获取真正的枪口位置
	WCoord muzzlePos = getMuzzlePos();
	// 转换为相对于玩家的偏移量
	WCoord playerPos = m_Host->getEyePosition();
	offset = muzzlePos - playerPos;
#endif
	ProjectileFactory::throwItemByActor(m_Host->getWorld(), m_Host, 1, id, false, false, 0, offset);
}

int GunUseComponent::getGunUse(int gunId)
{
	const GunDef* def = GetDefManagerProxy()->getGunDef(gunId);
	if (def)
	{
		unsigned int FireInterval = def->FireInterval;
		SandboxResult result = SandboxCoreDriver::GetInstance().GetManagers().GetEventDispatcherMgr().Emit("WeaponSkin_System_firingrate", SandboxContext(nullptr)
			.SetData_Number("itemid", gunId)
			.SetData_Number("uin", m_Host ? m_Host->getUin() : 0)
		);
		if (result.IsExecSuccessed())
		{
			FireInterval = FireInterval - FireInterval * (float)result.GetData_Number("fvalue");
		}
		return FireInterval;
	}
	return 0;
}


void GunUseComponent::setUpdate()
{
#ifndef IWORLD_SERVER_BUILD
	m_nAimAssistType = m_nTmpAimAssistType;//todo by charles xie 这里之后读取设置配置
	//m_bIsAimAssist = true;//todo by charles xie 这里之后读取设置配置.二重设置判定，可以不管
	//m_bIsAimReduce = true;
	if (m_Host && g_pPlayerCtrl == m_Host)
	{
		auto GunFunction = [&](float dtime)
		{
			OnUpdate(dtime);
		};
		m_Host->setGunUseUpdate(GunFunction);
	}
#endif
}

// 辅助瞄准：
// - 以屏幕AABB与两级圆（小圈吸附/大圈减速）相交作为触发
// - 小圈：可自瞄/自动开火；大圈：降低灵敏度
// - 做掩体遮挡检测，避免穿墙
void GunUseComponent::CheckAimAssist(float dtime)
{
	if (!m_Host)
		return;

	auto pWorld = m_Host->getWorld();
	if (!pWorld || !pWorld->getActorMgr())
		return;

	PlayerControl* player = g_pPlayerCtrl;
	if (!player)
		return;

	if (!player->m_pCamera->isCameraVaild())
		return;

	float maxRange = GetGunRange();//射程
	MINIW::WorldRay worldray;
	MINIW::Ray ray;
	player->m_pCamera->getViewRayByScreenPt(&worldray, 0.5, 0.5);
	worldray.m_Range = maxRange;
	worldray.getRelativeRay(ray);
	maxRange *= maxRange;

	Vector3f origin = worldray.m_Origin.toVector3();
	Vector3f& dir = worldray.m_Dir;
	const WCoord& playerPos = player->getPosition();//玩家位置
	int clientWidth, clientHeight;
	GetClientInfoProxy()->getClientWindowSize(clientWidth, clientHeight);
	float ox = clientWidth * 0.5f;
	float oy = clientHeight * 0.5f;

	const std::vector<ClientPlayer*>& vPlayers = pWorld->getActorMgr()->ToCastMgr()->getAllPlayer();
	std::vector<ClientActor*> actors;
	pWorld->getActorMgr()->ToCastMgr()->getAllLiveActors(actors);
	size_t iPlayerSize = vPlayers.size();
	size_t iSize = vPlayers.size() + actors.size();
	ClientActor* pActor = nullptr;
	ClientActor* pSelector = nullptr;
	CollideAABB box;
	float ds = 0.f;
	float min_s = -1.f;
	bool needReduce = false;
	std::vector<int> checkedActors;
	float dx = 0.f;
	float dy = 0.f;

	float raduisMax = GetSlowAimRadius();
	float raduisMin = GetAssistAimRadius();

	for (size_t index = 0; index < iSize; index++)
	{
		if (index < iPlayerSize)
		{
			pActor = vPlayers[index];
		}
		else
		{
			pActor = dynamic_cast<ActorLiving*>(actors[index - iPlayerSize]);
		}
		if (!pActor || pActor->isDead())
			continue;
		if (pActor->getObjId() == player->getObjId())
			continue;
		//if (pActor->getTeam() == 0 || pActor->getTeam() != player->getTeam())
		{
			const WCoord& pos = pActor->getPosition();
			int iDistance = (pos.x - playerPos.x) * (pos.x - playerPos.x) + (pos.y - playerPos.y) * (pos.y - playerPos.y) + (pos.z - playerPos.z) * (pos.z - playerPos.z);//不做开方
			if (iDistance > maxRange)
				continue;

			//锐角,大概小于75°
			if (DotProduct(Rainbow::Vector3f(dir.x, 0, dir.z), Rainbow::Vector3f((float)(pos.x - playerPos.x), 0, (float)(pos.z - playerPos.z))) > 0.25f)
			{
				pActor->getHitCollideBox(box);
				Vector3f screenPosMin(0.f);
				Vector3f screenPosMax(0.f);
				Vector3f _pos = box.pos.toVector3();
				Vector3f _dim = box.dim.toVector3();
				getMinMaxPos(player->m_pCamera->getEngineCamera(), _pos, _dim, screenPosMin, screenPosMax);

				//如果包围盒超出屏幕不处理
				if (screenPosMin.x < 0 || screenPosMin.y < 0 || screenPosMax.x > clientWidth || screenPosMax.y > clientHeight)
					continue;

				//准星减速（开火）
				if (m_bIsAimReduce && !needReduce && checkOverlap(raduisMax, ox, oy, screenPosMin.x, screenPosMin.y, screenPosMax.x, screenPosMax.y))
				{
					needReduce = true;
				}

				//吸附自瞄圈20
				if (m_bIsAimAssist && checkOverlap(raduisMin, ox, oy, screenPosMin.x, screenPosMin.y, screenPosMax.x, screenPosMax.y))
				{
					Vector3f center(box.centerX(), box.centerY(), box.centerZ());
					Vector3f centerScreen = 0.5f * (screenPosMax + screenPosMin);

					ds = (centerScreen.x - ox) * (centerScreen.x - ox) + (centerScreen.y - oy) * (centerScreen.y - oy);
					if (min_s < 0.0f || min_s > ds)
					{
						Rainbow::Vector3f v((float)(center.x - origin.x), (float)(center.y - origin.y), (float)(center.z - origin.z));
						worldray.m_Range = v.Length();
						worldray.m_Dir = v;
						worldray.m_Dir.NormalizeSafe();
						IntersectResult result;
						if (pWorld->pickGround(worldray, &result))
							continue;

						min_s = ds;
						dx = centerScreen.x - ox;
						dy = centerScreen.y - oy;
						pSelector = pActor;
					}
				}
			}
		}
	}
	//选定了目标
	if (pSelector)
	{
		//自瞄吸附
		if (m_bIsAimAssist)
		{
			float t = 0.f;
			pSelector->getHitCollideBox(box);
			int iPick = ray.intersectBox(box.minPos().toVector3(), box.maxPos().toVector3(), &t);
			if (iPick < 0)
			{
				//移动镜头，定位时间50ms
				float fact = dtime / 0.1f;
				dx *= fact;
				dy *= fact;
				float sensitivity = GetAutoAimCameraSpeed();
				player->m_pCamera->rotate(dx * sensitivity / clientWidth, dy * sensitivity / clientHeight);
			}
		}
	}

	if (m_bIsAimReduce)
	{
		if (needReduce)
			ReduceSensitivity();
		else
			RevertSensitivity();
	}
}

// 获取射程（cm）：基础射程*100 并叠加射程加成
float GunUseComponent::GetGunRange()
{
	if (!m_GunDef) m_fAimRange = 10000.f;
	
	//float m_nAimRange = m_GunDef->Range * 100.f;
	// 可以在这里添加射程加成计算
	return m_fAimRange;
}

// 获取小圈半径：区分瞄准/腰射配置
float GunUseComponent::GetAssistAimRadius()
{
	PlayerControl* player = g_pPlayerCtrl;
	if (!player)
		return 0;

	bool isAimState = getZoom(); // 使用现有的瞄准状态检测
	if (isAimState)
	{
		//ads
		return m_fRaduisMinZoom;
	}
	//hip
	return m_fRaduisMin;
}

// 获取大圈半径：区分瞄准/腰射配置
float GunUseComponent::GetSlowAimRadius()
{
	PlayerControl* player = g_pPlayerCtrl;
	if (!player)
		return 0;

	bool isAimState = getZoom(); // 使用现有的瞄准状态检测
	if (isAimState)
	{
		//ads
		return m_fSlowRaduisMaxZoom;
	}
	//hip
	return m_fSlowRaduisMax;
}

// 获取自瞄相机转动速度：PC与移动端分别配置
float GunUseComponent::GetAutoAimCameraSpeed()
{
	if (GetClientInfoProxy()->isPC())
	{
		return m_fAutoAimCameraSpeedPC;
	}

	return m_fAutoAimCameraSpeedMobile;
}

// 设置"辅助瞄准/减速"灵敏度因子
void GunUseComponent::SetSensitivityForAssist(float factor)
{
	m_assistFactor = factor;
	SetSensitivity();
}

// 应用灵敏度：按平台读取基础灵敏度并乘以 scopeFactor*assistFactor
void GunUseComponent::SetSensitivity()
{
	if (!GetClientInfoProxy() || !g_pPlayerCtrl)
		return;

	if (GetClientInfoProxy()->isPC())
	{
		if (m_sensitivity < 0 || g_pPlayerCtrl->getPCControl()->IsSetSensitivity())
		{
			m_sensitivity = g_pPlayerCtrl->getPCControl()->getSensitivity();
		}
		g_pPlayerCtrl->getPCControl()->setSensitivity2(m_sensitivity * m_scopeFactor * m_assistFactor);
	}
	else
	{
		if (m_sensitivity < 0 || g_pPlayerCtrl->getTouchControl()->IsSetSensitivity())
		{
			m_sensitivity = g_pPlayerCtrl->getTouchControl()->getSensitivity();
		}
		g_pPlayerCtrl->getTouchControl()->setSensitivity2(m_sensitivity * m_scopeFactor * m_assistFactor);
	}
}

// 进入减速圈：降低灵敏度（读取不同平台的减速系数）
void GunUseComponent::ReduceSensitivity()
{
	PlayerControl* player = g_pPlayerCtrl;
	if (!player)
		return;

	if (m_isLowSensityvity)
		return;
	m_isLowSensityvity = true;

	if (GetClientInfoProxy()->isPC())
	{
		float slowAimLevel = m_fSlowModulusPC; //系数
		if (slowAimLevel <= 0)
			slowAimLevel = 1;
		SetSensitivityForAssist(slowAimLevel);
	}
	else
	{
		float slowAimLevel = m_fSlowModulusMobile; //系数
		if (slowAimLevel <= 0)
			slowAimLevel = 1;
		SetSensitivityForAssist(slowAimLevel);
	}
}

// 离开减速圈：恢复灵敏度
void GunUseComponent::RevertSensitivity()
{
	PlayerControl* player = g_pPlayerCtrl;
	if (!player)
		return;

	if (!m_isLowSensityvity)
		return;

	m_isLowSensityvity = false;
	if (GetClientInfoProxy()->isPC())
	{
		SetSensitivityForAssist(1);
	}
	else
	{
		SetSensitivityForAssist(1);
	}
}