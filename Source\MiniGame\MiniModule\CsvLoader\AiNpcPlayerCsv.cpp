#include "AiNpcPlayerCsv.h" 
#include "OgreUtils.h"
#include "Common/OgreStringUtil.h"
#include "defmanager.h"

using MINIW::CSVParser;
IMPLEMENT_LAZY_SINGLETON(AiNpcPlayerCsv)

AiNpcPlayerCsv::AiNpcPlayerCsv() 
{
	m_AiNpcPlayers.clear();
}

AiNpcPlayerCsv::~AiNpcPlayerCsv() 
{ 
	onClear();
}

// 解析物品列表字符串，格式: [2426:3030208:3030206] 或 []
void ParseItemList(const std::string& itemListStr, std::vector<int>& outItems)
{
	outItems.clear();
	core::string strs = itemListStr;
	std::string str = Rainbow::StringUtil::trim(strs);
	if (str.empty() || str == "[]") {
		return; // 空列表
	}

	// 检查格式是否正确 [item1:item2:item3]
	if (str.length() < 2 || str[0] != '[' || str[str.length()-1] != ']') {
		return;
	}

	// 提取括号内的内容
	std::string content = str.substr(1, str.length() - 2);
	content = Rainbow::StringUtil::trim(content);
	
	if (content.empty()) {
		return; // 空内容
	}

	// 按冒号分割物品ID
	std::vector<core::string> itemStrs = Rainbow::StringUtil::split(content.c_str(), ":");
	for (const auto& itemStr : itemStrs) {
		std::string trimmedItemStr = Rainbow::StringUtil::trim(itemStr);
		if (!trimmedItemStr.empty()) {
			int itemId = atoi(trimmedItemStr.c_str());
			if (itemId > 0) {
				outItems.push_back(itemId);
			}
		}
	}
}

void AiNpcPlayerCsv::onParse(CSVParser& parser) 
{ 
	parser.SetTitleLine(1);
	int numLines = parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		int id = parser[i]["ID"].Int();
		if(id == 0) continue;

		AiNpcPlayerDef def;
		def.ID = id;
		def.TypeName = ColumnLang(parser[i],"TypeName");
		
		// 解析快捷栏物品列表
		ParseItemList(parser[i]["ShortCut"].Str(), def.ShortCut);
		
		// 解析主背包物品列表
		ParseItemList(parser[i]["BackPack"].Str(), def.BackPack);
		
		// 解析其他字段
		def.HandTool = parser[i]["HandTool"].Int();
		def.HeadEquip = parser[i]["HeadEquip"].Int();
		def.ChestEquip = parser[i]["ChestEquip"].Int();
		def.LegEquip = parser[i]["LegEquip"].Int();
		def.WanderRange = parser[i]["WanderRange"].Int();
		def.AlertRange = parser[i]["AlertRange"].Int();
		def.DigSearchRange = parser[i]["DigSearchRange"].Int();
		def.DigDistance = parser[i]["DigDistance"].Int();
		
		// 解析挖掘方块列表
		ParseItemList(parser[i]["DigBlocks"].Str(), def.DigBlocks);
		
		m_AiNpcPlayers.AddRecord(def.ID, def);
	}

	auto iter = m_AiNpcPlayers.m_Records.begin();
	for(; iter!=m_AiNpcPlayers.m_Records.end(); iter++)
	{
		m_AiNpcPlayerTable.push_back(&iter->second);
	}
	
	// g_DefMgr.resetCrcCode(CRCCODE_AINPCPLAYER); // 暂时注释，需要在defmanager.h中添加对应的CRCCODE常量
} 

void AiNpcPlayerCsv::onClear() 
{ 
	m_AiNpcPlayerTable.clear();
} 

const char* AiNpcPlayerCsv::getName() 
{ 
    return "AiNpcPlayer"; 
} 

const char* AiNpcPlayerCsv::getClassName() 
{ 
    return "AiNpcPlayerCsv"; 
} 

int AiNpcPlayerCsv::getNum()
{
	load();
	return (int)m_AiNpcPlayerTable.size();
}

AiNpcPlayerDef *AiNpcPlayerCsv::getOriginal(int id)
{	
	load();
	AiNpcPlayerDef* def = m_AiNpcPlayers.GetRecord(id);
	return def;
}

DefDataTable<AiNpcPlayerDef> &AiNpcPlayerCsv::getAiNpcPlayers()
{	
	load();
	return m_AiNpcPlayers;
}

AiNpcPlayerDef *AiNpcPlayerCsv::get(int id, bool takeplace/* =false */)
{	
	load();

	AiNpcPlayerDef* def = m_AiNpcPlayers.GetRecord(id);
	if(def == NULL && takeplace)
	{
		// 如果找不到，可以返回默认配置
		return m_AiNpcPlayers.GetRecord(700000); // 使用第一个配置作为默认
	}

	return def;
}

const AiNpcPlayerDef *AiNpcPlayerCsv::getByIndex(int index)
{	
	load();

	assert(index >= 0 && index < getNum());
	return m_AiNpcPlayerTable[index];
}

AiNpcPlayerDef* AiNpcPlayerCsv::add(int id, AiNpcPlayerDef* templateDef)
{
	if (!templateDef)
		return nullptr;

	AiNpcPlayerDef tmpDef;
	tmpDef.ID = id;
	tmpDef.TypeName = templateDef->TypeName;
	tmpDef.ShortCut = templateDef->ShortCut;
	tmpDef.BackPack = templateDef->BackPack;
	tmpDef.HandTool = templateDef->HandTool;
	tmpDef.HeadEquip = templateDef->HeadEquip;
	tmpDef.ChestEquip = templateDef->ChestEquip;
	tmpDef.LegEquip = templateDef->LegEquip;
	tmpDef.WanderRange = templateDef->WanderRange;
	tmpDef.AlertRange = templateDef->AlertRange;
	tmpDef.DigSearchRange = templateDef->DigSearchRange;
	tmpDef.DigDistance = templateDef->DigDistance;
	tmpDef.DigBlocks = templateDef->DigBlocks;

	m_AiNpcPlayers.AddRecord(tmpDef.ID, tmpDef);

	auto iter = m_AiNpcPlayers.m_Records.begin();
	for (; iter != m_AiNpcPlayers.m_Records.end(); iter++)
	{
		if (iter->second.ID == id)
		{
			m_AiNpcPlayerTable.push_back(&iter->second);
			break;
		}
	}

	return m_AiNpcPlayers.GetRecord(id);
}
