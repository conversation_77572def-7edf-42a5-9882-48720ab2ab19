#ifndef __GUNUSE_COMPONENT_H__
#define __GUNUSE_COMPONENT_H__

#include "ActorComponent_Base.h"
#include "Math/Vector3f.h"
#include "SandboxGame.h"
#include "BasicIK.h"
#include "BaseClass/EventDispatcher.h"

#define AlotmRecoilData 1

struct ToolDef;
class ClientPlayer;
struct GunDef;
#include "SandboxCallback.h"

enum AimAssistType
{
	AIM_ASSIST_NONE = 0,
	AIM_ASSIST_ALLTIME = 1,
	AIM_ASSIST_IN_AIM_STATE = 2,
};

class EXPORT_SANDBOXGAME GunUseComponent;
class GunUseComponent : public ActorComponentBase//tolua_export
{//tolua_export
public:
	DECLARE_COMPONENTCLASS(GunUseComponent)

	GunUseComponent();
	~GunUseComponent();
	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner);
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner);

	void registerThirdPersonEvent();
	void unregisterThirdPersonEvent();

	void registerFirstPersonEvent();
	void unregisterFirstPersonEvent();
	/**
	* 游戏帧更新
	*/
	virtual void OnUpdate(float dt) override;
	const char& getComViewMode() const { return m_cViewMode; }
public:
	void reset();
	//tolua_begin
	void setGunDef(const GunDef* def);
	const GunDef* getGunDef();
	bool isUseGun() { return m_GunDef != nullptr; }
	int fireOnce(bool bAuto = false);
	int getMagazine();
	void setMagazine(int count);
	void updateMagazine();

	void doReload(int count = -1);
	void doCostBulletItem(int bulletid, int num);
	bool isFirstShoot();
	bool doWaterCanoonSkill();
	bool isInGunAction()
	{
		return m_IsFire || m_IsReload;
	}
	WCoord getMuzzlePos();
	void setIsReload(bool isReload);

	void setIsFire(bool isFire)
	{
		m_IsFire = isFire;
		if (isFire)
		{
			m_IsReload = false;
		}
	}

	bool isReload()
	{
		return m_IsReload;
	}

	bool isFire()
	{
		return m_IsFire;
	}

	int getGunSpread();
	void getDir(float& jaw, float& pitch, Rainbow::Vector3f& pos) { 
		jaw = m_CurrentJaw; 
		pitch = m_CurrentPitch;
#ifndef IWORLD_SERVER_BUILD
		// 对于弹道线，使用枪口位置而不是瞄准位置
		WCoord muzzlePos = getMuzzlePos();
		pos = muzzlePos.toVector3();
#else
		pos = m_CurrentPos;
#endif
	}
	void setGunInfo(float spread, float jaw, float pitch, Rainbow::Vector3f pos);
	float getGunSpeedRatio();

	void resetRecoil();
	void setZoom(bool open);
	bool getZoom();

	float getSightAim();

	//获取枪当前子弹量
	int getBulletNum();
	int getMaxMagazines();
	//当前是否能够换子弹
	bool canReload();
	int getReloadTime();

	int getFireInterval()
	{
		return m_FireInterval;
	}
	void setFireInterval(int fireInterval)
	{
		m_FireInterval = fireInterval;
	}

	float getCurrentSpread()
	{
		return m_CurrentSpread;
	}

	void setPulledGunId(int gunid) { m_nPulledGunId = gunid; }
	int  getPulledGunId() { return m_nPulledGunId; }
	bool canUseGun(int gunId, unsigned int useTick);
	void setGunUse(int gunId, unsigned int useTick);
	int getGunUse(int gunId);
	void clearGunCanFireTime() { m_GunCanFireTime.clear(); }
	//tolua_end
	void doGunFire(int id);
	void setUpdate();
	void increaseSpreadOnFire();
	void decreaseSpreadOnTime(float dt);
	void onUpdate(float dt);
	void OnAnimatorPostEvent(const Rainbow::EventContent* evt);
	void setComViewMode(char viewMode);
	
	// 自动瞄准相关方法
	void CheckAimAssist(float dtime);
	float GetAssistAimRadius();
	float GetSlowAimRadius();
	float GetAutoAimCameraSpeed();
	void ReduceSensitivity();
	void RevertSensitivity();
	void SetSensitivityForAssist(float factor);
	void SetSensitivity();
	float GetGunRange();
	
private:
	void openTelescope(float aim = 0.0f);
	void closeTelescope();

	bool doShootJob();

	bool isWaterCannon();
private:
	const GunDef* m_GunDef;
	const ToolDef* m_ToolDef;
	ClientPlayer* m_Host;
	float m_GunCoolDown;
	int m_Magazine;
	float m_CurrentSpread;
	float m_CurrentJaw;		// 客机传来的当前朝向
	float m_CurrentPitch;	// 客机传来的当前朝向
	Rainbow::Vector3f m_CurrentPos;	// 客机传来的当前位置
	int m_FireInterval;		//客机传来的换挡
	bool m_IsReload;
	bool m_IsFire;
	float m_LastRotateX;
	bool m_NeedCameraRecovery;
	bool m_ReloadRecovery;
	float m_LastFov;
	float m_LastSensitivity;
	int m_LastViewMode;
	int m_nPulledGunId;
	// 服务器射击时检测可射击时间
	std::map<int, unsigned> m_GunCanFireTime;

	float m_RecoilTotalRotate = 0.f;
	bool m_IsIKRecovery = false;

	BasicIK* m_pBasicIK = nullptr;
	bool m_IsRegisterAfterAnimationProcessEvent = false;
	char m_cViewMode = -1;

#if AlotmRecoilData
	//test 调试数据 完成要转到配置表中
	//第一人称
	float m_RecoilSpeedFirst = 0.1f;
	float m_MaxRecoilFirst = 3.f;
	float m_RecoilRecoverySpeedFirst = 0.4f;

	//右手数据
	Rainbow::Vector3f m_vRightHandOffsetFirst = Rainbow::Vector3f(0,5,-2);
	Rainbow::Vector3f m_vRightPoleRelativeToEffectorFirst = Rainbow::Vector3f(0.1,0.5,-0.1);
	float m_fRightWeightFirst = 0.5f;
	bool m_bRightStretchEnableFirst = true;
	float m_fRightStretchStartRatioFirst = 0.9f;
	float m_fRightStretchMaxRatioFirst = 1.05f;
	float m_fRightSecondaryAxisWeightFirst = 0.05f;

	//左手数据
	Rainbow::Vector3f m_vLeftHandOffsetFirst = Rainbow::Vector3f(0, 5, -2);
	Rainbow::Vector3f m_vLeftPoleRelativeToEffectorFirst = Rainbow::Vector3f(0.1, 0.5, -0.1);
	float m_fLeftWeightFirst = 0.5f;
	bool m_bLeftStretchEnableFirst = true;
	float m_fLeftStretchStartRatioFirst = 0.9f;
	float m_fLeftStretchMaxRatioFirst = 1.05f;
	float m_fLeftSecondaryAxisWeightFirst = 0.05f;


	//第三人称
	float m_RecoilSpeedThird = 0.f;
	float m_MaxRecoilThird = 0.f;
	float m_RecoilRecoverySpeedThird = 0.f;

	float m_RecoilSpeedThirdIK = 0.f;
	float m_MaxRecoilThirdIK = 0.f;

	//右手数据
	Rainbow::Vector3f m_vRightHandOffsetThird = Rainbow::Vector3f::zero;
	Rainbow::Vector3f m_vRightPoleRelativeToEffectorThird = Rainbow::Vector3f::zero;
	float m_fRightWeightThird = 1.f;
	bool m_bRightStretchEnableThird = false;
	float m_fRightStretchStartRatioThird = 0.f;
	float m_fRightStretchMaxRatioThird = 0.f;
	float m_fRightSecondaryAxisWeightThird = 0.f;

	//左手数据
	Rainbow::Vector3f m_vLeftHandOffsetThird = Rainbow::Vector3f::zero;
	Rainbow::Vector3f m_vLeftPoleRelativeToEffectorThird = Rainbow::Vector3f::zero;
	float m_fLeftWeightThird = 1.f;
	bool m_bLeftStretchEnableThird = false;
	float m_fLeftStretchStartRatioThird = 0.f;
	float m_fLeftStretchMaxRatioThird = 0.f;
	float m_fLeftSecondaryAxisWeightThird = 0.f;

	MNSandbox::Callback m_pTestCallback;
#endif
	bool m_bIsAimAssist = true; //加一层辅助瞄准判定

	//调试参数使用的临时过渡变量
	int m_nTmpAimAssistType = AIM_ASSIST_ALLTIME;

	int m_nAimAssistType = AIM_ASSIST_ALLTIME;
	bool m_bIsAimReduce = true;//是否准心进入最大瞄准圈后移动减速
	
	float m_fSlowRaduisMax = 100.f;//进行移动减速开始的最大瞄准圈。普通
	float m_fSlowRaduisMaxZoom = 100.f;//进行移动减速开始的最大瞄准圈。打开瞄准镜
	float m_fSlowModulusPC; //降低灵敏度的系数小于1大于0--电脑端
	float m_fSlowModulusMobile;// 降低灵敏度的系数小于1大于0--移动端
	float m_fRaduisMin = 30.f;//辅助瞄准最小瞄准圈。普通
	float m_fRaduisMinZoom = 30.f;//辅助瞄准最小瞄准圈。打开瞄准镜
	float m_fAutoAimCameraSpeedPC = 0.5f;//辅助瞄准时，准心向目标移动的速度--电脑端
	float m_fAutoAimCameraSpeedMobile = 0.5f;//辅助瞄准时，准心向目标移动的速度--移动端

	float m_fAimRange = 10000.f;//辅助瞄准最远距离

	// 自动瞄准相关成员变量
	float m_sensitivity = -1.f;           // 游戏设置的灵敏度
	bool m_isLowSensityvity = false;      // 是否处于低灵敏度状态
	float m_assistFactor = 1.f;           // 辅助瞄准灵敏度因子
	float m_scopeFactor = 1.f;            // 瞄准镜灵敏度因子
};//tolua_export

#endif