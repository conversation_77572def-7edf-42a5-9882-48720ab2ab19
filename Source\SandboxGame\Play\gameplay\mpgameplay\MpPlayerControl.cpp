#include "MpPlayerControl.h"
#include "PlayerControl.h"
#include "OgreBlock.h"
#include "world.h"
#include "GameCamera.h"
#include "Input/OgreInputManager.h"
#include "ClientInfoProxy.h"
#include "WorldManager.h"
#include "IClientGameManagerInterface.h"
#include "ClientItem.h"
#include "SkyPlane.h"
#include "backpack.h"
#include "CommonUtil.h"
#include "PlayerLocoMotion.h"
#include "ActorBody.h"
#include "ActorAttrib.h"
#include "ClientActorManager.h"
#include "BlockScene.h"
#include "CameraModel.h"
#include "TouchControl.h"
#include "OgreUtils.h"
#include "world_types.h"
#include "DebugDataMgr.h"
#include "blocks/BlockMaterialMgr.h"
#include "EffectManager.h"
#include "DefManagerProxy.h"
#include "ActorBoss.h"
#include "ActorHorse.h"
#include "blocks/BlockMaterialMgr.h"
#include "OgreTimer.h"
#include "OgreScriptLuaVM.h"
#include "ui_framemgr.h"
#include "ui_scriptfunc.h"
#include "Common/OgreShared.h"
#include "ActorTrainCar.h"
#include "GameNetManager.h"
#include "MpActorManager.h"
#include "PlayerAnimation.h"
#include "GunUseComponent.h"
#include "FullyCustomModelMgr.h"
#include "PackingFullyCustomModelMgr.h"
#include "OpenContainerComponent.h"
#include "ItemSkillComponent.h"
#include "AccountHorseComponent.h"
#include "InteractTamedMobComponent.h"
#include "PetSummonComponent.h"
#include "TransformersSkinComponent.h"
#include "ClientActorFuncWrapper.h"
#include "RiddenComponent.h"
#include "CarryComponent.h"
#include "IWorldConfigProxy.h"
#include "PlayerCheat.h"
#include "InputInfo.h"
#include "MoveControl.h"
#include "SkillComponent.h"

#include "ActorVehicleAssemble.h"
#include "ServerInterpolTick.h"
#include "IClientGameManagerInterface.h"
#include <cmath>
#include "SandBoxManager.h"
#include "SandboxIdDef.h"
#include "CustomGunUseComponent.h"
#include "proto_pb/proto_ch.pb.h"
#include "IMiniDeveloperProxy.h"
#include "PlayerAttrib.h"
#include "thinkingdata/GameAnalytics.h"
#include <map>
#ifdef WIN32
#define isnan _isnan
#endif

using namespace MINIW;

IMPLEMENT_SCENEOBJECTCLASS(MpPlayerControl)
MpPlayerControl::MpPlayerControl()
{
	m_LastBodyAnimWeapon = -1;
	m_LastBodyAnim[0] = -1;
	m_LastBodyAnim[1] = -1;
	memset(&m_LastMotion, 0, sizeof(MPMOVEMOTION));
	m_LastJetpackFlying = false;

	//m_CurContainer = NULL;
	m_nMoveTick = 0;
	m_LastActID = -1;
	m_LastActTriggerID = -1;
	m_iRefreshBySyncCustomData = 0;
	m_iRefreshTick = 0;
	m_LastSkinAnimSeq = 0; 

	//CreateComponent<MPAccountHorseComponent>("AccountHorseComponent");
	//CreateComponent<MPPetSummonComponent>("PetSummonComponent");
	//CreateComponent<MPInteractTamedMobComponent>("InteractTamedMobComponent");
	m_TransformerSkinComponent = CreateComponent<MPTransformersSkinComponent>("TransformersSkinComponent");
	m_pItemSkillComponent = CreateComponent<MPControlItemSkillComponent>("ControlItemSkillComponent");
	CreateComponent<MPControlOpenContainerComponent>("ControlOpenContainerComponent");
	m_FlownInThisGame = false;
}

MpPlayerControl::~MpPlayerControl()
{
	//m_CurContainer = NULL; 
}

void MpPlayerControl::enterWorld(World* pworld)
{
	Super::enterWorld(pworld);
	//避免在联机房间坐下之后，被强制杀端，而无法向主机发送 PB_PLAYER_SLEEP_XX
	if (getSitting())
	{
		WCoord blockpos = CoordDivBlock(getPosition());
		bool inChair = isSittingInChair();
		bool inCabin = isSittingInStarStationCabin() || getBody()->isInStarStationCabin();
		bool inPiano = m_sitingPianoPos == blockpos;
		if (!inChair && !inCabin && !inPiano)
		{
			tryStandup();
		}
	}
}

void MpPlayerControl::playAnim(int seq, bool include_me/* =false */, int seq1/* =127 */, int isLoop/* =-1 */)
{
	if(m_pWorld->isRemoteMode()) 
	{
 		PB_ActorAnimCH actorAnimCH;
		actorAnimCH.set_anim(seq);
		actorAnimCH.set_anim1(seq1);
		if (getBody())
		{
			actorAnimCH.set_actid(getBody()->getActID());
			actorAnimCH.set_actidtrigger(getBody()->getActTriggerID());
			actorAnimCH.set_sideact(getBody()->isSideAct()); //2021-09-14 codeby:chenwei 新增标记为副动作
			actorAnimCH.set_animseq(getBody()->getAnimSeq());//20210927 codeby:chenwei 新增动作序列
			actorAnimCH.set_isloop(isLoop);
		}
		actorAnimCH.set_send_immediate(true); // 立刻广播给他人
		GetGameNetManagerPtr()->sendToHost(PB_ACTOR_ANIM_CH, actorAnimCH, 0, UNRELIABLE_SEQUENCED);

		if(getBody()) getBody()->playAnim(seq, isLoop);
	}
	else PlayerControl::playAnim(seq, include_me, seq1, isLoop);
}

void MpPlayerControl::stopAnim(int seq, bool include_me)
{
	if (m_pWorld->isRemoteMode())
	{
		PB_ActorStopAnimCH actorStopAnimCH;
		actorStopAnimCH.set_anim(seq);
		actorStopAnimCH.set_actorid(m_Body->getActID());
		GetGameNetManagerPtr()->sendToHost(PB_ACTOR_STOP_ANIM_CH, actorStopAnimCH, 0, UNRELIABLE_SEQUENCED);
		if (m_Body)
		{
			m_Body->stopAnim(seq);
		}
	}
	else PlayerControl::stopAnim(seq, include_me);
}

#define set_change_flag_bit(changeFlag, bit, val) {if (val) changeFlag |= (1 << (bit - 1));}

void MpPlayerControl::tick()
{
	if (m_pWorld == NULL)
	{
		return;
	}
	PlayerControl::tick();

	if(!m_pWorld->isRemoteMode())
	{
		return;
	}
	if (GetIClientGameManagerInterface()->getICurGame()->getGameTick() % 100 == 99)
	{
		if (m_FlownInThisGame.IC())
		{
			jsonxx::Object log;
			log << "info" << m_FlownInThisGame.info();
			std::string log_str = log.json_nospace();
			SendActionLog2Host(false, "c_client_flown", log_str);
			LOG_INFO("checkCheat cheat flown; info:%s", log_str.c_str());
		}

		if (AntiSetting::BoolCheatProtect::getStaticNumber() != Rainbow::GetIClientInfo().GetClientVersion())
		{
			jsonxx::Object log;
			log << "number" << AntiSetting::BoolCheatProtect::getStaticNumber();
			std::string log_str = log.json_nospace();
			SendActionLog2Host(false, "c_client_bool_protect", log_str);
			LOG_INFO("checkCheat AntiSetting::BoolCheatProtect::getStaticNumber; info:%s", log_str.c_str());
		}

		#define check_flag_match_upload(id, value) { \
			if (!AntiSetting::checkFlagMatch(id, value)){ \
				jsonxx::Object log; \
				log << "id" << id; \
				log << "v" << value; \
				log << "from" << #value; \
				SendActionLog2Host(false, "c_flag_" #id , log.json_nospace()); \
			} \
		}

		PlayerLocoMotion *loc = static_cast<PlayerLocoMotion *>(getLocoMotion());
		check_flag_match_upload(5, getFlying());
		check_flag_match_upload(5, getFlagBit(ACTORFLAG_FLY));
		// check_flag_match_upload(11, getSneaking());
		// check_flag_match_upload(11, getFlagBit(ACTORFLAG_SNEAK));
		check_flag_match_upload(13, loc->getMoveType());
		check_flag_match_upload(20, m_FlownInThisGame);
		check_flag_match_upload(33, loc->m_Position.x + loc->m_Position.y + loc->m_Position.z);
	}
	m_nMoveTick++;
	WCoord pos = CoordDivBlock(getPosition());
	bool mustSync = false;
	auto functionWrapper = getFuncWrapper();
	if (functionWrapper)
	{
		mustSync = functionWrapper->getMustSyncPos();
	}
	if (m_pWorld->getChunk(pos) == NULL && !mustSync)
	{
		// 避免长时间不发送移动协议
		if (m_nMoveTick > 100)
		{
			m_nMoveTick = 0;
			PB_RoleMoveCH roleMoveCH;
			PB_MoveMotion* moveMotion = roleMoveCH.mutable_movemotion();
			moveMotion->set_changeflags(0);
			GameNetManager::getInstance()->sendToHost(PB_ROLE_MOVE_CH, roleMoveCH);
		}
		return;
	}

	PlayerLocoMotion *loc = static_cast<PlayerLocoMotion *>(getLocoMotion());

	auto RidComp = getRiddenComponent();
	if (RidComp && RidComp->isRiding())
	{
		ActorTrainCar *car = dynamic_cast<ActorTrainCar *>(RidComp->getRidingActor());

		ActorVehicleAssemble *vehicle = dynamic_cast<ActorVehicleAssemble *>(RidComp->getRidingActor());
		WCoord railknot;
		int outindex;
		int flags;
		float t;
		if (car && car->getOnRailState(railknot, outindex, t, flags))
		{
			if (car->m_HeadCar == 0)
			{
				PB_TrainMoveCH trainMoveCH;
				trainMoveCH.set_objid(car->getObjId());
				trainMoveCH.set_mapid(m_pWorld->getCurMapID());
				trainMoveCH.set_outindex(outindex);
				trainMoveCH.set_carreverse(flags);
				trainMoveCH.set_curvet(t);
				trainMoveCH.set_motionx(car->getLocoMotion()->m_Motion.x);
				trainMoveCH.set_motiony(car->getLocoMotion()->m_Motion.y);
				trainMoveCH.set_motionz(car->getLocoMotion()->m_Motion.z);

				PB_Vector3* pRailKnot = trainMoveCH.mutable_railknot();
				pRailKnot->set_x(railknot.x);
				pRailKnot->set_y(railknot.y);
				pRailKnot->set_z(railknot.z);

				GetGameNetManagerPtr()->sendToHost(PB_TRAIN_MOVE_CH, trainMoveCH);
			}
		}
		// 条件：1、物理机械；2、驾驶座是该玩家 
		// 单独发送物理机械信息
		/*else if (vehicle && vehicle->getRiddenByActor() == g_pPlayerCtrl)
		{
			PB_PlayerVehicleMoveInputCH playerVehicleMoveCH;
			playerVehicleMoveCH.set_moveforward(loc->m_MoveForward);
			playerVehicleMoveCH.set_movestrafing(loc->m_MoveStrafing);
			GetGameNetManagerPtr()->sendToHost(PB_PLAYER_VEHICLE_MOVEINPUT_CH, playerVehicleMoveCH);
		}*/
		else
		{
			PB_PlayerMoveInputCH playerMoveInputCH;
			playerMoveInputCH.set_moveforward(loc->m_MoveForward);
			playerMoveInputCH.set_movestrafing(loc->m_MoveStrafing);
			playerMoveInputCH.set_jumping(loc->m_isJumping);
			playerMoveInputCH.set_sneaking(0);

			GetGameNetManagerPtr()->sendToHost(PB_PLAYER_MOVEINPUT_CH, playerMoveInputCH);
		}
	}

	if (!isNewMoveSyncSwitchOn())
	{
		GetIWorldConfigProxy()->VmpBeginUltra("PlayerPosSend");
		PB_RoleMoveCH roleMoveCH;
		if (functionWrapper)
		{
			roleMoveCH.set_speed(functionWrapper->getAIMoveSpeed());
		}
		PB_MoveMotion* moveMotion = roleMoveCH.mutable_movemotion();
		PB_Vector3* position = moveMotion->mutable_position();
		position->set_x(loc->m_Position.x);
		position->set_y(loc->m_Position.y);
		position->set_z(loc->m_Position.z);
		if (loc->add_motion.x != 0 || loc->add_motion.y != 0 || loc->add_motion.z != 0)
		{
			PB_Vector3 *addmotion = roleMoveCH.mutable_addmotion();
			if (addmotion)
			{
				addmotion->set_x(loc->add_motion.x);
				addmotion->set_y(loc->add_motion.y);
				addmotion->set_z(loc->add_motion.z);
			}
		}
		moveMotion->set_pitch(AngleFloat2Char(loc->m_RotationPitch));
		moveMotion->set_yaw(AngleFloat2Char(loc->m_RotateYaw));
		int changeflag = 0;
		bool needSync = false;

		if (m_bLastOnVehicle != loc->isOnVehicle())
		{
			needSync = true;
			m_bLastOnVehicle = loc->isOnVehicle();
		}

		// 大于5秒强制同步一次，避免一直不同步位置，客户端可以屏蔽上报位置
		if (m_nMoveTick > 100 || mustSync)
		{
			needSync = true;
			functionWrapper->setMustSyncPos(false);
			m_nMoveTick = 0;
		}
		if (m_nMoveTick > 40 && !loc->m_OnGround)
		{
			set_change_flag_bit(changeflag, 1, true);
			m_nMoveTick = 0;
		}

		if (!(RidComp && RidComp->isRiding()) && (abs(m_LastMotion.Position.X - position->x()) + abs(m_LastMotion.Position.Y - position->y()) + abs(m_LastMotion.Position.Z - position->z())) > 6)
		{
			set_change_flag_bit(changeflag, 1, true);
		}
		
		
		if (mustSync)
		{
			set_change_flag_bit(changeflag, 1, true);
			functionWrapper->setMustSyncPos(false);
		}
		if (!loc->add_motion.isZero())
		{
			set_change_flag_bit(changeflag, 1, true);
		}

		if (moveMotion->pitch() != m_LastMotion.Pitch || moveMotion->yaw() != m_LastMotion.Yaw)
		{
			set_change_flag_bit(changeflag, 2, true);
		}

		bool JetpackFlying = false;
		//auto functionWrapper = getFuncWrapper();
		if (functionWrapper)
		{
			JetpackFlying = functionWrapper->getJetpackFlying();
		}
		if (needSync || changeflag != 0 || m_LastJetpackFlying != JetpackFlying)
		{
			if (loc->isOnVehicle())
			{
				loc->refreshPos();
				WCoord relativeVehiclePos = loc->getRelativeVehiclePos();
				PB_Vector3* vehiclepos = roleMoveCH.mutable_vehiclepos();
				if (vehiclepos)
				{
					vehiclepos->set_x(relativeVehiclePos.x);
					vehiclepos->set_y(relativeVehiclePos.y);
					vehiclepos->set_z(relativeVehiclePos.z);
				}
			}

			set_change_flag_bit(changeflag, 3, JetpackFlying);   // 火箭背包
			set_change_flag_bit(changeflag, 4, loc->m_OnGround);   // 是否落地
			set_change_flag_bit(changeflag, 5, getFlying());       // 是否飞行
			set_change_flag_bit(changeflag, 6, loc->m_OnVehicle);  // 是否在载具上
			set_change_flag_bit(changeflag, 7, loc->m_OnLadder);   // 是否在梯子上
			set_change_flag_bit(changeflag, 8, loc->noClip());     // clip属性
			if (AntiSetting::gCheatConfig.SwitchCheckClipState > 0){
				set_change_flag_bit(changeflag, 9, loc->checkCheatClip());       // 是否使用过穿透移动
			}
			set_change_flag_bit(changeflag, 10, loc->isInLiquid());     // 是否在液体中
			set_change_flag_bit(changeflag, 11, getSneaking());     // 是否潜行

			// 为避免外挂直接简单修改changeflag的值 后面未作弊标记为1 
			set_change_flag_bit(changeflag, 12, !loc->checkMoveTypeCheat());     // 移动数据检测 
			int moveType = loc->getMoveType();
			set_change_flag_bit(changeflag, 13, moveType == 1);  // 水中移动
			set_change_flag_bit(changeflag, 14, moveType == 2);  // 飞行
			set_change_flag_bit(changeflag, 15, moveType == 3);  // 普通
			set_change_flag_bit(changeflag, 16, !loc->checkGravityCheat());  // 重力
			set_change_flag_bit(changeflag, 17, loc->getUseGravity());  // 重力

			set_change_flag_bit(changeflag, 18, loc->m_UseOnGround.Val());  // onground校验
			set_change_flag_bit(changeflag, 19, loc->m_InRun);
			set_change_flag_bit(changeflag, 20, m_FlownInThisGame);
			loc->m_UseOnGround.Set(0);

			// 用于验证 防止外挂随意修改changeflag
			int rc = getUin();
			set_change_flag_bit(changeflag, 29, rc & 1);
			set_change_flag_bit(changeflag, 30, rc & 2);
			set_change_flag_bit(changeflag, 31, rc & 4);

			moveMotion->set_changeflags(changeflag);
			moveMotion->set_mapid(getCurMapID() + 1);

			GetGameNetManagerPtr()->sendToHost(PB_ROLE_MOVE_CH, roleMoveCH);

			loc->add_motion.x = 0;
			loc->add_motion.y = 0;
			loc->add_motion.z = 0;
			m_LastMotion.MapID = moveMotion->mapid();
			m_LastMotion.Pitch = moveMotion->pitch();
			m_LastMotion.Yaw = moveMotion->yaw();
			m_LastMotion.ChangeFlags = moveMotion->changeflags();
			m_LastMotion.Position.X = moveMotion->position().x();
			m_LastMotion.Position.Y = moveMotion->position().y();
			m_LastMotion.Position.Z = moveMotion->position().z();

			m_LastJetpackFlying = JetpackFlying;
			loc->checkCheckClipHost();
		}
		GetIWorldConfigProxy()->VmpEnd();
	}

	// seq变化即重新同步动画
	int seqid0 = getBody()->getCurSeqID(0);
	int seqid1 = getBody()->getCurSeqID(1);
	if (m_LastBodyAnim[0] != seqid0 || m_LastBodyAnim[1] != seqid1 ||
		m_LastActID != getBody()->getActID() || 
		m_LastActTriggerID != getBody()->getActTriggerID() || 
		m_LastSkinAnimSeq != getBody()->getNowPlaySeqID()||
		m_LastBodyAnimWeapon != getBody()->getCurAnimWeapon()) 
	{
		PB_ActorAnimCH actorAnimCH;
		int anim0 = getBody()->getCurAnim(0);
		int anim1 = getBody()->getCurAnim(1);
		int animWeapon = getBody()->getCurAnimWeapon();
		int actid = getBody()->getActID();
		int triggerId = getBody()->getActTriggerID();
		int animSeq = getBody()->getNowPlaySeqID();
		actorAnimCH.set_anim(anim0);
		if (m_LastBodyAnim[1] != seqid1)
		{
			actorAnimCH.set_anim1(anim1);
		}
		if (m_LastBodyAnimWeapon != animWeapon)
		{
			actorAnimCH.set_animweapon(animWeapon);
		}
		if (m_LastActID != actid)
		{
			actorAnimCH.set_actid(actid);
		}
		if (m_LastActTriggerID != triggerId)
		{
			actorAnimCH.set_actidtrigger(triggerId);
		}
		if (m_LastSkinAnimSeq != animSeq) //20210923 codeby:chenwei ???????????????????????
		{
			actorAnimCH.set_actid(actid);
			actorAnimCH.set_anim(anim0);
			actorAnimCH.set_anim1(anim1);
			
		}
		actorAnimCH.set_animseq(animSeq); //20210927 codeby:chenwei ????????????????????
		actorAnimCH.set_sideact(getBody()->isSideAct());

		GetGameNetManagerPtr()->sendToHost(PB_ACTOR_ANIM_CH, actorAnimCH);

		m_LastBodyAnim[0] = seqid0;
		m_LastBodyAnim[1] = seqid1;
		m_LastBodyAnimWeapon = animWeapon;
		m_LastActID = actid;
		m_LastActTriggerID = triggerId;
		m_LastSkinAnimSeq = animSeq;
	}


	if (m_iRefreshBySyncCustomData > 0)
	{
		m_iRefreshTick++;
		if (m_iRefreshTick >= 20)
		{
			m_iRefreshTick = 0;

			//场景方块刷新
			//g_BlockMtlMgr.refreshCustomBlock();
			if (g_pPlayerCtrl && g_pPlayerCtrl->getIWorld() && m_iRefreshBySyncCustomData == 2)
				g_pPlayerCtrl->getIWorld()->resetGenMesh(g_pPlayerCtrl->GetPlayerControlPosition());

			//刷新手上模型
			if (g_WorldMgr)
				g_WorldMgr->resetAllPlayerHandModel();
			if (g_pPlayerCtrl)
				g_pPlayerCtrl->resetHandModel();

			//通知快捷栏重新设置图标
			//ge GameEventQue::GetInstance().postBackpackChange(-1);
			MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
				SetData_Number("grid_index", -1);
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_CHANGE", sandboxContext);
			}


			m_iRefreshBySyncCustomData = 0;
		}
	}
}

unsigned MpPlayerControl::genMoveID()
{
	static unsigned move_id = 0;
	return move_id++;
}
void MpPlayerControl::syncMove2Host()
{
	auto loc = getLocoMotion();
	if (!loc || !m_MoveControl)
		return;

	game::ch::PB_MoveSyncCH pbMoveSync;

	bool need_sync = false;
	unsigned char bits = 0;
	if (m_MoveControl->statusChanged())
	{ // 按键变化, 前后左右移动跳跃等
		auto move_tick = pbMoveSync.mutable_move_opera();
		move_tick->set_yaw((int)(m_MoveControl->getYaw() * 1000));
		move_tick->set_pitch((int)(m_MoveControl->getPitch() * 1000));
		move_tick->set_opera(m_MoveControl->toBits());
		need_sync = true;

		bits |= 1;
		m_MoveControl->recordSentYawPitch();
	}

	auto &flag_changed = m_MoveControl->getFlagChange();
	if (!flag_changed.empty())
	{ // 状态变化, 飞行/潜行/奔跑等
		for (auto it = flag_changed.begin(); it != flag_changed.end(); ++it)
		{
			auto change = pbMoveSync.add_flag_change();
			change->set_type(it->first);
			change->set_on(it->second);
		}
		m_MoveControl->clearFlagChange();
		need_sync = true;
		bits |= 4;
	}
	// 跑步存在临时打断状态，需要每次同步
	auto change = pbMoveSync.add_flag_change();
	change->set_type(ACTORFLAG_RUN);
	change->set_on(getRun());

	unsigned long long tick = GetIClientGameManagerInterface()->getICurGame()? GetIClientGameManagerInterface()->getICurGame()->getGameTick(): 0;
	bool tick_match = tick % m_MoveControl->clientGetMoveSyncInterval() == 0;
	if (!m_MoveControl->getCurrentStatus().empty() && tick_match)
	{ // 按键状态下, 定时同步
		need_sync = true;
		bits |= 16;
	}
	if (m_MoveControl->isNeedSync())
	{
		need_sync = true;
		bits |= 32;
		m_MoveControl->clearNeedSync();
	}

	bool not_sync_pos = false;
	auto sit = GetComponent<ServerInterpolTick>();
	if ((sit  && sit->isMoving())
		|| (getRiddenComponent() && getRiddenComponent()->isRiding()))
		not_sync_pos = true;
	
	if (!not_sync_pos)
	{
		const auto& pos = m_MoveControl->getPrePosition();
		if (need_sync || (tick_match && m_MoveControl->positionChanged(pos)))
		{
			auto pb_pos = pbMoveSync.mutable_pos();
			pb_pos->set_x(pos.x);
			pb_pos->set_y(pos.y);
			pb_pos->set_z(pos.z);

			need_sync = true;
			bits |= 2;
			m_MoveControl->setLastPosition(pos);
		}
	}

	if (need_sync)
	{
		// unsigned id = genMoveID();
		pbMoveSync.set_id(0);
		pbMoveSync.set_tick(tick);
		GetGameNetManagerPtr()->sendToHost(PB_SYNC_MOVE_CH, pbMoveSync);
		//LOG_INFO("[%llu]hltest send move sync to host, bit:%d", tick, int(bits));
	}
}

bool MpPlayerControl::interactActor(ClientActor *target, int interactType, bool interactplot /* = false */)
{
	if(!PlayerControl::interactActor(target, interactType, interactplot)) return false;

	if(m_pWorld->isRemoteMode() && target != NULL)
	{
		PB_ActorInteractCH actorInteractCH;
		actorInteractCH.set_itype(interactType);
		actorInteractCH.set_target(target->getObjId());
		actorInteractCH.set_iplot(interactplot ? 1 : 0);

		/*int viewMode = getCamera() != NULL ? getCamera()->getMode() : CAMERA_FPS;
		if (viewMode == CAMERA_FPS
			|| viewMode == CAMERA_CUSTOM_VIEW && getCameraConfigOption(CAMERA_OPTION_INDEX_CROSSHAIR) == CCT_SCREEN_CENTER)
		{
			if(m_pCamera){
				actorInteractCH.set_curyaw(m_pCamera->m_RotateYaw + 180.0f);
				actorInteractCH.set_curpitch(m_pCamera->m_RotatePitch);
			}
		}
		else
		{  
			if(getLocoMotion() != NULL){
				actorInteractCH.set_curyaw(getLocoMotion()->m_RotateYaw);
				actorInteractCH.set_curpitch(getLocoMotion()->m_RotationPitch);
			}
		}*/

		MINIW::WorldRay ray;
		if (m_ViewMode == CAMERA_TPS_OVERLOOK)
		{
			ray.m_Origin = (getPosition() + WCoord(0, BLOCK_SIZE / 2, 0)).toWorldPos();
			ray.m_Dir = m_pCamera->getLookDir();
		}
		else if (m_ViewMode == CAMERA_CUSTOM_VIEW
			&& getCameraConfigOption(CAMERA_OPTION_INDEX_CROSSHAIR) == CCT_BODY_EYES)
		{
			ray.m_Origin = (getPosition() + WCoord(0, BLOCK_SIZE / 2, 0)).toWorldPos();
			ray.m_Dir = getLocoMotion()->getLookDir();
		}
		else
		{
			m_pCamera->getViewRayByScreenPt(&ray, m_CurMouseX, m_CurMouseY);
		}
			
			
		float rotateyaw;
		float rotationpitch;
		Direction2PitchYaw(&rotateyaw, &rotationpitch, ray.m_Dir);
		actorInteractCH.set_curyaw(rotateyaw);
		actorInteractCH.set_curpitch(rotationpitch);

		PB_Vector3* curPos = actorInteractCH.mutable_curpos();
		if(curPos)
		{
			/*WCoord pos = g_pPlayerCtrl->getCamera()->getEyePos();
			Rainbow::Vector3f camlookdir = g_pPlayerCtrl->getCamera()->getLookDir();
			ClientActor *riding = g_pPlayerCtrl->getRidingActor();
			Rainbow::Quaternionf quat;
			if(riding && riding->getRiddenBindRot(g_pPlayerCtrl, quat))
			{
				quat.rotate(camlookdir, camlookdir);
			}

			CameraControlMode viewmode = g_pPlayerCtrl->getCamera()->getMode();
			if (viewmode == CAMERA_TPS_BACK)
			{
				Vector2 deltaXZ(pos.x - g_pPlayerCtrl->GetPlayerControlPosition().x, pos.z - g_pPlayerCtrl->GetPlayerControlPosition().z);
				pos += camlookdir * deltaXZ.length() * 1.1f;
			}
			else if (viewmode == CAMERA_FPS)
			{
				pos += BLOCK_SIZE * camlookdir;
			}
			else if (viewmode == CAMERA_TPS_OVERLOOK)
			{
				pos = getPosition() + WCoord(0, BLOCK_SIZE / 2, 0);
			}
			else if (viewmode == CAMERA_CUSTOM_VIEW)
			{
				pos = WCoord(getEyePosition());
			}
			*/
			WCoord pos = ray.m_Origin.toVector3();
			curPos->set_x(pos.x);
			curPos->set_y(pos.y);
			curPos->set_z(pos.z);
		}
		PB_Vector3* collidePos = actorInteractCH.mutable_collidepos();
		if (collidePos)
		{
			collidePos->set_x(m_PickResult.collide_pos.x);
			collidePos->set_y(m_PickResult.collide_pos.y);
			collidePos->set_z(m_PickResult.collide_pos.z);
		}
		GetGameNetManagerPtr()->sendToHost(PB_ACTOR_INTERACT_CH, actorInteractCH);
	}
	return true;
}

bool MpPlayerControl::rightClickUpInteract(ClientActor* pTarget)
{
	if (pTarget)
	{
		pTarget->rightClickUpInteract(this);
		
		if (m_pWorld->isRemoteMode())
		{
			PB_RClickUpInteractCH rClickUpInteractCH;
			rClickUpInteractCH.set_target(pTarget->getObjId());
			GetGameNetManagerPtr()->sendToHost(PB_RCLICKUP_INTERACT_CH, rClickUpInteractCH);
		}		
	}	
	return true;
}

//PC鼠标事件
bool MpPlayerControl::mouseEventTrigger(game::ch::PCMouseKeyType keyType, game::ch::PCMouseEventType eventType) 
{
	if (!m_pWorld)
		return false;

	if (m_pWorld->isRemoteMode())
	{
		PB_PCMouseEventCH event;
		event.set_target(getObjId());
		event.set_keytype(keyType);
		event.set_eventtype(eventType);
		GetGameNetManagerPtr()->sendToHost(PB_MOUSE_EVENT_CH, event);
	}
	else {
		ClientActor::mouseEventTrigger(keyType, eventType);
	}

	return true;
}

bool MpPlayerControl::triggerBlock(const WCoord& targetblock, DirectionType targetface, const Rainbow::Vector3f& colpoint)
{
	OPTICK_EVENT();
	if (m_pWorld->isRemoteMode())
	{
		m_PlayerAnimation->performDig();

		PB_BlockInteractCH blockInteractCH;
		blockInteractCH.set_face(targetface);
		blockInteractCH.set_colptx(char(colpoint.x * 100));
		blockInteractCH.set_colpty(char(colpoint.y * 100));
		blockInteractCH.set_colptz(char(colpoint.z * 100));

		if (getCurToolID() == ItemIDs::BLUEPRINT)//建筑图纸
		{
			blockInteractCH.set_blueprintid(getPlayerAttrib()->getCurBuildingId());
			blockInteractCH.set_blueprintplacediridx(getPlayerAttrib()->getRotateBuildingPreview());
		}

		PB_Vector3* blockPos = blockInteractCH.mutable_blockpos();
		blockPos->set_x(targetblock.x);
		blockPos->set_y(targetblock.y);
		blockPos->set_z(targetblock.z);

		GetGameNetManagerPtr()->sendToHost(PB_BLOCK_TRIGGER_CH, blockInteractCH);
		return true;
	}
	return PlayerControl::triggerBlock(targetblock, targetface, colpoint);
}

bool MpPlayerControl::interactBlock(const WCoord &targetblock, DirectionType targetface, const Rainbow::Vector3f &colpoint)
{
    OPTICK_EVENT();
	if (m_pWorld->isRemoteMode())
	{
		// m_PlayerAnimation->performDig();

		PB_BlockInteractCH blockInteractCH;
		blockInteractCH.set_face(targetface);
		blockInteractCH.set_colptx(char(colpoint.x * 100));
		blockInteractCH.set_colpty(char(colpoint.y * 100));
		blockInteractCH.set_colptz(char(colpoint.z * 100));

		if (getCurToolID() == ItemIDs::BLUEPRINT)//建筑图纸
		{
			int blueprintId = getPlayerAttrib()->getCurBuildingId();
			blockInteractCH.set_blueprintid(blueprintId);
			blockInteractCH.set_blueprintplacediridx(getPlayerAttrib()->getRotateBuildingPreview());
			
			// 添加建筑行为数据统计
			const ArchitecturalBlueprintCsvDef* blueprintDef = GetDefManagerProxy()->getArchitecturalBlueprintCsvDef(blueprintId);
			if (blueprintDef) {
				// 获取消耗资源信息
				std::map<int, int> requiredMaterials = blueprintDef->getRequiredMaterials();
				
				// 构建消耗资源的JSON字符串
				std::string costJson = "[";
				bool first = true;
				for (const auto& material : requiredMaterials) {
					if (!first) {
						costJson += ",";
					}
					costJson += "{\"item_id\":" + std::to_string(material.first) + ",\"count\":" + std::to_string(material.second) + "}";
					first = false;
				}
				costJson += "]";
				
				// 构建位置信息的JSON字符串
				std::string locationJson = "{\"x\":" + std::to_string(targetblock.x) + ",\"y\":" + std::to_string(targetblock.y) + ",\"z\":" + std::to_string(targetblock.z) + "}";

				// 建筑行为数据统计埋点
				GameAnalytics::TrackEvent("building_action", {
					{"action_type", GameAnalytics::Value(0)},                    // int 0是放置建筑
					{"bp_type", GameAnalytics::Value(blueprintId)},              // 蓝图类型  墙面、地板、楼梯
					{"tool_id", GameAnalytics::Value(ItemIDs::BLUEPRINT)},       // 使用的工具ID
					{"location", GameAnalytics::Value(locationJson.c_str())},    // 方块坐标
					{"cost", GameAnalytics::Value(costJson.c_str())},            // 消耗的资源列表
					{"uin", GameAnalytics::Value(getUin())}                      // 玩家UIN
				});
			}
		}

		PB_Vector3* blockPos = blockInteractCH.mutable_blockpos();
		blockPos->set_x(targetblock.x);
		blockPos->set_y(targetblock.y);
		blockPos->set_z(targetblock.z);

		GetGameNetManagerPtr()->sendToHost(PB_BLOCK_INTERACT_CH, blockInteractCH);
	}
	// 	return true;
	return PlayerControl::interactBlock(targetblock, targetface, colpoint);
}

bool MpPlayerControl::attackBlock(const WCoord& blockpos, DIG_METHOD_T dgmethod, ATTACK_TYPE attacktype)
{
	if (m_pWorld && m_pWorld->isRemoteMode())
	{
		PB_BlockAttackCH blockAttackCH;
		PB_Vector3* blockPos = blockAttackCH.mutable_blockpos();
		blockPos->set_x(blockpos.x);
		blockPos->set_y(blockpos.y);
		blockPos->set_z(blockpos.z);
		blockAttackCH.set_dgmethod(dgmethod);

		GetGameNetManagerPtr()->sendToHost(PB_BLOCK_ATTACK_CH, blockAttackCH);
	}

	return PlayerControl::attackBlock(blockpos, dgmethod, attacktype);
}

//bool MpPlayerControl::exploitBlock(const WCoord &targetblock, DirectionType targetface, int status, int pickType)
//{
//	if (!PlayerControl::exploitBlock(targetblock, targetface, status, pickType)) return false;
//
//	if (m_pWorld->isRemoteMode())
//	{
//		PB_BlockExploitCH blockExploitCH;
//		blockExploitCH.set_face(targetface);
//		blockExploitCH.set_status(status);
//
//		PB_Vector3* blockPos = blockExploitCH.mutable_blockpos();
//		blockPos->set_x(targetblock.x);
//		blockPos->set_y(targetblock.y);
//		blockPos->set_z(targetblock.z);
//
//		blockExploitCH.set_picktype(pickType);
//
//		GameNetManager::getInstance()->sendToHost(PB_BLOCK_EXPLOIT_CH, blockExploitCH);
//	}
//	return true;
//}

bool MpPlayerControl::doSpecialSkill()
{
	if(!PlayerControl::doSpecialSkill()) return false;
	if(m_pWorld && m_pWorld->isRemoteMode())
	{
		PB_SpecialSkillCH skillCH;
		skillCH.set_status(0);
		return GetGameNetManagerPtr()->sendToHost(PB_PLAYER_SPECIAL_SKILL_CH, skillCH);
	}
	return false;
}

void MpPlayerControl::doWakeUp()
{
	if (!m_pWorld) return;

	if (!m_pWorld->isRemoteMode())
	{
		PlayerControl::doWakeUp();
	}
	else
	{
		PB_Empty msg;
		//craftItemCH.set_craftid(craftid);
		//craftItemCH.set_num(num);//code_by：huangfubin 客机少设置了参数。

		GetGameNetManagerPtr()->sendToHost(PB_PLAYER_WAKE_UP_CH, msg);
		PlayerControl::doWakeUp();
	}
}

bool MpPlayerControl::useItem(int itemid, int status, bool onshift/* =false */, unsigned int useTick/*=0*/)
{
	useTick = Rainbow::Timer::getSystemTick();
	if (!PlayerControl::useItem(itemid, status, onshift, useTick)) return false;

	if(m_pWorld && m_pWorld->isRemoteMode())
	{
		syncUseItemInfo(itemid, status, onshift, useTick);
	}

	return true;
}

bool MpPlayerControl::usePackingFCMItem(int itemid, WCoord usepos)
{
	if(PackingFullyCustomModelMgr::GetInstancePtr() && PackingFullyCustomModelMgr::GetInstancePtr()->isLimit())
	{
		//ge GetGameEventQue().postInfoTips(16010);
		CommonUtil::GetInstance().PostInfoTips(16010);
		return false;
	}

	if (!PlayerControl::usePackingFCMItem(itemid, usepos))
		return false;

	if (m_pWorld && m_pWorld->isRemoteMode())
	{
		PB_UsePackingFCMItemCH usePackingFCMItemCH;
		usePackingFCMItemCH.set_itemid(itemid);

		PB_Vector3* pos = usePackingFCMItemCH.mutable_usepos();
		pos->set_x(usepos.x);
		pos->set_y(usepos.y);
		pos->set_z(usepos.z);

		GetGameNetManagerPtr()->sendToHost(PB_USE_PACKINGFCMITEM_CH, usePackingFCMItemCH);
	}

	return true;
}

bool MpPlayerControl::useSpecialItem(int grid_index, int itemId, int num/* =1 */)
{
	if(!PlayerControl::useSpecialItem(grid_index, itemId, num)) return false;
	if(m_pWorld->isRemoteMode())
	{
		PB_SpecialItemUseCH specialItemUseCH;
		specialItemUseCH.set_gridindex(grid_index);
		specialItemUseCH.set_itemid(itemId);
		specialItemUseCH.set_itemnum(num);

		GetGameNetManagerPtr()->sendToHost(PB_SPECIALITEM_USE_CH, specialItemUseCH);
	}
	return true;
}

//void MpPlayerControl::doKickBall(int type, float charge, ClientActor *ball)
//{
//	PlayerControl::doKickBall(type, charge, ball);
//
//	if (m_pWorld->isRemoteMode())
//	{
//		WORLD_ID objId = ball ? ball->getObjId() : 0;
//
//		PB_BallOperateCH ballOperateCH;
//		ballOperateCH.set_type(type);
//
//		ballOperateCH.set_actorid(objId);
//		ballOperateCH.set_extenddata((int)charge);
//
//		GameNetManager::getInstance()->sendToHost(PB_BALL_OPERATE_CH, ballOperateCH);
//	}
//}

//bool MpPlayerControl::doCatchBall(ClientActor *ball)
//{
//	if (!PlayerControl::doCatchBall(ball)) return false;
//
//	if (m_pWorld->isRemoteMode())
//	{
//		WORLD_ID objId = ball ? ball->getObjId() : 0;
//
//		PB_BallOperateCH ballOperateCH;
//		ballOperateCH.set_type(PLAYEROP_CATCH_BALL);
//		ballOperateCH.set_actorid(objId);
//		ballOperateCH.set_extenddata(0);
//
//		GameNetManager::getInstance()->sendToHost(PB_BALL_OPERATE_CH, ballOperateCH);
//	}
//
//	return true;
//}

//void MpPlayerControl::beginChargeKickBall()
//{
//	PlayerControl::beginChargeKickBall();
//	if (m_pWorld->isRemoteMode())
//	{
//		PB_BallOperateCH ballOperateCH;
//		ballOperateCH.set_type(PLAYEROP_BALL_CHARGE_BEGIN);
//		ballOperateCH.set_actorid(0);
//		ballOperateCH.set_extenddata(0);
//
//		GameNetManager::getInstance()->sendToHost(PB_BALL_OPERATE_CH, ballOperateCH);
//	}
//}

//bool MpPlayerControl::doTackle()
//{
//	if (!PlayerControl::doTackle()) return false;
//
//	if (m_pWorld->isRemoteMode())
//	{
//		PB_BallOperateCH ballOperateCH;
//		ballOperateCH.set_type(PLAYEROP_TACKLE);
//		ballOperateCH.set_actorid(0);
//		ballOperateCH.set_extenddata(0);
//
//		GameNetManager::getInstance()->sendToHost(PB_BALL_OPERATE_CH, ballOperateCH);
//	}
//
//	return true;
//}

//void MpPlayerControl::endTackle()
//{
//	PlayerControl::endTackle();
//
//	if (m_pWorld->isRemoteMode())
//	{
//		PB_BallOperateCH ballOperateCH;
//		ballOperateCH.set_type(PLAYEROP_TACKLE_END);
//		ballOperateCH.set_actorid(0);
//		ballOperateCH.set_extenddata(0);
//
//		GameNetManager::getInstance()->sendToHost(PB_BALL_OPERATE_CH, ballOperateCH);
//	}
//}

//bool MpPlayerControl::basketBallOPStart(int type, ClientActor *ball)
//{
//	if (m_pWorld->isRemoteMode())
//	{
//		WORLD_ID objId = ball ? ball->getObjId() : 0;
//		PB_BasketBallOperate basketballOperatorCH;
//		basketballOperatorCH.set_type(type);
//		basketballOperatorCH.set_actorid(0);
//		basketballOperatorCH.set_extenddata(0);
//		GameNetManager::getInstance()->sendToHost(PB_BASKETBALL_OPERATE_CH, basketballOperatorCH);
//	}
//	return PlayerControl::basketBallOPStart(type, ball);
//}
//
//void MpPlayerControl::basketBallOPEnd(int type, ClientActor *ball)
//{
//	PlayerControl::basketBallOPEnd(type, ball);
//	if (m_pWorld->isRemoteMode())
//	{
//		WORLD_ID objId = ball ? ball->getObjId() : 0;
//		PB_BasketBallOperate basketballOperatorCH;
//		basketballOperatorCH.set_type(type);
//		basketballOperatorCH.set_actorid(0);
//		basketballOperatorCH.set_extenddata(0);
//		GameNetManager::getInstance()->sendToHost(PB_BASKETBALL_OPERATE_CH, basketballOperatorCH);
//	}
//}

//bool MpPlayerControl::doRunDribbleRunBasketBall(ClientActor* ball)
//{
//	if (!PlayerControl::doRunDribbleRunBasketBall(ball)) return false;
//	if (m_pWorld->isRemoteMode())
//	{
//		PB_BasketBallOperate basketballOperatorCH;
//		basketballOperatorCH.set_type(PLAYEROP_BASKETBALL_DRIBBLERUN);
//		basketballOperatorCH.set_actorid(ball->getObjId());
//		basketballOperatorCH.set_extenddata(0);
//		GameNetManager::getInstance()->sendToHost(PB_BASKETBALL_OPERATE_CH, basketballOperatorCH);
//	}
//	return true;
//}

//void MpPlayerControl::beginChargeThrowBall()
//{
//	PlayerControl::beginChargeThrowBall();
//	if (m_pWorld->isRemoteMode())
//	{
//		PB_BasketBallOperate ballOperateCH;
//		ballOperateCH.set_type(PLAYEROP_BASKETBALL_CHARGE_BEGIN);
//		ballOperateCH.set_actorid(0);
//		ballOperateCH.set_extenddata(0);
//		GameNetManager::getInstance()->sendToHost(PB_BASKETBALL_OPERATE_CH, ballOperateCH);
//	}
//}

//void MpPlayerControl::doKickBasketBall(int type, BasketballFall result, float charge, ClientActor* ball, const WCoord& target_pos, float cameraYaw, float cameraPitch,int selectedActorUin)
//{
//	PlayerControl::doKickBasketBall(type, result, charge, ball, target_pos, cameraYaw, cameraPitch, selectedActorUin);
//	WORLD_ID objId = ball ? ball->getObjId() : 0;
//	if (m_pWorld->isRemoteMode())
//	{
//		PB_BasketBallOperate basketBallOperateCH;
//		basketBallOperateCH.set_type(type);
//		basketBallOperateCH.set_fallresult(result);
//		basketBallOperateCH.set_actorid(objId);
//		basketBallOperateCH.set_extenddata((int)charge);
//		PB_Vector3* pos = basketBallOperateCH.mutable_pos();
//		if (pos && (target_pos.x != 0 || target_pos.y != 0 || target_pos.z != 0))
//		{
//			pos->set_x(target_pos.x);
//			pos->set_y(target_pos.y);
//			pos->set_z(target_pos.z);
//		}
//		basketBallOperateCH.set_yaw(cameraYaw);
//		basketBallOperateCH.set_selectedactorid(selectedActorUin);
//		basketBallOperateCH.set_pitch(cameraPitch);
//		GameNetManager::getInstance()->sendToHost(PB_BASKETBALL_OPERATE_CH, basketBallOperateCH);
//	}
//}

void MpPlayerControl::doThrowGravityActor(int type, float charge, ClientActor *actor)
{
	PlayerControl::doThrowGravityActor(type, charge, actor);

	if (m_pWorld->isRemoteMode())
	{
		WORLD_ID objId = actor ? actor->getObjId() : 0;

		PB_GravityOperateCH gravityOperateCH;
		gravityOperateCH.set_type(type);

		gravityOperateCH.set_actorid(objId);
		gravityOperateCH.set_extenddata((int)charge);

		GetGameNetManagerPtr()->sendToHost(PB_GRAVITY_OPERATE_CH, gravityOperateCH);
	}
}

bool MpPlayerControl::doCatchGravityActor(ClientActor *actor, int bindDistance, int bindHeight)
{
	if (!PlayerControl::doCatchGravityActor(actor, bindDistance, bindHeight)) return false;

	if (m_pWorld->isRemoteMode())
	{
		WORLD_ID objId = actor ? actor->getObjId() : 0;

		PB_GravityOperateCH gravityOperateCH;
		gravityOperateCH.set_type(PLAYEROP_CATCH_GRAVITYACTOR);
		gravityOperateCH.set_actorid(objId);
		gravityOperateCH.set_extenddata(0);

		GetGameNetManagerPtr()->sendToHost(PB_GRAVITY_OPERATE_CH, gravityOperateCH);
	}

	return true;
}

void MpPlayerControl::beginChargeThrowGravityActor()
{
	PlayerControl::beginChargeThrowGravityActor();
	if (m_pWorld->isRemoteMode())
	{
		PB_GravityOperateCH gravityOperateCH;
		gravityOperateCH.set_type(PLAYEROP_GRAVITY_CHARGE_BEGIN);
		gravityOperateCH.set_actorid(0);
		gravityOperateCH.set_extenddata(0);

		GetGameNetManagerPtr()->sendToHost(PB_GRAVITY_OPERATE_CH, gravityOperateCH);
	}
}

void MpPlayerControl::starConvert(int num)
{
	if(m_pWorld->isRemoteMode())
	{
		PB_SpecialItemUseCH specialItemUseCH;
		specialItemUseCH.set_gridindex(-num);

		GetGameNetManagerPtr()->sendToHost(PB_SPECIALITEM_USE_CH, specialItemUseCH);
	}
	else
	{
		PlayerControl::starConvert(num);
	}
}

void MpPlayerControl::onCloseDialogue()
{
	if (m_OpenDialogueMobID > 0)
	{
		MINIW::ScriptVM::game()->setUserTypePointer("OpenedDialogueMob", "ClientMob", NULL);
		m_OpenDialogueMobID = 0;
	}
}

void MpPlayerControl::closePlotDialogue()
{
	if (m_pWorld->isRemoteMode())
	{
		PB_CloseDialogueCH closeDialogueCH;

		GetGameNetManagerPtr()->sendToHost(PB_CLOSEDIALOGUE_CH, closeDialogueCH);

		onCloseDialogue();
	}
	else
		PlayerControl::closePlotDialogue();
}


int MpPlayerControl::lootItem(int fromIndex, int num)
{
	if (m_pWorld && !m_pWorld->isRemoteMode())
	{
		return PlayerControl::lootItem(fromIndex, num);
	}
	else
	{
		PB_BackPackLootCH backPackLootCH;
		backPackLootCH.set_fromindex(fromIndex);
		backPackLootCH.set_num(num);

		GetGameNetManagerPtr()->sendToHost(PB_BACKPACK_LOOT_CH, backPackLootCH);
	}
	return 0;
}

void MpPlayerControl::swapItem(int fromIndex, int toIndex)
{
	if (m_pWorld && !m_pWorld->isRemoteMode())
	{
		PlayerControl::swapItem(fromIndex, toIndex);
	}
	else
	{
		PB_BackPackGridSwapCH backPackGridSwapCH;
		backPackGridSwapCH.set_fromgridid(fromIndex);
		backPackGridSwapCH.set_togridid(toIndex);

		GetGameNetManagerPtr()->sendToHost(PB_BACKPACK_GRID_SWAP_CH, backPackGridSwapCH);
	}
}
void MpPlayerControl::mergeItem(int fromIndex, int toIndex)
{
	if (m_pWorld && !m_pWorld->isRemoteMode())
	{
		PlayerControl::mergeItem(fromIndex, toIndex);
	}
	else
	{
		PB_BackPackGridSwapCH backPackGridSwapCH;
		backPackGridSwapCH.set_fromgridid(fromIndex);
		backPackGridSwapCH.set_togridid(toIndex);

		GetGameNetManagerPtr()->sendToHost(PB_BACKPACK_GRID_SWAP_CH, backPackGridSwapCH);
	}
}

void MpPlayerControl::discardItem(int index, int num)
{
	if (m_pWorld && !m_pWorld->isRemoteMode())
	{
		PlayerControl::discardItem(index, num);
	}
	else
	{
		PB_BackPackGridDiscardCH backPackGridDiscardCH;
		backPackGridDiscardCH.set_gridid(index);
		backPackGridDiscardCH.set_num(num);

		GetGameNetManagerPtr()->sendToHost(PB_BACKPACK_GRID_DISCARD_CH, backPackGridDiscardCH);
	}
}

int MpPlayerControl::gainItems(int itemid, int num, int priority, bool save)
{
	if(m_pWorld == NULL) return -1;
	if (!m_pWorld->isRemoteMode())
		return PlayerControl::gainItems(itemid, num, priority);
	else
	{
		
		/*PB_GainItemsToBackPackCH gainItemsToBackPackCH;
		gainItemsToBackPackCH.set_playeruin(getUin());
		gainItemsToBackPackCH.set_itemid(itemid);
		gainItemsToBackPackCH.set_itemnum(num);

		GameNetManager::getInstance()->sendToHost(PB_GAINITEMSTOBACKPACK_CH, gainItemsToBackPackCH);*/
	}
	return -1;
}

int MpPlayerControl::gainItemsUserdata(int itemid, int num, const char *userdata_str, int priority)
{
	if (m_pWorld == NULL) return -1;
	if (!m_pWorld->isRemoteMode())
		return PlayerControl::gainItemsUserdata(itemid, num, userdata_str, priority);
	else
	{

		PB_GainItemsUserDatastrToBackPackCH gainItemsUserDatastrToBackPackCH;
		gainItemsUserDatastrToBackPackCH.set_playeruin(getUin());
		gainItemsUserDatastrToBackPackCH.set_itemid(itemid);
		gainItemsUserDatastrToBackPackCH.set_itemnum(num);
		gainItemsUserDatastrToBackPackCH.set_userdata_str(userdata_str);
			GetGameNetManagerPtr()->sendToHost(PB_GainItemsUserDatastrToBackPack_CH, gainItemsUserDatastrToBackPackCH);
	}
	return -1;
}

int MpPlayerControl::tryBuyAdNpcGood(int tabId, int goodId)
{
	if (m_pWorld && !m_pWorld->isRemoteMode())
	{
		return PlayerControl::tryBuyAdNpcGood(tabId, goodId);
	}
	else
	{
		PB_BuyAdShopGoods buyGoods;
		buyGoods.set_tabid(tabId);
		buyGoods.set_goodid(goodId);
		buyGoods.set_step(1);

		if (GetGameNetManagerPtr())
		{
			GetGameNetManagerPtr()->sendToHost(PB_BUY_AD_SHOP_GOOD_CH, buyGoods);
		}
		return 2;
	}
}


void MpPlayerControl::onBuyAdNpcGood(int tabId, int goodId)
{
	if (m_pWorld && !m_pWorld->isRemoteMode())
	{
		return PlayerControl::onBuyAdNpcGood(tabId, goodId);
	}
	else
	{
		PB_BuyAdShopGoods buyGoods;
		buyGoods.set_tabid(tabId);
		buyGoods.set_goodid(goodId);
		buyGoods.set_step(2);    // 直接购买完成

		if (GetGameNetManagerPtr())
		{
			GetGameNetManagerPtr()->sendToHost(PB_BUY_AD_SHOP_GOOD_CH, buyGoods);
		}
	}
}


/*
	20210823:联机状态下使用背包道具兑换其他背包道具 codeby：wangyu
*/
void MpPlayerControl::doExchangeItems(int useitemid, int usenum, int gainitemid, int gainnum, int type)
{
	if (m_pWorld && !m_pWorld->isRemoteMode())
	{
		PlayerControl::doExchangeItems(useitemid, usenum, gainitemid, gainnum, type);
	}
	else
	{
		PB_ExchangeItemsToBackPackCH exchangeItemsToBackPackCH;
		exchangeItemsToBackPackCH.set_playeruin(getUin());
		exchangeItemsToBackPackCH.set_useitemid(useitemid);
		exchangeItemsToBackPackCH.set_useitemnum(usenum);
		exchangeItemsToBackPackCH.set_gainitemid(gainitemid);
		exchangeItemsToBackPackCH.set_gainitemnum(gainnum);
		exchangeItemsToBackPackCH.set_opertype(type);
		
		if (GetGameNetManagerPtr())
		{
			GetGameNetManagerPtr()->sendToHost(PB_EXCHANGEITEMSTOBACKPACK_CH, exchangeItemsToBackPackCH);
		}
	}
}
void MpPlayerControl::setItem(int itemid, int toIndex, int num, const char *sid_str/* = ""*/)
{
	if(m_pWorld && !m_pWorld->isRemoteMode())
	{
		PlayerControl::setItem(itemid, toIndex, num, sid_str);
	}
	else
	{
		PB_BackPackSetItemCH backPackSetItemCH;
		backPackSetItemCH.set_itemid(itemid);
		backPackSetItemCH.set_toindex(toIndex);
		backPackSetItemCH.set_num(num);

		GetGameNetManagerPtr()->sendToHost(PB_BACKPACK_SETITEM_CH, backPackSetItemCH);
	}
}

void MpPlayerControl::socMoveItem(int fromindex, int toindex, int mouseindex)
{
	if (m_pWorld && m_pWorld->isRemoteMode())
	{
		PB_BackPackSetItemCH backPackSetItemCH;
		backPackSetItemCH.set_itemid(fromindex);
		backPackSetItemCH.set_toindex(toindex);
		backPackSetItemCH.set_num(mouseindex);

		GetGameNetManagerPtr()->sendToHost(PB_PLAYER_SOCMOVEITEM, backPackSetItemCH);

		return;
	}

	ClientPlayer::socMoveItem(fromindex, toindex, mouseindex);
}

void MpPlayerControl::setItemWithoutLimit(int itemid, int toIndex, int num, const char *userdata_str, const char *sid_str/*=""*/)
{
	if(m_pWorld && !m_pWorld->isRemoteMode())
	{
		PlayerControl::setItemWithoutLimit(itemid, toIndex, num, userdata_str, sid_str);
	}
	else
	{
		PB_BackPackSetItemWithoutLimitCH backPackSetItemCH;
		backPackSetItemCH.set_itemid(itemid);
		backPackSetItemCH.set_toindex(toIndex);
		backPackSetItemCH.set_num(num);
		backPackSetItemCH.set_userdata_str(userdata_str);

		GetGameNetManagerPtr()->sendToHost(PB_BACKPACK_SETITEMWITHOUTLIMIT_CH, backPackSetItemCH);
	}
}

void MpPlayerControl::setDyeableItem(int itemid, int toIndex, int num, const char* userdata_str)
{
	if (m_pWorld && !m_pWorld->isRemoteMode())
	{
		PlayerControl::setDyeableItem(itemid, toIndex, num, userdata_str);
	}
	else
	{
		jsonxx::Object dyeableInfo;
		dyeableInfo << "uin" << getUin();
		dyeableInfo << "itemid" << itemid;
		dyeableInfo << "toindex" << toIndex;
		dyeableInfo << "num" << num;
		dyeableInfo << "userdata" << userdata_str;
		GetSandBoxManagerPtr()->sendToHost("PB_SYNC_DYEABLE_ITEM_CH", dyeableInfo.bin(), dyeableInfo.binLen());
	}
}

void MpPlayerControl::moveItem(int fromindex, int toindex, int num)
{
	if (!m_pWorld) return;

	if(!m_pWorld->isRemoteMode())
	{
		PlayerControl::moveItem(fromindex, toindex, num);
	}
	else
	{
		PB_BackPackMoveItemCH backPackMoveItemCH;
		backPackMoveItemCH.set_fromindex(fromindex);
		backPackMoveItemCH.set_toindex(toindex);
		backPackMoveItemCH.set_num(num);

		GetGameNetManagerPtr()->sendToHost(PB_BACKPACK_MOVEITEM_CH, backPackMoveItemCH);
	}
}

void MpPlayerControl::doReload(int bulletid, int num, bool isCustomGun)
{
	if (!m_pWorld) return;

	if (!m_pWorld->isRemoteMode())
	{
		PlayerControl::doReload(bulletid, num, isCustomGun);
	}
	else
	{
		PB_GunDoReloadCH gunDoReloadCH;
		gunDoReloadCH.set_bulletid(bulletid);
		gunDoReloadCH.set_num(num);
		gunDoReloadCH.set_usetick(Rainbow::Timer::getSystemTick());
		gunDoReloadCH.set_iscustomgun(isCustomGun);
		gunDoReloadCH.set_curshortcut(getCurShortcut());
		GetGameNetManagerPtr()->sendToHost(PB_GUN_DORELOAD_CH, gunDoReloadCH);

		CustomGunUseComponent* comp = sureCustomGunComponent();
		if (comp) comp->SetReloadComplete(false);
	}
}

//天赋：回弹专用
void MpPlayerControl::doReloadWithoutCheck(int num)
{
	if (!m_pWorld) return;

	if (!m_pWorld->isRemoteMode())
	{
		PlayerControl::doReloadWithoutCheck(num);
	}
	else
	{
		PB_GunDoReloadCH gunDoReloadCH;
		gunDoReloadCH.set_num(num);
		gunDoReloadCH.set_usetick(Rainbow::Timer::getSystemTick());
		gunDoReloadCH.set_iscustomgun(true);
		gunDoReloadCH.set_nocheck(true);
		gunDoReloadCH.set_curshortcut(getCurShortcut());
		GetGameNetManagerPtr()->sendToHost(PB_GUN_DORELOAD_CH, gunDoReloadCH);
	}
}

void MpPlayerControl::sortPack(int base_index)
{
	if (!m_pWorld) return;

	if (!m_pWorld->isRemoteMode())
	{
		PlayerControl::sortPack(base_index);
	}
	else
	{
		PB_BackPackSortCH backPackSortCH;
		backPackSortCH.set_baseindex(base_index);

		GetGameNetManagerPtr()->sendToHost(PB_BACKPACK_SORT_CH, backPackSortCH);
	}
}

void MpPlayerControl::sortStorageBox()
{
	if (!m_pWorld) return;

	if (!m_pWorld->isRemoteMode())
	{
		PlayerControl::sortStorageBox();
	}
	else
	{
		PB_StorageBoxSortCH storageBoxSortCH;
		storageBoxSortCH.set_baseindex(STORAGE_START_INDEX);

		GetGameNetManagerPtr()->sendToHost(PB_STORAGEBOX_SORT_CH, storageBoxSortCH);
	}
}

int MpPlayerControl::repair(int tgtGridIdx)
{
	if (!m_pWorld) return -1;

	if (!m_pWorld->isRemoteMode())
	{
		return PlayerControl::repair(tgtGridIdx);
	}
	else
	{
		PB_RepairItemCH repairItemCH;
		repairItemCH.set_gridindex(tgtGridIdx);

		GetGameNetManagerPtr()->sendToHost(PB_REPAIR_ITEM_CH, repairItemCH);
	}
	return -1;
}

void MpPlayerControl::craftItem(int craftid, int num)
{
	if (!m_pWorld) return;

	if (!m_pWorld->isRemoteMode())
	{
		PlayerControl::craftItem(craftid, num);
	}
	else
	{
		PB_CraftItemCH craftItemCH;
		craftItemCH.set_craftid(craftid);
		craftItemCH.set_num(num);//code_by：huangfubin 客机少设置了参数。

		GetGameNetManagerPtr()->sendToHost(PB_CRAFT_ITEM_CH, craftItemCH);
	}
}

void MpPlayerControl::AddToCraftingQueue(int craftId, int num)
{
	if (!m_pWorld) return;
	if (!m_pWorld->isRemoteMode())
	{
		PlayerControl::AddToCraftingQueue(craftId, num);
	}
	else
	{
		PB_CraftingQueueAddTaskCH add_task_ch;
		add_task_ch.set_crafting_id(craftId);
		add_task_ch.set_count(num);

		GetGameNetManagerPtr()->sendToHost(PB_CRAFTING_QUEUE_ADD_TASK_CH, add_task_ch);
	}
}

void MpPlayerControl::RemoveFromCraftingQueue(int index)
{
	if (!m_pWorld) return;
	if (!m_pWorld->isRemoteMode())
	{
		PlayerControl::RemoveFromCraftingQueue(index);
	}
	else
	{
		PB_CraftingQueueRemoveTaskCH remove_task_ch;
		remove_task_ch.set_task_index(index);

		GetGameNetManagerPtr()->sendToHost(PB_CRAFTING_QUEUE_REMOVE_TASK_CH, remove_task_ch);
	}
}

bool MpPlayerControl::playAct(int act, bool isSwitchViewMode)
{
	if (!PlayerControl::playAct(act, isSwitchViewMode))
		return false;

	//getBody() && (getBody()->getActID() == act) && 
	if (getBody() && (getBody()->getActID() == act) && m_pWorld)
	{
		if (m_pWorld->isRemoteMode())
		{
			PB_PlayActCH playActCH;
			playActCH.set_actid(act);
			playActCH.set_actidtrigger(-1);
			GetGameNetManagerPtr()->sendToHost(PB_PLAYER_ACT_CH, playActCH);
		}
		else
		{
			PB_PlayActHC playActHC;
			playActHC.set_uin(getUin());
			playActHC.set_actid(act);
			playActHC.set_actidtrigger(-1);
			getWorld()->getMpActorMgr()->sendMsgToTrackingPlayers(PB_PLAYER_ACT_HC, playActHC, this);
		}
	}

	return true;
}

static bool IsSameCustomSkin(const char *p1, const char *p2)
{
	if(p1==NULL || p2==NULL) return p1==p2;
	else return strcmp(p1, p2)==0;
}

void MpPlayerControl::changePlayerModel(int playerindex, int mutatemob, const char *customskin, const char* custommodel,int itemid, int blockid ,bool bforce)
{
	if (m_pWorld && !m_pWorld->isRemoteMode())
	{
		restoreSkinByReason(4);//4 表示使用商城等方式改变装扮
	}
	// 本地检测 避免被非法调用
	{
		int skinId = MNSandbox::PlayerIndex2Skin(playerindex);
		bool canUse = false;
		MINIW::ScriptVM::game()->callFunction("CheckSkinCanUse", "i>b", skinId, &canUse);
		if (!canUse)
		{
			return;
		}
	}

	ClientPlayer::changePlayerModel(playerindex, mutatemob, customskin, custommodel, itemid,blockid, bforce);

	if (blockid == 0 && itemid == 0 && mutatemob == 0 && m_pWorld != nullptr)
	{
		PB_PlayerBriefInfo playerBriefInfo;
		playerBriefInfo.set_uin(getUin());
		playerBriefInfo.set_playerindex(playerindex);
		playerBriefInfo.set_nickname(getNickname());
		playerBriefInfo.set_teamid(getTeam());
		int skinId = GetClientInfoProxy()->getAccountInfo()->RoleInfo.SkinID;
		if (customskin)
		{
			playerBriefInfo.set_customjson(customskin);
			skinId = MNSandbox::PlayerIndex2Skin(playerindex);
			if (skinId == 0)
			{
				MINIW::ScriptVM::game()->callFunction("ChangeAvtarBodyModel", "is>i", getUin(), customskin, &skinId);
				int player_index = composePlayerIndex(playerindex, 0, skinId);
				playerBriefInfo.set_playerindex(player_index);
			}
		}
		if (custommodel)
			playerBriefInfo.set_custommodel(custommodel);
		playerBriefInfo.set_acctountskinid(skinId);
		playerBriefInfo.set_skinid(skinId);
		m_originSkinId = skinId;
		m_strOriginCustomJson = GetClientInfoProxy()->getAccountInfo()->RoleInfo.CustomSkin;

		if (m_pWorld->isRemoteMode())
		{
			//同步给主机
			GameNetManager::getInstance()->sendToHost(PB_ACTOT_SET_CUSTOM_CH, playerBriefInfo);
		}
		else
		{
			// 同步给其他客机
			getWorld()->getMpActorMgr()->sendMsgToTrackingPlayers(PB_ACTOT_SET_CUSTOM_HC, playerBriefInfo, this);
		}
	}
}

int MpPlayerControl::enchant(int tgtGridIdx, int frmGridIdx, int enchants[MAX_ITEM_ENCHANTS])
{
	if (!m_pWorld->isRemoteMode())
	{
		return PlayerControl::enchant(tgtGridIdx, frmGridIdx, enchants);
	}
	else
	{
		PB_EnchantItemCH enchantItemCH;
		enchantItemCH.set_gridindex(tgtGridIdx);
		enchantItemCH.set_frmgridindex(frmGridIdx);
		RepeatedField<int>* enchantids = enchantItemCH.mutable_enchantids();
		enchantids->Resize(MAX_ITEM_ENCHANTS, 0);
		for (int i=0; i<MAX_ITEM_ENCHANTS; i++) enchantids->Set(i, enchants[i]);

		GetGameNetManagerPtr()->sendToHost(PB_ENCHANT_ITEM_CH, enchantItemCH);
	}
	return -1;
}

int MpPlayerControl::enchantRandom(int tgtGridIdx)
{
	if (!m_pWorld->isRemoteMode())
	{
		return PlayerControl::enchantRandom(tgtGridIdx);
	}
	else
	{
		PB_EnchantItemRandomCH enchantItemRandomCH;
		enchantItemRandomCH.set_gridindex(tgtGridIdx);

		GetGameNetManagerPtr()->sendToHost(PB_ENCHANT_ITEM_RANDOM_CH, enchantItemRandomCH);
	}
	return -1;
}

bool MpPlayerControl::writeLetters(int tgtGridIdx, std::string txt)
{
	if (!txt.empty() && ClientPlayer::writeLetters(tgtGridIdx, txt))
	{
		size_t len = (txt.length() >= MAX_ITEM_USERDATASTR) ? (MAX_ITEM_USERDATASTR - 1) : txt.length();

		if (!m_pWorld->isRemoteMode())
		{
			// 同步给其他客机
			PB_ItemGridUserData itemGridUserData;
			itemGridUserData.set_uin(getUin());
			itemGridUserData.set_gridindex(tgtGridIdx);
			itemGridUserData.set_userdatastr(txt.c_str(), len);

			GetGameNetManagerPtr()->sendBroadCast(PB_SYNC_GRIDUSERDATA_HC, itemGridUserData);
		}
		else
		{
			// 同步到主机，让主机同步给其他客机
			PB_ItemGridUserData itemGridUserData;
			itemGridUserData.set_uin(getUin());
			itemGridUserData.set_gridindex(tgtGridIdx);
			itemGridUserData.set_userdatastr(txt.c_str(), len);
			itemGridUserData.set_type(0);

			GetGameNetManagerPtr()->sendToHost(PB_SYNC_GRIDUSERDATA_CH, itemGridUserData);
		}
		return true;
	}

	return false;
}

bool MpPlayerControl::writeInstruction(int tgtGridIdx, std::string txt)
{
	if (!txt.empty() && ClientPlayer::writeInstruction(tgtGridIdx, txt))
	{
		size_t len = (txt.length() >= MAX_ITEM_USERDATASTR) ? (MAX_ITEM_USERDATASTR - 1) : txt.length();

		if (m_pWorld->isRemoteMode())
		{
			// 同步到主机，让主机同步给其他客机
			PB_ItemGridUserData itemGridUserData;
			itemGridUserData.set_uin(getUin());
			itemGridUserData.set_gridindex(tgtGridIdx);
			itemGridUserData.set_userdatastr(txt.c_str(), len);
			itemGridUserData.set_type(1);

			GetGameNetManagerPtr()->sendToHost(PB_SYNC_GRIDUSERDATA_CH, itemGridUserData);
		}
		return true;
	}

	return false;
}

void MpPlayerControl::setCurShortcut(int i)
{
	if (MNSandbox::IMiniDeveloperProxy::getMiniDeveloperProxy()->GetTriggerOperateAttr(getUin(), 3)) //AttrOperateTips 
	{
		return;
	}
	PlayerControl::setCurShortcut(i);

	if (m_pWorld != nullptr && m_pWorld->isRemoteMode())
	{
		PB_BackPackEquipWeaponCH equipWeaponCH;
		equipWeaponCH.set_gridid(i);

		GetGameNetManagerPtr()->sendToHost(PB_BACKPACK_EQUIP_WEAPON_CH, equipWeaponCH);
	}
}

void MpPlayerControl::fall(float fallDist)
{
	if (m_pWorld != nullptr && m_pWorld->isRemoteMode())
	{
		//DO NOTHING
	}
	else
	{
		ClientPlayer::fall(fallDist);
		addAchievement(2, ACHIEVEMENT_FALLDIST, 0, int(fallDist));
	}
}

bool MpPlayerControl::revive(int reviveType, int x/* =0 */, int y/* =-1 */, int z/* =0 */)
{
	if(m_pWorld && m_pWorld->isRemoteMode())
	{
		PB_ActorReviveCH actorReviveCH;
		actorReviveCH.set_objid(getObjId());
		actorReviveCH.set_type(reviveType);

		GetGameNetManagerPtr()->sendToHost(PB_ACTOR_REVIVE_CH, actorReviveCH);

		return false;
	}
	else
	{
		return PlayerControl::revive(reviveType, x, y, z);
	}
}

void MpPlayerControl::tryMountActor(ClientActor *actor, short shapeshiftid /* = 0 */)
{
	if(m_pWorld->isRemoteMode())
	{
		if (actor)
		{
			PB_ActorInteractCH actorInteractCH;
			actorInteractCH.set_itype(3);
			actorInteractCH.set_target(actor->getObjId());
			actorInteractCH.set_iplot(0);

			/*int viewMode = getCamera() != NULL ? getCamera()->getMode() : CAMERA_FPS;
			if (viewMode == CAMERA_FPS
				|| viewMode == CAMERA_CUSTOM_VIEW && getCameraConfigOption(CAMERA_OPTION_INDEX_CROSSHAIR) == CCT_SCREEN_CENTER)
			{
				if(m_pCamera){
					actorInteractCH.set_curyaw(m_pCamera->m_RotateYaw + 180.0f);
					actorInteractCH.set_curpitch(m_pCamera->m_RotatePitch);
				}
			}
			else
			{
				if(getLocoMotion() != NULL){
					actorInteractCH.set_curyaw(getLocoMotion()->m_RotateYaw);
					actorInteractCH.set_curpitch(getLocoMotion()->m_RotationPitch);
				}
			}*/

			MINIW::WorldRay ray;
			if (m_ViewMode == CAMERA_TPS_OVERLOOK)
			{
				ray.m_Origin = (getPosition() + WCoord(0, BLOCK_SIZE / 2, 0)).toWorldPos();
				ray.m_Dir = m_pCamera->getLookDir();
			}
			else if (m_ViewMode == CAMERA_CUSTOM_VIEW
				&& getCameraConfigOption(CAMERA_OPTION_INDEX_CROSSHAIR) == CCT_BODY_EYES)
			{
				ray.m_Origin = (getPosition() + WCoord(0, BLOCK_SIZE / 2, 0)).toWorldPos();
				ray.m_Dir = getLocoMotion()->getLookDir();
			}
			else
			{
				m_pCamera->getViewRayByScreenPt(&ray, m_CurMouseX, m_CurMouseY);
			}


			float rotateyaw;
			float rotationpitch;
			Direction2PitchYaw(&rotateyaw, &rotationpitch, ray.m_Dir);
			actorInteractCH.set_curyaw(rotateyaw);
			actorInteractCH.set_curpitch(rotationpitch);

			PB_Vector3* curPos = actorInteractCH.mutable_curpos();
			if (curPos)
			{
				WCoord pos = ray.m_Origin.toVector3();
				curPos->set_x(pos.x);
				curPos->set_y(pos.y);
				curPos->set_z(pos.z);
			}
			PB_Vector3* collidePos = actorInteractCH.mutable_collidepos();
			if (collidePos)
			{
				collidePos->set_x(m_PickResult.collide_pos.x);
				collidePos->set_y(m_PickResult.collide_pos.y);
				collidePos->set_z(m_PickResult.collide_pos.z);
			}
			GetGameNetManagerPtr()->sendToHost(PB_ACTOR_INTERACT_CH, actorInteractCH);

		}

		PB_PlayerMountActorCH playerMountActorCH;
		if (shapeshiftid > 0)
		{
			playerMountActorCH.set_actorid(shapeshiftid);
			playerMountActorCH.set_isshapeshift(true);
		}
		else
		{
			playerMountActorCH.set_actorid(actor ? actor->getObjId() : 0);
			playerMountActorCH.set_isshapeshift(false);
		}
		
		int seatIndex = 0;
		// 如果mount的是载具，需要特殊处理，判断客机点击的座位是驾驶座还是乘客座
		if ( actor !=  NULL && actor->getDefID() == 4002) {
			int x, y, z;
			ActorVehicleAssemble* actorVehicleAssemble = dynamic_cast<ActorVehicleAssemble*>(actor);
			ClientPlayer *player = dynamic_cast<ClientPlayer *>(this);
			actorVehicleAssemble->intersect(player, x, y, z);

			WCoord blockpos(x, y, z);
			
			LOG_INFO("mount pos: %d %d %d", x, y, z);
			seatIndex = actorVehicleAssemble->getSeatIndex(blockpos * BLOCK_SIZE);
			//rideIndex =  actorVehicleAssemble->findEmptyRiddenIndex(seatIndex);
			
		}
		//TODO 修改数据
		playerMountActorCH.set_interactblockid(seatIndex);

		LOG_INFO("tryMountActor seatIndex: %d", seatIndex);

		GetGameNetManagerPtr()->sendToHost(PB_PLAYER_MOUNTACTOR_CH, playerMountActorCH);
	}
	else PlayerControl::tryMountActor(actor);
}

void MpPlayerControl::tryCarryActor(ClientActor *actor, WCoord pos /* = WCoord(0, -1, 0) */)
{
	if (m_pWorld->isRemoteMode())
	{
		auto CarryComp = getCarryComponent();
		if (CarryComp && CarryComp->isCarrying() && actor == NULL)  //放下扛起的生物
			getBody()->playAttack();

		PB_PlayerCarryActorCH playerCarryActorCH;
		if(actor)
			playerCarryActorCH.set_actorid(actor->getObjId());
		else
			playerCarryActorCH.set_actorid(0);

		PB_Vector3* vPos = playerCarryActorCH.mutable_pos();
		vPos->set_x(pos.x);
		vPos->set_y(pos.y);
		vPos->set_z(pos.z);

		GetGameNetManagerPtr()->sendToHost(PB_PLAYER_CARRYACTOR_CH, playerCarryActorCH);
	}
	else
		PlayerControl::tryCarryActor(actor, pos);
}

//bool MpPlayerControl::carryActor(ClientActor *actor, WCoord pos /* = WCoord(0, -1, 0) */)
//{
//	if (m_pWorld->isRemoteMode())
//	{
//		if (isCarrying() && actor == NULL)  //放下扛起的生物
//			getBody()->playAttack();
//
//		PB_PlayerCarryActorCH playerCarryActorCH;
//		playerCarryActorCH.set_actorid(actor->getObjId());
//
//		PB_Vector3* vPos = playerCarryActorCH.mutable_pos();
//		vPos->set_x(pos.x);
//		vPos->set_y(pos.y);
//		vPos->set_z(pos.z);
//
//		GetGameNetManagerPtr()->sendToHost(PB_PLAYER_CARRYACTOR_CH, playerCarryActorCH);
//
//		return true;
//	}
//	else
//		return PlayerControl::carryActor(actor, pos);
//}

void MpPlayerControl::tryWakeup()
{
	if(m_pWorld->isRemoteMode())
	{
		PB_PlayerSleepCH playerSleepCH;
		playerSleepCH.set_flags(1);

		GetGameNetManagerPtr()->sendToHost(PB_PLAYER_SLEEP_CH, playerSleepCH);
	}
	else PlayerControl::tryWakeup();
}

void MpPlayerControl::trySleep()
{
	if (m_pWorld->isRemoteMode())
	{
		PB_PlayerSleepCH playerSleepCH;
		playerSleepCH.set_flags(3);

		GetGameNetManagerPtr()->sendToHost(PB_PLAYER_SLEEP_CH, playerSleepCH);
	}
	else PlayerControl::trySleep();
}

void MpPlayerControl::tryStandup()
{
	if(m_pWorld->isRemoteMode())
	{
		PB_PlayerSleepCH playerSleepCH;
		playerSleepCH.set_flags(2);

		GetGameNetManagerPtr()->sendToHost(PB_PLAYER_SLEEP_CH, playerSleepCH);
	}
	else PlayerControl::tryStandup();
}

bool MpPlayerControl::takeAccountItemsToHost(int itemid, int num)
{
	PB_GetAccountItemsCH getAccountItemsCH;
	getAccountItemsCH.set_itemid(itemid);
	getAccountItemsCH.set_num(num);

	return GetGameNetManagerPtr()->sendToHost(PB_ACTOR_GET_ACCOUNT_ITEM, getAccountItemsCH);
}

void MpPlayerControl::newAdNpcAddExp(int op, int starNum)
{
	if (starNum <= 0)
		return;

	if(m_pWorld)
	{
		if(!m_pWorld->isRemoteMode())
		{
			return PlayerControl::newAdNpcAddExp(op, starNum);
		}
		else
		{
			PB_AddExpCH addExpCH;
			addExpCH.set_op(op);
			addExpCH.set_starnum(starNum);

			if (GetGameNetManagerPtr())
			{
				GetGameNetManagerPtr()->sendToHost(PB_ADDEXP_CH, addExpCH);
			}
		}
	}
}

void MpPlayerControl::completeTask(int taskid)
{
	if (!m_pWorld) return;

	if (m_pWorld->isRemoteMode())
	{
		PB_CompleteTaskCH completeTaskCH;
		completeTaskCH.set_taskid(taskid);

		GetGameNetManagerPtr()->sendToHost(PB_COMPLETE_TASK_CH, completeTaskCH);
	}
	else
		PlayerControl::completeTask(taskid);
}

bool MpPlayerControl::tryShapeShift(short shapeshiftid)
{
	if (PlayerControl::tryShapeShift(shapeshiftid))
	{
		if (m_pWorld && m_pWorld->isRemoteMode())
			playAnim(SEQ_SHAPE_SHIFT);

		return true;
	}

	return false;
}

void MpPlayerControl::shapeShift()
{
	if (m_pWorld->isRemoteMode())
	{
		RoleSkinDef *def = GetDefManagerProxy()->getRoleSkinDef(m_iTryShapeShiftID);
		setCanControl(true);
		if (def && def->ChangeType == 2 && !isDead())
		{
			PB_PlayerDeformationSkinCH deformationSkin;
			deformationSkin.set_actorid(getObjId());
			deformationSkin.set_isshapeshift(true);
			GetGameNetManagerPtr()->sendToHost(PB_DEFORMATION_SKIN_CH, deformationSkin);
			m_iTryShapeShiftID = 0;
			return;
		}
		if(!isDead())
			tryMountActor(NULL, m_iTryShapeShiftID);
		//setCurShortcut(getCurShortcut());
		m_iTryShapeShiftID = 0;
	}
	else
	{
		PlayerControl::shapeShift();
	}
}

void MpPlayerControl::setRefreshType(int type)
{
	if(m_iRefreshBySyncCustomData < type)
		m_iRefreshBySyncCustomData = type;
}

//
//TransformersSkinComponent* MpPlayerControl::getTransformersSkinCom()
//{
//	if (m_pTransformersSkinComp)
//		return m_pTransformersSkinComp;
//	m_pTransformersSkinComp = new MPTransformersSkinComponent(this);
//	return m_pTransformersSkinComp;
//}


OpenContainerComponent*  MpPlayerControl::getOpenContainerCom()
{
	return GetComponent<MPControlOpenContainerComponent>();
}

void MpPlayerControl::onOpenContainer(ClientWorldContainer *container)
{
	(static_cast<MPControlOpenContainerComponent*>(getOpenContainerCom()))->onOpenContainer(container);
}

void MpPlayerControl::onCloseContainer()
{
	(static_cast<MPControlOpenContainerComponent*>(getOpenContainerCom()))->onCloseContainer();
}

ItemSkillComponent* MpPlayerControl::getItemSkillComponent()
{
	return m_pItemSkillComponent;
}

//AccountHorseComponent* MpPlayerControl::getAccountHorseComponent()
//{
//	if (m_pAccountHorseComp)
//		return m_pAccountHorseComp;
//	m_pAccountHorseComp = new MPAccountHorseComponent(this);
//	return m_pAccountHorseComp;
//}


//PetSummonComponent* MpPlayerControl::getPetSummonComp()
//{
//	if (m_pPetSummonComp)
//		return m_pPetSummonComp;
//	m_pPetSummonComp = new MPPetSummonComponent(this);
//	return m_pPetSummonComp;
//}


bool MpPlayerControl::playSkinAct(int act, const int inviteUin, const int acceptUin)
{
	//2021-10-08 codeby:chenwei 添加健壮性判断
	if (NULL == m_Body || NULL == m_pWorld)
		return false;

	if (!PlayerControl::playSkinAct(act, inviteUin, acceptUin))
		return false;

	if (getBody()->getActID() == act)
	{
		if (m_pWorld->isRemoteMode())
		{
			PB_PlaySkinActCH playSkinActCH;
			playSkinActCH.set_actid(act);
			playSkinActCH.set_actidtrigger(-1);
			playSkinActCH.set_inviteuin(inviteUin);
			playSkinActCH.set_acceptuin(acceptUin);
			GetGameNetManagerPtr()->sendToHost(PB_PLAYER_SKIN_ACT_CH, playSkinActCH);
		}
		else
		{
			PB_PlaySkinActHC playSkinActHC;
			playSkinActHC.set_actid(act);
			playSkinActHC.set_actidtrigger(-1);
			playSkinActHC.set_inviteuin(inviteUin);
			playSkinActHC.set_acceptuin(acceptUin);
			getWorld()->getMpActorMgr()->sendMsgToTrackingPlayers(PB_PLAYER_SKIN_ACT_HC, playSkinActHC, this);
		}
	}

	//// 在流星雨特效播放的情况下，做出双人许愿的动作，在周围生成许愿星
	//if (act == 120057 && g_WorldMgr && g_WorldMgr->GetSandboxMgrOrCreate("MeteorShowerMgr") && !g_WorldMgr->isRemote())
	//{
	//	MeteorShowerManager* pMeteorShowerMgr = static_cast<MeteorShowerManager*>(g_WorldMgr->GetSandboxMgrOrCreate("MeteorShowerMgr"));
	//	if (pMeteorShowerMgr->isShowMeteorShower() &&
	//		!pMeteorShowerMgr->isStartGenerated())
	//	{
	//		pMeteorShowerMgr->generateStar();
	//	}
	//}

	return true;
}



//20210908 codeby：chenwei 查找周边舞伴
void MpPlayerControl::scanSkinActActorList(int range)
{
	m_vecSkinActorList = static_cast<ClientActorMgr*>(m_pWorld->getActorMgr())->findPlayersByRange(range);
}

//20210915 codeby: chenwei 获取附近玩家数量
int MpPlayerControl::getSkinActPlayerNum()
{
	return m_vecSkinActorList.size();
}

//20210915 codeby: chenwei 通过UIN获取玩家uin
int MpPlayerControl::getSkinActPlayerUinByIndex(int index)
{
	if (!m_vecSkinActorList.empty() && index >= 0 && (unsigned int)index <= m_vecSkinActorList.size())
	{
		return m_vecSkinActorList.at(index);
	}

	return 0;
}



void MpPlayerControl::sendActorInvite(int inviteType, int targetuin, int actId, int inviterPosX, int inviterPosZ)
{
	if (m_pWorld && !m_pWorld->isRemoteMode())
	{
		PB_ActorInviteHC actorInviteHC;
		actorInviteHC.set_invitetype(inviteType);
		actorInviteHC.set_actid(actId);
		actorInviteHC.set_targetuin(getUin());
		actorInviteHC.set_inviterposx(inviterPosX);
		actorInviteHC.set_inviterposz(inviterPosZ);

		if (GetGameNetManagerPtr())
		{
			GetGameNetManagerPtr()->sendToClient(targetuin, PB_ACTORINVITE_HC, actorInviteHC, 0, false);
		}
	}
	else
	{
		PB_ActorInviteCH actorInviteCH;
		actorInviteCH.set_invitetype(inviteType);
		actorInviteCH.set_targetuin(targetuin);
		actorInviteCH.set_actid(actId);
		actorInviteCH.set_inviterposx(inviterPosX);
		actorInviteCH.set_inviterposz(inviterPosZ);

		if (GetGameNetManagerPtr())
		{
			GetGameNetManagerPtr()->sendToHost(PB_ACTORINVITE_CH, actorInviteCH);
		}
	}
}

void MpPlayerControl::SendActionLog2Host(bool cheat, const std::string& eventName, const std::string& detail)
{
	if (m_pWorld && m_pWorld->isRemoteMode())
	{
		PB_SyncClientActionLogCH actionlog;
		actionlog.set_cheat(cheat);
		actionlog.set_event(eventName);
		actionlog.set_detail(detail);
			GetGameNetManagerPtr()->sendToHost(PB_SYNC_CLIENT_ACTIONLOG_CH, actionlog);
	}
}

void MpPlayerControl::UploadCheckInfo2Host(int infoType, const std::string& checkInfo)
{
	if (m_pWorld && m_pWorld->isRemoteMode() && checkInfo.size() > 5)
	{
		PB_UploadCheckInfoCH uploadInfo;
		uploadInfo.set_info_type(infoType);
		uploadInfo.set_detail(checkInfo);
		GameNetManager::getInstance()->sendToHost(PB_UPLOAD_CHECK_INFO_CH, uploadInfo);
	}
}

void MpPlayerControl::GetAdShopExtraItemReward(int awardId, int itemId, int count)
{
	// ???????????????????ReceiveItemLogic???? ?????????gainItem
	if (m_pWorld && m_pWorld->isRemoteMode())
	{
		PB_GetAdShopExtraAwardCH award;
		award.set_award_id(awardId);
		award.set_item_id(itemId);
		award.set_item_count(count);
		GameNetManager::getInstance()->sendToHost(PB_GET_ADSHOP_EXTRA_AWARD_CH, award);
	}
}

/*
 * @param:storeIndex 1 开发者仓库   2 商店仓库
 */
void MpPlayerControl::ExtraStoreItem(int storeIndex, int itemId, int count)
{
	if (m_pWorld && m_pWorld->isRemoteMode())
	{
		PB_ExtractStoreItemCH extract;
		extract.set_store_index(storeIndex);
		extract.set_item_id(itemId);
		extract.set_item_count(count);
		GameNetManager::getInstance()->sendToHost(PB_EXTRACT_STORE_ITEM_CH, extract);
	}
}

void MpPlayerControl::afterMoveTick()
{
	if (m_MoveControl->isMotionUpEnd())
		m_MoveControl->addEvent(IMT_UpEnd);
	if (isNewMoveSyncSwitchOn() && m_pWorld && m_pWorld->isRemoteMode())
		syncMove2Host();
	PlayerControl::afterMoveTick();
}

void MpPlayerControl::changeMoveFlag(unsigned flag_id, bool on)
{
	if (!m_MoveControl)
		return;
	if (!m_MoveControl->isNewSyncSwitchOn() || (GetGameNetManagerPtr() && GetGameNetManagerPtr()->isHost()))
	{
		ClientPlayer::changeMoveFlag(flag_id, on);
	}
	else
	{
		m_MoveControl->addFlagChange(flag_id, on);
		if (!(flag_id == IFC_Fly && on))
		{
			PlayerControl::changeMoveFlag(flag_id, on);
		}
	}
}

void MpPlayerControl::update(float dtime)
{
	if (GetWorldManagerPtr()->isNewSandboxNodeGame())
	{
		return;
	}
	PlayerControl::update(dtime);
	if (g_WorldMgr && g_WorldMgr->isRemote())
	{
		m_nUpdateSkillTick += dtime;
		auto skillComp = getSkillComponent();
		if (!skillComp)
		{
			return;
		}
		if (!skillComp->haveActiveSkill() && !skillComp->shouldSkillCollect())
		{
			m_nUpdateSkillTick = 0.f;
			return;
		}
		bool needSync = false;
		jsonxx::Object object;
		//每隔1s我们同步一次技能所需的玩家信息, 如果这个玩家有技能在运行的话
		if (m_nUpdateSkillTick > 1.f)
		{
			m_nUpdateSkillTick = 0.f;
			if (skillComp)
			{
				//收集玩家射线信息
				MINIW::WorldRay ray;
				if (getOPWay() == PLAYEROP_WAY_FOOTBALLER)
				{
					//ray.m_Origin = (getPosition() + WCoord(0, BLOCK_SIZE / 10, 0)).toWorldPos();
					ray.m_Origin = (getPosition() + WCoord(0, 5, 0)).toWorldPos();
					ray.m_Dir = getLocoMotion()->getLookDir();
					ray.m_Dir.y = 0;
					ray.m_Dir = MINIW::Normalize(ray.m_Dir);
				}
				else
				{
					if (m_ViewMode == CAMERA_TPS_OVERLOOK)
					{
						ray.m_Origin = (getPosition() + WCoord(0, BLOCK_SIZE / 2, 0)).toWorldPos();
						ray.m_Dir = m_pCamera->getLookDir();
					}
					else if (m_ViewMode == CAMERA_CUSTOM_VIEW
						&& getCameraConfigOption(CAMERA_OPTION_INDEX_CROSSHAIR) == CCT_BODY_EYES)
					{
						ray.m_Origin = (getPosition() + WCoord(0, BLOCK_SIZE / 2, 0)).toWorldPos();
						ray.m_Dir = getLocoMotion()->getLookDir();
					}
					else if (m_pCamera)
					{
						m_pCamera->getViewRayByScreenPt(&ray, m_CurMouseX, m_CurMouseY);
					}
				}
				//发送给主机
				needSync = true;
				jsonxx::Object objectRay;
				objectRay << "originx" << ray.m_Origin.x;
				objectRay << "originy" << ray.m_Origin.y;
				objectRay << "originz" << ray.m_Origin.z;
				objectRay << "dirx" << ray.m_Dir.x;
				objectRay << "diry" << ray.m_Dir.y;
				objectRay << "dirz" << ray.m_Dir.z;
				object << "ray" << objectRay;
			}
	
		}
		//按键信息我们每个update查一下
		{
			if (skillComp)
			{
				jsonxx::Object keys;
				if (skillComp->getKeyBoardStateJson(keys))
				{
					needSync = true;
					object << "button" << keys;
				}
			}
		}
		if (needSync)
		{
			object << "platform" << (int)((GetClientInfoProxy()->isMobile() ? PlayerPlatformMask::mobile : PlayerPlatformMask::pc));
			object << "uin" << getUin();
			unsigned char* p = NULL;
			int size = 0;
			object.saveBinary(p, size);
			SandBoxManager::getSingletonPtr()->sendToHost((char*)("PB_SKILL_SYNC_PLAYER_INFO"), p, size);
			free(p);

			int itemid = getCurToolID();
			const ToolDef *curToolDef = GetDefManagerProxy()->getToolDef(itemid);
			if (curToolDef && curToolDef->CanThrow) // 同步投掷物瞄准朝向
			{
				syncGunAnimInfo(itemid);
			}
		}
	}
}

void MpPlayerControl::getGunAimInfo(int itemid, float& yaw, float &pitch, float &spreed, WCoord &pos)
{
	if (getGunComponent()->getGunDef() != NULL)
	{
		spreed = getGunComponent()->getCurrentSpread();
	}
	else
	{
		spreed = 0.f;
	}

	int viewMode = getCamera() != NULL ? getCamera()->getMode() : CAMERA_FPS;
	if (viewMode == CAMERA_FPS || viewMode == CAMERA_TPS_BACK_SHOULDER
		|| viewMode == CAMERA_CUSTOM_VIEW && getCameraConfigOption(CAMERA_OPTION_INDEX_CROSSHAIR) == CCT_SCREEN_CENTER)
	{
		if (m_pCamera) {
			yaw = m_pCamera->m_RotateYaw + 180.0f;
			pitch = m_pCamera->m_RotatePitch;
		}
	}
	else
	{
		if (getLocoMotion() != NULL) {
			yaw = getLocoMotion()->m_RotateYaw;
			pitch = getLocoMotion()->m_RotationPitch;
		}
	}
	
	int usetarget = 0;
	ItemDef* itemdef = GetDefManagerProxy()->getItemDef(itemid);
	if (itemdef)
	{
		usetarget = itemdef->UseTarget;
	}
	if (usetarget != ITEM_USE_GUN)
	{
		pos = g_pPlayerCtrl->getCamera()->getEyePos();
		Rainbow::Vector3f camlookdir = g_pPlayerCtrl->getCamera()->getLookDir();
		auto RidComp = g_pPlayerCtrl->getRiddenComponent();
		ClientActor* riding = NULL;
		if (RidComp)
		{
			riding = RidComp->getRidingActor();
		}
		Rainbow::Quaternionf quat;
		if (riding)
		{
			auto PlayerRidComp = g_pPlayerCtrl->getRiddenComponent();
			if (PlayerRidComp && PlayerRidComp->getRiddenBindRot(g_pPlayerCtrl, quat))
			{
				//quat.rotate(camlookdir, camlookdir);
				camlookdir = RotateVectorByQuat(quat, camlookdir);
			}
		}


		CameraControlMode viewmode = g_pPlayerCtrl->getCamera()->getMode();
		if (viewmode == CAMERA_TPS_BACK || viewmode == CAMERA_TPS_BACK_2 /*|| viewmode == CAMERA_TPS_BACK_SHOULDER*/)
		{
			Vector2f deltaXZ((float)(pos.x - g_pPlayerCtrl->getPosition().x), (float)(pos.z - g_pPlayerCtrl->getPosition().z));
			pos += camlookdir * deltaXZ.Length() * 1.1f;
		}
		else if (viewmode == CAMERA_TPS_BACK_SHOULDER)
		{
			// 肩射视角的准心位置计算，参考TPS_BACK模式但添加肩部偏移
			Vector2f deltaXZ((float)(pos.x - g_pPlayerCtrl->getPosition().x), (float)(pos.z - g_pPlayerCtrl->getPosition().z));
			pos += camlookdir * deltaXZ.Length() * 1.1f;
		}
		else if (viewmode == CAMERA_FPS)
		{
			pos += BLOCK_SIZE * camlookdir;
		}
		else if (viewmode == CAMERA_TPS_OVERLOOK)
		{
			pos = getPosition() + WCoord(0, BLOCK_SIZE / 2, 0);
		}
		else if (viewmode == CAMERA_CUSTOM_VIEW)
		{
			pos = WCoord(getEyePosition());
		}
	}
	else
	{
		//Rainbow::Vector3f camlookdir = g_pPlayerCtrl->getCamera()->getLookDir();
		pos = g_pPlayerCtrl->getGunLogical()->getMuzzlePos();
		//if (g_pPlayerCtrl->getSneaking()) pos.y -= 80;
		//Vector2f deltaXZ((float)(pos.x - g_pPlayerCtrl->getPosition().x), (float)(pos.z - g_pPlayerCtrl->getPosition().z));
		//pos += camlookdir * deltaXZ.Length() * 1.1f;
		//auto eyepos = g_pPlayerCtrl->getCamera()->getEyePos();
		//int efef = 1;
		//pos.y = eyepos.y;
		//CameraControlMode viewmode = g_pPlayerCtrl->getCamera()->getMode();
		//if (viewmode == CAMERA_TPS_BACK_SHOULDER)
		//{
		//	//Rainbow::Vector3f camlookdir = g_pPlayerCtrl->getCamera()->getLookDir();
		//	// 肩射视角的准心位置计算，参考TPS_BACK模式但添加肩部偏移
		//	//Vector2f deltaXZ((float)(pos.x - g_pPlayerCtrl->getEyePosition().x), (float)(pos.z - g_pPlayerCtrl->getEyePosition().z));
		//	//pos += camlookdir * deltaXZ.Length() * 1.1f;+
		//}
		//else
		//{
		//	pos.y -= 10;
		//}
	}
}

void MpPlayerControl::syncGunAnimInfo(int itemid)
{
	if (m_pWorld && m_pWorld->isRemoteMode())
	{

		PB_GunInfoCH gunInfoCH;
		float yaw = 0.f;
		float pitch = 0.f;
		float spreed = 0.f;
		WCoord pos;

		getGunAimInfo(itemid, yaw, pitch, spreed, pos);
		gunInfoCH.set_curjaw(yaw);
		gunInfoCH.set_curpitch(pitch);
		gunInfoCH.set_curspread(spreed);
		PB_Vector3 *v3 = gunInfoCH.mutable_curpos();
		if (v3)
		{
			v3->set_x(pos.x);
			v3->set_y(pos.y);
			v3->set_z(pos.z);
		}
		GetGameNetManagerPtr()->sendToHost(PB_GUN_INFO_CH, gunInfoCH);
	}
}

bool MpPlayerControl::syncUseItemInfo(int itemid, int status, bool onshift/* =false */, unsigned int useTick/*=0*/)
{
	useTick = Rainbow::Timer::getSystemTick();
	
	if (m_pWorld && m_pWorld->isRemoteMode())
	{
		PB_ItemUseCH itemUseCH;
		itemUseCH.set_itemid(itemid);
		itemUseCH.set_status(status);
		itemUseCH.set_shift(onshift ? 1 : 0);
		itemUseCH.set_usetick(useTick);

		float yaw = 0.f;
		float pitch = 0.f;
		float spreed = 0.f;
		WCoord pos;

		getGunAimInfo(itemid, yaw, pitch, spreed, pos);
		
		if (getGunComponent()->getGunDef() != NULL)
		{
			itemUseCH.set_fireinterval(getGunComponent()->getGunUse(itemid));
		}
		else
		{
			itemUseCH.set_fireinterval(0);
		}

		itemUseCH.set_curyaw(yaw);
		itemUseCH.set_curpitch(pitch);
		itemUseCH.set_curspread(spreed);
		
		PB_Vector3* curPos = itemUseCH.mutable_curpos();
		if (curPos) {
			curPos->set_x(pos.x);
			curPos->set_y(pos.y);
			curPos->set_z(pos.z);
		}
		GetGameNetManagerPtr()->sendToHost(PB_ITEM_USE_CH, itemUseCH);
	}

	return true;
}

void MpPlayerControl::kill()
{
	if (m_pWorld && m_pWorld->isRemoteMode())
	{
		PB_Empty ep;
		GetGameNetManagerPtr()->sendToHost(PB_PLAYER_KILLME, ep);
		return;
	}

	ClientPlayer::kill();
}

void MpPlayerControl::setFlying(bool b)
{
	PlayerControl::setFlying(b);
	AntiSetting::logFlagChange(5, b, "mp");
}
void MpPlayerControl::setSneaking(bool b)
{
	PlayerControl::setSneaking(b);
	AntiSetting::logFlagChange(11, b, "mp");
}

inline void MpPlayerControl::setFlownInThisGame(bool b)
{
	m_FlownInThisGame = true;
	AntiSetting::logFlagChange(20, true, "mp");
}

void MpPlayerControl::setMoveUp(int dir)
{
	PlayerControl::setMoveUp(dir);
	if (dir > 0)
		setFlownInThisGame(true);
}

void MpPlayerControl::createPolaroidPhotoItem(string userdata)
{
	jsonxx::Object infoObj;
	if (!infoObj.parse(userdata))
		return;
	if (!infoObj.has<jsonxx::String>("pngpath"))
		return;
	if (!infoObj.has<jsonxx::Number>("uin"))
		return;
	if (m_pWorld->isRemoteMode())
	{
		// 同步到主机，让主机同步给其他客机
		GetSandBoxManagerPtr()->sendToHost("PB_POLAROID_PHOTOITEM_CH", infoObj.bin(), infoObj.binLen());
	}
	else
	{
		ClientPlayer::createPolaroidPhotoItem(userdata);
		GetSandBoxManagerPtr()->sendBroadCast("PB_POLAROID_PHOTOITEM_HC", infoObj.bin(), infoObj.binLen());
	}
}

void MpPlayerControl::writePolaroidAlbumInfo(int gridIndex, std::string userdata)
{
	if (userdata.empty())
		return;
	if (userdata.length() >= MAX_ITEM_USERDATASTR)
		LOG_INFO("syncPolaroidAlbumInfo album info is too large");
	size_t len = (userdata.length() >= MAX_ITEM_USERDATASTR) ? (MAX_ITEM_USERDATASTR - 1) : userdata.length();
	if (m_pWorld->isRemoteMode())
	{
		// 同步到主机，让主机同步给其他客机
		PB_ItemGridUserData griduserdata;
		griduserdata.set_uin(getUin());
		griduserdata.set_gridindex(gridIndex);
		griduserdata.set_userdatastr(userdata.c_str(), len);
		griduserdata.set_type(2);
		GetGameNetManagerPtr()->sendToHost(PB_SYNC_GRIDUSERDATA_CH, griduserdata);
	}
	else
	{
		// 同步给其他客机
		PB_ItemGridUserData griduserdata;
		griduserdata.set_uin(getUin());
		griduserdata.set_gridindex(gridIndex);
		griduserdata.set_userdatastr(userdata.c_str(), len);
		griduserdata.set_type(2);
		GetGameNetManagerPtr()->sendBroadCast(PB_SYNC_GRIDUSERDATA_HC, griduserdata);
	}
}

AccountHorseComponent* MpPlayerControl::sureAccountHorseComponent()
{
	auto comp = GetComponent<AccountHorseComponent>();
	if (!comp)
	{
		comp = CreateComponent<MPAccountHorseComponent>("AccountHorseComponent");
	}

	return comp;
}

PetSummonComponent* MpPlayerControl::surePetSummonComponent()
{
	auto comp = GetComponent<PetSummonComponent>();
	if (!comp)
	{
		comp = CreateComponent<MPPetSummonComponent>("PetSummonComponent");
	}

	return comp;
}

InteractTamedMobComponent* MpPlayerControl::SureInteractTamedMobComponent()
{
	auto interactTaedMobComp = GetComponent<InteractTamedMobComponent>();
	if (!interactTaedMobComp)
	{
		interactTaedMobComp = CreateComponent<MPInteractTamedMobComponent>("InteractTamedMobComponent");
	}
	return interactTaedMobComp;
}

void MpPlayerControl::preMoveTick()
{
	Super::preMoveTick();
	if (m_MoveControl)
		m_MoveControl->setPrePosition(getPosition());

}