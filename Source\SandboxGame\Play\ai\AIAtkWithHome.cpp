﻿
#include "AIAtkWithHome.h"
#include "ActorVision.h"
#include "ActorVehicleAssemble.h"
#include "RiddenComponent.h"
#include "ToAttackTargetComponent.h"
#include "ActorBodySequence.h"
#include "navigationpath.h"
#include "PathEntity.h"
#include "ClientPlayer.h"
#include "LuaInterfaceProxy.h"

AIAtkWithHome::AIAtkWithHome(ClientMob *pActor, int atkeetype, bool trace, float speed) :AIBase(pActor), m_bAtkeeType(atkeetype), m_trace(trace),m_TraceTimer(0),m_Speed(speed),m_AttackTick(0), m_PathEntity(NULL)
{
	setMutexBits(3);

	float AttackInterval = m_pMobActor->getDef()->AttackInterval;//发起攻击间隔（秒）

	m_AttacAnimkTick = m_pMobActor->getAttackAnimTicks(ATTACK_PUNCH);
	m_AttackTick = 20* AttackInterval;
	//isReturnHome = false;
	//returnHomeTick = 20;
	m_homePoint = WCoord(0, -1, 0);
}

AIAtkWithHome::~AIAtkWithHome()
{
	if(m_PathEntity) m_PathEntity->Release();
}

bool AIAtkWithHome::willRun()
{

	if(m_pMobActor->getDef() && m_pMobActor->getDef()->TraceRange > 0 )
	{
		if (m_homePoint.x == 0 && m_homePoint.y == -1 && m_homePoint.z == 0) {
			m_homePoint = CoordDivBlock(m_pMobActor->getSpawnPoint());
			m_pMobActor->setHome(m_pMobActor->getDef()->TraceRange*100, m_homePoint.x * 100, m_homePoint.y * 100, m_homePoint.z * 100);
		}

		if (m_pMobActor->isNeedRetHome()) {
			return false;
		}
		
		WCoord pos = m_pMobActor->getPosition();
		if (!m_pMobActor->isInHomeDist(pos.x, pos.y, pos.z))
		{
			m_pMobActor->getNavigator()->clearPathEntity();
			m_pMobActor->setNeedRetHome(true);
			return false;
		}
		m_pMobActor->setNeedRetHome(false);
	}

	ClientActor *target = nullptr;
	auto targetComponent = m_pMobActor->getToAttackTargetComponent();
	if (targetComponent)
	{
		target = targetComponent->getTarget();
	}
	if (NULL == target || target->isDead() || target->needClear())
	{
		return false;
	}

	//判断被攻击对象的类型，以后细化
	if (m_bAtkeeType)
	{
		return false;
	}

	if (atkDist(target)) {
		return true;
	}

	if(m_PathEntity) m_PathEntity->Release();
	m_PathEntity = m_pMobActor->getNavigator()->getPathTo(target);
	return m_PathEntity != NULL;
}

bool AIAtkWithHome::continueRun()
{
	//检查是否出圈
	if(m_pMobActor->getDef() && m_pMobActor->getDef()->TraceRange > 0 )
	{
		WCoord pos = m_pMobActor->getPosition();
		if (!m_pMobActor->isInHomeDist(pos.x, pos.y, pos.z))
		{
			m_pMobActor->getNavigator()->clearPathEntity();
			return false;
		}
	}

	ClientActor *target = nullptr;
	auto targetComponent = m_pMobActor->getToAttackTargetComponent();
	if (targetComponent)
	{
		target = targetComponent->getTarget();
	}
	if (NULL == target || target->isDead() || target->needClear())
	{
		return false;
	}

	if (m_trace)
	{
		WCoord targetpos = target->getLocoMotion()->getPosition();
		return m_pMobActor->isInHomeDist(targetpos.x, targetpos.y, targetpos.z);
	}
	else
	{
		if (atkDist(target))
		{
			return true;
		}
		
		return (!m_pMobActor->getNavigator()->noPath());
	}
}

void AIAtkWithHome::start()
{
	m_pMobActor->getNavigator()->setPath(m_PathEntity, m_Speed);
	m_PathEntity = NULL;
	m_TraceTimer = 0;
}

void AIAtkWithHome::reset()
{
	m_pMobActor->getNavigator()->clearPathEntity();
}

//判断是否在攻击范围内
bool AIAtkWithHome::atkDist(ClientActor *pActor)
{
	ClientMob *mob = dynamic_cast<ClientMob*>(m_pMobActor);
	double dist = 0;
	CollideAABB box;
	pActor->getCollideBox(box);
	if (mob)
	{
		dist = mob->m_Def->AttackDistance*BLOCK_SIZE + mob->m_Def->Width*mob->m_Def->ModelScale / 2 + box.dim.x / 2;
	}
	else
		return false;

	if (pActor->getObjType() == OBJ_TYPE_ROLE)
	{
		ClientPlayer* player = dynamic_cast<ClientPlayer*>(pActor);
		if (player && player->getRidingVehicle())
		{
			ClientActor* attackTarget = static_cast<ActorManager*>(m_pMobActor->getWorld()->getActorMgr())->findActorByWID(player->getRidingVehicle());
			if (attackTarget)
			{
				ActorVehicleAssemble *vehicle = dynamic_cast<ActorVehicleAssemble *>(attackTarget);
				if (vehicle)
				{
					WCoord pos;
					pos = WCoord(m_pMobActor->getEyePosition());
					float yaw;
					float pitch;
					yaw = m_pMobActor->getLocoMotion()->m_RotateYaw;
					pitch = m_pMobActor->getLocoMotion()->m_RotationPitch;
					Rainbow::Vector3f dir;
					PitchYaw2Direction(dir, yaw, pitch);
					MINIW::WorldRay ray;
					ray.m_Origin = pos.toWorldPos();
					ray.m_Dir = dir;
					ray.m_Range = 10*BLOCK_FSIZE;
					int blockID = 0;
					float t = 0;
					int x,y,z;
					if (vehicle->intersect(ray, t, blockID, x, y, z) && dist >= t)
					{
						return true;
					}
					else
					{
						return false;
					}
				}
			}
		}
		if (player)
		{
			auto RidComp = player->getRiddenComponent();
			if (RidComp && RidComp->isRiding())
			{
				auto pRide = RidComp->getRidingActor();
				if (pRide)
				{
					pRide->getCollideBox(box);
					if (mob->m_Def)
						dist = mob->m_Def->AttackDistance*BLOCK_SIZE*2 + mob->m_Def->Width*mob->m_Def->ModelScale / 2 + box.dim.x / 2;
				}
			}

		}
		//如果是玩家躺在床上，把判定距离改大.一般攻击距离都是1格以上, 玩家位置在床头, 怪物在床尾后的一格,相距最大不超3格
		//玩家坐在发射器上同理
		if (player != NULL && (player->isRestInBed() || player->getUsingEmitter()) && mob->m_Def->AttackType == ATTACK_PUNCH)
		{
			World* pworld = player->getWorld();
			if (pworld)
			{
				dist += 200;
			}
		}
	}
	dist = dist*dist;
	WCoord targetpos = pActor->getLocoMotion()->getPosition();
	if(m_pMobActor->getSquareDistToPos(targetpos.x, targetpos.y, targetpos.z) <= dist)
	{
		return true;
	}
	else
	{
		return false;
	}
}

void AIAtkWithHome::update()
{
	ClientActor *target = nullptr;
	auto targetComponent = m_pMobActor->getToAttackTargetComponent();
	if (targetComponent)
	{
		target = targetComponent->getTarget();
	}
	if (NULL == target)  return;

	bool isAtkDist = atkDist(target);

	m_pMobActor->setLookAt(target, 30.0, 30.0);
	ActorVision *vision = m_pMobActor->getVision();
	if ((m_trace || m_pMobActor->getVision()->canSeeInAICache(target)) && !isAtkDist && (--m_AttacAnimkTick <= 0))
	{
		if(m_pMobActor->getAttrib()->getMoveSpeed()*m_Speed != 0)
		{
			m_TraceTimer = 4 + GenRandomInt(0, 6);			
			m_pMobActor->getNavigator()->tryMoveTo(target, m_Speed);
			//m_pMobActor->stopAnim(SEQ_ATTACK);
			m_pMobActor->playAnim(SEQ_WALK);
		}
	}

	if (--m_AttackTick <= 0)
	{
		if (isAtkDist && m_pMobActor->getVision()->canSee(target))
		{
			m_AttackTick = 20 * m_pMobActor->getDef()->AttackInterval;
			
			//近程:ATTACK_PUNCH=0  远程:ATTACK_RANGE=1
			if(m_pMobActor->getDef()->AttackType == ATTACK_PUNCH)//916冒险 2021/08/18 codeby:wudeshen
			{
				if (m_pMobActor->getDefID() == 3106)
				{
					int rdm = GenRandomInt(1, 100);
					if (rdm < 50)
					{
						m_pMobActor->attackActor(target, SEQ_ATTACK);
					}
					else
					{
						m_pMobActor->attackActor(target, SEQ_RUB_HAND);
					}
				}
				else if (m_pMobActor->getDefID() == 3824 || m_pMobActor->getDefID() == 3829)
				{
					m_pMobActor->attackActor(target, SEQ_SCORPION_ATTACK);
				}
				else
				{
					m_pMobActor->getNavigator()->clearPathEntity();
					m_pMobActor->attackActor(target, SEQ_ATTACK);//发起攻击
					m_AttacAnimkTick = m_pMobActor->getAttackAnimTicks(ATTACK_PUNCH);
				}
			}
		}
	}
}