事件id,事件名称,"事件类型
和程序约定的事件类型","事件位置
[类型;{左下角x坐标 左下角y坐标};{右上角x坐标 右上角y坐标}]",飞机模型,宝箱ID,接触伤害,"首次触发时间(秒)
服务器开启后",间隔时间(秒),是否可用,最大高度,最大范围,描述,是否调试,箱子模型
ID,Name,EventType,Pos,PlanceModel,Treasure,Harm,FirstTime,Interval,Enable,MaxHeight,MaxSize,Description,IsDebug,ChestModel
100000,系统空投,sys_airdrop,[1;{-800 -800};{800 800}],11000122,2331,500,900,1800,TRUE,200,2000,,FALSE,block
110000,召唤空投,player_airdrop,[],11000122,2331,500,,,TRUE,200,3000,,FALSE,block
,,,,,,,,,,,,,,
,,,,,,,,,,,,,,
