
#include "container.h"
#include "DefManagerProxy.h"
#include "blocks/special_blockid.h"
#include "Common/OgreShared.h"
#include "proto_common.h"
#include "proto_common.pb.h"
#include "OgreScriptLuaVM.h"
#include "WorldManager.h"
#include "gamemode/GameModeDef.h"
#include "RuneDef.h"
#include "SandboxCoreDriver.h"
#include "SandboxEventDispatcherManager.h"
#include "SandboxTimer.h"
#include "ChunkSave_generated.h"
#include "UgcComponentArray_generated.h"
//#include "SandboxGameDef.h"
#include "itemdata/GridDataComponent.h"

using namespace MNSandbox;
//rune符文相关: 获取符文数据//code by:tanzhenyu
GridRuneData& BackPackGrid::getRuneData()
{
	return runedata;
}

const GridRuneData& BackPackGrid::getRuneData()const {
	return runedata;
}
//耐久附魔改变(增加/删除 升级/降级)后   对耐久值的影响 code by:tanzhenyu
void BackPackGrid::changeDurationOnRuneOrEnchantChange(int old_dur_enchant)
{
	if(def)
	{
		DefDataTable<ToolDef> &toolTable = GetDefManagerProxy()->getToolTable();
		ToolDef *tooldef = toolTable.GetRecord(def->ID);
		if(tooldef)
		{
			int curenchant = enchant_num > 0 ?  getDurationEnchant() : runedata.getDuration();
			//durable += int(curenchant - old_dur_enchant) * tooldef->Duration / 100;

			/* 都是int类型会导致精度丢失
			// 需要按百分比计算
			int oldMax = tooldef->Duration * (100 + old_dur_enchant) / 100;
			int newMax = tooldef->Duration * (100 + curenchant) / 100;
			int oldRate = durable * 100 / oldMax; // 算出当前比例
			durable = oldRate * newMax / 100;
			*/
			// 需要按百分比计算
			int old_durable = durable * 100 / (old_dur_enchant + 100);
			durable = (curenchant + 100) * old_durable / 100;
			durable = Rainbow::Clamp(durable, 1, getMaxDuration());
		}
	}
}
//从另一个背包格子 加载附魔和符文数据 code by:tanzhenyu 
void BackPackGrid::loadRunesAndEnchants(const BackPackGrid* data)
{
	if(!data || data == this)
		return;

	if(data->getNumEnchant() > 0)
		setEnchants(data->getNumEnchant(), data->getEnchants());
	else
		memcpy(&runedata, &data->runedata, sizeof(runedata));
}
//是否有(附魔或符文) code by:tanzhenyu
bool BackPackGrid::hasRuneOrEnchants()const
{
	return enchant_num > 0 || runedata.getRuneNum() > 0;
}
//替换一条符文 code by:tanzhenyu
bool BackPackGrid::replaceRune(const GridRuneItemData &one, int index)
{
	if(index >= runedata.getRuneNum() || index < 0)
		return false;

	int old_dur_enchant = runedata.getDuration();
	if (old_dur_enchant == 0) old_dur_enchant = getDurationEnchant();

	if(runedata.setOneGridRuneItem(one, index)){
		changeDurationOnRuneOrEnchantChange(old_dur_enchant);
		return true;
	}
	return false;
}
//添加一条符文 code by:tanzhenyu
bool BackPackGrid::addRune(const GridRuneItemData &one, bool isCountDur/* = true*/)//添加一条符文
{
	if (!def) return false;

	int old_dur_enchant = runedata.getDuration();
	if (old_dur_enchant == 0) old_dur_enchant = getDurationEnchant();
	bool succ = runedata.addOneGridRuneItem(one);
	if(succ){
		if(def->EnchantAfterID > 0)//配置定义  如果附魔后需替换id
		{
			const ItemDef *newdef = GetDefManagerProxy()->getItemDef(def->EnchantAfterID);
			if (newdef != NULL)
				setItemDef(newdef); //def = newdef;
		}

		int enchantType = one.getEnchantType();
		if (isCountDur && enchantType == ENCHANT_DURABLE)
		{
			changeDurationOnRuneOrEnchantChange(old_dur_enchant);
		}
	}
	return true;
}
//移除一条符文 code by:tanzhenyu
bool BackPackGrid::removeRune(int id, bool isCountDur/* = true*/)//移除一条符文
{
	int index = runedata.findIndex(id);
	if (index < 0) return false;//找不到

	int old_dur_enchant = runedata.getDuration();
	if (old_dur_enchant == 0) old_dur_enchant = getDurationEnchant();
	if(runedata.removeOneGridRuneItem(id))
	{
		if (isCountDur)
		{
			int enchantType = runedata.getItemByIndex(index).getEnchantType();
			if (enchantType == ENCHANT_DURABLE)
			{
				//获取删除前的状态: 耐久附魔百分比加成
				changeDurationOnRuneOrEnchantChange(old_dur_enchant);
			}
		}

		return true;
	}
	return false;
}

//符文信息 序列化/反序列化------------------------------ code by:tanzhenyu
void BackPackGrid::loadRunesAndEnchants(const PB_ArmFumo& src)
{
	int fumoidsSize = src.fumoids_size();
	for (int i = 0; i < fumoidsSize; i++)
	{
		enchants[i] = src.fumoids().Get(i);
	}

	runedata.load(src.runes(), src.runes_size());
}
void BackPackGrid::saveRunesAndEnchants(PB_ArmFumo* dest) const
{
	for (int i = 0; i< getNumEnchant(); i++)
	{
		dest->add_fumoids(getIthEnchant(i));
	}
	runedata.save(dest->mutable_runes());
}

void BackPackGrid::loadRunesAndEnchants(const PB_ItemData &src)
{
	enchant_num = src.enchs_size();
	for (int i = 0; i < enchant_num; i++)
	{
		enchants[i] = src.enchs().Get(i);
	}
	runedata.load(src.runes(), src.runes_size());
}
void BackPackGrid::saveRunesAndEnchants(PB_ItemData *dest) const
{
	for (int i = 0; i< getNumEnchant(); i++)
	{
		dest->add_enchs(getIthEnchant(i));
	}

	runedata.save(dest->mutable_runes());
}
//符文信息 序列化/反序列化------------------------------end    code by:tanzhenyu



void BackPackGrid::reset(int i)
{
	index = i;

	SetBackPackGrid(*this, 0, 0);
}

void BackPackGrid::clear()
{
	SetBackPackGrid(*this, 0, 0);
}

int BackPackGrid::getIndex() const
{
	return index;
}

void BackPackGrid::setIndex(int i)
{
	index = i;
}

void BackPackGrid::recoverToughness()
{
	if (m_touRecTimer)
	{
		SANDBOX_RELEASE(m_touRecTimer);
	}

	// 韧性不足
	if (toughness < getMaxToughness())
	{
		// 设置定时器，缓慢恢复韧性
		const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(m_ItemID);
		if (tooldef)
		{
			float recoverDelay = tooldef->TouRecoverDelay;
			int recoverSpeed = tooldef->TouRecoverSpeed;

			//盾牌韧性恢复速度附魔 renjie
			{
				for (int i = 0; i < getRuneNum(); i++)
				{
					GridRuneItemData runeData = getRuneData().getItemByIndex(i);
					if (runeData.getEnchantType() == ENCHANT_TOUGH_REC) {
						int calVal = recoverSpeed * runeData.getRuneVal0();
						recoverSpeed += calVal;
					}
				}
			}
			
			m_touRecTimer = MNTimer::CreateTimer(SANDBOX_NEW(ListenerFunctionRef<AutoRef<MNTimer>>, [this, recoverSpeed](AutoRef<MNTimer> t)
			{
				BackPackGrid *grid = this;
				if (grid)
				{
					grid->addToughness(recoverSpeed);
					if (grid->toughness >= grid->getMaxToughness())
					{
						SANDBOX_RELEASE(grid->m_touRecTimer);
					}
				}
			}), recoverDelay, true, 1.0f);
		}
	}
}

void BackPackGrid::stopRecoverToughness()
{
	SANDBOX_RELEASE(m_touRecTimer);
}

int BackPackGrid::getNum() const
{
	return DecodeActorAttr(num);
}

bool BackPackGrid::needSrcItemId() const
{
	if (def && def->CopyID > 0)
	{
		switch (def->CopyID)
		{
		case 12058:		// 红色福袋
			return true;
		}
	}

	return false;
}

bool BackPackGrid::canChangeDuration() const
{
	if (g_WorldMgr && g_WorldMgr->isGameMakerRunMode())
	{
		int opId = 0;
		float val = 0.0f;
		g_WorldMgr->getRuleOptionID(GMRULE_CHANGE_DURABILITY, opId, val);
		if (opId == 2)
		{
			return false;
		}
	}

	return true;
}

int BackPackGrid::getItemID() const
{
	return def == NULL ? 0 : def->ID;
}

int BackPackGrid::getInvolvedID() const
{
	return def == NULL ? 0 : def->InvolvedID;
}

int BackPackGrid::getSrcItemID() const
{
	if (!def)
	{
		return 0;
	}
	return def->CopyID > 0 ? def->CopyID : def->ID;
}

void BackPackGrid::setUserdataStr(const char *str)
{
	if (str != NULL)
		userdata_str = str;
}

std::string BackPackGrid::getUserdataStr()
{ 
	//如果有枪械组件，就返回当前枪械的等级，不同等级配件不一样
	GridDataComponent* comp = getGunDataComponent();
	if (comp)
	{
		int level = comp->GetCurLevel();
		return IntToString(level);
	}
	return userdata_str; 
}

void BackPackGrid::setSidStr(const char *pSid)
{
	sid_str = pSid;
}

bool BackPackGrid::isEmpty() const
{
	return def==NULL;
}



int BackPackGrid::getNumEnchant() const
{
	return enchant_num;
}

const int *BackPackGrid::getEnchants() const
{
	return enchants;
}

int *BackPackGrid::getEnchants()
{
	return enchants;
}

int BackPackGrid::getIthEnchant(int i) const
{
	assert(i>=0 && i<enchant_num);
	return enchants[i];
}

int BackPackGrid::getMaxStack() const
{
	if(def) return def->StackMax;
	else return 0;
}

int BackPackGrid::addNum(int n)
{
	if (n == 0) return getNum();

	int n2 = getNum() + n;
	if(n2 < 0) n2 = 0;
	setNum(n2);
	return n2;
}

void BackPackGrid::setNum(int n)
{
	num = EncodeActorAttr(n);
}

std::string BackPackGrid::getJsonStr()
{
	if (isEmpty() || getNum() == 0)
	{
		return "";
	}

	jsonxx::Object obj;
	save(obj);
	return obj.json_nospace();
}

void BackPackGrid::loadJsonStr(std::string jsonstr)
{
	jsonxx::Object obj;
	bool ret = obj.parse(jsonstr);
	if (ret)
	{
		load(obj);
	}
}

void  BackPackGrid::setModItemName(const std::string& name)
{
	jsonxx::Object obj;
	if (userdata_str.length() > 0)
	{
		bool ret = obj.parse(userdata_str);
		if (ret)
		{
			if (obj.has<jsonxx::String>("ModItemName"))
			{
				obj.get<jsonxx::String>("ModItemName") = name;
				userdata_str = obj.json();
				return;
			}	
		}
	}
	obj << "ModItemName" << name;
	userdata_str = obj.json();
}
void  BackPackGrid::setModItemDesc(const std::string& desc)
{
	jsonxx::Object obj;
	if (userdata_str.length() > 0)
	{
		bool ret = obj.parse(userdata_str);
		if (ret)
		{
			if (obj.has<jsonxx::String>("ModItemDesc"))
			{
				obj.get<jsonxx::String>("ModItemDesc") = desc;
				userdata_str = obj.json();
				return;
			}
		}
	}
	obj << "ModItemDesc" << desc;
	userdata_str = obj.json();
}

void  BackPackGrid::setModExtradata(const std::string& extradata)
{
	jsonxx::Object obj;
	if (userdata_str.length() > 0)
	{
		bool ret = obj.parse(userdata_str);
		if (ret)
		{
			if (obj.has<jsonxx::String>("ModExtradata"))
			{
				obj.get<jsonxx::String>("ModExtradata") = extradata;
				userdata_str = obj.json();
				return;
			}
		}
	}
	obj << "ModExtradata" << extradata;
	userdata_str = obj.json();
}

void BackPackGrid::setKVData(const std::string& key, int value)
{
	m_KVS[key] = value;
}

// key各个使用的地方自行定义，最好不要重复
// 已使用：枪械配件  slot1-slot4、duration1-duration4  枪械子弹 magzine_id
//             
int BackPackGrid::getKVData(const std::string& key) const
{
	auto it = m_KVS.find(key);
	if (it != m_KVS.end())
	{
		return it->second;
	}
	return 0;
}

void BackPackGrid::setDataEX(int value)
{
	m_nDataEX = value;
};

std::string BackPackGrid::getModItemName()
{
	std::string name;
	if (userdata_str.length() > 0)
	{
		jsonxx::Object obj;
		bool ret = obj.parse(userdata_str);
		if (ret)
		{
			if (obj.has<jsonxx::String>("ModItemName"))
			{
				name = obj.get<jsonxx::String>("ModItemName");
			}
		}
	}
	return name;
}
std::string  BackPackGrid::getModItemDesc()
{
	std::string desc;
	if (userdata_str.length() > 0)
	{
		jsonxx::Object obj;
		bool ret = obj.parse(userdata_str);
		if (ret)
		{
			if (obj.has<jsonxx::String>("ModItemDesc"))
			{
				desc = obj.get<jsonxx::String>("ModItemDesc");
			}
		}
	}
	return desc;
}
std::string  BackPackGrid::getModExtradata()
{
	std::string extradata;
	if (userdata_str.length() > 0)
	{
		jsonxx::Object obj;
		bool ret = obj.parse(userdata_str);
		if (ret)
		{
			if (obj.has<jsonxx::String>("ModExtradata"))
			{
				extradata = obj.get<jsonxx::String>("ModExtradata");
			}
		}
	}
	return extradata;
}



int BackPackGrid::addDuration(int d, bool limit_maxdur)
{
	if (d < 0)//只有扣耐久时才生效
	{
		// 道具不消耗耐久
		if (!canChangeDuration())
		{
			return durable;
		}

		//- 概率不掉耐久：概率，百分比数值，计算时变为区间，随机比值确定是否扣除 renjie
		{
			for (int i = 0; i < getRuneNum(); i++)
			{
				GridRuneItemData runeData = getRuneData().getItemByIndex(i);
				if (runeData.getEnchantType() == ENCHANT_DURABLE_PROB) {
					if (GenRandomInt(0, 100) < (runeData.getRuneVal0() * 100))
					{
						return durable;
					}
				}
			}
		}
	}

	durable += d;
	if(durable < 0) durable = 0;
	
	if(limit_maxdur && d>0)
	{
		int md = getMaxDuration();
		if(durable > md) durable = md;
	}

	return durable;
}

int BackPackGrid::addToughness(int t)
{
	toughness += t;
	if (toughness < 0) toughness = 0;

	int mt = getMaxToughness();
	if (toughness > mt) toughness = mt;

	return toughness;
}

void BackPackGrid::setEnchants(int n, const int *ids)
{
	if(n > MAX_ITEM_ENCHANTS) n = MAX_ITEM_ENCHANTS;
	enchant_num = n;
	memset(enchants, 0, sizeof(enchants));

	for(int i=0; i<n; i++)
	{
		enchants[i] = ids[i];
	}
}

//附魔逻辑整理 ---code by:tanzhenyu
bool BackPackGrid::addEnchant(int id, bool isCountDur/* =true */)
{
	if(def == NULL || enchant_num>=MAX_ITEM_ENCHANTS) 
		return false;

	const EnchantDef *enchdef = GetDefManagerProxy()->getEnchantDef(id);//注意这里的id = EnchantType * 100 + EnchantLevel
	if(enchdef == NULL) 
		return false;

	int old_dur_enchant = 0;
	if(enchdef->EnchantType == ENCHANT_DURABLE) //获取添加前的状态: 耐久附魔百分比加成
		old_dur_enchant = getDurationEnchant();

	int entype = id / 100;
	int enlevel = id % 100;

	int findIndex = findEnchantIndex(entype);
	if(findIndex != -1){//之前已经有了该类型的 附魔
		int level = enchants[findIndex] % 100;
		if(level > enlevel)//之前的等级还高一些   返回添加失败
			return false;

		enchants[findIndex] = id;//新的更高,覆盖
		if(isCountDur && enchdef->EnchantType == ENCHANT_DURABLE)//耐久附魔百分比加成  改变耐久值
			changeDurationOnRuneOrEnchantChange(old_dur_enchant);
		return true;
	}
	//之前没有该类型的附魔
	enchants[enchant_num++] = id;

	if(def->EnchantAfterID > 0)//配置定义  如果附魔后需替换id
	{
		const ItemDef *newdef = GetDefManagerProxy()->getItemDef(def->EnchantAfterID);
		if (newdef != NULL)
			setItemDef(newdef);//def = newdef;
	}
	if(isCountDur && enchdef->EnchantType == ENCHANT_DURABLE)//耐久附魔百分比加成  改变耐久值
		changeDurationOnRuneOrEnchantChange(old_dur_enchant);
	return true;
}

bool BackPackGrid::removeEnchant(int id)	//移除附魔效果
{
	if(def == NULL) 
		return false;

	int enchantType = id / 100;
	int old_dur_enchant = 0;
	if(enchantType == ENCHANT_DURABLE)
		old_dur_enchant = getDurationEnchant();//获取删除前的状态: 耐久附魔百分比加成

	int findIndex = findEnchantIndex(enchantType);
	if(findIndex != -1){
		enchant_num--;
		if( findIndex < enchant_num) 
			memmove(&enchants[findIndex], &enchants[findIndex+1], (enchant_num-findIndex)*sizeof(int));
		enchants[enchant_num] = 0;

		if(enchantType == ENCHANT_DURABLE)//耐久附魔百分比加成  改变耐久值(移除附魔的耐久加成效果)
			changeDurationOnRuneOrEnchantChange(old_dur_enchant);
		return true;
	}

	return false;
}

flatbuffers::Offset<FBSave::ItemGrid> BackPackGrid::save(flatbuffers::FlatBufferBuilder &builder)
{
	auto enchs = builder.CreateVector(enchants, enchant_num);
	auto str = builder.CreateString(userdata_str);
	auto sidStr = builder.CreateString(sid_str);

	flatbuffers::Offset<flatbuffers::Vector<int8_t>> componentsOffset;
	saveDataComponent(builder, componentsOffset);
	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::KVData>>> kvsOffset;
	saveKVS(builder, kvsOffset);
	return FBSave::CreateItemGrid(builder, getItemID(), getNum(), durable, enchs, (short)(int)(size_t)userdata, str, sidStr, 
		(int)(size_t)userdataEx, runedata.save(builder), toughness, componentsOffset, maxDurable, kvsOffset);//符文信息保存code by tanzhenyu
}



flatbuffers::Offset<FBSave::ItemIndexGrid> BackPackGrid::saveWithIndex(flatbuffers::FlatBufferBuilder &builder)
{
	//此处有崩溃，怀疑enchants访问越界
	int enchantnum = enchant_num;
	if (enchant_num < 0)
	{
		enchantnum = 0;
	}
	else if (enchant_num > MAX_ITEM_ENCHANTS)
	{
		enchantnum = MAX_ITEM_ENCHANTS;
	}
	auto enchs = builder.CreateVector(enchants, enchantnum);
	auto str = builder.CreateString(userdata_str);
	auto sidStr = builder.CreateString(sid_str);

	flatbuffers::Offset<flatbuffers::Vector<int8_t>> componentsOffset;
	saveDataComponent(builder, componentsOffset);
	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::KVData>>> kvsOffset;
	saveKVS(builder, kvsOffset);

	return FBSave::CreateItemIndexGrid(builder, index, m_ItemID, getNum(), durable, enchs, (short)(int)(size_t)userdata, 
		str, sidStr, (int)(size_t)userdataEx, runedata.save(builder), toughness, componentsOffset, maxDurable, kvsOffset);//符文信息保存code by tanzhenyu
}

int g_BackgridCheckNumMethod = 0; //-1: 不限制， 0: 超过设置为1， 1：超过设置为StackMax
inline int CheckItemNum(int num, const ItemDef* def)
{
	if (num < 0)
	{
		return 1;
	}
	else if (def && num > def->StackMax && num > 0)
	{
		//if(g_BackgridCheckNumMethod < 0) return num;
		//else if(g_BackgridCheckNumMethod == 0) return 1;
		//else return def->StackMax;
		if (num > 200)
			num = 200;
		return num;
	}
	else return num;
}

int BackPackGrid::savePB(PB_ItemIndexGrid* equip)
{
	equip->set_index(getIndex());
	equip->set_itemid(getItemID());
	equip->set_num(getNum());
	equip->set_durable(getDuration());
	equip->set_maxdurable(getMaxDuration());
	for (int j = 0; j < getNumEnchant() && j < MAX_ITEM_ENCHANTS; ++j)
	{
		equip->add_enchants(getEnchants()[j]);
	}
	equip->set_userdata((short)(int)(size_t)(userdata));
	if (userdata_str != "")
		equip->set_userdata_str(userdata_str);
	if (sid_str != "")
		equip->set_sid_str(sid_str);
	equip->set_userdataex((int)(size_t)(userdataEx));
	const GridRuneData& runeData = getRuneData();
	if (runeData.getRuneNum() > 0)
		runeData.save(equip->mutable_runes());

	if (m_effects.size() > 0)
	{
		for (auto it = m_effects.begin(); it != m_effects.end(); it++)
		{
			auto info = equip->add_effects();
			info->set_effectname(it->second.effectname);
			info->set_effecscale(it->second.scale);
		}
	}
	equip->set_toughness(getToughness());
	if (m_KVS.size() > 0)
	{
		for (auto it = m_KVS.begin(); it != m_KVS.end(); it++)
		{
			auto kv = equip->add_kvs();
			kv->set_key(it->first);
			kv->set_val(it->second);
		}
	}
	equip->set_dataex(m_nDataEX);
	saveDataComponentPB(equip);
	return 0;
}
int BackPackGrid::loadPB(const PB_ItemIndexGrid& equip)
{
	index = equip.index();
	setItemDef(GetDefManagerProxy()->getItemDef(equip.itemid(), equip.itemid() > 0));
	//def = GetDefManagerProxy()->getItemDef(equip.itemid(), equip.itemid() > 0);
	setNum(CheckItemNum(equip.num(), def));
	if (getNum() == 0)
	{
		clear();
		return 0;
	}
	durable = equip.durable();
	maxDurable = equip.maxdurable();
	enchant_num = equip.enchants_size();
	if (enchant_num > MAX_ITEM_ENCHANTS) enchant_num = 0;
	for (int i = 0; i < enchant_num; i++)
	{
		enchants[i] = equip.enchants(i);
	}
	int32_t t = equip.userdata();
	userdata = (void*)t;
	t = equip.userdataex();
	userdataEx = (void*)(t);
	if (equip.has_userdata_str())
		userdata_str = equip.userdata_str();
	if (equip.has_sid_str())
		sid_str = equip.sid_str();
	runedata.load(equip.runes(), equip.runes_size());//符文信息加载code by tanzhenyu	

	m_effects.clear();
	if (equip.effects_size() > 0)
	{
		for (int i = 0; i < equip.effects_size(); i++)
		{
			auto info = equip.effects(i);
			GridEffect grideff;
			grideff.effectname = info.effectname();
			grideff.scale = info.effecscale();
			m_effects[info.effectname()] = grideff;
		}
	}
	if (equip.has_toughness())
	{
		toughness = equip.has_toughness();
	}
	m_KVS.clear();
	if (equip.kvs_size() > 0)
	{
		for (int i = 0; i < equip.kvs_size(); i++)
		{
			m_KVS[equip.kvs(i).key()] = equip.kvs(i).val();
		}
	}
	m_nDataEX = equip.dataex();
	loadDataComponentPB(equip);
	return 0;	
}

void BackPackGrid::save(jsonxx::Object &obj)
{
	if (enchant_num)
	{
		obj<<"enchants"<<jsonxx::Array();
		jsonxx::Array &obj_ = (jsonxx::Array &)obj.get<jsonxx::Array>("enchants");
		for (int i=0; i<enchant_num; i++)
		{
			obj_<<enchants[i];
		}
	}
	obj<<"userdata_s"<<userdata_str;
	obj<<"sid"<<sid_str;
	//obj<<"index"<<index;
	obj<<"itemid"<<getItemID();
	obj<<"num"<<getNum();
	obj<<"durable"<<durable;
	obj << "maxdurable" << getMaxDuration();
	obj<<"toughness"<<toughness;
	obj<<"unserdata"<<(int)(size_t)userdata;
	obj<<"unserdata_ex"<<(int)(size_t)userdataEx;
	obj << "gunDamage" << getGunDamage();

	for (auto* comp : m_dataComps)
	{
		if (comp && comp->GetComponentName())
		{
			if (!obj.has<jsonxx::Object>(comp->GetComponentName()))
			{
				obj << comp->GetComponentName() << jsonxx::Object();
			}
			jsonxx::Object& obj_ = (jsonxx::Object&)obj.get<jsonxx::Object>(comp->GetComponentName());
			comp->CreateComponentData(obj_);
		}
	}
		
	obj<<"rune"<<jsonxx::Object();
	jsonxx::Object &obj_ = (jsonxx::Object &)obj.get<jsonxx::Object>("rune");
	runedata.save(obj_);
}

void BackPackGrid::saveWithIndex(jsonxx::Object &obj)
{
	if (enchant_num)
	{
		if (!obj.has<jsonxx::Array>("enchants"))
		{
			obj << "enchants" << jsonxx::Array();
		}
		jsonxx::Array &obj_ = (jsonxx::Array &)obj.get<jsonxx::Array>("enchants");
		for (int i=0; i<enchant_num; i++)
		{
			obj_<<enchants[i];
		}
	}
	obj<<"userdata_s"<<userdata_str;
	obj<<"sid"<<sid_str;
	obj<<"index"<<index;
	obj<<"itemid"<<getItemID();
	obj<<"num"<<getNum();
	obj<<"durable"<<durable;
	obj<<"toughness"<<toughness;
	obj<<"unserdata"<<(int)(size_t)userdata;
	obj<<"unserdata_ex"<<(int)(size_t)userdataEx;

	if (!obj.has<jsonxx::Object>("rune"))
	{
		obj << "rune" << jsonxx::Object();
	}
	jsonxx::Object &obj_ = (jsonxx::Object &)obj.get<jsonxx::Object>("rune");
	runedata.save(obj_);

	for (auto* comp : m_dataComps)
	{
		if (comp && comp->GetComponentName())
		{
			if (!obj.has<jsonxx::Object>(comp->GetComponentName()))
			{
				obj << comp->GetComponentName() << jsonxx::Object();
			}
			jsonxx::Object& obj_ = (jsonxx::Object&)obj.get<jsonxx::Object>(comp->GetComponentName());
			comp->LoadComponentData(obj_, true);
		}
	}
}

void BackPackGrid::load(const jsonxx::Object &obj)
{
	std::string str = obj.json();
	if (obj.has<jsonxx::Number>("itemid"))
	{
		int itemid = obj.get<jsonxx::Number>("itemid");
		if (itemid == 0)
		{
			setItem(itemid, 0);
			return;
		}
		setItemDef(GetDefManagerProxy()->getItemDef(itemid, itemid > 0));
		//def = GetDefManagerProxy()->getItemDef(itemid, itemid>0);
	}
	int num = 0;
	if (obj.has<jsonxx::Number>("num"))
	{
		num = obj.get<jsonxx::Number>("num");
	}
	if (index >= BUILDBLUEPRINT_START_INDEX && index < BUILDBLUEPRINT_START_INDEX + 1000)
		setNum(num);
	else
		setNum(CheckItemNum(num, def));
	if(getNum() == 0 && (index < BUILDBLUEPRINT_START_INDEX || index >= BUILDBLUEPRINT_START_INDEX+1000))
	{
		clear();
		return;
	}
	if (obj.has<jsonxx::Number>("durable"))
		durable = obj.get<jsonxx::Number>("durable");
	if (obj.has<jsonxx::Number>("toughness"))
		toughness = obj.get<jsonxx::Number>("toughness");

	if (obj.has<jsonxx::Array>("enchants"))
	{
		const jsonxx::Array &enchants_ = obj.get<jsonxx::Array>("enchants"); 
		enchant_num = enchants_.size();
		if(enchant_num > MAX_ITEM_ENCHANTS) enchant_num = 0;
		for(int i=0; i<enchant_num; i++)
		{
			enchants[i] = enchants_.get<jsonxx::Number>(i);
		}
	}
	if (obj.has<jsonxx::Number>("unserdata"))
	{
		userdata = (void *)(int)obj.get<jsonxx::Number>("unserdata"); 
	}
	if (obj.has<jsonxx::Number>("unserdata_ex"))
	{
		userdataEx = (void *)(int)obj.get<jsonxx::Number>("unserdata_ex"); 
	}
	if (obj.has<jsonxx::String>("userdata_s"))
	{
		userdata_str = obj.get<jsonxx::String>("userdata_s"); 
	}
	if (obj.has<jsonxx::String>("sid"))
	{
		sid_str = obj.get<jsonxx::String>("sid"); 
	}
	runedata.reset();
	m_effects.clear();
	if (obj.has<jsonxx::Object>("rune"))
	{
		jsonxx::Object &obj_ = (jsonxx::Object &)obj.get<jsonxx::Object>("rune");
		runedata.load(obj_);
	}

	if (obj.has<jsonxx::Object>(GunComp_Name))
	{
		jsonxx::Object& obj_ = (jsonxx::Object&)obj.get<jsonxx::Object>(GunComp_Name);
		AddAndLoadDataComponent(GunComp_Name, obj_);
	}
	else if (obj.has<jsonxx::Object>(EntryComp_Name))
	{
		jsonxx::Object& obj_ = (jsonxx::Object&)obj.get<jsonxx::Object>(EntryComp_Name);
		AddAndLoadDataComponent(EntryComp_Name, obj_);
	}
}
void BackPackGrid::loadWithIndex(const jsonxx::Object &obj)
{
	if (obj.has<jsonxx::Number>("itemid"))
	{
		int itemid = obj.get<jsonxx::Number>("itemid");
		if (itemid == 0)
		{
			setItem(itemid, 0);
			return;
		}
		setItemDef(GetDefManagerProxy()->getItemDef(itemid, itemid > 0));
		//def = GetDefManagerProxy()->getItemDef(itemid, itemid > 0);
	}
	if (obj.has<jsonxx::Number>("index"))
	{
		index = obj.get<jsonxx::Number>("index");
	}
	int num = 0;
	if (obj.has<jsonxx::Number>("num"))
	{
		num = obj.get<jsonxx::Number>("num");
	}
	if (index >= BUILDBLUEPRINT_START_INDEX && index < BUILDBLUEPRINT_START_INDEX + 1000)
		setNum(num);
	else
		setNum(CheckItemNum(num, def));
	if(getNum() == 0 && (index < BUILDBLUEPRINT_START_INDEX || index >= BUILDBLUEPRINT_START_INDEX+1000))
	{
		clear();
		return;
	}
	if (obj.has<jsonxx::Number>("durable"))
		durable = obj.get<jsonxx::Number>("durable");
	if (obj.has<jsonxx::Number>("toughness"))
		toughness = obj.get<jsonxx::Number>("toughness");

	if (obj.has<jsonxx::Array>("enchants"))
	{
		const jsonxx::Array &enchants_ = obj.get<jsonxx::Array>("enchants"); 
		enchant_num = enchants_.size();
		if(enchant_num > MAX_ITEM_ENCHANTS) enchant_num = 0;
		for(int i=0; i<enchant_num; i++)
		{
			enchants[i] = enchants_.get<jsonxx::Number>(i);
		}
	}
	if (obj.has<jsonxx::Number>("unserdata"))
	{
		userdata = (void *)(int)obj.get<jsonxx::Number>("unserdata"); 
	}
	if (obj.has<jsonxx::Number>("unserdata_ex"))
	{
		userdataEx = (void *)(int)obj.get<jsonxx::Number>("unserdata_ex"); 
	}
	if (obj.has<jsonxx::String>("userdata_s"))
	{
		userdata_str = obj.get<jsonxx::String>("userdata_s"); 
	}
	if (obj.has<jsonxx::String>("sid"))
	{
		sid_str = obj.get<jsonxx::String>("sid"); 
	}
	
	/*obj<<"rune"<<jsonxx::Object();
	jsonxx::Array &obj_ = (jsonxx::Array &)obj.get<jsonxx::Object>("rune");
	runedata.load(obj_);*/
	if (obj.has<jsonxx::Object>("rune"))
	{
		jsonxx::Object &obj_ = (jsonxx::Object &)obj.get<jsonxx::Object>("rune");
		runedata.load(obj_);
	}

	if (obj.has<jsonxx::Object>(GunComp_Name))
	{
		jsonxx::Object& obj_ = (jsonxx::Object&)obj.get<jsonxx::Object>(GunComp_Name);
		AddAndLoadDataComponent(GunComp_Name, obj_);
	}
	else if (obj.has<jsonxx::Object>(EntryComp_Name))
	{
		jsonxx::Object& obj_ = (jsonxx::Object&)obj.get<jsonxx::Object>(EntryComp_Name);
		AddAndLoadDataComponent(EntryComp_Name, obj_);
	}
}

void BackPackGrid::load(const FBSave::ItemGrid *src)
{
	setItemDef(GetDefManagerProxy()->getItemDef(src->itemid(), src->itemid() > 0));
	//def = GetDefManagerProxy()->getItemDef(src->itemid(), src->itemid()>0);
	if (index >= BUILDBLUEPRINT_START_INDEX && index < BUILDBLUEPRINT_START_INDEX + 1000)
		setNum(src->num());
	else
		setNum(CheckItemNum(src->num(), def));

	if(getNum() == 0 && (index < BUILDBLUEPRINT_START_INDEX || index >= BUILDBLUEPRINT_START_INDEX+1000))
	{
		clear();
		return;
	}

	durable = src->durable();
	maxDurable = src->maxdurable();
	if (maxDurable < durable || maxDurable == 0)
	{
		maxDurable = getDefMaxDuration();
	}

	enchant_num = src->enchants()->size();
	if(enchant_num > MAX_ITEM_ENCHANTS) enchant_num = 0;

	for(int i=0; i<enchant_num; i++)
	{
		enchants[i] = src->enchants()->Get(i);
	}

	userdata = (void *)src->userdata();
	userdataEx = (void *)src->userdataEx();

	if (src->userdata_str())
	{
		userdata_str = src->userdata_str()->c_str();
	}

	if (src->sid_str())
	{
		sid_str = src->sid_str()->c_str();
	}

	runedata.load(src->runes());//符文信息加载code by tanzhenyu

	toughness = src->toughness();

	loadDataComponent(src->datacomponents());

	// 加载KVS数据
	m_KVS.clear();
	if (src->KVS() && src->KVS()->size() > 0)
	{
		for (unsigned int i = 0; i < src->KVS()->size(); i++)
		{
			const FBSave::KVData* kvData = src->KVS()->Get(i);
			if (kvData && kvData->key())
			{
				m_KVS[kvData->key()->str()] = kvData->val();
			}
		}
	}
	m_nDataEX = src->dataex();
}

void BackPackGrid::load(const FBSave::ItemIndexGrid *src)
{
	if (src == NULL) return;
	index = src->index();
	setItemDef(GetDefManagerProxy()->getItemDef(src->itemid(), src->itemid() > 0));
	//def = GetDefManagerProxy()->getItemDef(src->itemid(), src->itemid() > 0);
	setNum(CheckItemNum(src->num(), def));
	if (getNum() == 0)
	{
		clear();
		return;
	}
	durable = src->durable();
	maxDurable = src->maxdurable();
	if (maxDurable < durable || maxDurable == 0)
	{
		maxDurable = getDefMaxDuration();
	}

	enchant_num = src->enchants()->size();
	if (enchant_num > MAX_ITEM_ENCHANTS) enchant_num = 0;

	for (int i = 0; i < enchant_num; i++)
	{
		enchants[i] = src->enchants()->Get(i);
	}

	userdata = (void *)src->userdata();
	userdataEx = (void *)src->userdataEx();

	if (src->userdata_str())
	{
		userdata_str = src->userdata_str()->c_str();
	}

	if (src->sid_str())
	{
		sid_str = src->sid_str()->c_str();
	}
	runedata.load(src->runes());//符文信息加载code by tanzhenyu

	toughness = src->toughness();

	loadDataComponent(src->datacomponents());

	// 加载KVS数据
	m_KVS.clear();
	if (src->KVS() && src->KVS()->size() > 0)
	{
		for (unsigned int i = 0; i < src->KVS()->size(); i++)
		{
			const FBSave::KVData* kvData = src->KVS()->Get(i);
			if (kvData && kvData->key())
			{
				m_KVS[kvData->key()->str()] = kvData->val();
			}
		}
	}
	m_nDataEX = src->dataex();
}

bool BackPackGrid::haveEnchant(int type)const
{
	auto runeItem = getRuneData();
	if (findEnchantIndex(type) != -1
		|| runeItem.findDef(type) != NULL)
		return true;
	return false;
}


//找到第一个指定类型的附魔 下标  code by tanzhenyu
int BackPackGrid::findEnchantIndex(int type) const
{
	for(int i=0; i<enchant_num; i++)
	{
		if (enchants[i] <= 0) continue;
		const EnchantDef* def = GetDefManagerProxy()->getEnchantDef(enchants[i]);
		if(def && def->ID == type){  //EnchantType有些类型匹配不上,最开始设计的附魔类型就是表格里的ID，用 def->ID == type
			return i;
		}
	}
	return -1;
}


int BackPackGrid::getDurationEnchant() const
{
	if (enchant_num >= MAX_ITEM_ENCHANTS) return 0;

	for (int i = 0; i < enchant_num; i++)
	{
		if (enchants[i] <= 0) continue;
		const EnchantDef* def = GetDefManagerProxy()->getEnchantDef(enchants[i]);
		if(def && def->EnchantType == ENCHANT_DURABLE){
			return int(def->EnchantValue[0]);
		}
	}
	return 0;
}

int BackPackGrid::getMaxDuration() const
{
	return maxDurable;
}

int BackPackGrid::getDefMaxDuration() const
{
	if(def)
	{
		const ToolDef *tooldef = GetDefManagerProxy()->getToolDef(def->ID);
		if (tooldef)
		{
			int percent = enchant_num > 0 ? getDurationEnchant() : runedata.getDuration();
			return tooldef->Duration * (100 + percent) / 100;
		}
	}
	return 0;
}

void BackPackGrid::setMaxDuration(int maxDur)
{
	maxDurable = maxDur;
	if (maxDurable == -1 && def)
	{
		maxDurable = getDefMaxDuration();
	}
}

int BackPackGrid::getToughness() const
{
	return toughness;
}

int BackPackGrid::getMaxToughness() const
{
	if (def)
	{
		const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(def->ID);
		if (tooldef) return tooldef->Toughness;
	}
	return 0;
}

int BackPackGrid::getDuration() const
{
	return durable;
}

void BackPackGrid::setDuration(int dur)
{
	durable = dur;
}

void BackPackGrid::setToughness(int tou)
{
	toughness = tou;
}

int BackPackGrid::getConstUserDataInt() const
{
	return (int)(size_t)userdata;
}

int BackPackGrid::setItem(int itemid, int n, int d, int tou, void *u, int e, int s, const char *p, const char *pSid /*= ""*/)
{
	if(itemid == 0)
	{
		setItemDef(nullptr);
		//def = 0;
		setNum(0);
		durable = -1;
		userdata = 0;
		userdataEx = 0;
		enough = 1;
		sortId = 0;
		enchant_num = 0;
		userdata_str = "";
		sid_str = "";
		runedata.reset();//符文信息初始化code by tanzhenyu
		m_effects.clear();
		toughness = -1;
		m_KVS.clear();
		m_nDataEX = 0;

		stopRecoverToughness();
		removeAllDataComponents();
		return 0;
	}
	else
	{
		sid_str = "";
		//if(itemid > MAX_BLOCK_ID && itemid < 10000) itemid += 10000;
		setItemDef(GetDefManagerProxy()->getItemDef(itemid));
		//def = GetDefManagerProxy()->getItemDef(itemid);
		if(def == NULL)
		{
			LOG_INFO("Cannot find itemdef: %d", itemid);
			setItem(0, 0); // 初始化，避免报错后未初始化导致的程序崩溃
			return 0;
		}
		durable = d;

		if(durable<0)
		{
			const ToolDef *tooldef = GetDefManagerProxy()->getToolDef(itemid);          
			if(tooldef) durable = tooldef->Duration;
		}

		toughness = tou;
		if (toughness < 0)
		{
			const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(itemid);
			if (tooldef) toughness = tooldef->Toughness;
		}

//		int putnum = def->StackMax;
//		if(putnum > num) putnum = num;
		setNum(n);
		
		if (itemid == ITEM_BLUEPRINT)
		{
			userdataEx = u;
		}
		else
		{
			userdata = u;
		}

		enough = e;
		sortId = s;
		enchant_num = 0;
		userdata_str = p == nullptr ? "" : p;
		sid_str = pSid == nullptr ? "" : pSid;
		runedata.reset();//符文信息初始化code by tanzhenyu
		m_KVS.clear();
		m_nDataEX = 0;

		recoverToughness();
		removeAllDataComponents();
		return n;
	}
}


BackPackGrid::BackPackGrid(const BackPackGrid &src): index(0)
{
	//先清除datacomp
	removeAllDataComponents();
	setItemDef(src.def);
	//def = src.def;

	durable = src.durable;
	maxDurable = src.maxDurable;
	num = src.num;
	userdata = src.userdata;
	userdataEx = src.userdataEx;
	sortId = src.sortId;
	enough = src.enough;

	enchant_num = src.enchant_num;
	for (int i = 0; i < MAX_ITEM_ENCHANTS; i++)
	{
		enchants[i] = src.enchants[i];
	}
	userdata_str = src.userdata_str;
	sid_str = src.sid_str;
	runedata = src.runedata;//符文信息拷贝  code by:tanzhenyu
	m_effects = src.m_effects;
	toughness = src.toughness;
	isAttracted = false;
	m_KVS = src.m_KVS;
	m_nDataEX = src.m_nDataEX;
	copyDataDataComponents(src);
}

BackPackGrid::~BackPackGrid()
{
	SANDBOX_RELEASE(m_touRecTimer);
	removeAllDataComponents();
}

const BackPackGrid &BackPackGrid::operator=(const BackPackGrid &src)
{
	//先清除datacomp
	removeAllDataComponents();
	setItemDef(src.def);
	//def = src.def;
	durable = src.durable;
	maxDurable = src.maxDurable;
	num = src.num;
	userdata = src.userdata;
	userdataEx = src.userdataEx;
	sortId = src.sortId;
	enough = src.enough;
	m_KVS = src.m_KVS;
	m_nDataEX = src.m_nDataEX;

	enchant_num = src.enchant_num;
	for(int i=0; i<enchant_num; i++)
	{
		enchants[i] = src.enchants[i];
	}
	userdata_str = src.userdata_str;
	sid_str = src.sid_str;
	runedata = src.runedata;//符文信息拷贝  code by:tanzhenyu
	m_effects = src.m_effects;
	toughness = src.toughness;
	isAttracted = src.isAttracted;
	copyDataDataComponents(src);
	return *this;
}

void BackPackGrid::setItem(const BackPackGrid &src, int copynum)
{
	if(src.getItemID() == 0)
	{
		setItemDef(nullptr);
		//def = 0;
		setNum(0);
		durable = -1;
		maxDurable = 0;
		userdata = 0;
		userdataEx = 0;
		enough = 1;
		sortId = 0;
		enchant_num = 0;
		userdata_str = "";
		sid_str = "";
		runedata.reset();//符文信息初始化code by tanzhenyu
		m_effects.clear();
		toughness = -1;
		m_KVS.clear();
		m_nDataEX = 0;

		stopRecoverToughness();
		removeAllDataComponents();
		return;
	}
	//从另外一个grid复制时，需要先清除自身的dataComp,swap时from有datacomp，to没有datacomp会导致交换后本不应该有datacomp的多出了datacomp
	removeAllDataComponents();
	setItemDef(src.def);
	//def = src.def;
	durable = src.durable;
	maxDurable = src.maxDurable;
	m_KVS = src.m_KVS;
	m_nDataEX = src.m_nDataEX;

	setNum(copynum<0 ? src.getNum() : copynum);
	if(index < BUILDBLUEPRINT_START_INDEX || index >= BUILDBLUEPRINT_START_INDEX + 1000)
	{
		userdata = src.userdata;
		userdataEx = src.userdataEx;
	}
	sortId = src.sortId;
	enough = src.enough;

	enchant_num = src.enchant_num;
	for(int i=0; i<enchant_num; i++)
	{
		enchants[i] = src.enchants[i];
	}
	userdata_str = src.userdata_str;
	sid_str = src.sid_str;
	runedata = src.runedata;//符文信息拷贝  code by:tanzhenyu
	m_effects = src.m_effects;
	toughness = src.toughness;
	isAttracted = false;
	// 韧性恢复
	recoverToughness();
	copyDataDataComponents(src);
}

void BackPackGrid::setItemDef(const ItemDef* itemdef)
{
	def = itemdef;
	if (itemdef)
	{
		m_ItemID = itemdef->ID;
		maxDurable = getDefMaxDuration();
	}
	else
		m_ItemID = 0;
}

float BackPackGrid::getGunDamage()
{
	auto* comp = getGunDataComponent();
	if (!comp)
		return 0.f;

	return GetISandboxActorSubsystem()->GetGunDamage(comp);;
}

int InsertItemToSameGrids(BaseContainer *container, int baseindex, BackPackGrid *gridarray, int arraylen, int resid, int num)
{
	const ItemDef *def = GetDefManagerProxy()->getItemDef(resid);
	if(num <=0 || def == NULL) return 0;

	int sum = 0;
	for(int i=0; i<arraylen; i++)
	{
		BackPackGrid &grid = gridarray[i];
		if(grid.getItemID() == resid)
		{
			int putnum = def->StackMax - grid.getNum();
			if(putnum > num) putnum = num;
			if(gridarray->getIndex() >= BUILDBLUEPRINT_START_INDEX && gridarray->getIndex() < BUILDBLUEPRINT_START_INDEX+1000)
				putnum = num;
			if(putnum > 0)
			{
				grid.addNum(putnum);
				num -= putnum;
				sum += putnum;

				container->afterChangeGrid(grid.getIndex()+baseindex);
			}

			if(num == 0) break;
		}
	}
	return sum;
}

//增加对应接口:  通过结构体传递格子信息  便于扩展数据  code by:tanzhenyu
int InsertItemToEmptyGrids_byGridCopyData(BaseContainer *container, int baseindex, BackPackGrid *gridarray, int arraylen, const GridCopyData& gridcopydata, int* emptyIndex)
{

	int num = gridcopydata.num;
	const GridRuneData* rune = gridcopydata.runedata;
	// 是否有新符文石附魔
	bool hasTunestoneRune = false;

	const ItemDef *def = GetDefManagerProxy()->getItemDef(gridcopydata.resid);
	if(num <=0 || def == NULL) return 0;

	int sum = 0;
	for(int i=0; i<arraylen; i++)
	{
		BackPackGrid &grid = gridarray[i];
		if(grid.isEmpty())
		{
			int putnum = def->StackMax;
			if(putnum > num) putnum = num;
			if (gridarray->getIndex() >= BUILDBLUEPRINT_START_INDEX && gridarray->getIndex() < BUILDBLUEPRINT_START_INDEX + 1000)
				putnum = num;

			if(putnum > 0)
			{
				if (emptyIndex)
				{
					*emptyIndex = i;
				}
				SetBackPackGrid(grid, gridcopydata.resid, putnum, gridcopydata.duration, gridcopydata.toughness);
				grid.setMaxDuration(gridcopydata.maxduration);
				grid.m_KVS = gridcopydata.m_KVS;
				grid.m_nDataEX = gridcopydata.m_nDataEX;
				num -= putnum;
				sum += putnum;

				if (gridcopydata.enchantnum > 0)
				{
					grid.setEnchants(gridcopydata.enchantnum, gridcopydata.enchants);
				}
				else if (gridcopydata.tunestonenum > 0)
				{
					int num = sizeof(gridcopydata.tunestones) / sizeof(int);
					int tunestonenum = Rainbow::Min(num, gridcopydata.tunestonenum);
					for (int i = 0; i < tunestonenum; i++)
					{
						int rune_id = gridcopydata.tunestones[i];
						auto* runDef = GetDefManagerProxy()->getRuneDef(rune_id);
						if (runDef)
						{
							float runeVal0 = 0.0;
							float runeVal1 = 0.0;
							/*RuneSubSystem* runeSubSystem = GetPluginManager().FindSubsystem<RuneSubSystem>();
							if (runeSubSystem)
							{
								runeSubSystem->getRandomVals(runDef, runeVal0, runeVal1);

								 合成自带附魔默认使用万能激活石附魔
								GridRuneItemData data(rune_id, runeVal0, runeVal1, 11612);
								grid.addRune(data);
							}*/

							MNSandbox::SandboxResult result = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("Rune_getRandomVals",
								MNSandbox::SandboxContext(nullptr).SetData_Usertype("def", runDef));
							if (result.IsExecSuccessed())
							{
								runeVal0 = (int)result.GetData_Number("val0");
								runeVal1 = (int)result.GetData_Number("val1");
								GridRuneItemData data(rune_id, runeVal0, runeVal1, 11612);
								grid.addRune(data);
								hasTunestoneRune = true;
							}
						}
					}
					
				}

				grid.userdata = gridcopydata.userdata;
				if(!gridcopydata.userdata_str.empty())
					grid.userdata_str = gridcopydata.userdata_str;
				else 
					grid.userdata_str = "";

				// 拷贝符文信息可能会冲掉自带的新符文石附魔
				if (rune) {//拷贝符文信息
					grid.getRuneData().setdata(*rune);
				}
				else if(!hasTunestoneRune)
				{
					grid.getRuneData().reset();
				}

				if (!gridcopydata.effects.empty())
				{
					grid.m_effects.clear();
					grid.m_effects = gridcopydata.effects;
				}

				if (gridcopydata.componentsPtr && gridcopydata.componentsPtr->size() > 0)
				{
					grid.copyDataDataComponentsFrom(*gridcopydata.componentsPtr);
				}

				container->afterChangeGrid(grid.getIndex()+baseindex);
			}

			if(num == 0) break;
		}
	}

	return sum;
}

//int InsertItemToEmptyGrids(BaseContainer *container, int baseindex, BackPackGrid *gridarray, int arraylen, int resid, int num, int durable, int toughness, int enchantnum, const int enchants[], void *userdata, const char *userdata_str, int *emptyIndex)
//{
//	const ItemDef *def = GetDefManagerProxy()->getItemDef(resid);
//	if(num <=0 || def == NULL) return 0;
//
//	int sum = 0;
//	for(int i=0; i<arraylen; i++)
//	{
//		BackPackGrid &grid = gridarray[i];
//		if(grid.isEmpty())
//		{
//			int putnum = def->StackMax;
//			if(putnum > num) putnum = num;
//			if (gridarray->getIndex() >= BUILDBLUEPRINT_START_INDEX && gridarray->getIndex() < BUILDBLUEPRINT_START_INDEX + 1000)
//				putnum = num;
//
//			if(putnum > 0)
//			{
//				if (emptyIndex)
//				{
//					*emptyIndex = i;
//				}
//				SetBackPackGrid(grid, resid, putnum, durable, toughness);
//
//				num -= putnum;
//				sum += putnum;
//
//				grid.setEnchants(enchantnum, enchants);
//				grid.userdata = userdata;
//				if(userdata_str)
//					grid.userdata_str = userdata_str;
//				else 
//					grid.userdata_str = "";
//				container->afterChangeGrid(grid.getIndex()+baseindex);
//			}
//
//			if(num == 0) break;
//		}
//	}
//
//	return sum;
//}
//
//extern int InsertItemToEmptyGridsWithTunestone(BaseContainer* container, int baseindex, BackPackGrid* gridarray, int arraylen, int resid, int num, int durable, int toughness, int tunestonenum, const int tunestones[], void* userdata, const char* userdata_str, int* emptyIndex /*= NULL*/)
//{
//	const ItemDef* def = GetDefManagerProxy()->getItemDef(resid);
//	if (num <= 0 || def == NULL) return 0;
//
//	int sum = 0;
//	for (int i = 0; i < arraylen; i++)
//	{
//		BackPackGrid& grid = gridarray[i];
//		if (grid.isEmpty())
//		{
//			int putnum = def->StackMax;
//			if (putnum > num) putnum = num;
//			if (gridarray->getIndex() >= BUILDBLUEPRINT_START_INDEX && gridarray->getIndex() < BUILDBLUEPRINT_START_INDEX + 1000)
//				putnum = num;
//
//			if (putnum > 0)
//			{
//				if (emptyIndex)
//				{
//					*emptyIndex = i;
//				}
//				SetBackPackGrid(grid, resid, putnum, durable, toughness);
//
//				num -= putnum;
//				sum += putnum;
//
//				int num = sizeof(tunestones) / sizeof(int);
//				tunestonenum = Rainbow::Min(num, tunestonenum);
//				for (int i = 0; i < tunestonenum; i++)
//				{
//					int rune_id = tunestones[i];
//					auto* runDef = GetDefManagerProxy()->getRuneDef(rune_id);
//					if (runDef)
//					{
//						float runeVal0 = 0.0;
//						float runeVal1 = 0.0;
//						RuneSubSystem* runeSubSystem = GetPluginManager().FindSubsystem<RuneSubSystem>();
//						if (runeSubSystem)
//						{
//							runeSubSystem->getRandomVals(runDef, runeVal0, runeVal1);
//
//							// 合成自带附魔默认使用万能激活石附魔
//							GridRuneItemData data(rune_id, runeVal0, runeVal1, 11612);
//							grid.addRune(data);
//						}
//					}
//				}
//
//				grid.userdata = userdata;
//				if (userdata_str)
//					grid.userdata_str = userdata_str;
//				else
//					grid.userdata_str = "";
//				container->afterChangeGrid(grid.getIndex() + baseindex);
//			}
//
//			if (num == 0) break;
//		}
//	}
//
//	return sum;
//}

//增加对应接口:  通过结构体传递格子信息  便于扩展数据  code by:tanzhenyu
int InsertItemToGridsByIndex_byGridCopyData(BaseContainer *container, int baseindex, BackPackGrid *gridarray, int arraylen, int gridIndex, const GridCopyData& gridcopydata)
{
	int num        = gridcopydata.num;
	const GridRuneData* rune = gridcopydata.runedata;

	const ItemDef *def = GetDefManagerProxy()->getItemDef(gridcopydata.resid);
	if(num <=0 || def == NULL) return 0;

	int sum = 0;
	BackPackGrid &grid = gridarray[gridIndex];
	if(grid.isEmpty())
	{
		sum = 1;
		SetBackPackGrid(grid, gridcopydata.resid, sum, gridcopydata.duration, gridcopydata.toughness);
		grid.setMaxDuration(gridcopydata.maxduration);

		if (gridcopydata.enchantnum > 0)
		{
			grid.setEnchants(gridcopydata.enchantnum, gridcopydata.enchants);
		}
		else if (gridcopydata.tunestonenum > 0)
		{
			int tunestonenum = gridcopydata.tunestonenum;
			for (int i = 0; i < tunestonenum; i++)
			{
				int rune_id = gridcopydata.tunestones[i];
				auto* runDef = GetDefManagerProxy()->getRuneDef(rune_id);
				if (runDef)
				{
					float runeVal0 = 0.0;
					float runeVal1 = 0.0;
					/*RuneSubSystem* runeSubSystem = GetPluginManager().FindSubsystem<RuneSubSystem>();
					if (runeSubSystem)
					{
						runeSubSystem->getRandomVals(runDef, runeVal0, runeVal1);

						 合成自带附魔默认使用万能激活石附魔
						GridRuneItemData data(rune_id, runeVal0, runeVal1, 11612);
						grid.addRune(data);
					}*/

					MNSandbox::SandboxResult result = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("Rune_getRandomVals",
						MNSandbox::SandboxContext(nullptr).SetData_Usertype("def", runDef));
					if (result.IsExecSuccessed())
					{
						runeVal0 = (int)result.GetData_Number("val0");
						runeVal1 = (int)result.GetData_Number("val1");
						GridRuneItemData data(rune_id, runeVal0, runeVal1, 11612);
						grid.addRune(data);
					}
				}
			}

		}
		grid.m_KVS = gridcopydata.m_KVS;
		grid.userdata = gridcopydata.userdata;
		grid.m_nDataEX = gridcopydata.m_nDataEX;
		if(!gridcopydata.userdata_str.empty())
			grid.userdata_str = gridcopydata.userdata_str;
		else 
			grid.userdata_str = "";
		if(rune){
			grid.getRuneData().setdata(*rune);
		}else{
			grid.getRuneData().reset();
		}
		container->afterChangeGrid(grid.getIndex()+baseindex);
	}

	return sum;
}


//int InsertItemToGridsByIndex(BaseContainer *container, int baseindex, BackPackGrid *gridarray, int arraylen, int gridIndex, int resid, int num, int durable, int toughness, int enchantnum, const int enchants[], void *userdata, const char *userdata_str)
//{
//	const ItemDef *def = GetDefManagerProxy()->getItemDef(resid);
//	if(num <=0 || def == NULL) return 0;
//
//	int sum = 0;
//	BackPackGrid &grid = gridarray[gridIndex];
//	if(grid.isEmpty())
//	{
//		sum = 1;
//		SetBackPackGrid(grid, resid, sum, durable, toughness);
//		grid.setEnchants(enchantnum, enchants);
//		grid.userdata = userdata;
//		grid.userdata_str = userdata_str;
//		container->afterChangeGrid(grid.getIndex()+baseindex);
//	}
//
//	return sum;
//}

//int InsertItemIntoArray(BaseContainer *container, BackPackGrid *gridarray, int arraylen, int resid, int num, int durable, int toughness, int enchantnum, const int enchants[], void *userdata, const char *userdata_str)
//{
//	int sum = InsertItemToSameGrids(container, 0, gridarray, arraylen, resid, num);
//	if(sum < num)
//	{
//		sum += InsertItemToEmptyGrids(container, 0, gridarray, arraylen, resid, num-sum, durable, toughness, enchantnum, enchants, userdata, userdata_str);
//	}
//	return sum;
//}

//extern int InsertItemIntoArrayWithTunestone(BaseContainer* container, BackPackGrid* gridarray, int arraylen, int resid, int num, int durable, int toughness, int tunestonenum, const int tunestones[], void* userdata/*=0*/, const char* userdata_str/*=""*/)
//{
//	int sum = InsertItemToSameGrids(container, 0, gridarray, arraylen, resid, num);
//	if (sum < num)
//	{
//		sum += InsertItemToEmptyGridsWithTunestone(container, 0, gridarray, arraylen, resid, num - sum, durable, toughness, tunestonenum, tunestones, userdata, userdata_str);
//	}
//	return sum;
//}

int InsertItemToSameGridsWithUserData(BaseContainer* container, int baseindex, BackPackGrid* gridarray, int arraylen, int resid, int num, const char* userdata_str)
{
	const ItemDef* def = GetDefManagerProxy()->getItemDef(resid);
	if (num <= 0 || def == NULL) return 0;

	int sum = 0;
	for (int i = 0; i < arraylen; i++)
	{
		BackPackGrid& grid = gridarray[i];
		if (grid.getItemID() == resid && grid.userdata_str == userdata_str)
		{
			int putnum = def->StackMax - grid.getNum();
			if (putnum > num) putnum = num;
			if (gridarray->getIndex() >= BUILDBLUEPRINT_START_INDEX && gridarray->getIndex() < BUILDBLUEPRINT_START_INDEX + 1000)
				putnum = num;
			if (putnum > 0)
			{
				grid.addNum(putnum);
				num -= putnum;
				sum += putnum;

				container->afterChangeGrid(grid.getIndex() + baseindex);
			}

			if (num == 0) break;
		}
	}
	return sum;
}

//增加对应接口:  通过结构体传递格子信息  便于扩展数据  code by:tanzhenyu
int InsertItemIntoArray_byGridCopyData(BaseContainer *container, BackPackGrid *gridarray, int arraylen, const GridCopyData& grid )
{

	int sum = 0;
	//染色方块根据id和userdata_str判断是否堆叠 by:Jeff 2022/12/14
	if (IsDyeableBlock(grid.resid))
	{
		sum = InsertItemToSameGridsWithUserData(container, 0, gridarray, arraylen, grid.resid, grid.num, grid.userdata_str.c_str());
	}
	else
	{
		sum = InsertItemToSameGrids(container, 0, gridarray, arraylen, grid.resid, grid.num);
	}

	if(sum < grid.num)
	{
		GridCopyData tmp(grid);
		tmp.num = grid.num - sum;
		sum += InsertItemToEmptyGrids_byGridCopyData(container, 0, gridarray, arraylen, tmp);
	}
	return sum;
}

int InsertItemIntoArray(BaseContainer *container, BackPackGrid *gridarray, int arraylen, const BackPackGrid &grid, int num)
{
	const ItemDef *def = GetDefManagerProxy()->getItemDef(grid.getItemID());
	if (!def)
		return 0;

	int sum = 0;
	for(int i=0; i<arraylen; i++)
	{
		BackPackGrid &dest = gridarray[i];
		if(dest.getItemID() == def->ID)
		{
			int leftspace = def->StackMax - dest.getNum();
			int n = Rainbow::Min(num, leftspace);
			if(n > 0)
			{
				gridarray[i].addNum(n);
				num -= n;
				sum += n;
				container->afterChangeGrid(dest.getIndex());
				if(num == 0) return sum;
			}
		}
	}

	for(int i=0; i<arraylen; i++)
	{
		BackPackGrid &dest = gridarray[i];
		if(dest.isEmpty())
		{
			int leftspace = def->StackMax;
			int n = Rainbow::Min(num, leftspace);
			if(n > 0)
			{
				dest.setItem(grid, n);
				num -= n;
				sum += n;
				container->afterChangeGrid(dest.getIndex());
				if(num == 0) return sum;
			}
		}
	}
	return sum;
}

int CheckInsertItemIntoArray(BaseContainer* container, BackPackGrid* gridarray, int arraylen, const BackPackGrid& grid)
{
	int num = grid.getNum();
	int id = grid.getItemID();
	if (id == 0)
	{
		return num;
	}
	const ItemDef* def = GetDefManagerProxy()->getItemDef(grid.getItemID());
	if (!def)
		return num;

	int leftSpace = 0;
	for (int i = 0; i < arraylen; i++)
	{
		BackPackGrid& dest = gridarray[i];
		if (dest.isEmpty())
		{
			num -= def->StackMax;
			if (num <= 0)
			{
				return 0;
			}
		}
		else if (dest.getItemID() == id)
		{
			num = num - def->StackMax + dest.getNum();
			if (num <= 0)
			{
				return 0;
			}
		}
	}
	return num;
}

void* BaseContainer::s_CurrentOpenContainerLua = nullptr;

void BaseContainer::onSubtractItem(BackPackGrid *grid, int num)
{
	assert(num <= grid->getNum());
	if(grid == NULL) return;
	if(num > grid->getNum()) return;
	grid->addNum(-num);
	if(grid->getNum() == 0) grid->clear();

	afterChangeGrid(grid->getIndex());
}


//增加对应接口:  通过结构体传递格子信息  便于扩展数据  code by:tanzhenyu
int BaseContainer::addItem_byGrid(const BackPackGrid* data)
{
	GridCopyData copydata(data);
	return addItem_byGridCopyData(copydata);
}

int BaseContainer::addItem_byGridWithNum(const BackPackGrid* data, int num)
{
	GridCopyData copydata(data);
	copydata.num = num;
	return addItem_byGridCopyData(copydata);
}

int BaseContainer::addItem_byGridCopyData(const GridCopyData& grid)
{
	return 0;
}
//end

void SubtractItemFromContainerEx(BaseContainer *container, BackPackGrid *grid, int num, int index)
{
	assert(num <= grid->getNum());
	grid->addNum(-num);
	if(grid->getNum() == 0) grid->clear();
	if (container)
		container->afterChangeGrid(index);
}

int CalculateItemsComparatorInput(const BackPackGrid *gridarray[], int arraylen)
{
	int count = 0;
	float weight = 0.0f;
	for(int i=0; i<arraylen; i++)
	{
		const BackPackGrid *grid = gridarray[i];

		if(!grid->isEmpty())
		{
			weight += float(grid->getNum()) / grid->getMaxStack();
			count++;
		}
	}

	weight /= arraylen;
	return int(weight*14.0f) + (count>0?1:0);
}

int CalculateItemsComparatorInputOld(const BackPackGrid *gridarray, int arraylen)
{
	int count = 0;
	float weight = 0.0f;
	for(int i=0; i<arraylen; i++)
	{
		const BackPackGrid &grid = gridarray[i];

		if(!grid.isEmpty())
		{
			int curnum = grid.getNum();
			if (curnum > 64)
				curnum = 64;
			int maxnum = grid.getMaxStack();
			if (maxnum > 64)
				maxnum = 64;
			weight += float(curnum) / maxnum;
			count++;
		}
	}

	weight /= arraylen;
	return int(weight*14.0f) + (count>0?1:0);
}

int CalculateItemsComparatorInput(const BackPackGrid *gridarray, int arraylen)
{
	int count = 0;
	float weight = 0.0f;
	for(int i=0; i<arraylen; i++)
	{
		const BackPackGrid &grid = gridarray[i];

		if(!grid.isEmpty())
		{
			weight += float(grid.getNum()) / grid.getMaxStack();
			count++;
		}
	}

	weight /= arraylen;
	return int(weight*14.0f) + (count>0?1:0);
}

int BackPackGrid::getItemMeshType() const
{
	return def == NULL ? 0 : def->MeshType;
}

int BackPackGrid::getRuneNum() const
{
	return runedata.getRuneNum();
}

void BaseContainer::SetLuaOpenContainer(const char* classname, void* ptr)
{
	if (MINIW::ScriptVM::game())
	{
		MINIW::ScriptVM::game()->setUserTypePointer("OpenContainer", classname, ptr);
		s_CurrentOpenContainerLua = ptr;
	}
}

BaseContainer::~BaseContainer()
{
	if (s_CurrentOpenContainerLua == this && MINIW::ScriptVM::game())
	{
		MINIW::ScriptVM::game()->setUserTypePointer("OpenContainer", "BaseContainer", NULL);
		s_CurrentOpenContainerLua = nullptr;
	}
}

//int ToolType2EquipIndex(int tool_type){
//	const static std::map<int, int> tool2Index = {
//		{8, EQUIP_START_INDEX},
//		{9, EQUIP_START_INDEX + 1},
//		{10, EQUIP_START_INDEX + 2},
//		{11, EQUIP_START_INDEX + 3},
//		{16, EQUIP_START_INDEX + 4},
//		{19, HORSE_EQUIP_INDEX},
//		{20, HORSE_EQUIP_INDEX + 1},
//		{30, EQUIP_START_INDEX + 4}
//	};
//	auto iter = tool2Index.find(tool_type);
//	return iter == tool2Index.end()? 0: iter->second;
//}
void BackPackGrid::AddEffect(const char* effect, float scale)
{
	if (!effect)
	{
		return;
	}
	if (m_effects.find(effect) == m_effects.end())
	{
		GridEffect ef;
		ef.effectname = effect;
		ef.scale = scale;
		m_effects[effect] = ef;
	}
	else
	{
		m_effects[effect].scale = scale;
	}
}
void BackPackGrid::RemoveEffect(const char* effect)
{
	auto it = m_effects.find(effect);
	if (it != m_effects.end())
	{
		m_effects.erase(it);
	}

}

GridDataComponent* BackPackGrid::getGunDataComponent()
{
	GridDataComponent* comp = getDataComponent(GunComp_Name);
	if (comp)
		return comp;

	return nullptr;
}

GridDataComponent* BackPackGrid::addGunDataComponent()
{
	GridDataComponent* comp = getGunDataComponent();
	if (comp)
		return comp;

	comp = GetISandboxActorSubsystem()->CreateGridDataComponent("gungrid");//  ENG_NEW(GunGridDataComponent);
	m_dataComps.push_back(comp);
	return comp;
}

GridDataComponent* BackPackGrid::getEntryDataComponent()
{
	GridDataComponent* comp = getDataComponent(EntryComp_Name);
	if (comp)
		return comp;

	return nullptr;
}

GridDataComponent* BackPackGrid::addEntryDataComponent()
{
	GridDataComponent* comp = getEntryDataComponent();
	if (comp)
		return comp;

	comp = GetISandboxActorSubsystem()->CreateGridDataComponent("entrygrid"); //ENG_NEW(EntryGridDataComponent);
	m_dataComps.push_back(comp);
	return comp;
}

GridDataComponent* BackPackGrid::getDataComponent(const std::string& name)
{
	for (auto* comp : m_dataComps)
	{
		if (comp->GetComponentName() == name)
			return comp;
	}
	return nullptr;
}

void BackPackGrid::removeDataComponent(std::string& name)
{
	for (auto iter = m_dataComps.begin(); iter != m_dataComps.end();)
	{
		GridDataComponent* comp = *iter;
		if (comp->GetComponentName() == name)
		{
			ENG_DELETE(comp);
			comp = NULL;
			m_dataComps.erase(iter);
			return;
		}
		iter++;
	}
}

void BackPackGrid::saveKVS(flatbuffers::FlatBufferBuilder& builder, flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::KVData>>>& kvsOffset)
{
	if (m_KVS.size() <= 0)
	{
		kvsOffset = 0;
		return;
	}

	std::vector<flatbuffers::Offset<FBSave::KVData>> kvsArray;
	kvsArray.clear();
	for (auto& kv : m_KVS)
	{
		kvsArray.push_back(FBSave::CreateKVData(builder, builder.CreateString(kv.first), kv.second));
	}
	kvsOffset = builder.CreateVector(kvsArray);
}

void BackPackGrid::saveDataComponent(flatbuffers::FlatBufferBuilder& builder, flatbuffers::Offset<flatbuffers::Vector<int8_t>>& ugccomponentsOffset)
{
	if (m_dataComps.size() <= 0)
	{
		ugccomponentsOffset = 0;
		return;
	}

	flatbuffers::FlatBufferBuilder componentsBuilder;
	std::vector<flatbuffers::Offset<FBSave::UgcComponent>> componentsArray;
	componentsArray.clear();
	for (auto* comp : m_dataComps)
	{
		if (comp && comp->GetComponentName())
		{
			jsonxx::Object componentData;
			comp->CreateComponentData(componentData);
			string componentName = comp->GetComponentName();
			componentsArray.push_back(FBSave::CreateUgcComponent(componentsBuilder, componentsBuilder.CreateString(componentName), componentsBuilder.CreateVector((int8_t*)componentData.bin(), componentData.binLen())));
		}
	}
	auto array = FBSave::CreateUgcComponentArray(componentsBuilder, componentsBuilder.CreateVector(componentsArray));
	componentsBuilder.Finish(array);
	if (componentsBuilder.GetSize() > 0)
	{
		ugccomponentsOffset = builder.CreateVector((int8_t*)componentsBuilder.GetBufferPointer(), componentsBuilder.GetSize());
	}
	else
	{
		ugccomponentsOffset = 0;
	}
}

void BackPackGrid::loadDataComponent(const flatbuffers::Vector<int8_t>* datacomponents)
{
	if (datacomponents && datacomponents->size() > 0)
	{
		flatbuffers::Verifier verifier((const uint8_t*)datacomponents->data(), datacomponents->size());
		if (FBSave::VerifyUgcComponentArrayBuffer(verifier))
		{
			const FBSave::UgcComponentArray* dataComponentArray = FBSave::GetUgcComponentArray(datacomponents->data());
			if (dataComponentArray)
			{
				auto datacomponents = dataComponentArray->ugccomponents();
				for (unsigned int i = 0; i < datacomponents->size(); i++)
				{
					auto datacomponent = datacomponents->Get(i);
					std::string name = datacomponent->name()->str();
					jsonxx::Object obj;
					if (obj.parseBinary((unsigned char*)datacomponent->detail()->data(), datacomponent->detail()->size()))
					{
						AddAndLoadDataComponent(name, obj);
					}
				}
			}
		}
	}
}

void BackPackGrid::loadDataComponent(const BackPackGrid* data)
{
	if (!data || data == this)
		return;

	if (data->hasDataComp())
	{
		copyDataDataComponents(*data);
	}
}

void BackPackGrid::saveDataComponentPB(PB_ItemIndexGrid* item) const
{
	if (m_dataComps.size() <= 0)
	{
		return;
	}

	for (auto* comp : m_dataComps)
	{
		if (comp && comp->GetComponentName())
		{
			jsonxx::Object componentData;
			comp->CreateComponentData(componentData);
			string componentName = comp->GetComponentName();
			auto* datacomp = item->add_datacomponents();
			datacomp->set_name(componentName);
			datacomp->set_data(componentData.bin(), componentData.binLen());
		}
	}
}

void BackPackGrid::loadDataComponentPB(const PB_ItemIndexGrid& item)
{
	if (item.datacomponents_size() > 0)
	{
		for (size_t i = 0; i < item.datacomponents_size(); i++)
		{
			auto& datacomponent = item.datacomponents(i);
			std::string name = datacomponent.name();
			jsonxx::Object obj;
			if (obj.parseBinary((unsigned char*)datacomponent.data().c_str(), datacomponent.data().size()))
			{
				AddAndLoadDataComponent(name, obj);
			}
		}
	}
}

void BackPackGrid::saveDataComponentPB(game::common::PB_ActorItem* item) const
{
	if (m_dataComps.size() <= 0)
	{
		return;
	}

	for (auto* comp : m_dataComps)
	{
		if (comp && comp->GetComponentName())
		{
			jsonxx::Object componentData;
			comp->CreateComponentData(componentData);
			string componentName = comp->GetComponentName();
			auto* datacomp = item->add_datacomponents();
			datacomp->set_name(componentName);
			datacomp->set_data(componentData.bin(), componentData.binLen());
		}
	}
}

void BackPackGrid::loadDataComponentPB(const game::common::PB_ActorItem& item)
{
	if (item.datacomponents_size() > 0)
	{
		for (size_t i = 0; i < item.datacomponents_size(); i++)
		{
			auto& datacomponent = item.datacomponents(i);
			std::string name = datacomponent.name();
			jsonxx::Object obj;
			if (obj.parseBinary((unsigned char*)datacomponent.data().c_str(), datacomponent.data().size()))
			{
				AddAndLoadDataComponent(name, obj);
			}
		}
	}
}


void BackPackGrid::saveDataComponentPB(game::common::PB_ItemData* item) const
{
	if (m_dataComps.size() <= 0)
	{
		return;
	}

	for (auto* comp : m_dataComps)
	{
		if (comp && comp->GetComponentName())
		{
			jsonxx::Object componentData;
			comp->CreateComponentData(componentData);
			string componentName = comp->GetComponentName();
			auto* datacomp = item->add_datacomponents();
			datacomp->set_name(componentName);
			datacomp->set_data(componentData.bin(), componentData.binLen());
		}
	}
}

void BackPackGrid::loadDataComponentPB(const game::common::PB_ItemData& item)
{
	if (item.datacomponents_size() > 0)
	{
		for (size_t i = 0; i < item.datacomponents_size(); i++)
		{
			auto& datacomponent = item.datacomponents(i);
			std::string name = datacomponent.name();
			jsonxx::Object obj;
			if (obj.parseBinary((unsigned char*)datacomponent.data().c_str(), datacomponent.data().size()))
			{
				AddAndLoadDataComponent(name, obj);
			}
		}
	}
}

void BackPackGrid::saveDataComponentPB(game::common::PB_Arm* item) const
{
	if (m_dataComps.size() <= 0)
	{
		return;
	}

	for (auto* comp : m_dataComps)
	{
		if (comp && comp->GetComponentName())
		{
			jsonxx::Object componentData;
			comp->CreateComponentData(componentData);
			string componentName = comp->GetComponentName();
			auto* datacomp = item->add_datacomponents();
			datacomp->set_name(componentName);
			datacomp->set_data(componentData.bin(), componentData.binLen());
		}
	}
	item->set_dataex(m_nDataEX);
}

void BackPackGrid::loadDataComponentPB(const game::common::PB_Arm& item)
{
	if (item.datacomponents_size() > 0)
	{
		for (size_t i = 0; i < item.datacomponents_size(); i++)
		{
			auto& datacomponent = item.datacomponents(i);
			std::string name = datacomponent.name();
			jsonxx::Object obj;
			if (obj.parseBinary((unsigned char*)datacomponent.data().c_str(), datacomponent.data().size()))
			{
				AddAndLoadDataComponent(name, obj);
			}
		}
	}
	m_nDataEX = item.dataex();
}

void BackPackGrid::removeAllDataComponents()
{
	for (auto iter = m_dataComps.begin(); iter != m_dataComps.end();)
	{
		GridDataComponent* comp = *iter;
		ENG_DELETE(comp);
		comp = NULL;
		iter = m_dataComps.erase(iter);
	}
}

void BackPackGrid::copyDataDataComponents(const BackPackGrid& src)
{
	copyDataDataComponentsFrom(src.m_dataComps);
}

//获取全部组件的指针
const std::vector<GridDataComponent*>* BackPackGrid::getDataComponentsPtr() const
{
	return &m_dataComps;
}

void BackPackGrid::copyDataDataComponentsFrom(const std::vector<GridDataComponent*>& from)
{
	for (auto iter = from.begin(); iter != from.end(); iter++)
	{
		GridDataComponent* comp = *iter;
		if (comp)
		{
			if (strcmp(comp->GetComponentName(), GunComp_Name) == 0)
			{
				GridDataComponent* gun = addGunDataComponent();
				gun->Copy(comp);
			}
			else if (strcmp(comp->GetComponentName(), EntryComp_Name) == 0)
			{
				GridDataComponent* gun = addEntryDataComponent();
				gun->Copy(comp);
			}
		}
	}
}

void BackPackGrid::AddAndLoadDataComponent(const std::string& name, const jsonxx::Object& obj)
{
	//TODO:这里先不做工厂化处理，暂时按名字判断
	if (strcmp(name.c_str(), GunComp_Name) == 0)
	{
		GridDataComponent* comp = addGunDataComponent();
		if (comp)
		{
			comp->LoadComponentData(obj, true);
		}
	}
	else if (strcmp(name.c_str(), EntryComp_Name) == 0)
	{
		GridDataComponent* comp = addEntryDataComponent();
		if (comp)
		{
			comp->LoadComponentData(obj, true);
		}
	}
}

const GridRuneItemData* BackPackGrid::getRuneItemData(int runeIndex) const
{
	const GridRuneData& rdata = getRuneData();
	if (runeIndex < 0 || runeIndex >= rdata.getRuneNum())//越界
		return NULL;

	const GridRuneItemData& data = rdata.getItemByIndex(runeIndex);
	return &data;
}

int BackPackGrid::getWaterVolume() 
{
	if (def && def->Type == 100)
	{
		return m_nDataEX;
	}
	return 0;
}

int BackPackGrid::getMaxWaterVolume()
{
	if (def && def->Type == 100)
	{
		const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(def->ID);
		if (tooldef)
		{
			return tooldef->Duration;
		}
	}
	return 0;
}

void BackPackGrid::addWaterVolume(int value)
{
	int maxValue = getMaxWaterVolume();
	if (maxValue > 0)
	{
		int curValue = getWaterVolume() + value;
		m_nDataEX = 0 >= curValue ? 0 : maxValue <= curValue ? maxValue : curValue;
	}
}