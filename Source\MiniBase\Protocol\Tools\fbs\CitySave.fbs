namespace FBSave;

table CitySaveData
{
	leftDownX : int;
	leftDownZ : int;
	height    : int;
	cityNum   : int;
	rangeX    : byte;
	rangeZ    : byte;
	configIndex : int;
}

table CityBuildRectFB
{
	type : byte;
	dir  : byte;
	minX : int;
	minZ : int;
	maxX : int;
	maxZ : int;
	originMinX : int;
	originMinZ : int;
	originMaxX : int;
	originMaxZ : int;
}

table CityRunData
{
	step : byte;
	cityBuildNum : int;
	hasBuildNum : int;
	leftDownX : int;
	leftDownZ : int;
	height    : int;
	rangeX    : byte;
	rangeZ    : byte;
	configIndex : int;
	buildNums : [int];
	buildGather : [CityBuildRectFB];
}

table CityRoadNodeData
{
	startX : int;
	startZ : int;
	endX : int;
	endZ : int;
	roadType : byte = 0;  // 0: 普通道路, 1: 轨道
}

table SingleBuildData
{
	leftDownX : int;
	leftDownZ : int;
	rangeX : byte;
	rangeZ : byte;
	brangex : int;
	brangez : int;
}

table CitySingleBuildData
{
	buildName : string;
	buildData : [SingleBuildData];
}

table CityFBData
{
	allData : [CitySaveData];
	runData : [CityRunData];
	roadData : [CityRoadNodeData];
	singleBuildData : [CitySingleBuildData];
	spawnCidxList : [int];
}

root_type CityFBData;