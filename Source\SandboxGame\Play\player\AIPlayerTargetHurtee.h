
#ifndef __AIPlayerTargetHurtee_H__
#define __AIPlayerTargetHurtee_H__

#include "AIBase.h"

class AINpcPlayer;

class AIPlayerTargetHurtee:public AIBase //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	AIPlayerTargetHurtee(AINpcPlayer *pActor);
	~AIPlayerTargetHurtee() {}
	virtual bool willRun();
	virtual void start();
	virtual void reset();
	virtual bool continueRun();
	virtual void update();

	virtual bool canInterruptInteract() { return true; }
	virtual bool canInterruptedByInteract() { return false; }
	//tolua_end
private:
	int m_LastBeAtkTimer;

	bool m_CheckSight;
	int m_CheckSightCount;
	long long getObjId;

}; //tolua_exports

#endif