
#include "ClientActorProjectile.h"
#include "BlockScene.h"
#include "backpack.h"
#include "ClientItem.h"
#include "ProjectileLocoMotion.h"
#include "GameMode.h"
#include "PlayerControl.h"
////#include "MpGameSurvive.h"
#include "block_tickmgr.h"
#include "FullyCustomModelMgr.h"
#include "ImportCustomModelMgr.h"
#include "MpGameSurviveCdnResMgr.h"
#include "ActorBall.h"
#include "ActorHorse.h"
#include "Environment.h"
#include "container_peristele.h"
#include "IClientGameManagerInterface.h"
#include "IClientGameInterface.h"
#include "ActorBindVehicle.h"
#include "PlayerAttrib.h"
#include "AITargetOwnerHurter.h"
#include "AITargetOwnerHurtee.h"
#include "AIFollowOwner.h"
#include "ClientActorHelper.h"
#include "EffectComponent.h"
#include "SoundComponent.h"
#include "FireBurnComponent.h"
#include "BindActorComponent.h"
#include "AttackedComponent.h"
#include "ParticlesComponent.h"
#include "Pkgs/PkgUtils.h"
#include "GameNetManager.h"
#include "LightningChainComponent.h"
#include "GameInfoProxy.h"
#include "WeaponSkinMgr.h"
#include "ClientActorIcicle.h"
#include "ActorUpdateFrequency.h"
#include "MusicManager.h"
#ifdef IWORLD_SERVER_BUILD
#include "ICloudProxy.h"
#endif
#include "ICloudProxy.h"
#include "special_blockid.h"
#include "BlockMaterialMgr.h"
#include "GameEffectManager.h"
#include "ActorVehicleAssemble.h"
#include "CustomModelMgr.h"
#include "SandBoxManager.h"
#include "Entity/OgreEntity.h"
#include "ClientInfoProxy.h"
#include "BlockMoss.h"
#include "EffectParticle.h"
#include "SkillComponent.h"

#include "LuaInterfaceProxy.h"
#include "OgreUtils.h"
#include "PermitsDef.h"
#include "UgcAssetMgr.h"
#include "BulletMgr.h"
#include "WorldManager.h"
#include "UGCEntity.h"

#include "ModPackMgr.h"
#include "BlockCottonrug.h"

using namespace MINIW;
using namespace Rainbow;
using namespace MNSandbox;
EXPORT_SANDBOXGAME int  g_all_projectile = 0;
int g_all_physxitem = 0;
float g_projectile_factor = 1000.0f;
float g_projectile_ratio = 0.0f;

unsigned int GetTeamColor(int teamid)
{
	int color = -1;
	if (teamid >= 1 && teamid <= 6)
	{
		MINIW::ScriptVM::game()->callFunction("GetTeamColor", "i>i", teamid, &color);
	}
	if (color == -1)
	{
		return 0;
	}
	return color;
}

static int Color2Team(unsigned int color)
{
	int teamid = -1;
	MINIW::ScriptVM::game()->callFunction("Color2Team", "i>i", color, &teamid);
	return teamid;
}

void ActorKnockbackByMotion(ClientActor *actor, const Rainbow::Vector3f &motion, float strength)
{
	// if(strength <= 0) return;
	//
	// float r = Rainbow::Sqrt(motion.x*motion.x + motion.z*motion.z);
	// if(r > 0)
	// {
	// 	float knockup = 10.0f;
	// 	if(strength < 1.0f) knockup = 10.0f;
	//
	// 	//actor->getLocoMotion()->addMotion(strength*60.0f*motion.x/r, knockup, strength*60.0f*motion.z/r);
	// 	actor->setMotionChange(Rainbow::Vector3f(strength*60.0f*motion.x/r, knockup, strength*60.0f*motion.z/r));
	// }
}
IMPLEMENT_SCENEOBJECTCLASS(ClientActorProjectile)
ClientActorProjectile::ClientActorProjectile():m_ItemID(0), m_ShootingActorID(0), m_BaseAtk(0), m_isCheckFinally(true)
, m_strEffectName("")
{ 
	CreateComponent<ActorAttrib>("ActorAttrib");
	CreateComponent<ProjectileLocoMotion>("ProjectileLocoMotion");
	getLocoMotion()->setBound(50, 50);
	getLocoMotion()->m_yOffset = getLocoMotion()->m_BoundHeight / 2;
	m_LiveTicks = 0;
	m_Durable = 0;
	m_EnchantNum = 0;
	memset(&m_Enchants, 0, sizeof(m_Enchants));
	m_HasImpackActor = false;

	g_all_projectile++;
	m_Color = 0;
	m_bAttackFly = false;
	m_nBlockID = 0;
	m_starBulletDuration =0.0f;
	m_BoomerangState = 0;
	m_TriggerAnimID = 0;
	m_AnimMode = 0;
	m_cureffectName = "";
	m_curTicksNum = 0;
	m_passActorNum = 0;
	m_canPassActorNum = -1;
	m_touReduce = 0.f;
	m_bowDamageIns = 0.f;
	m_strength = 0.f;
	m_atkType = 0;
	m_skillName = "";
	m_skillTriggerType = -1;
	m_dropable = true;
	m_puncture = 0;
	m_Model_Left = NULL;
	m_EntityModel_Left = NULL;
	m_isUseCustomAtkDuration = false;
	memset(&m_AttackPointsNew, 0, sizeof(m_AttackPointsNew));
	memset(&m_ExplodePoints, 0, sizeof(m_ExplodePoints));

	CreateEvent2();
}

ClientActorProjectile::~ClientActorProjectile() 
{

	// 这里的逻辑可能还是会有问题。因为model和m_EntityModel是否是父子关系 
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_Model);
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_EntityModel);
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_Model_Left);
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_EntityModel_Left);
	g_all_projectile--;
	const PhysicsActorDef* physicsActorDef = GetDefManagerProxy()->getPhysicsActorDef(this->m_ItemID);
	if (physicsActorDef && (physicsActorDef->EditType == PhysicsLocoMotion::PHYS_TYPE_NOEDIT_YESPHY || physicsActorDef->EditType == PhysicsLocoMotion::PHYS_TYPE_YESEDIT_YESPHY))
		g_all_physxitem--;

	DestroyEvent2();
}
void ClientActorProjectile::CreateEvent2()
{
	typedef ListenerFunctionRef<IClientPlayer*&> Listener1;
	m_listenerProj1 = SANDBOX_NEW(Listener1, [this](IClientPlayer*& player) -> void {
		player = dynamic_cast<IClientPlayer*>(this->getShootingActor());
		});
	Event2().Subscribe("Projectile_getshootingActor", m_listenerProj1);

	typedef ListenerFunctionRef<int&, int&> Listener2;
	m_listenerProj2 = SANDBOX_NEW(Listener2, [this](int& actid, int& animode) -> void {
		actid = this->GetAnimID();
		animode = this->GetAnimMode();
		});
	Event2().Subscribe("ActorProjectile_GetValue", m_listenerProj2);
}

void ClientActorProjectile::DestroyEvent2()
{
	SANDBOX_RELEASE(m_listenerProj1);
	SANDBOX_RELEASE(m_listenerProj2);
}
void ClientActorProjectile::setNeedClearEx(int delay_ticks)
{
	clearBlock();
	setNeedClear(delay_ticks);
}

void ClientActorProjectile::clearBlock()
{
	if (m_nBlockID && !IsAirBlockID(getWorld()->getBlockID(m_BlockPos)))
	{
		//特殊ID需要通知临近方块 by：Jeff
		if (m_nBlockID == 1069 || m_nBlockID == 1070 || m_nBlockID == 1071)
			getWorld()->setBlockAll(m_BlockPos, 0, 0, 3);
		else
			getWorld()->setBlockAll(m_BlockPos, 0, 0, 2);
	}
}
bool ClientActorProjectile::isBullets(int id)
{
	return id == 15003;
}

bool ClientActorProjectile::needRoteBullets(int id)
{
	if (id > 10000000)
	{
		std::string resid = ModPackMgr::GetInstancePtr()->GetResIdByCfgId(CustomModType::Mod_Item, id);
		if (resid.length() > 0)
		{
			std::list<std::string> ress = { "r2_7383269810165564629_23723","r2_7383269814460531925_23728","r2_7386883638598091989_16258","r2_7386883642893059285_16263",
											"r2_7383269814460531925_23753","r2_7383269814460531925_23758","r2_7383269810165564629_23763","r2_7384628883846907093_1676",
											"r2_7384628888141874389_16768","r2_7384628888141874389_16773"};
			auto iter = std::find(ress.begin(), ress.end(), resid);
			if (iter != ress.end())
			{
				return true;
			}
		}
	}
	return false;
}

void ClientActorProjectile::init(int itemid, ClientActor *shooter)
{
	m_ProjectileDef = GetDefManagerProxy()->getProjectileDef(itemid, true);
	if(m_ProjectileDef == NULL) return;
	m_ItemID = m_ProjectileDef->ID;
	m_touReduce = m_ProjectileDef->TouReduce;
	m_puncture = m_ProjectileDef->Puncture;
	m_canPassActorNum = m_ProjectileDef->penetrateLimit;

	// 投掷物攻击削韧保底5（策划：cjt）
	if (m_touReduce == 0)
	{
		m_touReduce = 5;
	}
	m_strength = 1.0f;

	if(itemid != m_ItemID)
	{
		auto tooDef = GetDefManagerProxy()->getToolDef(m_ItemID);
		if(tooDef)
			m_Durable = tooDef->Duration;
		else
			m_Durable = -1;
	}
	const PhysicsActorDef* physicsActorDef = GetDefManagerProxy()->getPhysicsActorDef(itemid);
	if (physicsActorDef && (physicsActorDef->EditType == PhysicsLocoMotion::PHYS_TYPE_NOEDIT_YESPHY || physicsActorDef->EditType == PhysicsLocoMotion::PHYS_TYPE_YESEDIT_YESPHY))
		g_all_physxitem++;

#ifdef IWORLD_SERVER_BUILD
	m_EntityModel = Entity::Create();
#else
	setItemId(itemid);
	int uin = shooter ? shooter->getObjId() : 0;
	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit(
		"WeaponSkin_System_UseSkinBullet",
		SandboxContext(nullptr)
		.SetData_Number("uin", uin)
	);

	if (result.IsExecSuccessed())
	{
		m_Model = (Rainbow::Model *)result.GetData_Userdata("Rainbow::Model", "model");
		m_EntityModel = Entity::Create();
		if (m_Model) m_EntityModel->Load(m_Model);
	}
	else
	{
		auto *itemDef = GetDefManagerProxy()->getItemDef(itemid);
		if (!itemDef)
			return;

		std::string modeStr = "";
		std::string modeType = "";
		int modeId = 0;
		if (itemDef->MeshType == CUSTOM_GEN_MESH)
		{
			modeType = "custom";
			modeStr = itemDef->Model.c_str();
		}
		else if (itemDef->MeshType == FULLY_CUSTOM_GEN_MESH)
		{
			modeType = "fullycustom";
			modeStr = itemDef->Model.c_str();
		}
		else if (itemDef->MeshType == IMPORT_MODEL_GEN_MESH)
		{
			modeType = "import";
			modeStr = itemDef->Model.c_str();
		}
		else if (itemDef->MeshType == UGC_MODEL_GEN_MESH)
		{
			auto entity = UgcAssetMgr::GetInstance().CreateEntity(itemDef->ModelComp.c_str(), modeType, modeStr, modeId);
			if (entity)
			{
				m_EntityModel = entity;
				//异步
				MNSandbox::WeakRef<MNSandbox::Ref> self = entity->GetWeakRef();
				entity->FinishCallback([this, self, entity](bool success) {
					if (!self || !success)
						return;

					m_Model = entity->GetMainModel();
				});
			}
			else
			{
				//block mob等,item的就走item的逻辑，
				if (modeType != "item")
				{
					//TODO:mob和block 待处理,
				}
			}
		}
		else
		{
			modeType = "item";
			modeId = m_ProjectileDef->ID;
			modeStr = m_ProjectileDef->Model.c_str();
		}


		if (modeType == "custom")
		{
			if (CustomModelMgr::GetInstancePtr())
			{
				m_Model = CustomModelMgr::GetInstancePtr()->getAvatarModel(modeStr.c_str(), PROJECTILE_ACTOR_MESH);
				m_EntityModel = Entity::Create();
				m_EntityModel->Load(m_Model);
			}
		}
		else if (modeType == "fullycustom")
		{
			if (FullyCustomModelMgr::GetInstancePtr())
			{
				m_FullySKey = itemDef->Model;
				m_EntityModel = FullyCustomModelMgr::GetInstancePtr()->getEntity(modeStr.c_str(), NULL, itemDef->Icon == "fullycustompacking");
				if (m_EntityModel)
					m_EntityModel->PlayAnim(0);
			}
		}
		else if (modeType == "import")
		{
			m_Model = ImportCustomModelMgr::GetInstancePtr()->getImportModel(modeStr.c_str());
			if (m_Model)
			{
				m_EntityModel = Entity::Create();
				m_EntityModel->Load(m_Model);
			}
		}
		else if (modeType == "obj")
		{
			if (m_EntityModel)
				m_Model = m_EntityModel->GetMainModel();
		}
		else if (modeType == "omod")
		{
			if (m_EntityModel)
				m_Model = m_EntityModel->GetMainModel();
		}
		else if (modeType == "shape")
		{
			if (m_EntityModel)
				m_Model = m_EntityModel->GetMainModel();
		}
		else if (modeType == "item")
		{
			char modelPath[128];
			if (!modeStr.empty())
			{
				ItemDef* realitemdef = nullptr;
				if (itemDef && itemDef->gamemod)
				{
					//插件改了模型需要取对应道具定义的模型 ClientItem::createItemModel
					int realitemid = ClientItem::getRealModelItemId(modeId);
					realitemdef = realitemid == modeId ? itemDef : GetDefManagerProxy()->getItemDef(realitemid);
					if (realitemdef) //插件投掷物模型
					{
						sprintf(modelPath, "itemmods/%s/body.omod", realitemdef->Model.c_str());
					}
					else
					{
						realitemdef = itemDef;
						sprintf(modelPath, "itemmods/%s/body.omod", modeStr.c_str());
					}
				}
				else
				{
					if (isDoubleWeapon(itemid))//双持武器投掷物模型
					{
						const CraftingDef* craftDef = GetDefManagerProxy()->findCrafting(itemid);
						if (craftDef && craftDef->MaterialID[0])
						{
							auto* itemDef_right = GetDefManagerProxy()->getItemDef(craftDef->MaterialID[0]);
							if (itemDef_right)
							{
								realitemdef = itemDef_right;
								modeStr = realitemdef->Model.c_str();
								sprintf(modelPath, "itemmods/%s/body.omod", itemDef_right->Model.c_str());
							}
						}
						if (craftDef && craftDef->MaterialID[1])
						{
							auto* realitemdef_left = GetDefManagerProxy()->getItemDef(craftDef->MaterialID[1]);
							if (realitemdef_left)
							{
								char modelPath_left[128];
								sprintf(modelPath_left, "itemmods/%s/body.omod", realitemdef_left->Model.c_str());
								m_EntityModel_Left = g_BlockMtlMgr.getEntity(modelPath_left);

								if (realitemdef_left && realitemdef_left->TextureID >= 0)
								{
									if (realitemdef_left->TextureID > 0)
									{
										sprintf(modelPath_left, "itemmods/%s/texture%d.png", realitemdef_left->Model.c_str(), realitemdef_left->TextureID);
									}
									else if (realitemdef_left->TextureID == 0)
									{
										sprintf(modelPath_left, "itemmods/%s/texture.png", realitemdef_left->Model.c_str());
									}
									SharePtr<Texture2D> tex = GetAssetManager().LoadAsset<Texture2D>(modelPath_left);
									if (m_EntityModel_Left && m_EntityModel_Left->GetMainModel() && tex)
										m_EntityModel_Left->GetMainModel()->SetTexture("g_DiffuseTex", tex);
								}
							}
						}
					}
					else
					{
						realitemdef = itemDef;
						sprintf(modelPath, "itemmods/%s/body.omod", modeStr.c_str());
					}
				}

				LOG_INFO("modelPath: %s", modelPath);
				m_EntityModel = g_BlockMtlMgr.getEntity(modelPath);

				if (realitemdef && realitemdef->TextureID >= 0)
				{
					if (realitemdef->TextureID > 0)
					{
						sprintf(modelPath, "itemmods/%s/texture%d.png", modeStr.c_str(), realitemdef->TextureID);
					}
					else if(realitemdef->TextureID == 0)
					{
						sprintf(modelPath, "itemmods/%s/texture.png", modeStr.c_str());
					}
					SharePtr<Texture2D> tex = GetAssetManager().LoadAsset<Texture2D>(modelPath);
					if (m_EntityModel && m_EntityModel->GetMainModel() && tex)
						m_EntityModel->GetMainModel()->SetTexture("g_DiffuseTex", tex);
				}
				
			}
		}
	}
#endif
	ClientPlayer* player = dynamic_cast<ClientPlayer*>(shooter);
	if (player && IsColorableItem(m_ItemID))
	{
		Rainbow::ColourValue cv(1.0f, 1.0f, 1.0f, 1.0f);
		int opt;
		float score;

		// 玩法模式下，彩蛋枪/彩蛋/小彩蛋的颜色由队伍颜色决定
		if (GetWorldManagerPtr())
		{
			GetWorldManagerPtr()->getRuleOptionID(GMRULE_SCORE_COLORCHANGE, opt, score);
		}

		if (GetWorldManagerPtr() && GetWorldManagerPtr()->isGameMakerRunMode() && (int)score != 0 && player->getTeam())
		{
			m_Color = GetTeamColor(player->getTeam());
		}
		else
		{
			m_Color = player->getSelectedColor();
			if (m_Color == -1)
			{
				MINIW::ScriptVM::game()->callFunction("GetRandomColor", ">i", &m_Color);
			}
		}

		Rainbow::ColorQuad cq(m_Color);
		cv.r = cq.r / 255.0f;
		cv.g = cq.g / 255.0f;
		cv.b = cq.b / 255.0f;

		if (m_EntityModel)
			m_EntityModel->SetOverlayColor(&cv);
	}
	if (m_EntityModel && m_EntityModel->GetMainModel())
		m_EntityModel->GetMainModel()->SetInstanceData(Rainbow::Vector4f(1.0f, 1.0f, 0, 0)); // 啥意思??? 对应一个shader变量
	if (m_EntityModel_Left && m_EntityModel_Left->GetMainModel())
		m_EntityModel_Left->GetMainModel()->SetInstanceData(Rainbow::Vector4f(1.0f, 1.0f, 0, 0));

	if (physicsActorDef && m_EntityModel)
	{
		if (m_EntityModel && (physicsActorDef->EditType == PhysicsLocoMotion::PHYS_TYPE_NOEDIT_YESPHY || physicsActorDef->EditType == PhysicsLocoMotion::PHYS_TYPE_YESEDIT_YESPHY))
			m_EntityModel->SetScale(Rainbow::Vector3f(physicsActorDef->ModelScale, physicsActorDef->ModelScale, physicsActorDef->ModelScale));
		if (m_EntityModel_Left && (physicsActorDef->EditType == PhysicsLocoMotion::PHYS_TYPE_NOEDIT_YESPHY || physicsActorDef->EditType == PhysicsLocoMotion::PHYS_TYPE_YESEDIT_YESPHY))
			m_EntityModel_Left->SetScale(Rainbow::Vector3f(physicsActorDef->ModelScale, physicsActorDef->ModelScale, physicsActorDef->ModelScale));
	}

	if (m_EntityModel)
		m_EntityModel->SetScale(m_ProjectileDef->ModelScale);
	
	ProjectileLocoMotion *locmove = static_cast<ProjectileLocoMotion *>(getLocoMotion());
	locmove->setBound((int)m_ProjectileDef->Bounds, (int)m_ProjectileDef->Bounds);
	locmove->m_yOffset = locmove->m_BoundHeight / 2;
	locmove->m_Gravity = m_ProjectileDef->Gravity;
	locmove->m_SpeedDecay = m_ProjectileDef->SpeedDecay;
	locmove->m_TriggerCondition = (TRIGGER_CONDITION)m_ProjectileDef->TriggerCondition;

	m_ShootingActorID = 0;
	m_ImpactTimeMark = -1;
	m_AttachedEffect = 0;

	m_StartPos = WCoord(0, 0, 0);
	m_AttackPoints = 0;
	m_BuffAttackAdd = 0;
	m_KnockbackStrength = 0;
	m_BuffId = 0;

	const HorseAbilityDef* abdef = GetDefManagerProxy()->getHorseAbilityDef(HORSE_SKILL_STARLIGHT);
	if (abdef)
	{
		m_starBulletDuration = abdef->Effect[3] <= Rainbow::EPSILON ? 3.0f : abdef->Effect[3];
	}

	processSkillOnInit(shooter);
}

void ClientActorProjectile::setShootingActor(ClientActor *actor)
{
	m_BaseAtk = 0;
	if(actor)
	{
		m_ShootingActorID = actor->getObjId();
		ClientPlayer *player = dynamic_cast<ClientPlayer *>(actor);
		if(player) m_BaseAtk = player->getGeniusValue(GENIUS_BASEATK_INC);
	}
	else m_ShootingActorID = 0;
}

ClientActor *ClientActorProjectile::getShootingActor()
{
	if (!m_pWorld)
	{
		return nullptr;
	}
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_pWorld->getActorMgr());
	if (!actorMgr) return nullptr;
	return actorMgr->findActorByWID(m_ShootingActorID);
}

ClientActorProjectile *ClientActorProjectile::shootProjectileAuto(int itemid, World *pworld, const WCoord &pos, const Rainbow::Vector3f &dir, float speed, float deviation, long long shooterObjId, int durable)
{
	ClientActorProjectile *projectile = SANDBOX_NEW(ClientActorProjectile);
	projectile->init(itemid);

	//冰雾瓶落地特效需要射击者
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(pworld->getActorMgr());
	if (!actorMgr) return nullptr;
	if (shooterObjId > 0 && itemid == ITEM_ICE_BOTTLE)
	{
		ClientActor* shooter = actorMgr->findActorByWID(shooterObjId);
		if (shooter)
		{
			projectile->setShootingActor(shooter);
		}
	}
	actorMgr->spawnActor(projectile, pos, 0, 0);
	projectile->m_StartPos = projectile->getPosition();

	ProjectileLocoMotion *locomove = static_cast<ProjectileLocoMotion *>(projectile->getLocoMotion());
	locomove->setThrowableHeading(dir, speed, deviation);

	projectile->playMotion(projectile->m_ProjectileDef->TailEffect.c_str(), true, 0, 2);
	if (GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate)
	{
		projectile->UpdateAttackPoints();

		projectile->m_strength = 1.0f;
	}
	else
	{
		projectile->m_AttackPoints = projectile->m_ProjectileDef->AttackValue;
	}
	if (projectile->m_ProjectileDef->TriggerCondition == 4)
	{
		projectile->m_ImpactTimeMark = 0;
	}
	projectile->setMasterObjId(shooterObjId);
	//耐久度
	if (durable == -1)
	{
		auto tooDef = GetDefManagerProxy()->getToolDef(itemid);
		if (tooDef)
			projectile->m_Durable = tooDef->Duration;
		else
			projectile->m_Durable = 0;
	}
	else
	{
		projectile->m_Durable = durable;
	}
	return projectile;
}

//void ClientActorProjectile::onCull(MINIW::CullResult *presult, MINIW::CullFrustum *frustum)
//{
//	if (getBody())
//	{
//		getBody()->onCull(presult, frustum);
//	}
//	else
//	{
//		if (m_EntityModel) presult->addRenderable(m_EntityModel, RL_SCENE, NULL);
//	}
//
//}
void ClientActorProjectile::onMotionStop()
{
	if (GetItemId() == 11662 || !m_dropable)
	{
		setNeedClearEx();
	}
}

//漂流瓶获取服务器数据
static void driftGetLetterMessage(const WCoord& position, int playUin, const std::string& userData, World* world, int itemid)
{
	if (world == NULL)
	{
		return;
	}
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(world->getActorMgr());
	if (!actorMgr) return ;
	jsonxx::Object object;
	object.parse(userData);
	if (object.has<jsonxx::Number>("uin"))
	{
		//已经有数据了,直接返回
		ClientItem* item = actorMgr->spawnItem(position, ITEM_LETTERS, 1);
		item->m_ItemData.userdata_str = userData;
	}
	else if (itemid == BLOCK_FAR_DRIFTBOTTLE)
	{
		////主机自己
		if (GetGameInfoProxy()->GetRoomHostType() != ROOM_SERVER_RENT && (g_pPlayerCtrl != NULL && g_pPlayerCtrl->getUin() == playUin))
		{
			BackPackGrid grid;
			grid.setItem(ITEM_LETTERS, 1);
			bool b = false;
			MINIW::ScriptVM::game()->callFunction("DriftBottleGetContent", "u[BackPackGrid]i>b", &grid, playUin, &b);
			actorMgr->spawnItem(position, grid);
		}
		else
		{
			//什么信息都没有,表示要拿取新数据
			jsonxx::Object object;
			object << "spawnType" << 2;
			object << "x" << position.x;
			object << "y" << position.y;
			object << "z" << position.z;
			int size = 0;
			unsigned char* p = NULL;
			object.saveBinary(p, size);
			SandBoxManager::getSingletonPtr()->sendToClient(playUin, (char*)("DRIFTBOTTLE_GET"), p, size);
			free(p);
		}
	}
	else
	{
		actorMgr->spawnItem(position, ITEM_LETTERS, 1);
	}
}

void ClientActorProjectile::tick()
{
	ClientActor::tick();
	
	if (m_ImpactTimeMark >= 0)
	{
		if (m_ProjectileDef)
		{
			if (m_ProjectileDef->ID == BLOCK_STARBULLET)//投射物是星星弹
			{
				if (m_ImpactTimeMark > m_ProjectileDef->TriggerDelay * 20 && m_ImpactTimeMark < (m_ProjectileDef->TriggerDelay+0.1) * 20)//延迟触发生成光照方块
				{
					setPosition(m_BlockPos.x * BLOCK_SIZE, 200 * BLOCK_SIZE, m_BlockPos.z * BLOCK_SIZE);
					doTrigger();//生成光照方块	
				}
				else if (m_ImpactTimeMark > (m_ProjectileDef->TriggerDelay+m_starBulletDuration+0.2) *20)
				{
					if (!m_ProjectileDef->Pickable || m_nBlockID || !m_dropable)
					{
						setNeedClearEx();
					}
				}
				else
				{
					++m_ImpactTimeMark;
				}
			}
			else
			{
				if (m_ProjectileDef && m_ImpactTimeMark > m_ProjectileDef->TriggerDelay * 20)
				{
					doTrigger(m_strEffectName);
					if (!m_ProjectileDef->Pickable || m_nBlockID || !m_dropable)
					{
						setNeedClearEx();
					}
				}
				else
					++m_ImpactTimeMark ;
			}
		}
	}

	if (!m_pWorld->isRemoteMode())
	{
		//子弹速度低于一定值就直接clear掉
		if (m_ItemID == 15003)
		{
			//LOG_INFO("getLocoMotion()->m_Motion: %f, %f, %f", getLocoMotion()->m_Motion.x, getLocoMotion()->m_Motion.y, getLocoMotion()->m_Motion.z);
			if (getLocoMotion() && getLocoMotion()->m_Motion.LengthSqr() < 10.0f)
			{
				setNeedClearEx();
			}
		}

		int y = getLocoMotion()->getPosition().y;
		if(y > 256*BLOCK_SIZE*2 || y < -64*BLOCK_SIZE) setNeedClear(); //飞得太高也clear
		if (m_ItemID == 11662)//海马水泡
		{
			if (m_StartPos.distanceTo(getLocoMotion()->getPosition()) >= BLOCK_SIZE * GetLuaInterfaceProxy().get_lua_const()->bubble_move_max_distance)
			{
				setNeedClearEx();
			}
		}
		if (m_ItemID == ITEM_SPOUT)
		{
			if (m_StartPos.distanceTo(getLocoMotion()->getPosition()) >= BLOCK_SIZE * 16)
				setNeedClear();
		}
#ifndef USE_ACTOR_CONTROL
		ProjectileLocoMotion *loc = static_cast<ProjectileLocoMotion *>(getLocoMotion());
		if(loc->m_PhysActor)
		{
			CollideAABB box;
			getCollideBox(box);
			box.expand(20, 0, 20);

			std::vector<ClientActor *>actors;
			m_pWorld->getActorsInBoxExclude(actors, box, this);

			for(size_t i=0; i<actors.size(); i++)
			{
				ClientActor *pactor = actors[i];
				if(pactor->canBePushed())
				{
					ClientActor *actor = nullptr;
					auto bindAComponent = getBindActorCom();
					if (bindAComponent)
					{
						actor = dynamic_cast<ClientActor *>(bindAComponent->getTarget());
	}
					if (actor != pactor)
						pactor->applyActorCollision(this);
				}
			}
		}
#endif
	}

	if (m_nBlockID && IsAirBlockID(getWorld()->getBlockID(m_BlockPos)))
	{
		m_nBlockID = 0;
		setNeedClearEx();
	}

	if (m_EntityModel && m_EntityModel->GetMainModel())
	{
		Rainbow::Vector4f lightparam(0,0,0,0);
		WCoord center = getPosition();
		center.y += getLocoMotion()->m_BoundHeight/2;
		if(m_pWorld) m_pWorld->getBlockLightValue2_Air(lightparam.x, lightparam.y, CoordDivBlock(center));
		m_EntityModel->GetMainModel()->SetInstanceData(lightparam);
		if (getBody() && getBody()->getEntity() && getBody()->getEntity()->GetMainModel())
		{
			getBody()->getEntity()->GetMainModel()->SetInstanceData(lightparam);
		}
	}
	if (m_EntityModel_Left && m_EntityModel_Left->GetMainModel())
	{
		Rainbow::Vector4f lightparam(0, 0, 0, 0);
		WCoord center = getPosition();
		center.y += getLocoMotion()->m_BoundHeight / 2;
		if (m_pWorld) m_pWorld->getBlockLightValue2_Air(lightparam.x, lightparam.y, CoordDivBlock(center));
		m_EntityModel_Left->GetMainModel()->SetInstanceData(lightparam);
	}

	if (!m_delayMotionMap.empty() && m_EntityModel)
	{
		m_curTicksNum++;
		auto itr = m_delayMotionMap.begin();
		int delay = itr->first;
		if (m_curTicksNum >= delay)
		{
			DelayMotionData data;
			for (;itr != m_delayMotionMap.end();)
			{
				if (itr->first > m_curTicksNum)
				{
					break;
				}
				
				data = itr->second;
 				m_EntityModel->PlayMotion(data.name.c_str(), data.reset_play, data.motion_class, data.looptime);
				if (data.looptime > 0)
				{
					SharePtr<ModelMotion> pmotion = m_EntityModel->FindMotion(data.name.c_str());
					if (pmotion)
					{
						pmotion->m_loop = data.looptime > 0 ? ML_LOOP : ML_ONCE;
						pmotion->m_fLoopStopTime = data.looptime;
					}
				}
				itr = m_delayMotionMap.erase(itr);
			}

			m_curTicksNum = 0;
		}
	}
	else
	{
		m_curTicksNum = 0;
	}
}


void ClientActorProjectile::RotateForWeapon(int itemId, Rainbow::Entity* entity)
{
	auto* itemDef_left = GetDefManagerProxy()->getItemDef(itemId);
	auto def = GetDefManagerProxy()->getProjectileDef(itemId);
	auto toolDef = GetDefManagerProxy()->getToolDef(itemId);//双持武器模型旋转
	if (def && (def->Model == ITEM_ARROW_MODEL || def->Model == ITEM_MISSILE_MODEL || def->Model == ITEM_REDFIREWORKDS_MODEL
		|| def->Model == ITEM_PURPLEFIREWORKDS_MODEL || def->Model == ITEM_GREENFIREWORKDS_MODEL
		|| def->Model == ITEM_OCEANARROW_MODEL))
	{
		entity->SetRotation(getLocoMotion()->m_RotateYaw, -getLocoMotion()->m_RotationPitch - 90, 0);
	}
	//子弹（模型翻转特殊处理）
	else if (def && (def->Model == ITEM_BULLET_MODEL))
	{
		entity->SetRotation(getLocoMotion()->m_RotateYaw, -getLocoMotion()->m_RotationPitch - 180, 0);
	}
	else if (isBoomerangItem(m_ItemID))
	{
		entity->SetRotation(getLocoMotion()->m_RotateYaw, -getLocoMotion()->m_RotationPitch + 90, 0);
	}
	else
	{
		entity->SetRotation(getLocoMotion()->m_RotateYaw, -getLocoMotion()->m_RotationPitch - 90, 0);
	}
}

void ClientActorProjectile::update(float dtime)
{
	ClientActor::update(dtime);

	auto updateFrequencyComp = getUpdateFrequencyCom();
	float retdt = 0.0f;
	if (updateFrequencyComp && !updateFrequencyComp->isNeedUpdate())// getLocoMotion 不用更新返回
	{
		return;
	}

	#ifndef IWORLD_SERVER_BUILD
	if (!m_EntityModel)
		return;

	ProjectileLocoMotion *loc = static_cast<ProjectileLocoMotion *>(getLocoMotion());
	do
	{
		auto comp = m_pActorBindVehicle;//by__Logo		
		Rainbow::Vector3f pos(0, MAX_FLOAT, 0);
		if (comp && comp->getInVehiclePos(pos)/*getActorBindVehicle()->getInVehiclePos(pos)*/)
			m_EntityModel->SetPosition(WorldPos::fromVector3(pos));
		else
			m_EntityModel->SetPosition(loc->m_UpdatePos);

		if (GetWorldManagerPtr() && GetWorldManagerPtr()->m_updategraphicsvec)
		{
			GetWorldManagerPtr()->m_updategraphicsvec(dtime, this, getPosition().toVector3());
		}
	}
	while(0);
	
	//Need calculate arrow rotation 
	if (GetWorldManagerPtr()->m_SurviveGameConfig->physxconfig.enable && loc->m_hasPhysActor)
	{
		m_EntityModel->SetRotation(loc->m_UpdateRot);
	}
	else if (m_EntityModel_Left && m_EntityModel)
	{
		if (isDoubleWeapon(m_ItemID))//双持武器投掷物模型
		{
			const CraftingDef* craftDef = GetDefManagerProxy()->findCrafting(m_ItemID);
			if (craftDef && craftDef->MaterialID[0])
			{
				RotateForWeapon(craftDef->MaterialID[0], m_EntityModel);
			}
			if (craftDef && craftDef->MaterialID[1])
			{
				RotateForWeapon(craftDef->MaterialID[1], m_EntityModel_Left);
			}
		}
		m_EntityModel_Left->SetPosition(WorldPos(loc->m_UpdatePos.x + 100, loc->m_UpdatePos.y, loc->m_UpdatePos.z + 100));
		m_EntityModel_Left->UpdateTick(TimeToTick(dtime));
	}
	else
	{
		auto def = GetDefManagerProxy()->getProjectileDef(m_ItemID);
		auto toolDef = GetDefManagerProxy()->getToolDef(m_ItemID);
		if (needRoteBullets(m_ItemID))
		{
			m_EntityModel->SetRotation(getLocoMotion()->m_RotateYaw + 180, -getLocoMotion()->m_RotationPitch, 0);
		}
		else if (def && (def->Model == ITEM_ARROW_MODEL || def->Model == ITEM_MISSILE_MODEL || def->Model == ITEM_REDFIREWORKDS_MODEL
			|| def->Model == ITEM_PURPLEFIREWORKDS_MODEL || def->Model == ITEM_GREENFIREWORKDS_MODEL
			|| def->Model == ITEM_OCEANARROW_MODEL))
		{
			m_EntityModel->SetRotation(getLocoMotion()->m_RotateYaw, -getLocoMotion()->m_RotationPitch - 90, 0);
		}
		//子弹（模型翻转特殊处理）
		else if (def && (def->Model == ITEM_BULLET_MODEL))
		{
			m_EntityModel->SetRotation(getLocoMotion()->m_RotateYaw, -getLocoMotion()->m_RotationPitch - 180, 0);
		}
		else if (isBoomerangItem(m_ItemID))
		{
			m_EntityModel->SetRotation(getLocoMotion()->m_RotateYaw, -getLocoMotion()->m_RotationPitch + 90, 0);
		}
		// 矛
		else if (toolDef && toolDef->SubType == 4)
		{
			m_EntityModel->SetRotation(getLocoMotion()->m_RotateYaw, -getLocoMotion()->m_RotationPitch - 90, 0);
		}
		else if (toolDef&&(isBullets(toolDef->ID)|| isBullets(def->ID)))
		{
			m_EntityModel->SetRotation(getLocoMotion()->m_RotateYaw, -getLocoMotion()->m_RotationPitch - 180, 0);
			/*m_EntityModel->SetRotation(getLocoMotion()->m_RotateYaw + 180, -getLocoMotion()->m_RotationPitch, 0);*/
		}
		else
		{
			m_EntityModel->SetRotation(getLocoMotion()->m_RotateYaw, -getLocoMotion()->m_RotationPitch, 0);
		}
	}
	m_EntityModel->UpdateTick(TimeToTick(dtime));
	#endif
}

flatbuffers::Offset<FBSave::SectionActor> ClientActorProjectile::save(SAVE_BUFFER_BUILDER &builder)
{
	auto basedata = saveActorCommon(builder);
	auto enchants = builder.CreateVector(m_Enchants, m_EnchantNum);
	auto blockpos = WCoordToCoord3(m_BlockPos);
	ProjectileLocoMotion *loco = static_cast<ProjectileLocoMotion *>(getLocoMotion());
	auto quat = QuaternionToSave(loco->m_RotateQuat);
	auto obj = FBSave::CreateActorProjectile(builder, basedata, m_ShootingActorID, m_ItemID, m_Durable, enchants, 
		m_Color, m_nBlockID, &blockpos, &quat, m_Runedata.save(builder), 
		builder.CreateString(m_UserDataStr.c_str()), m_passActorNum, m_puncture, m_MaxDurable);

	return saveSectionActor(builder, FBSave::SectionActorUnion_ActorProjectile, obj.Union());
}


bool ClientActorProjectile::load(const void *srcdata, int version)
{
	m_LiveTicks = 0;
	auto src = reinterpret_cast<const FBSave::ActorProjectile *>(srcdata);
	init(src->itemid());
	loadActorCommon(src->basedata());

	m_Durable = src->durable();
	m_MaxDurable = src->maxdurable();

	if (src->itemid() == ITEM_BOMB || src->itemid() == ITEM_CANNED_BOMB || src->itemid() == ITEM_PINEAPPLE_BOMB)
	{
		m_ImpactTimeMark = 0;
	}

	m_ShootingActorID = src->shooter();

	if (IsColorableItem(m_ItemID))
	{
		m_Color = src->color();

		if(m_EntityModel)
		{
			Rainbow::ColourValue cv(1.0f, 1.0f, 1.0f, 1.0f);
			Rainbow::ColorQuad cq(m_Color);
			cv.r = cq.r / 255.0f;
			cv.g = cq.g / 255.0f;
			cv.b = cq.b / 255.0f;

			if(m_EntityModel->GetMainModel()){
				m_EntityModel->SetOverlayColor(&cv);
				m_EntityModel->GetMainModel()->SetInstanceAmbient(cv);
			}
		}
	}

	playMotion(m_ProjectileDef->TailEffect.c_str());

	if(src->enchants())
	{
		m_EnchantNum = src->enchants()->size();
		if (m_EnchantNum > MAX_ITEM_ENCHANTS) m_EnchantNum = MAX_ITEM_ENCHANTS;
		for (int i = 0; i < m_EnchantNum; i++)
		{
			m_Enchants[i] = src->enchants()->Get(i);
		}
		
	}

	m_Runedata.load(src->runes());

	m_nBlockID = src->blockid();
	if (src->blockpos())
	m_BlockPos = Coord3ToWCoord(src->blockpos());
	ProjectileLocoMotion* loco = dynamic_cast<ProjectileLocoMotion *>(getLocoMotion());
	if (loco)
	{
		loco->syncPos = getLocoMotion()->getPosition();
		if(src->rotatequat())
			loco->m_RotateQuat = QuaternionFromSave(src->rotatequat());
		else
			loco->m_RotateQuat= AngleEulerToQuaternionf(Vector3f(loco->m_RotationPitch, loco->m_RotateYaw, 0));

		loco->m_CanTrigger = false;  
		if (loco->needFullRotation())
		{
			m_ServerYawCmp = loco->m_RotateQuat.ToUInt32();
		}
		else
		{
			loco->syncYaw = loco->m_RotateYaw;
			loco->syncPitch = loco->m_RotationPitch;
		}
	}
	if (src->userData())
	{
		m_UserDataStr = src->userData()->str();
	}

	m_passActorNum = src->passActorNum();
	m_puncture = src->puncture();
	return true;
}

void ClientActorProjectile::moveToPosition(const WCoord &pos, float yaw, float pitch, int interpol_ticks)
{
	ProjectileLocoMotion *locmove = static_cast<ProjectileLocoMotion *>(getLocoMotion());
	
	locmove->m_InterplateStep = 3;
	locmove->syncPos = pos;
	locmove->syncYaw = yaw;
	locmove->syncPitch = pitch;
}

void ClientActorProjectile::moveToPosition(const WCoord &pos, Rainbow::Quaternionf &rot, int interpol_ticks)
{
	ProjectileLocoMotion *locmove = static_cast<ProjectileLocoMotion *>(getLocoMotion());
	if (!(locmove->m_ServerRot == rot && pos == locmove->m_ServerPos))
	{
		locmove->m_PosRotationIncrements = interpol_ticks;
		locmove->m_ServerPos = pos;
		locmove->m_ServerRot = rot;
	}
}

std::vector<WCoord> ClientActorProjectile::doPickBlockByItemSkill(int skillid, const WCoord *blockpos, int face)
{
	std::vector<WCoord> wCoordVec;
	const ItemSkillDef *skilldef = GetDefManagerProxy()->getItemSkillDef(skillid);
	if(skilldef)
	{
		if (skilldef->RangeType == 0)//单体
		{
			wCoordVec.push_back(*blockpos);
			WCoord placepos = NeighborCoord(*blockpos, face);
			wCoordVec.push_back(placepos);
		}
		else if	(skilldef->RangeType == 1)//射线	
		{
			//起始位置
			WCoord coord = CoordDivBlock(getPosition());

			//射线方向
			Rainbow::Vector3f origin = getLocoMotion()->m_Position.toVector3(); 
			Rainbow::Vector3f dir  = MINIW::Normalize((const Rainbow::Vector3f)getLocoMotion()->m_Motion);
			Rainbow::Vector3f dir2 = CrossProduct(Rainbow::Vector3f(0, 1.0f, 0), dir);
			dir2  = MINIW::Normalize(dir2);
			Rainbow::Vector3f dir3 = CrossProduct(dir2, dir);
			dir3  = MINIW::Normalize(dir3);
			HashMap<long long, int> blockmap;

			float length = skilldef->RangeVal1;
			float width = skilldef->RangeVal2/2.0f;
			for(float x = 0.0f; x<=length; x += BLOCK_SIZE/2.0f)
			{
				for(float y = -width; y<=width; y += BLOCK_SIZE/2.0f)
				{
					for(float z = -width; z<=width; z += BLOCK_SIZE/2.0f)
					{
						Rainbow::Vector3f dp = dir2*y + dir3*z;
						if(dp.LengthSqr() < width*width)
						{
							Rainbow::Vector3f blockPosV3 = origin + dir*x + dp; 
							WCoord blockpos(CoordDivBlock(WCoord((int)blockPosV3.x, (int)blockPosV3.y, (int)blockPosV3.z)));

							 int blockid = getWorld()->getBlockID(blockpos);
							 const BlockDef* def = GetDefManagerProxy()->getBlockDef(blockid);
							 if(blockid == 0 ||
							 (def && (skilldef->TargetClass== 0 || skilldef->TargetClass == def->MineTool)))
							 {
								if(blockmap.find(BLOCK_HASH_FUN(0, blockpos.y, blockpos.x, blockpos.z)) == blockmap.end())
								{
									blockmap[BLOCK_HASH_FUN(0, blockpos.y, blockpos.x, blockpos.z)] = 1;
									wCoordVec.push_back(blockpos);
								}
							 }
						}
					}

				}
			}
		}
		else  //立体
		{
			float length = skilldef->RangeVal1/2.0f;
			float width = skilldef->RangeVal2/2.0f;
			float height = skilldef->RangeVal3/2.0f;
			//起始位置
			WCoord coord = CoordDivBlock(getPosition());

			//立方体方向
			Rainbow::Vector3f origin = getLocoMotion()->m_Position.toVector3(); 
			Rainbow::Vector3f dir  = MINIW::Normalize((const Rainbow::Vector3f)getLocoMotion()->m_Motion);

			//算出正方体x,y,z三轴
			float rotateYaw; 
			float rotationPitch;
			Direction2PitchYaw(&rotateYaw, &rotationPitch, dir);
			Quaternionf rotation= AngleEulerToQuaternionf(Vector3f(rotationPitch, rotateYaw, 0));

			dir = rotation.GetAxisZ(); 
			Rainbow::Vector3f dir2 = rotation.GetAxisY(); 
			Rainbow::Vector3f dir3 = rotation.GetAxisX(); 
			HashMap<long long, int> blockmap;
			for(float x = 0.0f; x<=length; x += BLOCK_SIZE/2.0f)
			{
				for(float y = -height; y<=height; y += BLOCK_SIZE/2.0f)
				{
					for(float z = -width; z<=width; z += BLOCK_SIZE/2.0f)
					{
						Rainbow::Vector3f blockPosV3 = origin + dir*x + dir2*y + dir3*z; 
						WCoord blockpos(CoordDivBlock(WCoord((int)blockPosV3.x, (int)blockPosV3.y, (int)blockPosV3.z)));

						//判断方块类型是否一致
						 int blockid = getWorld()->getBlockID(blockpos);
						 const BlockDef* def = GetDefManagerProxy()->getBlockDef(blockid);
						 if(blockid == 0 ||
						 (def && (skilldef->TargetClass== 0 || skilldef->TargetClass == def->MineTool)))
						 {
							if(blockmap.find(BLOCK_HASH_FUN(0, blockpos.y, blockpos.x, blockpos.z)) == blockmap.end())
							{
								blockmap[BLOCK_HASH_FUN(0, blockpos.y, blockpos.x, blockpos.z)] = 1;
								wCoordVec.push_back(blockpos);
							}
						}
					}

				}
			}

			
		}
	}
	return wCoordVec;
}

std::vector<ClientActor*> ClientActorProjectile::doPickActorByItemSkill(ClientActor* target, int skillid, WCoord& centerPos)
{
	std::vector<ClientActor*> idvec;
	const ItemSkillDef* skilldef = GetDefManagerProxy()->getItemSkillDef(skillid);
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_pWorld->getActorMgr());
	if (!actorMgr) return idvec;
	if (skilldef)
	{
		ClientPlayer* attacker = dynamic_cast<ClientPlayer*>(actorMgr->findActorByWID(m_ShootingActorID));
		if (skilldef->RangeType == 0)//单体
		{
			if (attacker && target && target->canAttackByItemSkill(skillid, attacker))
			{
				if (target)
				{
					idvec.push_back(target);
				}
			}
		}
		else if (skilldef->RangeType == 1)//射线	
		{
			if (target)
			{
				idvec.push_back(target);
			}
			float length = skilldef->RangeVal1;
			float width = skilldef->RangeVal2 / 2.0f;
			int range = (int)max(length, width);
			CollideAABB box;
			getCollideBox(box);
			box.expand((int)(range * 1.5f), (int)(range * 1.5f), (int)(range * 1.5f));
			std::vector<IClientActor*>tmpactors;
			m_pWorld->getActorsInBox(tmpactors, box);

			Rainbow::Vector3f origin = getLocoMotion()->m_Position.toVector3(); 
			Rainbow::Vector3f dir  = MINIW::Normalize((const Rainbow::Vector3f)getLocoMotion()->m_Motion);

			for (size_t i = 0; i < tmpactors.size(); i++)
			{
				ClientActor* actor = tmpactors[i]->GetActor();
				if (attacker && !actor->canAttackByItemSkill(skillid, attacker))
				{
					continue;
				}
				WCoord originpos((int)origin.x, (int)origin.y, (int)origin.z);
				WCoord hitcenter(actor->getPosition().x, actor->getPosition().y + BLOCK_SIZE / 2, actor->getPosition().z);
				Rainbow::Vector3f dp = (hitcenter - originpos).toVector3();

				float t = DotProduct(dp, dir);
				if (t > 0 && t < length)
				{
					Rainbow::Vector3f tmp = CrossProduct(dp, dir);
					if (tmp.LengthSqr() < width * width && actor->getObjId() != getObjId())
					{
						if (target)
						{
							if (target->getObjId() != actor->getObjId())
							{
								idvec.push_back(actor);
							}
						}
						else
						{
							idvec.push_back(actor);
						}
					}
				}
			}
		}
		else //立体
		{
			if (target && isInfluence(skillid, target))
			{
				idvec.push_back(target);
			}
			float length = skilldef->RangeVal1 / 2.0f;
			float width = skilldef->RangeVal2 / 2.0f;
			float height = skilldef->RangeVal3 / 2.0f;
			float range = max(length, width);
			range = max(range, height) * 1.5f;

			CollideAABB box;
			getCollideBox(box);
			centerPos.x = box.centerX();
			centerPos.y = box.centerY();
			centerPos.z = box.centerZ();
			//射线方向
			Rainbow::Vector3f origin = getLocoMotion()->m_Position.toVector3(); 
			//::Rainbow::Vector3f dir  = MINIW::Normalize((const Rainbow::Vector3f)getLocoMotion()->m_Motion);
			Rainbow::Vector3f dir;

			box.expand((int)range, (int)range, (int)range);
			std::vector<IClientActor *>tmpactors;
			m_pWorld->getActorsInBox(tmpactors, box);

			//算出正方体x,y,z三轴
			float rotateYaw = getLocoMotion()->m_RotateYaw; 
			float rotationPitch = getLocoMotion()->m_RotationPitch;
			//Direction2PitchYaw(&rotateYaw, &rotationPitch, dir);
			Quaternionf rotation;
			rotation= AngleEulerToQuaternionf(Vector3f(rotationPitch, rotateYaw, 0));
			dir = rotation.GetAxisX(); 
			Rainbow::Vector3f dir2 = rotation.GetAxisY(); 
			Rainbow::Vector3f dir3 = rotation.GetAxisZ(); 

			for(size_t i=0; i<tmpactors.size(); i++)
			{
				ClientActor *actor = tmpactors[i]->GetActor();
				if(attacker && !actor->canAttackByItemSkill(skillid, attacker))
				{
					continue;
				}
				CollideAABB hitbox;
				actor->getHitCollideBox(hitbox);
				WCoord originpos(box.centerX(), box.centerY(), box.centerZ());
				WCoord hitcenter(hitbox.centerX(), hitbox.centerY(), hitbox.centerZ());
				Rainbow::Vector3f dp = (hitcenter - originpos).toVector3();

				if (actor->getObjId() == getObjId() || (target && target->getObjId() == actor->getObjId()))
				{
					continue;
				}
				float t = DotProduct(dp, dir);
				if (t == 0 || abs(t) > length)
				{
					continue;
				}
				t = DotProduct(dp, dir2);
				if (t == 0 || abs(t) > width)
				{
					continue;
				}
				t = DotProduct(dp, dir3);
				if (t == 0 || abs(t) > height)
				{
					continue;
				}

				idvec.push_back(actor);
			}

		}
	}
	return idvec;
}

extern WCoord GetNearMobSpawnPos(ClientPlayer *player);

void ClientActorProjectile::checkUseItemSkillBlock(const WCoord* block, int face)
{
	if (!block || needClear())
	{
		return;
	}
	DoUseItemSkillBlock(block, face);
}

void ClientActorProjectile::DoUseItemSkillBlock(const WCoord* block, int face)
{
	const ItemDef* def = GetDefManagerProxy()->getItemDef(m_ItemID);
	if (def == NULL) return;

	for (int i = 0; i < (int)def->SkillID.size(); i++)
	{
		const ItemSkillDef* skilldef = GetDefManagerProxy()->getItemSkillDef(def->SkillID[i]);
		if (skilldef)
		{
			//触发所有方块效果
			std::vector<WCoord> blockpos = doPickBlockByItemSkill(def->SkillID[i], block, face);
			//触发所有的方块效果
			for (int j = 0; j < (int)skilldef->SkillFuncions.size(); j++)
			{
				ItemSkillDef::SkillFuncionsDef* functiondef = (ItemSkillDef::SkillFuncionsDef*)&skilldef->SkillFuncions[j];
				for (int t = 0; t < (int)blockpos.size(); t++)
				{
					if (functiondef->oper_id == 1) //方块功能
					{
						if (functiondef->func.blockfun.ActionType == 0)
						{
							setNeedClearEx();
							int blockid = getWorld()->getBlockID(blockpos[t].x, blockpos[t].y, blockpos[t].z);
							if (blockid)
							{
								BlockMaterial* blockmtl = g_BlockMtlMgr.getMaterial(blockid);
								int blockdata = m_pWorld->getBlockData(blockpos[t]);
								if (blockmtl
									&& (blockmtl->getDestroyHardness(blockdata, NULL) >= 0
										|| g_WorldMgr->isGodMode()
										|| m_pWorld->CheckBlockSettingEnable(blockmtl, ENABLE_DESTROYED) == 2))
								{
									if (functiondef->func.blockfun.DropOut == 1)
										getWorld()->destroyBlock(blockpos[t], BLOCK_MINE_TOOLFIT);
									else
										getWorld()->destroyBlock(blockpos[t], BLOCK_MINE_NONE);
								}
							}
						}
						else if (functiondef->func.blockfun.ActionType == 1)
						{
							setNeedClearEx();
							int blockId = getWorld()->getBlockID(blockpos[t].x, blockpos[t].y, blockpos[t].z);
							const BlockDef* def = GetDefManagerProxy()->getBlockDef(blockId);
							if (def->MoveCollide == 0)
							{		
								getWorld()->destroyBlock(blockpos[t], BLOCK_MINE_TOOLFIT);
								int downBlockId = getWorld()->getBlockID(DownCoord(blockpos[t]));
								if (skilldef->ID == 337 && (downBlockId == BLOCK_FARMLAND || downBlockId == BLOCK_BURYLAND))//沙球在耕地变为土块
								{
									getWorld()->setBlockAll(blockpos[t], BLOCK_DIRT, 0, 2);
								}
								else
								{
									getWorld()->setBlockAll(blockpos[t], functiondef->func.blockfun.BlockID, 0, 2);
								}								
							}
						}
						else if (functiondef->func.blockfun.ActionType == 2)
						{
							setNeedClearEx();
							int blockid = getWorld()->getBlockID(blockpos[t].x, blockpos[t].y, blockpos[t].z);
							if (blockid)
							{
									auto mtl = g_BlockMtlMgr.getMaterial(blockid);
								const BlockDef* def = GetDefManagerProxy()->getBlockDef(blockid);
									if (mtl && (mtl->getBlockHardness() >= 0 || g_WorldMgr->isGodMode()))//(def && (def->Hardness>=0 || g_WorldMgr->isGodMode()))
								{
									getWorld()->setBlockAll(blockpos[t], functiondef->func.blockfun.BlockID, 0, 2);
								}
							}
						}
						else if (functiondef->func.blockfun.ActionType == 4) //放置后会自动销毁的方块
						{
							if (IsAirBlockID(getWorld()->getBlockID(blockpos[t]))
								|| getWorld()->getBlockMaterial(blockpos[t])->isReplaceable()
								|| IsWaterBlockID(getWorld()->getBlockID(blockpos[t])))
							{
								if (m_nBlockID)
								{
									clearBlock();
								}
								m_isCheckFinally = false;
								getWorld()->setBlockAll(blockpos[t], functiondef->func.blockfun.BlockID, 0, 2);
								m_nBlockID = functiondef->func.blockfun.BlockID;
								m_BlockPos = blockpos[t];
							}
                        } else if (functiondef->func.blockfun.ActionType == 5) {
                            int blockid = getWorld()->getBlockID(blockpos[t].x, blockpos[t].y, blockpos[t].z);
                            if (blockid == functiondef->func.blockfun.curBlockID) {
                                getWorld()->setBlockAll(blockpos[t], functiondef->func.blockfun.toBlockID, 0);
                            }
                        }
						else if (functiondef->func.blockfun.ActionType == 6) {
							// 只有碰撞的朝向是空气方块才能放置。地刺等碰撞为空气类型非空气方块的不会产生新方块
							setNeedClearEx();
							if (IsAirBlockID(getWorld()->getBlockID(blockpos[t])))
							{
								getWorld()->setBlockAll(blockpos[t], functiondef->func.blockfun.BlockID, 0, 2);
							}
						}
					}
				}
			}
		}
	}
}
void ClientActorProjectile::useItemSkill(ClientActor* actor, const WCoord* block, int face)
{
	if (!actor)
	{
		checkUseItemSkillBlock(block, face);
	}
	else
	{
		checkUseItemSkillActor(actor);
	}
	//召唤怪物
	checkCallMob();
	checkCreateActor(actor, block, face);
}
bool ClientActorProjectile::isProjectileSkill()
{
	const ItemDef* def = GetDefManagerProxy()->getItemDef(m_ItemID);
	if (def != NULL)
	{
		for (int i = 0; i < (int)def->SkillID.size(); i++)
		{
			const ItemSkillDef* skilldef = GetDefManagerProxy()->getItemSkillDef(def->SkillID[i]);
			if (skilldef && skilldef->SkillType == 2) //投掷物触发
			{
				return true;
			}
		}
	}
	return false;
}

void ClientActorProjectile::checkCreateActor(ClientActor* actor, const WCoord* block, int face)
{
	const ItemDef* def = GetDefManagerProxy()->getItemDef(m_ItemID);
	if (def == NULL) return;
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_pWorld->getActorMgr());
	if (!actorMgr) return ;
	ActorLiving* attacker = dynamic_cast<ActorLiving*>(actorMgr->findActorByWID(m_ShootingActorID));
	long long actorId = -1;
	if (attacker) {
		actorId = attacker->getObjId();
	}
	
	for (int i = 0; i < (int)def->SkillID.size(); i++)
	{
		const ItemSkillDef* skilldef = GetDefManagerProxy()->getItemSkillDef(def->SkillID[i]);
		if (skilldef)
		{
			bool haveCreateActor = false;
			WCoord block = getPosition();

			//触发所有的效果
			for (int j = 0; j < (int)skilldef->SkillFuncions.size(); j++)
			{
				ItemSkillDef::SkillFuncionsDef* functiondef = (ItemSkillDef::SkillFuncionsDef*)&skilldef->SkillFuncions[j];
				if (functiondef->oper_id == 19 && functiondef->func.createActorFun.num > 0 && functiondef->func.createActorFun.actorName != "")
				{
					if (haveCreateActor)
					{
						setNeedClearEx();
					}
					haveCreateActor = true;
					
					MINIW::ScriptVM::game()->callFunction("ProjectileCreateActor", "siiiii", functiondef->func.createActorFun.actorName, functiondef->func.createActorFun.num, block.x, block.y, block.z, actorId);
						
				}
			}
		}
	}
}

void ClientActorProjectile::checkCallMob()
{
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_pWorld->getActorMgr());
	if (!actorMgr) return;
	ActorLiving* attacker = dynamic_cast<ActorLiving*>(actorMgr->findActorByWID(m_ShootingActorID));
	const ItemDef* def = GetDefManagerProxy()->getItemDef(m_ItemID);
	if (def == NULL) return;

	for (int i = 0; i < (int)def->SkillID.size(); i++)
	{
		const ItemSkillDef* skilldef = GetDefManagerProxy()->getItemSkillDef(def->SkillID[i]);
		if (skilldef)
		{
			//召唤怪物
			bool haveCallMob = false;
			for (int j = 0; j < (int)skilldef->SkillFuncions.size(); j++)
			{
				ItemSkillDef::SkillFuncionsDef* functiondef = (ItemSkillDef::SkillFuncionsDef*)&skilldef->SkillFuncions[j];
				if (functiondef->oper_id == 5)
				{
					haveCallMob = true;
				}
			}
			if (haveCallMob)
			{
				WCoord block = CoordDivBlock(getPosition());
				std::vector<WCoord> blockpos = doPickBlockByItemSkill(def->SkillID[i], &block, DIR_POS_Y);
				std::vector<WCoord> blockposMob;
				for (int j = 0; j < (int)blockpos.size(); j++)
				{
					if (getWorld()->getBlockID(blockpos[j].x, blockpos[j].y, blockpos[j].z) == 0)
					{
						blockposMob.push_back(blockpos[j]);
					}
				}
				//触发所有的效果
				for (int j = 0; j < (int)skilldef->SkillFuncions.size(); j++)
				{
					ItemSkillDef::SkillFuncionsDef* functiondef = (ItemSkillDef::SkillFuncionsDef*)&skilldef->SkillFuncions[j];
					if (functiondef->oper_id == 5 && functiondef->func.summonfun.CallNum > 0) //召唤
					{
						setNeedClearEx();
						int countmob = blockposMob.size() / functiondef->func.summonfun.CallNum + 1;
						int leftMob = functiondef->func.summonfun.CallNum;
						for (int t = 0; t < (int)blockposMob.size(); t = t + countmob)
						{
							ClientMob* newmob = ClientMob::createFromDef(functiondef->func.summonfun.MobID);
							if (newmob) {
								if (attacker)
									newmob->setTamedOwnerUin((int)attacker->getObjId());

								newmob->getLocoMotion()->gotoPosition(getLocoMotion()->m_Position);
								WCoord pos = blockposMob[t] * BLOCK_SIZE;
								actorMgr->spawnActor(newmob, pos, GenRandomFloat() * 360.0f, 0);

								if (attacker)
									newmob->setTeam(attacker->getTeam());

								if (functiondef->func.summonfun.IsFollow)
								{
									if (!newmob->IsUseAILua())
									{
										newmob->addAiTask<AIFollowOwner>(4, 2.0, 1000, 200, 0);
										newmob->addAiTaskTarget<AITargetOwnerHurtee>(5);
										newmob->addAiTaskTarget<AITargetOwnerHurter>(6);
									}
								}
								else
									newmob->setTamedOwnerUin(0);


								if (functiondef->func.summonfun.Duration > 0)
									newmob->setDieTick(functiondef->func.summonfun.Duration * 20);
								leftMob--;
							}
						}

						for (int t = 0; t < leftMob; t++)
						{
							ClientMob* newmob = ClientMob::createFromDef(functiondef->func.summonfun.MobID);
							if (newmob) {
								if (attacker)
									newmob->setTamedOwnerUin((int)(attacker->getObjId()));

								newmob->getLocoMotion()->gotoPosition(getLocoMotion()->m_Position);
								WCoord pos = getLocoMotion()->m_Position;
								actorMgr->spawnActor(newmob, pos, GenRandomFloat() * 360.0f, 0);

								if (attacker)
									newmob->setTeam(attacker->getTeam());

								if (functiondef->func.summonfun.IsFollow)
								{
									if (!newmob->IsUseAILua())
									{
										newmob->addAiTask<AIFollowOwner>(4, 2.0, 1000, 200, 0);
										newmob->addAiTaskTarget<AITargetOwnerHurtee>(5);
										newmob->addAiTaskTarget<AITargetOwnerHurter>(6);
									}
								}
								else
									newmob->setTamedOwnerUin(0);


								if (functiondef->func.summonfun.Duration > 0)
									newmob->setDieTick(functiondef->func.summonfun.Duration * 20);
							}
						}
					}
				}
			}
		}
	}
}
void ClientActorProjectile::checkUseItemSkillActor(ClientActor* actor)
{
	if (needClear())
	{
		return;
	}
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_pWorld->getActorMgr());
	if (!actorMgr) return;
	const ItemDef* def = GetDefManagerProxy()->getItemDef(m_ItemID);
	if (def == NULL) return;
	for (int i = 0; i < (int)def->SkillID.size(); i++)
	{
		const ItemSkillDef* skilldef = GetDefManagerProxy()->getItemSkillDef(def->SkillID[i]);
		if (skilldef)
		{
			ActorLiving* attacker = dynamic_cast<ActorLiving*>(actorMgr->findActorByWID(m_ShootingActorID));
			//触发所有生物效果
			m_bAttackFly = false;
			WCoord centerPos;
			std::vector<ClientActor*> obj = doPickActorByItemSkill(actor, def->SkillID[i], centerPos);

			/*bool hasPunctureSkill = false;
			for (int t = 0; t < (int)skilldef->SkillFuncions.size(); t++)
			{
				ItemSkillDef::SkillFuncionsDef* functiondef = (ItemSkillDef::SkillFuncionsDef*)&skilldef->SkillFuncions[t];
				if (functiondef->oper_id == 21)
				{
					hasPunctureSkill = true;
				}
			}*/
			if (!isBoomerangItem(m_ItemID) && !canPassActor())
			{
				setNeedClearEx();
			}
			else
			{
				m_isCheckFinally = false;
			}
			for (int j = 0; j < (int)obj.size(); j++)
			{
				//触发所有的效果
				ClientActor* actornew = obj[j];
				ActorLiving* live = dynamic_cast<ActorLiving*>(actornew);
				if (!live)
					continue;
				if (isBoomerangItem(m_ItemID) && actornew->getObjId() == m_ShootingActorID)//回旋镖不对自己生效
					continue;
				//目标身上加特效
				if (skilldef->EffectTarget.size() && actornew->getBody())
				{
					auto effectComponent = actornew->getEffectComponent();
					if (effectComponent)
					{
						effectComponent->playBodyEffect((char*)skilldef->EffectTarget.c_str(), true, skilldef->EffectTargetTime);
						effectComponent->setBodyEffectScale((char*)skilldef->EffectTarget.c_str(), skilldef->EffectTargetSize);
					}
				}
				//播放音效
				if (skilldef->EffectTargetSound.size() && actornew->getBody())
				{
					auto sound = actornew->getSoundComponent();
					if (sound)
					{
						sound->playSound(skilldef->EffectTargetSound.c_str(), 1.0f, 1.0f);
					}
				}
				for (int t = 0; t < (int)skilldef->SkillFuncions.size(); t++)
				{
					ItemSkillDef::SkillFuncionsDef* functiondef = (ItemSkillDef::SkillFuncionsDef*)&skilldef->SkillFuncions[t];
					if (functiondef->oper_id == 2)	//攻击
					{
						if (attacker && functiondef->func.attackfun.AttackVal > 0)
						{
							// 是否有削韧技能
							int touReduce = -1;
							for (int k = 0; k < (int)skilldef->SkillFuncions.size(); k++)
							{
								ItemSkillDef::SkillFuncionsDef* functiondef = (ItemSkillDef::SkillFuncionsDef*)&skilldef->SkillFuncions[k];
								// 削韧
								if (functiondef->oper_id == 22)
									touReduce = functiondef->func.toughnessReduceFun.touReduce;

							}
							attacker->doActualItemSkillAttack(actornew, (ATTACK_TYPE)functiondef->func.attackfun.AttackType, (int)functiondef->func.attackfun.AttackVal, touReduce);
						}
						else
							actornew->getAttrib()->addHP((float)-functiondef->func.attackfun.AttackVal);
					}
					else if (functiondef->oper_id == 3)	//加buff
					{
						if (live && (functiondef->func.bufffun.Odds == 0 || GenRandomInt(100) < functiondef->func.bufffun.Odds))
						{
							live->getLivingAttrib()->addBuff(functiondef->func.bufffun.BuffID / 1000, functiondef->func.bufffun.BuffID % 1000, (int)functiondef->func.bufffun.Duration * 20, 0, m_ShootingActorID);
						}
					}
					else if (functiondef->oper_id == 4) //击飞击退
					{
						if (skilldef->RangeType == 0 || skilldef->RangeType == 1)
						{
							WCoord originpos(getPosition().x, getPosition().y, getPosition().z);
							WCoord hitcenter(actornew->getPosition().x, actornew->getPosition().y, actornew->getPosition().z);
							Vector3f dp = (hitcenter - originpos).toVector3();
							if (skilldef->SkillType == 1 || skilldef->RangeType == 1)
							{
								PitchYaw2Direction(dp, getLocoMotion()->m_RotateYaw, 0);
							} 
							float rotateYaw;
							float rotationPitch;
							Direction2PitchYaw(&rotateYaw, &rotationPitch, dp);
							Vector3f dir = Yaw2FowardDir(rotateYaw);
							//Rainbow::Vector3f dir = Yaw2FowardDir(getLocoMotion()->m_RotateYaw);
							Vector3f addmotion(dir.x * functiondef->func.driftfun.RepelPower, functiondef->func.driftfun.BlowUpPower, dir.z * functiondef->func.driftfun.RepelPower);
							actornew->setMotionChange(addmotion, true);
							m_bAttackFly = true;
						}
						else
						{
							WCoord originpos(centerPos.x, centerPos.y, centerPos.z);
							WCoord hitcenter(actornew->getPosition().x, actornew->getPosition().y, actornew->getPosition().z);
							Vector3f dp = (hitcenter - originpos).toVector3();
							if (actornew == actor)
							{
								PitchYaw2Direction(dp, getLocoMotion()->m_RotateYaw, 0);
								m_bAttackFly = true;
								if (functiondef->func.driftfun.RepelPower < 0)
								{
									continue;
								}
							}
							float rotateYaw;
							float rotationPitch;
							Direction2PitchYaw(&rotateYaw, &rotationPitch, dp);
							Vector3f dir = Yaw2FowardDir(rotateYaw);
							//Rainbow::Vector3f dir = Yaw2FowardDir(getLocoMotion()->m_RotateYaw);
							Vector3f addmotion(dir.x * functiondef->func.driftfun.RepelPower, functiondef->func.driftfun.BlowUpPower, dir.z * functiondef->func.driftfun.RepelPower);
							actornew->setMotionChange(addmotion, true);
						}
					}
					else if (functiondef->oper_id == 6) //治疗
					{
						if (functiondef->func.curefun.TreatType == 0)//生命值
						{
							actornew->getAttrib()->addHP((float)functiondef->func.curefun.TreatVal);
						}
						else if (functiondef->func.curefun.TreatType == 1)  //饥饿值
						{
							ClientPlayer* player = dynamic_cast<ClientPlayer*>(actornew);
							if (player)
							{
								player->getPlayerAttrib()->m_FoodLevel += functiondef->func.curefun.TreatVal;
							}
						}
						else if (functiondef->func.curefun.TreatType == 2)  //饥饿耐力
						{
							ClientPlayer* player = dynamic_cast<ClientPlayer*>(actornew);
							if (player)
							{
								player->getPlayerAttrib()->m_FoodSatLevel += functiondef->func.curefun.TreatVal;
							}
						}
						else if (functiondef->func.curefun.TreatType == 3)  //氧气值
						{
							//ActorLiving *live = dynamic_cast<ActorLiving *>(actornew);
							//if(live)
							//{
							live->getLivingAttrib()->addOxygen((float)functiondef->func.curefun.TreatVal);
							//}
						}
						else if (functiondef->func.curefun.TreatType == 4)  //体力值
						{
							ClientPlayer* player = dynamic_cast<ClientPlayer*>(actornew);
							if (player)
							{
								player->getPlayerAttrib()->addStrength((float)functiondef->func.curefun.TreatVal);
							}
						}
					}
					else if (functiondef->oper_id == 9)	//把生物弄成宠物蛋效果
					{
						ClientMob* mob = dynamic_cast<ClientMob*>(actor);
						if (mob)
						{
							int EggID = 0;
							MINIW::ScriptVM::game()->callFunction("MonsterToEgg", "i>i", mob->getDef()->ID, &EggID);

							bool isTeamdBySame = true; //若是驯服的生物需是同一主人才能变蛋 code-by:lizb
							if (mob->getTamedOwnerID() > 0)
							{
								isTeamdBySame = mob->getTamedOwnerID() == m_ShootingActorID;
							}
							if (EggID != 0 && isTeamdBySame) //
							{
								mob->dropEgg(EggID, 1);
								//actor->dropItem(EggID, 1);
								actor->setNeedClear();
							}
						}
					}
					else if (functiondef->oper_id == 14) //给攻击者上buff
					{
						if (attacker && (functiondef->func.selfbufffun.Odds == 0 || GenRandomInt(100) < functiondef->func.selfbufffun.Odds))
						{
							attacker->getLivingAttrib()->addBuff(functiondef->func.selfbufffun.BuffID / 1000, functiondef->func.selfbufffun.BuffID % 1000, (int)functiondef->func.selfbufffun.Duration * 20, 0, m_ShootingActorID);
						}
					}
					else if (functiondef->oper_id == 15) //会出现落雷
					{
						if (actornew)
						{
							m_pWorld->m_Environ->createLightning(actornew->getPosition());
							if (m_pWorld && !m_pWorld->isRemoteMode())
							{
								m_pWorld->m_Environ->notifyLightning2Tracking(actornew->getPosition(), actor);
							}
						}
					} else if (functiondef->oper_id == 17) { // 清除某类型状态
                        std::vector<int> buffIdVect = GetDefManagerProxy()->GetBuffByNature(functiondef->func.clearBuffNatureFun.buffNature);
                        for (size_t m = 0; m < buffIdVect.size(); m++) {
                            live->getLivingAttrib()->removeBuff(buffIdVect[m] / 1000);
                        }
                    } else if (functiondef->oper_id == 18) { // 仅给某种生物加状态
                        int* mobIds = functiondef->func.monterBuffFun.monterIDs; 
                        int realBuffId = functiondef->func.monterBuffFun.buffID;
                        int buffId = realBuffId / 1000;
                        int buffLevel = realBuffId % 1000;
                        short odds = functiondef->func.monterBuffFun.odds;
                        int r = GenRandomInt(1, 100);
                        if (r <= odds) {
							ClientPlayer* player = dynamic_cast<ClientPlayer*>(actornew);
							ClientMob* mob = dynamic_cast<ClientMob*>(actornew);
							for (int i = 0; i < MAX_MONSTER_NUM; i++) {
								if (mobIds[i] == -1) {
									continue;
								}
								if (mobIds[i] == 0) {
									if (player != nullptr) {
										player->getLivingAttrib()->addBuff(buffId, buffLevel);
									}
								} else {
									if (mob != nullptr && mob->getDefID() == mobIds[i]) {
										mob->getLivingAttrib()->addBuff(buffId, buffLevel);
									}
								}
							}
                        }
                    }
					else if (functiondef->oper_id == 24 && actor && m_pWorld)
					{
						WCoord actorDownPos = DownCoord(CoordDivBlock(actor->getPosition()));
						int blockID = m_pWorld->getBlockID(actorDownPos);
						if (blockID > 0)
						{
							DoUseItemSkillBlock(&actorDownPos, 5);
						}
					}
                }
            }
        }
    }
}
void ClientActorProjectile::playEffect()
{
	bool explosion = false;
	int damageType = m_ProjectileDef->DamageType < 4 ? 0 : m_ProjectileDef->DamageType - 3;
	//爆炸效果
	if (hasExplodeEffect())
	{
		if (!m_pWorld->isRemoteMode())
		{
			//Add pos delta
			m_pWorld->createExplosion(this, WCoord(getLocoMotion()->getFramePosition()) + WCoord(0, 10, 0), 2, false, true, 0, damageType);
			explosion = true;
			setNeedClearEx();
		}
	}
	else if (m_ProjectileDef->AttackType == 1 || m_ProjectileDef->AttackType == 2)
	{
		if (!m_pWorld->isRemoteMode())
		{
			bool newBomb = m_ProjectileDef->AttackType == 2;
			if (newBomb)
			{
				m_pWorld->createExplosionNew(this, getPosition() + WCoord(0, 10, 0), (int)m_ProjectileDef->ExplodeRange, false, m_ProjectileDef->AttackValue, m_ProjectileDef->BombBlock, 0);
			}
			else
			{
				m_pWorld->createExplosion(this, WCoord(getLocoMotion()->getFramePosition()) + WCoord(0, 10, 0), (int)m_ProjectileDef->AttackValue, false, true, 0, damageType);
			}
			explosion = true;
			setNeedClearEx();
		}
	}
	if (explosion)
	{
		jsonxx::Object log;
		log << "itemid" << m_ItemID;
		log << "from" << "ClientActorProjectile";
		GetICloudProxyPtr()->InfoLog(m_ShootingActorID, 0, "create_explosion", log);
	}
}

void ClientActorProjectile::onImpactWithActor(ClientActor *actor, const std::string& partname)
{
	m_strEffectName = "";
	ActorLiving* pLive = dynamic_cast<ActorLiving*>(actor);
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_pWorld->getActorMgr());
	if (!actorMgr) return ;
	ClientActor *attacker = actorMgr->findActorByWID(m_ShootingActorID);

	//设置被攻击的actor
	setBeShootedActor(actor);

	//带物理的投掷物不触发
	const PhysicsActorDef* physicsActorDef = GetDefManagerProxy()->getPhysicsActorDef(actor->GetItemId());
	bool hasPhysx = false;
	if (physicsActorDef)
	{
		if (physicsActorDef->EditType == PhysicsLocoMotion::PHYS_TYPE_NOEDIT_YESPHY || physicsActorDef->EditType == PhysicsLocoMotion::PHYS_TYPE_YESEDIT_YESPHY)
			hasPhysx = true;
		else
			hasPhysx = false;
	}

	bool canImpact = true; //生物新增是否可变蛋字段 code-by:lizb
	if (m_ProjectileDef->ID == ITEM_GUNTOEGG_BULLET)
	{
		MonsterDef *monsterDef = GetDefManagerProxy()->getMonsterDef(actor->getDefID());
		if (monsterDef != NULL && !monsterDef->CanToEgg) canImpact = false;
	}

	//道具技能释放
	if (canImpact)
	{
		checkUseItemSkillActor(actor);
		checkCallMob();
		checkCreateActor(actor, NULL, -1);
	}
	
	//soc 组队也要有伤害
	//if (attacker!= NULL)
	//{
	//	ActorLiving* pAttackLive = dynamic_cast<ActorLiving*>(attacker);
	//	if (pLive && pLive->getTeam() != 0 && pAttackLive&& pAttackLive->getTeam() != 0 && pLive->getTeam() == pAttackLive->getTeam())
	//	{
	//		return;
	//	}
	//}
	//const ToolDef* toolDef = GetDefManagerProxy()->getToolDef(m_ItemID);

	if (m_Durable > 0 && !m_isUseCustomAtkDuration)
	{ 
		const ToolDef* tool = GetDefManagerProxy()->getToolDef(m_ItemID);
		if (tool) {
			m_Durable -= 2 * tool->AtkDuration;
		}
		//LOG_INFO("m_Durable %d", m_Durable);
	}

	stopMotion();

	bool bNeedClear = hasPhysx;
	auto itemDef = GetDefManagerProxy()->getItemDef(m_ProjectileDef->ID);
	ActorVehicleAssemble* pVehicle = dynamic_cast<ActorVehicleAssemble*>(actor);
	if (pVehicle) {
		PhysicsLocoMotion* pLoco = dynamic_cast<PhysicsLocoMotion*>(pVehicle->getLocoMotion());
		bNeedClear = pLoco ? pLoco->m_hasPhysActor : false;
	}

	ActorBall* pBall = dynamic_cast<ActorBall*>(actor);
	if (pBall) {
		bNeedClear = pBall->isPhysics();
	}

	if (itemDef && bNeedClear) {
		for (int i = 0; i < (int)itemDef->SkillID.size(); i++) {
			const ItemSkillDef *skilldef = GetDefManagerProxy()->getItemSkillDef(itemDef->SkillID[i]);
			for (int j = 0; j < (int)skilldef->SkillFuncions.size(); j++) {
				if (skilldef->SkillFuncions[j].oper_id == 13) {//物理清除
					actor->setNeedClear();
					break;
				}
			}
		}
	}

	if (hasPhysx && m_ProjectileDef->TriggerDelay == 0 && 0 == m_ProjectileDef->Pickable && (m_ProjectileDef->TriggerCondition == 1 || m_ProjectileDef->TriggerCondition == 3))
	{
		doTrigger();
	}

	unsigned int monsterId = 0;
	ClientMob* pMob = dynamic_cast<ClientMob*>(actor);
	if (pMob != nullptr && pMob->getDef() != nullptr) {
		monsterId = pMob->getDef()->ID;
	}

	//接触actor触发或者都可触发
	if (!hasPhysx && !m_HasImpackActor && (m_ProjectileDef->TriggerCondition == 1 || m_ProjectileDef->TriggerCondition == 3) || canPassActor())
	{
		m_HasImpackActor = true;
		m_passActorNum++;
		if (m_ProjectileDef->TriggerDelay == 0)
		{
			doTrigger();
			if (!m_pWorld->isRemoteMode())
			{
				if (IsMaoItem(m_ItemID) || m_ProjectileDef->Pickable == 1)
				{
					if (m_Durable <= 0)
					{
						float pitch = 1.2f / (GenRandomFloat()*0.2f + 0.9f);
						float volume = 1.0f;
						auto sound = getSoundComponent();
						if (sound)
						{
							sound->playSound("misc.break", volume, pitch);
						}
						setNeedClearEx();
					}
				}
				else if (isBoomerangItem(m_ItemID)) //回旋镖
				{
					if (m_Durable <= 0)
					{
						float pitch = 1.2f / (GenRandomFloat()*0.2f + 0.9f);
						float volume = 1.0f;
						auto sound = getSoundComponent();
						if (sound)
						{
							sound->playSound("misc.break", volume, pitch);
						}
						setNeedClearEx();
					}
				}
				else if (m_ItemID == BLOCK_FAR_DRIFTBOTTLE || m_ItemID == BLOCK_DRIFTBOTTLE)
				{
					{
						ClientActor* shooter = getShootingActor();
						ClientPlayer* player = dynamic_cast<ClientPlayer*>(shooter);
						if (player)
						{
							MINIW::ScriptVM::game()->callFunction("DriftBottleUseReport", "isi", player->getUin(), m_UserDataStr.c_str(), 2);
						}
						driftGetLetterMessage(actor->getPosition(), 0, m_UserDataStr, m_pWorld, m_ItemID);
					}
					setNeedClearEx();
				}
				else
				{
					
					// 羊和惨叫鸡染色
					if (pMob && (monsterId == 3403	|| monsterId == 3424)
						&& IsColorableItem(m_ItemID))
					{
						pMob->setColor(m_Color);
					}

					if (m_AttackPoints == 0 && pMob && monsterId == 3424)
					{
						pMob->playHurtSound();
					}

					// 如果带有穿刺技能，可以穿过actor（会被防御状态的actor挡住）
					ActorLiving* actorliving = dynamic_cast<ActorLiving*>(actor);
					if (!canPassActor() || (actorliving && actorliving->IsInDefanceState()))
					{
						setNeedClearEx();
						//中箭返还-记录中箭次数 投掷物一开始属于玩家
						if (IsArrowTypeItem(m_ItemID) && attacker && attacker->getObjType() == OBJ_TYPE_ROLE)
						{
							if (actorliving != NULL && actorliving->getLivingAttrib() != NULL)
							{
								actorliving->getLivingAttrib()->addArrowById(m_ItemID);
							}
						}
					}
					else
					{
						m_HasImpackActor = false;
					}

				}
			}
		}
		else
		{
			if (needClear()) //销毁直接调用效果
			{
				doTrigger();
			}
			//has not trigger
			if (m_ImpactTimeMark < 0)
				m_ImpactTimeMark = 0;
		}

		ClientActor *attacker = actorMgr->findActorByWID(m_ShootingActorID);
		ClientMob *mob = dynamic_cast<ClientMob*>(attacker);

		if (attacker == NULL) attacker = this;

		if (isInFire() && !(isBoomerangItem(m_ItemID) && m_ShootingActorID == actor->getObjId()))
		{
			auto FireBurnComp = actor->sureFireBurnComponent();
			if (FireBurnComp)
			{
				FireBurnComp->setFire(33, getFireLv());
			}
		}
		if ((m_ItemID == ITEM_HARPOON || m_ItemID == ITEM_OCEANARROW || m_ItemID == ITEM_IMPULSE) && m_ShootingActorID != actor->getObjId()) {
			int bleedlv = 0;
			bool showbleed = false;
			ActorLiving* live = dynamic_cast<ActorLiving*>(actor);
			if (live)
			{
				int runenum = m_Runedata.getRuneNum();
				for (int i = 0; i < runenum; i++) {
					GridRuneItemData runedata = m_Runedata.getItemByIndex(i);
					if (runedata.getRuneVal0() == 1020) {
						showbleed = true;
						bleedlv = round(runedata.getRuneVal1());
					}
				}
				if (showbleed) {
					live->getLivingAttrib()->addBuff(1020, bleedlv, -1, 0, actor->getObjId());
				}
			}
		}

		bool damaged = false;

		OneAttackData atkdata;
		//memset(&atkdata, 0, sizeof(atkdata));
		atkdata.damage_armor = true;
		atkdata.ignore_resist = true;
		float atkpoints = m_AttackPoints;
		bool isOpenNewHpdecCalculate = GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate;

		const MonsterDef *monsterDef = NULL;
		if (mob)
		{
			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("ModManager_tryGetMonsterDef", SandboxContext(NULL).SetData_Number("monsterid", mob->m_Def->ID));
			if (result.IsSuccessed())
			{
				monsterDef = result.GetData_Usertype<MonsterDef>("monsterDef");
			}
		}
		//新年福袋buff
		if (m_ItemID == 12058)
		{
			atkdata.buffId = GenRandomInt(BECOME_CHIKEN_BUFF, 1005);
			atkdata.buffLevel = 1;
		}
		else if (m_BuffId != 0)
		{
			atkdata.buffId = m_BuffId / 1000;
			atkdata.buffLevel = m_BuffId % 1000;
		}
		else if (mob && (mob->m_Def->BuffId != 0 || monsterDef != nullptr))
		{
			atkdata.buffId = mob->m_Def->BuffId / 1000;
			atkdata.buffLevel = mob->m_Def->BuffId % 1000;
		}
		else
		{
			atkdata.buffId = m_ProjectileDef->BuffId / 1000;
			atkdata.buffLevel = m_ProjectileDef->BuffLevel % 1000;
		}

		if (GetWorldManagerPtr()->m_RuleMgr)
		{
			GameRuleMod *rulemod = GetWorldManagerPtr()->m_RuleMgr->findGameRuleMod(RULEMOD_THROWABLE_HURT);
			if (rulemod && rulemod->modvars[0] == m_ItemID)
			{
				atkpoints = rulemod->modvars[1] + m_BaseAtk;
			}
			else
			{
				atkpoints = m_AttackPoints;
				atkdata.knockback = m_KnockbackStrength;
			}
		}
		else
		{
			atkpoints = m_AttackPoints;
			atkdata.knockback = m_KnockbackStrength;
		}
		

		ClientPlayer *playerattacker = dynamic_cast<ClientPlayer *>(attacker);
		if (m_ProjectileDef->ID == 15075)
		{
			ClientPlayer* player = dynamic_cast<ClientPlayer*>(actor);
			if (player && player->hasUIControl())
			{
				MINIW::ScriptVM::game()->callFunction("CppOpenUI", "s", "spittingShelter");
			}
			else if (player && player->getWorld())
			{
				PB_PlayerOpenUIHC msg;
				msg.set_uiname("spittingShelter");
				msg.set_uiparam("");
				GameNetManager::getInstance()->sendToClient(player->getUin(), PB_PLAYER_OPENUI_HC, msg);
			}
		}

		//头部暴击
		if(playerattacker && actor->getObjType() != OBJ_TYPE_VEHICLE && actor->getObjType() != OBJ_TYPE_GAMEOBJECT)
		{
			// if (getPosition().y - actor->getPosition().y > 0.65f * box.dim.y)
			auto bodytype = MNSandbox::getAttackBodyType(partname);
			atkdata.parttype = bodytype;
			if (bodytype == ATTACK_BODY_HEAD)
			{
				if (isOpenNewHpdecCalculate)
				{
					atkdata.critical = true;
				}
				else
				{
					atkpoints += 0.5f * m_AttackPoints;
				}
				atkdata.isAttackHead = true;
				if (playerattacker->canShowShotTip())
				{
					if (isBoomerangItem(GetItemId()))
					{
						if (actor->getObjId() != m_ShootingActorID)
						{
							g_pPlayerCtrl->triggerHeadshotTip();
						}
					}
					else
						g_pPlayerCtrl->triggerHeadshotTip();
				}
				else
				{
					auto effectComponent = playerattacker->getEffectComponent();
					if (effectComponent)
					{
						effectComponent->playBodyEffect(HUDFX_HEADSHOT);
					}
				}
				//LOG_INFO("Headshot %f", atkdata.atkpoints);
			}
			else {
				if (playerattacker->canShowShotTip())
				{
					if (isBoomerangItem(GetItemId()))
					{
						if (actor->getObjId() != m_ShootingActorID)
						{
							g_pPlayerCtrl->triggerNormalshotTip();
						}
					}
					else
						g_pPlayerCtrl->triggerNormalshotTip();
				}
				else
				{
					auto effectComponent = playerattacker->getEffectComponent();
					if (effectComponent)
					{
						effectComponent->playBodyEffect(HUDFX_NORMALSHOT);
					}
				}
			}
			atkdata.fromplayer = playerattacker;
		}		

		bool canAttack = true;
		if (playerattacker && !playerattacker->canHurtActor(actor))
		{
			canAttack = false;
			atkpoints = 0.f;
		}
			
		// 新伤害计算系统 code-by:liya
		if (isOpenNewHpdecCalculate)
		{
			if (canAttack)
			{
				atkdata.atkTypeNew = m_atkType;
				memcpy(atkdata.atkPointsNew, m_AttackPointsNew, sizeof(m_AttackPointsNew));
				memcpy(atkdata.explodePoints, m_ExplodePoints, sizeof(m_ExplodePoints));
				atkdata.charge = m_strength;
				//atkdata.damping = 1.0f;
			}
			else
			{
				atkdata.atkTypeNew = m_atkType;
				atkdata.charge = m_strength;
				//atkdata.damping = 1.0f;
			}
		}
		else
		{
			atkdata.atkpoints = atkpoints;
		}

		if (m_atkType & ATTACK_EXPLODE > 0)
			atkdata.atktype = ATTACK_EXPLODE;
		else
			atkdata.atktype = ATTACK_RANGE;

		if (!actor->getCanCollide())//是否允许Actor碰撞产生位移
		{
			atkdata.knockback = 0;
		}
		if (/*atkpoints != 0 || */atkdata.buffId != 0 || canAttack)
		{
			if (this->getMasterObjId() == actor->getObjId() || (actor->getObjId() == m_ShootingActorID && isBoomerangItem(GetItemId())))//发射出来的不能打中自己
			{
				atkdata.knockback = atkdata.knockup = 0;
			}
			else
			{
				auto component = actor->getAttackedComponent();
				if (component)
				{
					damaged = component->attackedFrom(atkdata, this);
				}
			}
			
		}
		//
		// if ((monsterId != 3416 && monsterId != 3417) && !m_bAttackFly) {
		// 	ActorKnockbackByMotion(actor, getLocoMotion()->m_Motion, atkdata.knockback);
		// }

		applyRuneData(actor, damaged);

		if (monsterId == 3626 && (m_ItemID == 11662 || m_ItemID == 11651)) //击中水母
		{
			int data = pMob->getData();
			if (data < 4) {
				pMob->playAnim(SEQ_JELLYFISH_FLOOD_GAS);
				pMob->setData(++data);
			}
		}
		#ifdef IWORLD_SERVER_BUILD
		GetICloudProxyPtr()->OnBulletHurt(playerattacker, actor, atkdata.atkpoints, getObjId());
		#endif
	}
	if (m_ItemID == ITEM_EXTREME_COLD && actor != NULL && attacker != NULL)
	{
		auto pos = getPosition();
		auto actorpos = actor->getPosition();
		auto blockpos = CoordDivBlock(actorpos);
		pos.y = blockpos.y * BLOCK_SIZE;
		CollideAABB box;
		actor->getHitCollideBox(box);
		int addHeight = box.dim.y;
		m_pWorld->getEffectMgr()->GetGameEffectMgr()->playEffectIceCircle(attacker, pos, 100, addHeight, DIR_POS_Y, m_AttachedEffect);
		setNeedClear();
	}
	if (m_ItemID == ITEM_ICE_BOTTLE && actor != NULL && attacker != NULL)
	{
		auto pos = getPosition();
		auto actorpos = actor->getPosition();
		auto blockpos = CoordDivBlock(actorpos);
		pos.y = blockpos.y * BLOCK_SIZE;
		m_pWorld->getEffectMgr()->GetGameEffectMgr()->playEffectIceBottle(attacker, pos, 100);
		setNeedClear();
	}
	if (m_ItemID == BLOCK_VOID_FRUIT || m_ItemID == ITEM_VOID_SUPER_FRUIT)
	{
		auto pos = getPosition();
		auto actorpos = actor->getPosition();
		auto blockpos = CoordDivBlock(actorpos);
		int mobid = m_ItemID == BLOCK_VOID_FRUIT ? 3242 : 3243;
		actorMgr->spawnMob(BlockCenterCoord(blockpos), mobid, false, false);
		setNeedClear();
	}

	if (actor && !m_pWorld->isRemoteMode()) {
		Rainbow::Vector3f objpos = getPosition().toVector3() * 0.01f;
		ObserverEvent_ActorItemPos obevent(getObjId(), actor->getObjId(), GetItemId(), objpos.x, objpos.y, objpos.z);
		if (actor->IsTriggerCreature())
		{
			obevent.SetData_TargetActorID(actor->getDefID());
		}
		obevent.SetData_HelperObjid(m_ShootingActorID);
		if (m_ShootingActorID > 0)
		{
			ClientMob* attacker = actorMgr->findMobByWID(m_ShootingActorID);
			if (attacker)
			{
				obevent.SetData_Actor(attacker->getDefID());
			}
		}
		GetObserverEventManager().OnTriggerEvent("Actor.Projectile.Hit", &obevent);
	}

	if (m_skillTriggerType == PROJECTILE_SKILL_IMPACT_WITH_ACTOR_OR_BLOCK) //和角色碰撞时
	{
		activeSkill(m_skillName.c_str());
	}
}

void ClientActorProjectile::onCollideWithPlayer(ClientActor *player)
{
	if (player == nullptr) { return; }
	auto pTempPlayer = dynamic_cast<ClientPlayer*>(player);
	if (nullptr != pTempPlayer)
	{
		ProjectileLocoMotion* loc = static_cast<ProjectileLocoMotion*>(getLocoMotion());

		SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canUseItem",
			SandboxContext(nullptr).SetData_Number("uin", pTempPlayer->getUin()).SetData_Number("itemid", m_ItemID));
		bool canUseItemFlag = false;
		if (result.IsExecSuccessed())
		{
			canUseItemFlag = result.GetData_Bool();
		}
		if (canUseItemFlag)
		{
			if (loc->m_InGround && loc->m_ShakeTick == 0 || (pTempPlayer->getObjId() == m_ShootingActorID && isBoomerangItem(m_ItemID) && m_BoomerangState > 1))
			{
				if (!pTempPlayer->checkActionAttrState(ENABLE_PICKUP))
				{
					pTempPlayer->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 14008);
					return;
				}

				bool pick = GetDefManagerProxy()->getProjectileDef(m_ItemID, true)->Pickable && canPickup();

				//射出的箭捡回就没有符文了
				if ((m_ItemID == 12062 || m_ItemID == 12051) && m_Runedata.getRuneNum() > 0) m_Runedata.clear();


				//if (pick && player->getBackPack()->addItem(m_ItemID, 1) == 0)
				GridCopyData gridcopydata(m_ItemID, 1, m_Durable, -1, m_EnchantNum, m_Enchants, 0, 0, NULL, "", &m_Runedata, m_MaxDurable);
				if (pick && pTempPlayer->getBackPack()->tryAddItem_byGridCopyData(gridcopydata) == 0)
				{
					pick = false;
				}

				if (pTempPlayer->getObjId() == m_ShootingActorID && GetWorldManagerPtr()->isGodMode() && isBoomerangItem(m_ItemID) && m_BoomerangState > 1)
				{
					//创造模式打到自己将回旋镖移除。
					if (pick)
						setNeedClearEx(2);
					return;
				}

				if (pick)
				{
					pTempPlayer->onPickupItem(this);
					if (isBoomerangItem(m_ItemID))
						setNeedClearEx(2);
					else
						setNeedClearEx(10);

					ObserverEvent_ActorItem obevent(pTempPlayer->getObjId(), m_ItemID, 1, (long long)getObjId());
					WCoord pos = this->getPosition();
					pos = CoordDivBlock(pos);
					obevent.SetData_Position(pos.x, pos.y, pos.z);
					GetObserverEventManager().OnTriggerEvent("Item.Pickup", &obevent);
				}
			}
		}

//#ifndef USE_ACTOR_CONTROL
		if (loc->m_PhysActor)
		{
			int r = loc->m_BoundHeight / 2;
			int d2 = r + player->getLocoMotion()->m_BoundSize / 2;
			WCoord dp = getPosition() - player->getPosition();
			if (dp.y <= player->getEyeHeight() && dp.x * dp.x + dp.z * dp.z < d2 * d2)
			{
				Rainbow::Vector3f force = dp.toVector3();
				force.y = 0;
				force = MINIW::Normalize(force);

				float ratio = GetWorldManagerPtr()->m_SurviveGameConfig->physxconfig.force_collide;
				const PhysicsActorDef* physicsActorDef = GetDefManagerProxy()->getPhysicsActorDef(this->m_ItemID);
				if (physicsActorDef)
				{
					ratio /= physicsActorDef->Mass;
				}

				static_cast<RigidDynamicActor*>(loc->m_PhysActor)->AddForce(force * ratio * 100.0f);
				}
			}
//#endif
		}
}

bool ClientActorProjectile::canBeCollidedWith()
{
	ProjectileLocoMotion *loc = dynamic_cast<ProjectileLocoMotion *>(getLocoMotion());
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->m_SurviveGameConfig->physxconfig.enable && loc && loc->m_hasPhysActor)
	{
		return true;
	}
	else
	{
		return false;
	}
}


void ClientActorProjectile::doTrigger(std::string effectFileName)
{
	OPTICK_EVENT();
	ProjectileLocoMotion* loco = dynamic_cast<ProjectileLocoMotion*>(getLocoMotion());
	if (loco && !needClear())//没有释放过，投掷物还存在的情况 在触发一次
	{
		if (isProjectileSkill() && m_isCheckFinally)
		{
			checkUseItemSkillBlock(&loco->m_BlockPos, loco->m_BlockFace);
			checkUseItemSkillActor(nullptr);
		}
	}
	playEffect();
	//投掷物击中生物特效
	Rainbow::ColorQuad cq(255, 255, 255, 255);
	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit(
		"WeaponSkin_System_UseCustomHitEffect",
		SandboxContext(nullptr)
		.SetData_Number("playerUin", m_ShootingActorID)
		.SetData_Userdata("ClientActorProjectile", "projectile", this)
		.SetData_Userdata("World", "world", m_pWorld)
	);

	if (((!m_ProjectileDef->TriggerEffect.empty() || !effectFileName.empty()) && !result.IsExecSuccessed()))
	{
		Rainbow::ColorQuad cq(255, 255, 255, 255);
		char effectPath[128];
		if (effectFileName.empty())
		{
			sprintf(effectPath, "particles/%s.ent", m_ProjectileDef->TriggerEffect.c_str());
		}
		else
		{
			sprintf(effectPath, "particles/%s.ent", effectFileName.c_str());
		}
		
		// 彩蛋和彩蛋枪的子弹
		if (IsColorableItem(m_ItemID))
		{
			cq.c = m_Color;
		}

		//子弹打在载具上，播放特效
		bool shootingVehicle = false;
		if (getBeShootedActor())
		{
			ActorVehicleAssemble* pVehicle = dynamic_cast<ActorVehicleAssemble*>(getBeShootedActor());
			if (pVehicle)
			{
				shootingVehicle = true;
				int x = 0;
				int y = 0;
				int z = 0;
				bool res = pVehicle->intersect(this, x, y, z);
				if (res)
				{
					if (pVehicle->getVehicleWorld())
					{
						WCoord effectPos = pVehicle->convertWcoord(WCoord(x, y, z));
						effectPos.y += 100;
						auto eff = m_pWorld->getEffectMgr()->playParticleEffectAsync(effectPath, effectPos, 100, 0, 0, true, 0, 0, cq);
						if (eff) {
							m_pWorld->getEffectMgr()->SetPersistentUpdate(eff, true);
						}
						//播放完特效后，重置数据
						setBeShootedActor(NULL);
					}
				}

			}
		}

		//子弹打在非载具上，播放特效
		if (!shootingVehicle)
		{
			auto eff = m_pWorld->getEffectMgr()->playParticleEffectAsync(effectPath, getPosition(), 100, 0, 0, true, 0, 0, cq);
			if (eff) {
				m_pWorld->getEffectMgr()->SetPersistentUpdate(eff, true);
			}
		}
			

		//生成光照方块，ID1069
	/*	if (m_ProjectileDef->ID == BLOCK_STARBULLET)
		{

			WCoord blockPos = WCoord(getPosition().x / BLOCK_SIZE,getPosition().y / BLOCK_SIZE,getPosition().z / BLOCK_SIZE);
			useItemSkill(NULL,&blockPos,0);

		}*/
	}


	//投掷物击中生物音效
	SandboxResult result2 = SandboxEventDispatcherManager::GetGlobalInstance().Emit(
		"WeaponSkin_System_UseCustomHitSound",
		SandboxContext(nullptr)
		.SetData_Number("uin", m_ShootingActorID)
		.SetData_Userdata("ClientActorProjectile", "projectile", this)
	);
	
	//播放声音
	if (!result2.IsExecSuccessed())
	{
		float pitch = 1.2f / (GenRandomFloat()*0.2f + 0.9f);
		float volume = 1.0f;
		if (m_ProjectileDef->ID == 12836 || m_ProjectileDef->ID == 12837 || m_ProjectileDef->ID == 12838)
		{
			volume = 4.0f;
		}
		auto sound = getSoundComponent();
		if (sound)
		{
			sound->playSound(m_ProjectileDef->TriggerSound, volume, pitch);
		}
	}
	
	if (m_ProjectileDef->ID == BLOCK_STARBULLET)//投射物是星星弹
	{
		m_ImpactTimeMark =(m_ProjectileDef->TriggerDelay+0.2) * 20;
	}
	else
	{
		m_ImpactTimeMark = -1;
	}

}

int ClientActorProjectile::getDelayClearTicks()
{
	/*
	//子弹残留时间
	ClientPlayer* attacker = dynamic_cast<ClientPlayer*>(m_pWorld->getActorMgr()->findActorByWID(m_ShootingActorID));

	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("WeaponSkin_System_GetBulletClearDelayTicks",
		SandboxContext(nullptr)
		.SetData_Number("uin", m_ShootingActorID)
		.SetData_Number("itemid", attacker ? attacker->getCurToolID() : 0)
	);
	int delay_ticks = 0;
	if (result.IsExecSuccessed())
	{
		delay_ticks = result.GetData_Number("nDelayTicks");
	}
	*/

	//子弹残留时间
	if (m_pWorld == NULL) return 0;
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_pWorld->getActorMgr());
	if (!actorMgr) return 0;
	ClientActor* pActor = actorMgr->findActorByWID(m_ShootingActorID);
	ClientPlayer* pAttacker = dynamic_cast<ClientPlayer*>(pActor);

	int delay_ticks = 0;
	WeaponSkinMgr* weaponSkinMgr = GET_SUB_SYSTEM(WeaponSkinMgr);
	if (weaponSkinMgr != NULL)
	{
		const char* attrType = "WeaponSkin_System_attachment";
		float fdelay_time = weaponSkinMgr->GetSkinAdditionValue(attrType, pAttacker);
		int delay_ticks = fdelay_time * 20; //20tick/sec --修正秒到tick
	}

	return delay_ticks;
}


void ClientActorProjectile::onImpactWithBlock(const WCoord *blockpos, int face)
{
	m_strEffectName = "";
	Block block = m_pWorld->getBlock(*blockpos);
	int triggerblockid = block.getResID();

	///道具技能释放
	checkUseItemSkillBlock(blockpos, face);
	checkCallMob();
	checkCreateActor(NULL, blockpos, face);
	const ItemDef* itemdef = GetDefManagerProxy()->getItemDef(m_ItemID);

	if (itemdef && !itemdef->UseScript.empty()) {
		DirectionType tmpface = DIR_POS_Y;
		bool scripthandled = false;
		int setBlockAllRet = 0;
		ClientActor* shooter = getShootingActor();
		ClientPlayer* player = dynamic_cast<ClientPlayer*>(shooter);
		if (player)
		{
			MINIW::ScriptVM::game()->callFunction(itemdef->UseScript.c_str(), "u[ClientPlayer]u[World]iiii>bi", player, getWorld(), blockpos->x, blockpos->y, blockpos->z, tmpface, &scripthandled, &setBlockAllRet);
		}
	}

	stopMotion();
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_pWorld->getActorMgr());
	if (!actorMgr) return ;

	if (m_ProjectileDef->TriggerCondition == 2 || m_ProjectileDef->TriggerCondition == 3)
	{
		if (m_ProjectileDef->ProjectileType == 1)
		{
			auto blockDef = m_pWorld->getBlockMaterial(*blockpos)->GetBlockDef();
			m_strEffectName = "BulletHitBlock_" + std::to_string(blockDef->Score);
		}
		if (m_ProjectileDef->TriggerDelay == 0)
		{
			doTrigger(m_strEffectName);

			// 变色效果
			if (IsColorableItem(m_ProjectileDef->ID))
			{
				bool ret = false;
				//Block block = m_pWorld->getBlock(*blockpos);
				int blockid = block.getResID();
				const BlockDef *def = GetDefManagerProxy()->getBlockDef(blockid);

				if (def->CopyID > 0)
				{
					blockid = def->CopyID;
				}

				MINIW::ScriptVM::game()->callFunction("IsColorBlock", "i>b", blockid, &ret);
				if (ret)
				{
					int new_blockid, new_blockdata;
					ActorLiving *living = dynamic_cast<ActorLiving*>(getShootingActor());
					if (blockid == BLOCK_PERISTELE || blockid == BLOCK_TOP_PERISTELE)
					{
						//石柱 柱顶特殊判断
						if (m_pWorld && m_pWorld->getContainerMgr())
						{
							PeristeleContainer* container = dynamic_cast<PeristeleContainer*>(m_pWorld->getContainerMgr()->getContainer(*blockpos));
							if (container)
							{
								container->setColor(m_Color);

								m_pWorld->markBlockForUpdate(*blockpos, true);
							}
						}
					}
					else
					{
						MINIW::ScriptVM::game()->callFunction("Color2BlockInfo", "ii>ii", m_Color, blockid, &new_blockid, &new_blockdata);

						// 玩法模式：染色记分
						if (GetWorldManagerPtr() && GetWorldManagerPtr()->isGameMakerRunMode() && GetWorldManagerPtr()->m_RuleMgr && living)
						{
							int opt;
							float score;
							unsigned int oldblock_color;
							int oldblock_teamid;

							MINIW::ScriptVM::game()->callFunction("GetColorFromBlockInfo", "ii>i", blockid, block.getData(), &oldblock_color);

							if (GetWorldManagerPtr())
							{
								GetWorldManagerPtr()->getRuleOptionID(GMRULE_SCORE_COLORCHANGE, opt, score);
							}

							oldblock_teamid = Color2Team(oldblock_color);
							if (oldblock_teamid != living->getTeam())
							{
								if (oldblock_teamid != -1)
								{
									GetWorldManagerPtr()->m_RuleMgr->addTeamScore(oldblock_teamid, (int)-score);
									if (GetWorldManagerPtr()->m_RuleMgr->getTeamScore(oldblock_teamid) < 0)
									{
										GetWorldManagerPtr()->m_RuleMgr->setTeamScore(oldblock_teamid, 0);
									}
								}
								living->addGameScoreByRule(GMRULE_SCORE_COLORCHANGE);
							}
						}

						//m_pWorld->setBlockAir(*blockpos, 2);
						TriggerBlockAddRemoveDisable Tmp2(m_pWorld); //染色方块不多次触发创建和删除
						m_pWorld->setBlockAll(*blockpos, new_blockid, new_blockdata, 2);
						ClientActor* shooter = getShootingActor();
						if (shooter && new_blockid != blockid)
						{
							ObserverEvent_ActorBlock obevent(shooter->getObjId(), blockid, blockpos->x, blockpos->y, blockpos->z);
							obevent.SetData_Actor(shooter->getDefID());
							GetObserverEventManager().OnTriggerEvent("Block.ChangeColor", &obevent);
						}
					}

					// 彩蛋（AOE变色）
					if (m_ProjectileDef->ID == ITEM_COLORED_EGG)
					{
						ClientActor* shooter = getShootingActor();
						WCoord pos_original, pos_target, pos_step_x, pos_step_y, pos_front, pos_back;
						int r = 1;
						switch (face)
						{
						case DIR_POS_X:
							pos_original = *blockpos + WCoord(0, -1*r, -1*r);
							pos_step_x = WCoord(0, 1, 0);
							pos_step_y = WCoord(0, 0, 1);
							pos_front = WCoord(1, 0, 0);
							pos_back = WCoord(-1, 0, 0);
							break;
						case DIR_NEG_X:
							pos_original = *blockpos + WCoord(0, 1*r, -1*r);
							pos_step_x = WCoord(0, -1, 0);
							pos_step_y = WCoord(0, 0, 1);
							pos_front = WCoord(-1, 0, 0);
							pos_back = WCoord(1, 0, 0);
							break;
						case DIR_POS_Y:
							pos_original = *blockpos + WCoord(1*r, 0, -1*r);
							pos_step_x = WCoord(-1, 0, 0);
							pos_step_y = WCoord(0, 0, 1);
							pos_front = WCoord(0, 1, 0);
							pos_back = WCoord(0, -1, 0);
							break;
						case DIR_NEG_Y:
							pos_original = *blockpos + WCoord(-1*r, 0, -1*r);
							pos_step_x = WCoord(1, 0, 0);
							pos_step_y = WCoord(0, 0, 1);
							pos_front = WCoord(0, -1, 0);
							pos_back = WCoord(0, 1, 0);
							break;
						case DIR_POS_Z:
							pos_original = *blockpos + WCoord(-1*r, -1*r, 0);
							pos_step_x = WCoord(1, 0, 0);
							pos_step_y = WCoord(0, 1, 0);
							pos_front = WCoord(0, 0, 1);
							pos_back = WCoord(0, 0, -1);
							break;
						case DIR_NEG_Z:
							pos_original = *blockpos + WCoord(1*r, -1*r, 0);
							pos_step_x = WCoord(-1, 0, 0);
							pos_step_y = WCoord(0, 1, 0);
							pos_front = WCoord(0, 0, -1);
							pos_back = WCoord(0, 0, 1);
							break;
						}

						for (int i = 0; i < 3; ++i)
						{
							for (int j = 0; j < 3; ++j)
							{
								for (int k = 0; k < 3; ++k)
								{
									switch (k)
									{
									case 0:
										pos_target = pos_original + pos_step_x*i + pos_step_y*j + pos_front;
										break;
									case 1:
										pos_target = pos_original + pos_step_x*i + pos_step_y*j;
										break;
									case 2:
										pos_target = pos_original + pos_step_x*i + pos_step_y*j + pos_back;
										break;
									}

									MINIW::ScriptVM::game()->callFunction("IsColorBlock", "i>b", m_pWorld->getBlockID(pos_target), &ret);
									if (ret)
									{
										int x_diff, y_diff;
										bool change = true;

										switch (face)
										{
										case DIR_POS_X:
										case DIR_NEG_X:
											x_diff = Rainbow::Abs(pos_target.y - (*blockpos).y);
											y_diff = Rainbow::Abs(pos_target.z - (*blockpos).z);
											break;
										case DIR_POS_Y:
										case DIR_NEG_Y:
											x_diff = Rainbow::Abs(pos_target.x - (*blockpos).x);
											y_diff = Rainbow::Abs(pos_target.z - (*blockpos).z);
											break;
										case DIR_POS_Z:
										case DIR_NEG_Z:
											x_diff = Rainbow::Abs(pos_target.x - (*blockpos).x);
											y_diff = Rainbow::Abs(pos_target.y - (*blockpos).y);
											break;
										}

										if (x_diff == r && y_diff == r)
										{
											if (GenRandomInt(2) == 0)
											{
												change = false;
											}
										}

										if (change)
										{
											block = m_pWorld->getBlock(pos_target);
											blockid = m_pWorld->getBlockID(pos_target);
											if (blockid == BLOCK_PERISTELE || blockid == BLOCK_TOP_PERISTELE)
											{
												//石柱 柱顶特殊判断
												if (m_pWorld && m_pWorld->getContainerMgr())
												{
													PeristeleContainer* container = dynamic_cast<PeristeleContainer*>(m_pWorld->getContainerMgr()->getContainer(pos_target));
													if (container)
													{
														container->setColor(m_Color);
														m_pWorld->markBlockForUpdate(pos_target, true);
													}
												}
											}
											else
											{
												MINIW::ScriptVM::game()->callFunction("Color2BlockInfo", "ii>ii", m_Color, m_pWorld->getBlockID(pos_target), &new_blockid, &new_blockdata);

												// 玩法模式：染色记分
												if (GetWorldManagerPtr() && GetWorldManagerPtr()->isGameMakerRunMode() && GetWorldManagerPtr()->m_RuleMgr && living)
												{
													int opt;
													float score;
													unsigned int oldblock_color;
													int oldblock_teamid;

													MINIW::ScriptVM::game()->callFunction("GetColorFromBlockInfo", "ii>i", block.getResID(), block.getData(), &oldblock_color);

													if (GetWorldManagerPtr())
													{
														GetWorldManagerPtr()->getRuleOptionID(GMRULE_SCORE_COLORCHANGE, opt, score);
													}
													
													oldblock_teamid = Color2Team(oldblock_color);
													if (oldblock_teamid != living->getTeam())
													{
														if (oldblock_teamid != -1)
														{
															GetWorldManagerPtr()->m_RuleMgr->addTeamScore(oldblock_teamid, (int)-score);
															if (GetWorldManagerPtr()->m_RuleMgr->getTeamScore(oldblock_teamid) < 0)
															{
																GetWorldManagerPtr()->m_RuleMgr->setTeamScore(oldblock_teamid, 0);
															}
														}
														living->addGameScoreByRule(GMRULE_SCORE_COLORCHANGE);
													}
												}

												//m_pWorld->setBlockAir(pos_target, 2);
												TriggerBlockAddRemoveDisable Tmp2(m_pWorld); //染色方块不多次触发创建和删除
												m_pWorld->setBlockAll(pos_target, new_blockid, new_blockdata, 2);
												if (new_blockid != blockid && !pos_target.__eq(*blockpos))
												{
													if (shooter)
													{
														ObserverEvent_ActorBlock obevent(shooter->getObjId(), blockid, blockpos->x, blockpos->y, blockpos->z);
														obevent.SetData_Actor(shooter->getDefID());
														GetObserverEventManager().OnTriggerEvent("Block.ChangeColor", &obevent);
													}
												}
											}
										}
										break;
									}
								}
							}
						}
					}
				}
			}

			int delay_ticks = getDelayClearTicks();
			////子弹残留时间
			//ClientPlayer *attacker = dynamic_cast<ClientPlayer *>(m_pWorld->getActorMgr()->findActorByWID(m_ShootingActorID));
			//
			//SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("WeaponSkin_System_GetBulletClearDelayTicks",
			//	SandboxContext(nullptr)
			//	.SetData_Number("uin", m_ShootingActorID)
			//	.SetData_Number("itemid", attacker ? attacker->getCurToolID() : 0)
			//);

			//if (result.IsExecSuccessed())
			//{
			//	delay_ticks = result.GetData_Number("nDelayTicks");
			//}


			if (!m_ProjectileDef->Pickable || m_nBlockID || !m_dropable)
			{
				setNeedClearEx(delay_ticks);
			}
		}
		else
		{
			if (needClear()) //销毁直接调用效果
			{
				doTrigger(m_strEffectName);
			}
			//has not trigger
			if (m_ImpactTimeMark < 0)
				m_ImpactTimeMark = 0;
		}

		// 投射物击碎方块判断
		Block block = m_pWorld->getBlock(*blockpos);
		int blockid = block.getResID();
		//石中剑钥匙
		if (blockid == BLOCK_KEY_OF_BROKEN_SWORD && m_pWorld)
		{
			m_pWorld->blockKeyOnTrigger(*blockpos);
		}

		const BlockDef *blockdef = GetDefManagerProxy()->getBlockDef(blockid);
		auto vel = getLocoMotion()->m_Motion / Rainbow::Normalize(getLocoMotion()->getLookDir());
		ClientActor *shooter = getShootingActor();
		ClientPlayer *player = dynamic_cast<ClientPlayer*>(shooter);
		auto game = GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame);
		bool gamerule_forbid = GetWorldManagerPtr()->isGameMakerRunMode() && GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_BLOCKDESTROY) == 0;
		if (player != NULL) gamerule_forbid = !player->checkActionAttrState(ENABLE_DESTROYBLOCK); //将设置界面的权限赋值到玩家身上
		SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_getGuestMode",
			SandboxContext(nullptr));
		bool GuestModeFlag = false;
		if (result.IsExecSuccessed())
		{
			GuestModeFlag = result.GetData_Bool();
		}
		
		if ((!gamerule_forbid && player && blockdef && blockdef->Hardness >= 0) || checkCanPenetrate(blockid))
			//&& blockdef->Hardness < vel.x / g_projectile_factor + g_projectile_ratio)
		{
			do
			{
				if (player)
				{
					SandboxResult resultCanCSPermit = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canCSPermit",
						SandboxContext(nullptr)
						.SetData_Number("uin", player->getUin())
						.SetData_Number("blockid", 0)
						.SetData_Number("bit", CS_PERMIT_DESTROY_BLOCK));
					bool canCSPermitFlag = false;
					if (resultCanCSPermit.IsExecSuccessed())
					{
						canCSPermitFlag = resultCanCSPermit.GetData_Bool();
					}
					if (!canCSPermitFlag)
					{

						SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_isHost",
							SandboxContext(nullptr).SetData_Number("uin", player->getUin()));
						bool isHostFlag = false;
						if (result.IsExecSuccessed())
						{
							isHostFlag = result.GetData_Bool();
						}
						if (isHostFlag)
							MINIW::ScriptVM::game()->callFunction("notifyAuthorityGameInfo2Player", "iiii", player->getUin(), int(PLAYER_NOTIFYINFO_TIPS), 9725, int(CS_PERMIT_DESTROY_BLOCK));
						else
							MINIW::ScriptVM::game()->callFunction("notifyAuthorityGameInfo2Player", "iiii", player->getUin(), int(PLAYER_NOTIFYINFO_TIPS), 9639, int(CS_PERMIT_DESTROY_BLOCK));
						break;
					}
				}

				auto living = dynamic_cast<ActorLiving*>(shooter);
				if (living && m_AttackPointsNew[1] > 0.001f)
				{
					bool isDestroy = false;
					living->DoHurtBlock(*blockpos, ATTACK_TYPE::ATTACK_RANGE, m_AttackPointsNew[1], isDestroy, this->GetItemId(), getPosition());
					if (isDestroy)
					{
						getWorld()->destroyBlock(*blockpos, BLOCK_MINE_TOOLFIT);
					}
					memset(&m_AttackPointsNew, 0, sizeof(m_AttackPointsNew)); // 子弹只可以作用一次伤害方块
				}

				// // 观察者事件接口
				// if (player != NULL) { //方块被玩家的投射物击碎
				// 	ObserverEvent_ActorBlock obevent(player->getObjId(), blockid, blockpos->x, blockpos->y, blockpos->z);
				// 	GetObserverEventManager().OnTriggerEvent("Block.DestroyBy", &obevent);
				// }
				// if (shooter && shooter->IsTriggerCreature()) // 生物投掷物击碎方块
				// {
				// 	ObserverEvent_ActorBlock obevent(shooter->getObjId(), blockid, blockpos->x, blockpos->y, blockpos->z);
				// 	obevent.SetData_Actor(shooter->getDefID());
				// 	GetObserverEventManager().OnTriggerEvent("Block.DestroyBy", &obevent);
				// }

				// if (shooter == NULL && player == NULL) // 投掷物没有所属Actor时 击碎方块
				// {
				// 	ObserverEvent_ActorBlock obevent(getObjId(), blockid, blockpos->x, blockpos->y, blockpos->z);
				// 	obevent.SetData_Item(getDefID());
				// 	GetObserverEventManager().OnTriggerEvent("Block.DestroyBy", &obevent);
				// }
			} while (false);

			if (m_ProjectileDef->Pickable)
			{
				const ToolDef* toolDef = GetDefManagerProxy()->getToolDef(m_ItemID);
				if (toolDef && !m_isUseCustomAtkDuration)
				{
					m_Durable -= 2 * toolDef->AtkDuration;
					m_Durable = m_Durable < 0 ? 0 : m_Durable;
				}
				if (m_Durable == 0) setNeedClearEx();
			}
			else setNeedClearEx();
		}
		if (m_ItemID == BLOCK_FAR_DRIFTBOTTLE || m_ItemID == BLOCK_DRIFTBOTTLE)
		{
			{
				jsonxx::Object object;
				object.parse(m_UserDataStr);
				if (player)
				{
					MINIW::ScriptVM::game()->callFunction("DriftBottleUseReport", "isi", player->getUin(), m_UserDataStr.c_str(), 2);
					driftGetLetterMessage((*blockpos + WCoord((float)0.5, 1, 0.5)) * BLOCK_SIZE, player->getUin(), m_UserDataStr, m_pWorld, m_ItemID);
				}
			}
			setNeedClear();
		}
		//if (m_ItemID == ITEM_ICE_BALL)
		//{
		//	//MKTODO:温度-10
		//	if (blockid == BLOCK_STILL_LAVA || blockid == BLOCK_FLOW_LAVA)
		//	{
		//		//岩浆变成凝浆块
		//		m_pWorld->setBlockAll(*blockpos, BLOCK_COAGULATION, 0);
		//	}
		//	else if (blockid == BLOCK_STILL_WATER || blockid == BLOCK_FLOW_WATER)
		//	{
		//		//岩浆变成凝浆块
		//		m_pWorld->setBlockAll(*blockpos, BLOCK_ICE, 0);
		//	}
		//}
		if (m_ItemID == ITEM_EXTREME_COLD && shooter)
		{

			auto pos = getPosition();
			pos.y = (blockpos->y + 1) * BLOCK_SIZE;
			m_pWorld->getEffectMgr()->GetGameEffectMgr()->playEffectIceCircle(shooter, pos, 100, 0, face, m_AttachedEffect/*, m_AttackPoints*/);
			setNeedClear();
		}
		if (m_ItemID == ITEM_ICE_BOTTLE && shooter != NULL)
		{
			auto pos = getPosition();
			pos.y = (blockpos->y + 1) * BLOCK_SIZE;
			m_pWorld->getEffectMgr()->GetGameEffectMgr()->playEffectIceBottle(shooter, pos, 100);
			setNeedClear();
		}
		//苔藓球击中
		if (m_ItemID == 11595)
		{
			WCoord offset = WCoord(0, 0, 0);
			int towards = DIR_NOT_INIT;
			     if (face == DIR_NEG_X) { towards = DIR_POS_X; offset = WCoord(-1,  0,  0); }
			else if (face == DIR_POS_X) { towards = DIR_NEG_X; offset = WCoord( 1,  0,  0); }
			else if (face == DIR_NEG_Z) { towards = DIR_POS_Z; offset = WCoord( 0,  0, -1); }
			else if (face == DIR_POS_Z) { towards = DIR_NEG_Z; offset = WCoord( 0,  0,  1); }
			else if (face == DIR_NEG_Y) { towards = DIR_POS_Y; offset = WCoord( 0, -1,  0); }
			else if (face == DIR_POS_Y) { towards = DIR_NEG_Y; offset = WCoord( 0,  1,  0); }
			BlockMaterial* pmtl = g_BlockMtlMgr.getMaterial(blockid);
			if (IsGlassBlockID(blockid) || (pmtl && pmtl->isOpaqueCube() && pmtl->defBlockMove()))//(blockid > 631 && blockid < 649)
			{
				int frontBlockId = m_pWorld->getBlockID(*blockpos + offset);
				if (frontBlockId == BLOCK_MOSS)
				{
					m_pWorld->setBlockAll(*blockpos + offset, BLOCK_MOSS_HUGE, towards);
				}
				else if(frontBlockId == BLOCK_MOSS_HUGE)
				{
					int mossData = m_pWorld->getBlockData(*blockpos + offset);
					int mossLevel=(mossData & 8)? 1 : 0;
					if (!mossLevel)
					{
						m_pWorld->setBlockData(*blockpos + offset, (mossData | 8));
					}
				}
				else
				{
					m_pWorld->setBlockAll(*blockpos + offset, BLOCK_MOSS, towards);
				}
				
			}
			setNeedClear();
		}
		if (blockid == BLOCK_WEAK_ICICLE)//投掷物撞击冰凌
		{
			int blockcount = 0;
			int upblockcount = 0;
			vector<WCoord> posArray;
			WCoord _downblockpos = *blockpos;
			while (m_pWorld->getBlockID(_downblockpos) == BLOCK_WEAK_ICICLE)
			{
				posArray.emplace_back(_downblockpos);
				_downblockpos = _downblockpos - WCoord(0, 1, 0);
				blockcount++;
			}
			WCoord _upblockpos = *blockpos + WCoord(0, 1, 0);
			while (m_pWorld->getBlockID(_upblockpos) == BLOCK_WEAK_ICICLE)
			{
				posArray.emplace_back(_upblockpos);
				_upblockpos = _upblockpos + WCoord(0, 1, 0);
				blockcount++;
				upblockcount++;
			}
			//下方有至少1格空间则坠落
			if (blockcount > 0 && m_pWorld->getBlockID(_downblockpos) == BLOCK_AIR)
			{
				for (int i = 0; i < posArray.size(); i++)
				{
					m_pWorld->setBlockAir(posArray[i]);
				}

				WCoord shootPos = (upblockcount > 0) ? DownCoord(_upblockpos)  : *blockpos;
				auto pos = BlockBottomCenter(shootPos);
				Rainbow::GetMusicManager().PlaySound("sounds/item/32/prepare.ogg", pos.toVector3(), 1.0f, 1.0f);
				ClientActorIcicle::shootIcicleAuto(ITEM_WEAK_ICICLE, m_pWorld, BlockBottomCenter(shootPos), blockcount);
			}
		}
		if (m_ItemID == BLOCK_VOID_FRUIT || m_ItemID == ITEM_VOID_SUPER_FRUIT)
		{
			int mobid = m_ItemID == BLOCK_VOID_FRUIT ? 3242 : 3243;
			actorMgr->spawnMob(BlockCenterCoord(*blockpos), mobid, false, false);
			setNeedClear();
		}

		ProjectileLocoMotion* loco = dynamic_cast<ProjectileLocoMotion *>(getLocoMotion());
		if (loco && loco->m_CanTrigger)
		{
			if (triggerblockid > 0 && triggerblockid != 4095 && blockpos && !m_pWorld->isRemoteMode())
			{
				// 观察者事件接口
				ObserverEvent obevent;
				obevent.SetData_EventObj(getObjId());
				obevent.SetData_Item(GetItemId());
				obevent.SetData_Block(blockid);
				obevent.SetData_Position((float)blockpos->x, (float)blockpos->y, (float)blockpos->z);
				obevent.SetData_HelperObjid(m_ShootingActorID);
				if (m_ShootingActorID > 0)
				{
					ClientMob* attacker = actorMgr->findMobByWID(m_ShootingActorID);
					if (attacker)
					{
						obevent.SetData_Actor(attacker->getDefID());
					}
				}
				GetObserverEventManager().OnTriggerEvent("Actor.Projectile.Hit", &obevent);
			}
		}
	}

	if (m_skillTriggerType == PROJECTILE_SKILL_IMPACT_WITH_ACTOR_OR_BLOCK) //和方块碰撞时
	{
		activeSkill(m_skillName.c_str());
	}
	
	//投掷物的击碎值大于方块的韧度值则要削减击碎值
	const BlockDef* def = GetDefManagerProxy()->getBlockDef(block.getResID());
	if (def && m_puncture >= def->Tenacity)
	{
		m_puncture -= def->Tenacity;
	}
	//子弹弹孔
	if (isBullets(GetItemId()))
	{
	}
}

int ClientActorProjectile::getObjType()const
{
	if (m_ItemID == ITEM_COBBLE)
		return OBJ_TYPE_COBBLE;
	else
		return OBJ_TYPE_THROWABLE;
}

int ClientActorProjectile::getMass()
{
	return 1000;
}

// 投掷物触发附魔效果，目前只处理闪电链，确定可以触发再调用
void ClientActorProjectile::applyRuneData(ClientActor* targetActor, bool damaged)
{
	if (m_ShootingActorID == targetActor->getObjId())
	{
		return;
	}

	int runenum = m_Runedata.getRuneNum();
	if (!runenum)
	{
		return;
	}

	ActorLiving* live = dynamic_cast<ActorLiving*>(targetActor);
	if (!live)
	{
		return;
	}

	// 闪电链
	float baseChainDamage = 0;
	int baseChainCnt = 0;

	for (int i = 0; i < runenum; i++) {
		GridRuneItemData runedata = m_Runedata.getItemByIndex(i);

		const RuneDef* def = runedata.getRuneDef();
		if (def && def->EnchantType == ENCHANT_LIGHTNING_CHAIN)
		{
			baseChainDamage = runedata.getRuneVal1();
			baseChainCnt = runedata.getRuneVal0();
		}
	}
	if (damaged && baseChainCnt > 0 && GenRandomInt(2) > 0)	// 50概率
	{
		auto pComponent = live->getLightningChainComponent();
		if (pComponent)
		{
			CollideAABB box1;
			getCollideBox(box1);
			WCoord srcPos = { box1.centerX(),box1.centerY(),box1.centerZ() };

			auto srcObjId = getObjId();
			baseChainCnt--;
			pComponent->addChain(srcObjId, m_ShootingActorID, baseChainCnt, baseChainDamage, live->getTeam(), LightningChainComponent::ChainType::RUNE, { m_ShootingActorID }, srcPos);
		}
	}
}

void ClientActorProjectile::playMotion(const char *name, bool reset_play, int motion_class, int delayTicks, float looptime)
{
	if (m_EntityModel)
	{
		if (delayTicks > 0)
		{
			DelayMotionData data;
			data.name = name;
			data.reset_play = reset_play;
			data.motion_class = motion_class;
			data.looptime = looptime;
			m_delayMotionMap[delayTicks] = data;
		}
		else
		{
			m_EntityModel->PlayMotion(name, reset_play, motion_class, looptime);
			if (looptime > 0)
			{
				SharePtr<ModelMotion> pmotion = m_EntityModel->FindMotion(name);
				if (pmotion)
				{
					pmotion->m_loop = looptime > 0 ? ML_LOOP : ML_ONCE;
					pmotion->m_fLoopStopTime = looptime;
				}
			}
		}

		// 观察者事件接口:投掷物创建特效
		//PlayMotionOnTrigger(TriggerEffect_Projectile, name, getObjId());
	}
	else
	{
		m_cureffectName = name;
	}
}

bool ClientActorProjectile::skillPlayBodyEffect(const char* path, float loopPlayTime, const Rainbow::Vector3f& OffsetPosition, const Rainbow::Vector3f& rote, const Rainbow::Vector3f& scale, bool isLoop, int motion_class)
{
	if (m_EntityModel)
	{
		if (!m_EntityModel->HasMotionIsPlaying(path))
			m_EntityModel->PlayMotion(path, loopPlayTime, OffsetPosition, rote, scale, isLoop, motion_class);
	}
	World* pworld = getWorld();
	if (pworld && !pworld->isRemoteMode())
	{
		jsonxx::Object context;
		char objid_str[128];
		sprintf(objid_str, "%lld", getObjId());
		context << "objid" << objid_str;
		context << "path" << path;
		context << "loopPlayTime" << loopPlayTime;
		jsonxx::Object offsetpos;
		offsetpos << "x" << OffsetPosition.x;
		offsetpos << "y" << OffsetPosition.y;
		offsetpos << "z" << OffsetPosition.z;
		jsonxx::Object jrote;
		jrote << "x" << rote.x;
		jrote << "y" << rote.y;
		jrote << "z" << rote.z;
		context << "OffsetPosition" << offsetpos;
		context << "rote" << jrote;
		jsonxx::Object jscale;
		jscale << "x" << scale.x;
		jscale << "y" << scale.y;
		jscale << "z" << scale.z;
		context << "scale" << jscale;
		context << "isLoop" << isLoop;
		context << "motionclass" << motion_class;
		SandBoxManager::getSingleton().sendBroadCast("PB_SKILLPLAYBODYEFFECT_HC", context.bin(), context.binLen());
		
	}
	return true;
}

bool ClientActorProjectile::skillStopBodyEffect(const char* path)
{
	stopMotion(path);
	World* pworld = getWorld();
	if (pworld && !pworld->isRemoteMode())
	{
		jsonxx::Object context;
		char objid_str[128];
		sprintf(objid_str, "%lld", getObjId());
		context << "objid" << objid_str;
		context << "path" << path;
		SandBoxManager::getSingleton().sendBroadCast("PB_SKILLSTOPBODYEFFECT_HC", context.bin(), context.binLen());
	}
	return true;
}

void ClientActorProjectile::stopMotion()
{
	if(m_EntityModel) m_EntityModel->DelayStopMotion(2);

	if (!m_dropable)
	{
		setNeedClearEx(3);
	}
}

//停止指定特效
void ClientActorProjectile::stopMotion(const char* name)
{
	if (m_EntityModel)
		m_EntityModel->StopMotion(name);
}

//设置特效大小
void ClientActorProjectile::setMotionScale(const char* name, float fScale)
{
	if (m_EntityModel)
		m_EntityModel->SetMotionScale(name, fScale);
}

//获取特效大小
float ClientActorProjectile::getMotionScale(const char* name)
{
	if (m_EntityModel)
		return m_EntityModel->GetMotionScale(name).x;

	return 1;
}

//获取特效数
int ClientActorProjectile::getMotionCount()
{
	if (m_EntityModel)
		return m_EntityModel->GetMotionCount();

	return 0;
}

void ClientActorProjectile::playCustomMotion(char *zipPath, const char* resPath, bool reset_play /*= true*/, int motion_class /*= 211111*/)
{
	if (!m_EntityModel)
		return;

	std::map<std::string, const char *>::iterator iter = m_motionNames.find(zipPath);
	if (iter == m_motionNames.end())
	{
		bool loadResult = false;
		SharePtr<EntityData> model = PkgUtils::ZipFileResLoad<EntityData>(zipPath, resPath);
		if (model) {
			m_EntityModel->Load(model);
			loadResult = true;
		}
		if (loadResult)
		{
			int index = m_EntityModel->GetMotionCount() - 1;
			if (index >= 0)
			{
				const char *motion_name = m_EntityModel->GetMotionNameByIndex(index, motion_class);
				if (stricmp(motion_name, "") != 0)
				{
					m_motionNames.insert(std::make_pair(zipPath, motion_name));
				}
			}
		}
	}
	else
	{
		m_EntityModel->PlayMotion(iter->second, reset_play, motion_class);
	}
}


//获取特效名
const char* ClientActorProjectile::getMotionNameByIndex(int index, int class_type)
{
	if (m_EntityModel)
		return m_EntityModel->GetMotionNameByIndex(index, class_type);

	return NULL;
}

void ClientActorProjectile::setProperty_byGrid(const BackPackGrid* grid)
{
	setProperty(grid->getDuration(), grid->getNumEnchant(), grid->getEnchants());
	m_Runedata = grid->getRuneData();
}

void ClientActorProjectile::addSkillCfg(const Rainbow::FixedString& id, int triggerType)
{
	m_skillName = id;
	m_skillTriggerType = triggerType;
	
	if (triggerType == PROJECTILE_SKILL_INSTANITATE) //投掷物创建就触发，创建添加的技能配置所以这里触发
	{
		activeSkill(id);
	}
}

bool ClientActorProjectile::checkCanPenetrate(int blockid)
{
	const BlockDef* def = GetDefManagerProxy()->getBlockDef(blockid);
	if (def)
	{
		auto tooDef = GetDefManagerProxy()->getToolDef(m_ItemID);
		if (m_puncture > def->Tenacity && tooDef && def->MineTool == tooDef->Type) //满足击碎值和工具匹配的方块才能穿透和破坏
		{
			return true;
		}
	}
	return false;
}

void ClientActorProjectile::setProperty(int durable, int enchantNum, const int* enchants)
{
	m_Durable = durable;
	m_EnchantNum = enchantNum;
	for (int i = 0; i < enchantNum; i++)
	{
		m_Enchants[i] = enchants[i];
	}
}

bool ClientActorProjectile::IsAutoClear()
{
	//物理物体不自动清除
	if (GetWorldManagerPtr()->m_SurviveGameConfig->physxconfig.enable && static_cast<ProjectileLocoMotion *>(getLocoMotion())->m_hasPhysActor)
	{
		return false;
	}

	if (m_ImpactTimeMark >= 0 && m_ImpactTimeMark < (m_ProjectileDef->TriggerDelay * 20))
	{
		return false;
	}
	else
	{
		clearBlock();
		if (m_ProjectileDef->TriggerDelay > 0)
		{
			doTrigger(m_strEffectName);
		}
		return true;
	}
}

bool ClientActorProjectile::canPassActor()
{
	return m_passActorNum < m_canPassActorNum;
}

void ClientActorProjectile::enterWorld(World *pworld)
{
	ClientActor::enterWorld(pworld);

	// m_EntityModel 可能为NULL
	//AssertMsg(m_EntityModel, "m_EntityModel not create!");

	if(m_EntityModel)
		m_EntityModel->AttachToScene(pworld->getScene());

	if (m_EntityModel_Left)
		m_EntityModel_Left->AttachToScene(pworld->getScene());
	if (GetWorldManagerPtr()->m_SurviveGameConfig->physxconfig.enable)
	{
		static_cast<ProjectileLocoMotion *>(getLocoMotion())->checkPhysWorld();
		static_cast<ProjectileLocoMotion *>(getLocoMotion())->attachPhysActor(GetItemId());
	}
}

void ClientActorProjectile::leaveWorld(bool keep_inchunk)
{
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->m_SurviveGameConfig->physxconfig.enable)
	{
		ProjectileLocoMotion *loc = static_cast<ProjectileLocoMotion *>(getLocoMotion());
		loc->detachPhysActor();
		if (loc->m_hasPhysActor)
		{
			//物理物体在绑定的状态下消失,则做清除动作
			ClientPlayer *player = nullptr;
			auto bindAComponent = m_pBindActorComponent;
			if (bindAComponent)
			{
				player = dynamic_cast<ClientPlayer *>(bindAComponent->getTarget());
			}
			if (player)
			{
				player->doPutGravityActor(this);
				player->onOperateEnded();
				player->stopMotion(30);
			}
		}
	}

	ClientActor::leaveWorld(keep_inchunk);

	//移除掉落物
	if (!keep_inchunk)
	{
		ObserverEvent obevent;
		obevent.SetData_EventObj(m_ObjId);
		GetObserverEventManager().OnTriggerEvent("dev.remove", &obevent);
	}
}

bool ClientActorProjectile::interact(ClientActor*pPlayer, bool onshift /* = false */, bool isMobile)
{
	auto pTempPlayer = dynamic_cast<ClientPlayer*>(pPlayer);
	ProjectileLocoMotion *loc = static_cast<ProjectileLocoMotion *>(getLocoMotion());
	if (isMobile == true || (GetWorldManagerPtr()->m_SurviveGameConfig->physxconfig.enable == false && loc->m_hasPhysActor == false))
	{
		return leftClickInteract(pTempPlayer);
	}
	else
		return false;
}

bool ClientActorProjectile::leftClickInteract(ClientActor*player)
{
	auto pTempPlayer = dynamic_cast<ClientPlayer*>(player);
	if (nullptr != pTempPlayer)
	{
		ProjectileLocoMotion* loc = static_cast<ProjectileLocoMotion*>(getLocoMotion());
		if (g_WorldMgr->m_SurviveGameConfig->physxconfig.enable && loc->m_hasPhysActor)
		{
			ClientActor *actor = nullptr;
			auto bindAComponent = m_pBindActorComponent;
			if (bindAComponent)
			{
				actor = dynamic_cast<ClientActor *>(bindAComponent->getTarget());
			}
			if (actor) return true;

			pTempPlayer->playAnim(SEQ_ATTACK);
			if (pTempPlayer->getCurToolID() == ITEM_SHEARS)
			{
				m_pWorld->getEffectMgr()->playSound(getPosition(), "ent.3420.clear", 1.0f, 1.0f, PLAYSND_SYNC | PLAYSND_LONGDIST);
				setNeedClear();
				return true;
			}
			else
			{
				Rainbow::Vector3f dir = pTempPlayer->getLocoMotion()->getLookDir();

				float ratio = GetWorldManagerPtr()->m_SurviveGameConfig->physxconfig.force_interact;
				const PhysicsActorDef* physicsActorDef = GetDefManagerProxy()->getPhysicsActorDef(this->m_ItemID);
				if (physicsActorDef)
				{
					ratio /= physicsActorDef->Mass;
					m_pWorld->getEffectMgr()->playSound(getPosition(), "misc.hit1", 1.0f, 1.0f, PLAYSND_SYNC | PLAYSND_LONGDIST);
				}

				loc->m_Motion = Rainbow::Vector3f(dir.x * ratio * 10, dir.y * ratio * 10, dir.z * ratio * 10);
				if (loc->m_PhysActor)
					loc->m_PhysActor->SetLinearVelocity(loc->m_Motion * 10.0f);
				loc->doPickThrough();

				//playBodyEffect("ball_power_low");
				//player->playSound("ent.3420.lob_power_low", 1.0f, 1.0f, 6);
				return false;
			}
		}
	}

	return false;
}

void ClientActorProjectile::playSoundByPhysCollision()
{
	ProjectileLocoMotion *loc = static_cast<ProjectileLocoMotion *>(getLocoMotion());
	if (GetWorldManagerPtr()->m_SurviveGameConfig->physxconfig.enable && loc->m_hasPhysActor)
	{
		auto physActor = loc->m_PhysActor;
		float strength = physActor->GetLinearVelocity().Length();
		float strengthFactor = strength / GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.threshold - 1;
		WCoord dp = GetWorldManagerPtr()->m_RenderEyePos - getPosition();
		float dist = dp.length();
		float distFactor = 1 - dist / GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.soundrange;
		if (distFactor > 1.0f) distFactor = 1.0f;
		if (strengthFactor > 1.0f) strengthFactor = 1.0f;
		if (physActor && strengthFactor>0 && distFactor>0)
		{
			float weidgt = GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.weidgt;
			if (loc->m_collisionSound != "")
				m_pWorld->getEffectMgr()->playSound(getPosition(), loc->m_collisionSound.c_str(), 1.0f*(weidgt*strengthFactor + distFactor) / (weidgt + 1), 1.0f, PLAYSND_SYNC | PLAYSND_LONGDIST);
		}
	}
}

void ClientActorProjectile::createPhysActor(int id)
{
	if (GetWorldManagerPtr()->m_SurviveGameConfig->physxconfig.enable)
	{
		//static_cast<ProjectileLocoMotion *>(getLocoMotion())->checkPhysWorld();
		static_cast<ProjectileLocoMotion *>(getLocoMotion())->attachPhysActor(GetItemId());
	}
}

void ClientActorProjectile::resetModel()
{
	auto *itemDef = GetDefManagerProxy()->getItemDef(m_ItemID);
	if (itemDef && itemDef->MeshType == CUSTOM_GEN_MESH)
	{
		if (CustomModelMgr::GetInstancePtr())
		{
			m_Model = CustomModelMgr::GetInstancePtr()->getAvatarModel(itemDef->Model.c_str(), PROJECTILE_ACTOR_MESH);
			if (m_Model)
			{
				if(!m_EntityModel)
					m_EntityModel = Entity::Create();

				m_EntityModel->Load(m_Model);
			}
		}
	}
}

void ClientActorProjectile::onClear()
{

	//带物理的投掷物不触发
	const PhysicsActorDef* physicsActorDef = GetDefManagerProxy()->getPhysicsActorDef(this->GetItemId());
	if (physicsActorDef)
	{
		if (physicsActorDef->EditType == PhysicsLocoMotion::PHYS_TYPE_NOEDIT_YESPHY || physicsActorDef->EditType == PhysicsLocoMotion::PHYS_TYPE_YESEDIT_YESPHY)
		{
			ParticlesComponent::playParticles(this, "10021.ent");
		}
	}
}

void ClientActorProjectile::updateBodyByFullyCustomModel()
{
	auto *itemDef = GetDefManagerProxy()->getItemDef(m_ItemID);
	if (itemDef && itemDef->MeshType == FULLY_CUSTOM_GEN_MESH && FullyCustomModelMgr::GetInstancePtr())
	{
		if(m_EntityModel)
			DESTORY_GAMEOBJECT_BY_COMPOENT(m_EntityModel);
		m_EntityModel = FullyCustomModelMgr::GetInstancePtr()->getEntity(itemDef->Model.c_str(), NULL, itemDef->Icon == "fullycustompacking");
		if (m_EntityModel)
			m_EntityModel->PlayAnim(0);

		if (m_EntityModel && m_EntityModel->GetMainModel())
			m_EntityModel->GetMainModel()->SetInstanceData(Rainbow::Vector4f(1.0f, 1.0f, 0, 0));
		if (!m_cureffectName.empty())
		{
			playMotion(m_cureffectName.c_str());
			m_cureffectName = "";
		}
	}
}

void ClientActorProjectile::updateBodyByImportModel()
{
	auto *itemDef = GetDefManagerProxy()->getItemDef(m_ItemID);
	if (!itemDef || itemDef->MeshType != IMPORT_MODEL_GEN_MESH)
		return;

	if (!ImportCustomModelMgr::GetInstancePtr())
		return;

	if (!MpGameSurviveCdnResMgr::GetInstancePtr())
		return;

	if (!MpGameSurviveCdnResMgr::GetInstancePtr()->hasRes(2, itemDef->Model.c_str()))
		return;

	if (m_EntityModel)
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_EntityModel);

	if (m_Model.Get())
	{
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_Model);
	}
	//else
	//{
	//	assert(false);
	//}

	m_Model = ImportCustomModelMgr::GetInstancePtr()->getImportModel(itemDef->Model.c_str());
	if (m_Model)
	{
		m_EntityModel = Rainbow::Entity::Create();
		m_EntityModel->Load(m_Model);

		if (m_EntityModel)
			m_EntityModel->PlayAnim(0);

		if (m_EntityModel && m_EntityModel->GetMainModel())
			m_EntityModel->GetMainModel()->SetInstanceData(Rainbow::Vector4f(1.0f, 1.0f, 0, 0));
	}
	if (!m_cureffectName.empty())
	{
		playMotion(m_cureffectName.c_str());
		m_cureffectName = "";
	}
}

void ClientActorProjectile::setBeShootedActor(ClientActor *actor)
{
	m_BaseAtk = 0;
	if (actor)
	{
		m_BeShootedActorID = actor->getObjId();
	}
	else m_BeShootedActorID = 0;
}

ClientActor *ClientActorProjectile::getBeShootedActor()
{
	if (!m_pWorld)
	{
		return nullptr;
	}
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_pWorld->getActorMgr());
	if (!actorMgr) return nullptr;
	return actorMgr->findActorByWID(m_BeShootedActorID);
}

void ClientActorProjectile::playEntityModelAnim(int seq, int inputloopmode/* =-1 */, bool include_me /* = false */)
{
	if (m_EntityModel)
	{
		m_EntityModel->PlayAnim(seq, inputloopmode);
		NotifyPlayAnim2Tracking(seq, include_me);
	}
}

void ClientActorProjectile::stopEntityModelAnim(int seq/* =-1 */)
{
	if (m_EntityModel)
	{
		if (seq == -1)
			m_EntityModel->StopAnim();
		else
			m_EntityModel->StopAnim(seq);
	}
}

bool ClientActorProjectile::supportSaveToPB()
{
	return true;
}
int ClientActorProjectile::saveToPB(PB_GeneralEnterAOIHC* pb)
{
	PB_ActorProjectile* actorMob = pb->mutable_actorprojectile();
	const PB_ActorCommon& actorCommon = actorMob->basedata();
	return saveToProjectilePB(actorMob);
}
int ClientActorProjectile::LoadFromPB(const PB_GeneralEnterAOIHC& pb)
{
	const PB_ActorProjectile &actorMob = pb.actorprojectile();
	const PB_ActorCommon &actorCommon = actorMob.basedata();
	int ret = loadPBActorCommon(actorCommon);
	if (ret != 0)
		return ret;
	return LoadFromProjectilePB(actorMob);
}
int ClientActorProjectile::saveToProjectilePB(PB_ActorProjectile* actorProj)
{
	PB_ActorCommon* actorCommon = actorProj->mutable_basedata();
	savePBActorCommon(actorCommon);

	for (int i = 0; i < m_EnchantNum; ++i)
	{
		actorProj->add_enchants(m_Enchants[i]);
	}
	PB_Vector3* blockpos = actorProj->mutable_blockpos();
	blockpos->set_x(m_BlockPos.x);
	blockpos->set_y(m_BlockPos.y);
	blockpos->set_z(m_BlockPos.z);

	ProjectileLocoMotion *loco = static_cast<ProjectileLocoMotion *>(getLocoMotion());
	actorProj->add_rotatequat(loco->m_RotateQuat.x * 1000);
	actorProj->add_rotatequat(loco->m_RotateQuat.y * 1000);
	actorProj->add_rotatequat(loco->m_RotateQuat.z * 1000);
	actorProj->add_rotatequat(loco->m_RotateQuat.w * 1000);

	actorProj->add_prerotatequat(loco->m_PrevRotateQuat.x * 1000);
	actorProj->add_prerotatequat(loco->m_PrevRotateQuat.y * 1000);
	actorProj->add_prerotatequat(loco->m_PrevRotateQuat.z * 1000);
	actorProj->add_prerotatequat(loco->m_PrevRotateQuat.w * 1000);
	

	if (m_Runedata.getRuneNum() > 0)
		m_Runedata.save(actorProj->mutable_runes());

	actorProj->set_shooter(m_ShootingActorID);
	actorProj->set_itemid(m_ItemID);
	actorProj->set_durable(m_Durable);
	actorProj->set_maxdurable(m_MaxDurable);
	actorProj->set_color(m_Color);
	actorProj->set_blockid(m_nBlockID);


	PB_Vector3* pos = actorCommon->mutable_pos();
	pos->set_x(m_StartPos.x);
	pos->set_y(m_StartPos.y);
	pos->set_z(m_StartPos.z);

	return 0;
}
int ClientActorProjectile::LoadFromProjectilePB(const PB_ActorProjectile& actorProj)
{
	const PB_ActorCommon &actorCommon = actorProj.basedata();
	loadPBActorCommon(actorCommon);
	m_LiveTicks = 0;

	m_Durable = actorProj.durable();
	m_MaxDurable = actorProj.maxdurable();

	init(actorProj.itemid());
	if (actorProj.itemid() == ITEM_BOMB || actorProj.itemid() == ITEM_CANNED_BOMB || actorProj.itemid() == ITEM_PINEAPPLE_BOMB)
	{
		m_ImpactTimeMark = 0;
	}

	m_ShootingActorID = actorProj.shooter();

	if (IsColorableItem(m_ItemID))
	{
		m_Color = actorProj.color();

		if (m_EntityModel)
		{
			Rainbow::ColourValue cv(1.0f, 1.0f, 1.0f, 1.0f);
			Rainbow::ColorQuad cq(m_Color);
			cv.r = cq.r / 255.0f;
			cv.g = cq.g / 255.0f;
			cv.b = cq.b / 255.0f;

			if (m_EntityModel->GetMainModel()) {
				m_EntityModel->SetOverlayColor(&cv);
				m_EntityModel->GetMainModel()->SetInstanceAmbient(cv);
			}
		}
	}

	playMotion(m_ProjectileDef->TailEffect.c_str());

	m_EnchantNum = actorProj.enchants_size();
	if (m_EnchantNum > MAX_ITEM_ENCHANTS)
		m_EnchantNum = MAX_ITEM_ENCHANTS;
	for (int i = 0; i < m_EnchantNum; i++)
	{
		m_Enchants[i] = actorProj.enchants(i);
	}

	m_Runedata.load(actorProj.runes(), actorProj.runes_size());

	m_nBlockID = actorProj.blockid();
	if (actorProj.has_blockpos())
	{
		const PB_Vector3& pos = actorProj.blockpos();
		m_BlockPos.setElement(pos.x(), pos.y(), pos.z());
	}
	ProjectileLocoMotion* loco = dynamic_cast<ProjectileLocoMotion*>(getLocoMotion());
	if (loco)
	{
		loco->m_UpdatePos = getLocoMotion()->getPosition().toWorldPos();
		loco->syncPos = getLocoMotion()->getPosition();
		if(actorProj.rotatequat_size() == 4)
		{
			loco->m_RotateQuat.x = actorProj.rotatequat(0) / 1000.0;
			loco->m_RotateQuat.y = actorProj.rotatequat(1) / 1000.0;
			loco->m_RotateQuat.z = actorProj.rotatequat(2) / 1000.0;
			loco->m_RotateQuat.w = actorProj.rotatequat(3) / 1000.0;
		}
		else
			loco->m_RotateQuat = AngleEulerToQuaternionf(Vector3f(loco->m_RotationPitch, loco->m_RotateYaw, 0));
		if (actorProj.prerotatequat_size() == 4)
		{
			loco->m_PrevRotateQuat.x = actorProj.prerotatequat(0) / 1000;
			loco->m_PrevRotateQuat.y = actorProj.prerotatequat(1) / 1000;
			loco->m_PrevRotateQuat.z = actorProj.prerotatequat(2) / 1000;
			loco->m_PrevRotateQuat.w = actorProj.prerotatequat(3) / 1000;
		}
		loco->m_CanTrigger = false;
		if (loco->needFullRotation())
		{
			m_ServerYawCmp = loco->m_RotateQuat.ToUInt32();
		}
		else
		{
			loco->syncYaw = loco->m_RotateYaw;
			loco->syncPitch = loco->m_RotationPitch;
		}
	}
	return 0;
}

std::string ClientActorProjectile::getFullyCustomModelKey()
{
	return m_FullySKey;
}

void ClientActorProjectile::changeModel(Rainbow::Model* model, Rainbow::Entity* entity)
{
	if (m_Model && entity)
	{
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_Model);
		if (model)
		{
			m_Model = model;
		}
	}
	if (entity)
	{
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_EntityModel);
		m_EntityModel = entity;
		if (m_EntityModel && getWorld())
		{
			m_EntityModel->AttachToScene(getWorld()->getScene());
		}
	}
}

bool ClientActorProjectile::playAct(int act, int playmode)
{
	m_TriggerAnimID = act;
	m_AnimMode = playmode;
	ItemDef* item = GetDefManagerProxy()->getItemDef(m_ItemID);
	if (item &&  item->MeshType == FULLY_CUSTOM_GEN_MESH)
	{
		if (m_EntityModel)
		{
			auto def = GetDefManagerProxy()->getTriggerActDef(act);
			if (def )
			{
				act = def->ActID;
				m_EntityModel->PlayAnim(act, playmode);
			}
			return true;
		}
	}
	return false;
}

bool ClientActorProjectile::isInfluence(int skillId, ClientActor* actor)
{
	if (actor == nullptr) {
		return false;
	}
	if (skillId == 335) //ò?±?1T×ó ???üID
	{
		//2?êüó°?ì
		if (actor->getObjType() == OBJ_TYPE_VACANTBOSS || actor->getObjType() == OBJ_TYPE_DRAGON
			|| actor->getObjType() == OBJ_TYPE_GIANT || actor->getObjType() == OBJ_TYPE_FLYSNAKEGOD)
		{
			return false;
		}
	}
	return true;
}

void ClientActorProjectile::processSkillOnInit(ClientActor * shooter /* = nullptr */)
{
	if (!m_EntityModel)
	{
		return;
	}

	ClientActor* curShooter = shooter;
	if (!curShooter)
	{
		curShooter = getShootingActor();
	}

	const ItemDef* def = GetDefManagerProxy()->getItemDef(m_ItemID);
	if (!def)
	{
		return;
	}
	bool hasProcessHideProjectile = false;
	for (int i = 0; i < (int)def->SkillID.size(); i++)
	{
		const ItemSkillDef* skilldef = GetDefManagerProxy()->getItemSkillDef(def->SkillID[i]);
		if (skilldef)
		{
			if (skilldef->AttackEffect.size() && curShooter && curShooter->getBody())
			{
				auto effectComponent = curShooter->getEffectComponent();
				if (effectComponent)
				{
					effectComponent->playBodyEffect((char*)skilldef->AttackEffect.c_str(), true, skilldef->AttackEffectTime);
					effectComponent->setBodyEffectScale((char*)skilldef->AttackEffect.c_str(), skilldef->AttackEffectSize);
				}
			}

			if (skilldef->AttackSound.size() && curShooter)
			{
				auto sound = curShooter->getSoundComponent();
				if (sound)
				{
					sound->playSound(skilldef->AttackSound.c_str(), 1.0f, 1.0f);
				}
			}

			// 隐藏投掷物
			bool showFlag = skilldef->ShowProjectile;
			if (!hasProcessHideProjectile)
			{
				hasProcessHideProjectile = true;
				// 处理隐藏
				if (!showFlag)
				{
					m_EntityModel->Show(false);
					if (m_EntityModel_Left)
					{
						m_EntityModel_Left->Show(false);
					}
				}
			}
		}
	}
}


void ClientActorProjectile::UpdateAttackPoints()
{
	// 近战、远程、元素伤害
	int damageType = m_ProjectileDef->DamageType;
	if (m_ProjectileDef->AttackType == 0)
	{
		if (damageType == 0 || damageType == 2 || damageType == 3)
		{
			assert(false && "Projectiledef damageType error!");
		}
		m_AttackPointsNew[damageType] = m_ProjectileDef->AttackValue;
		m_atkType = (1 << damageType);
	}
	// 独立爆炸伤害
	else
	{
		m_atkType = (1 << ATTACK_EXPLODE);
		if (damageType < 4)
		{
			m_ExplodePoints[0] = m_ProjectileDef->AttackValue;
		}
		else
		{
			m_ExplodePoints[damageType - 3] = m_ProjectileDef->AttackValue;
		}
	}
}

void ClientActorProjectile::UpdateAttackPointsByAttri(LivingAttrib* attri)
{
	if (attri == nullptr)
	{
		return;
	}

	for (int atkType = ATTACK_RANGE; atkType <= MAX_MAGIC_ATTACK; atkType++)
	{
		int atkPoints = attri->getAttackPoint((ATTACK_TYPE)atkType, 1);
		if (atkPoints > 0)
		{
			int index = AtkType2ArmorIndex(ATTACK_TYPE(atkType));

			if (m_ProjectileDef->AttackType == 0)
			{
				m_atkType |= (1 << index);
				m_AttackPointsNew[index] += atkPoints;
			}
			else
			{
				if (atkType < MAX_PHYSICS_ATTACK)
				{
					m_ExplodePoints[0] += atkPoints;
				}
				else
				{
					m_ExplodePoints[index - 3] += atkPoints;
				}
			}
		}
	}
}


