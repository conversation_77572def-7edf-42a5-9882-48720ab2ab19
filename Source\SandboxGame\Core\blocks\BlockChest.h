
#ifndef __BLOCKCHEST_H__
#define __BLOCKCHEST_H__

#include "BlockMaterial.h"

class ChestMaterial : public ModelBlockMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(ChestMaterial)
public:

	virtual void init(int resid) override;
	//tolua_begin
	ChestMaterial();
	virtual ~ChestMaterial();

	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh) override;
	virtual SectionMesh *createBlockProtoMesh(int protodata=0);

	virtual bool canPutOntoPos(WorldProxy *pworld, const WCoord &blockpos);
	virtual bool onTrigger(World *pworld, const WCoord &blockpos, DirectionType face, IClientPlayer *player, const Rainbow::Vector3f &colpoint=Rainbow::Vector3f(0, 0, 0));
	//virtual bool canOutputQuantityEnergy()
	//{
	//	return true;
	//}
	virtual int outputQuantityEnergy(World *pworld, const WCoord &blockpos, DirectionType dir);
	//virtual bool hasContainer() override
	//{
	//	return true;
	//}
	virtual WorldContainer *createContainer(World *pworld, const WCoord &blockpos) override;
	virtual void createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos) override;
	virtual void createPickData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos);
	virtual int convertDataByRotate(int blockdata, int rotatetype) override;
	virtual void onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player) override;
	void onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata) override;
	virtual int getBlockHP(World* pworld, const WCoord& blockpos) override;
	virtual bool onBlockDamaged(World* pworld, const WCoord& blockpos, IClientPlayer* player, int attack_type, float damage) override;
	//tolua_end
private:
	bool isThereANeighborChest(WorldProxy *pworld, const WCoord &blockpos);

	WCoord leftOnPlaceDir(const WCoord& blockpos, int placedir);
	WCoord rightOnPlaceDir(const WCoord& blockpos, int placedir);

	WCoord frontOnPlaceDir(const WCoord& blockpos, int placedir);
	WCoord backOnPlaceDir(const WCoord& blockpos, int placedir);
	WCoord rightLeanOnPlaceDir(const WCoord& blockpos, int placedir);

public:
	DECLARE_BLOCKMATERIAL_INSTANCE_BEGIN(ChestMaterial)
		DECLARE_BLOCKMATERIAL_INSTANCE_R_PARAM(R_Dir, int)
		DECLARE_BLOCKMATERIAL_INSTANCE_R_PARAM(R_MaxCount, int)		// �����������

		void SetBlockDir(const int& dir);
		int GetBlockDir() const;

		void SetMaxCount(const int& count);
		int GetMaxCount() const;
	DECLARE_BLOCKMATERIAL_INSTANCE_END(ChestMaterial)

}; //tolua_exports


#endif