#include "GridContainer.h"
#include "ActorBody.h"
#include "ClientActor.h"
//#include "ClientMob.h"
#include "MobAttrib.h"
#include "DropItemComponent.h"
#include "DefManagerProxy.h"
#include "container_backpack.h"
#include "special_blockid.h"
#include "ScriptComponent.h"
#include "SandboxEventDispatcherManager.h"
#include "ActorLocoMotion.h"
#include "ClientItem.h"
#include "ClientActorManager.h"
#include <random>
#include <ctime>
#include "world.h"

IMPLEMENT_COMPONENTCLASS(GridContainer)
GridContainer::GridContainer()
{

}
GridContainer::~GridContainer()
{

}

void  GridContainer::setGridInfo(int index, const jsonxx::Object& info)
{
	BaseGridContainer::setGridInfo(index, info);
	sendIndexChange(index, info);
	
}
void  GridContainer::setGridInfo(int index, BackPackGrid* data)
{
	BackPackGrid* grid = index2Grid(index);
	if (grid)
	{
		jsonxx::Object info;
		data->save(info);
		setGridInfo(index, info);
	}

}

void GridContainer::loadStrJson(const std::string& data)
{
	BaseGridContainer::loadStrJson(data);
}
void GridContainer::loadJson(const jsonxx::Array& data)
{
	BaseGridContainer::loadJson(data);
	sendAllChange(data);
}

void GridContainer::setGridsNum(int num)
{
	if(m_Grids.size() != num)
		m_Grids.resize(num);
	for (int i = 0; i < num; i++)
	{
		BackPackGrid* grid = index2Grid(i);
		if (grid)
		{
			grid->setIndex(i);
		}
	}
}
int GridContainer::getGridItemNum(int index)
{
	BackPackGrid* grid = index2Grid(index);
	if (grid)
	{
		return grid->getNum();
	}
	return 0;
}

void GridContainer::dropItems()
{
	ClientActor* actor = GetOwnerActor()->ToCast<ClientActor>();
	if (actor)
	{
		/*auto dropComponent = actor->GetComponent<DropItemComponent>();*/
		for (int i = 0; i < (int)m_Grids.size(); i++)
		{
			if (!m_Grids[i].isEmpty())
			{
				dropItem(m_Grids[i]);
				m_Grids[i].setItem(0, 0);
				jsonxx::Object info;
				m_Grids[i].save(info);
				sendIndexChange(i, info);
				//发送变化
			}
		}
	}

}

void GridContainer::drop(int index, int len)
{
	if (index + len > m_Grids.size() || index + len < 0 || index < 0 || len < 0)
	{
		return;
	}
	ClientActor* actor = GetOwnerActor()->ToCast<ClientActor>();
	if (actor)
	{
			for (int i = 0; i < len; ++i)
			{
				int realindex = index + i;

				if (!m_Grids[realindex].isEmpty())
				{
					dropItem(m_Grids[i]);
					m_Grids[realindex].setItem(0, 0);
					jsonxx::Object info;
					m_Grids[realindex].save(info);
					sendIndexChange(realindex, info);
				}
			}
	}
}


//随机掉落
void GridContainer::randomDropOneEquipItem()
{
	randomDropOne(0,static_cast<int>(m_Grids.size()));
}
void GridContainer::randomDropOne(int Index, int len)
{
	if (Index + len > m_Grids.size()|| Index + len < 0 || Index  < 0 || len < 0)
	{
		return;
	}

	std::vector<int> indexs;
	for (int i = 0; i < len; ++i)
	{
		if (!m_Grids[Index+i].isEmpty())
		{
			indexs.push_back(Index + i);
		}
	}
	if (indexs.size() > 0)
	{
		int seed = std::rand();
		std::default_random_engine gen(seed);
		std::uniform_int_distribution<int> distribution(0, indexs.size()-1);
		int result = distribution(gen);
		if (result < indexs.size())
		{
			ClientActor* actor = GetOwnerActor()->ToCast<ClientActor>();
			if (actor)
			{
					dropItem(m_Grids[indexs.at(result)]);
					m_Grids[indexs.at(result)].setItem(0, 0);
					jsonxx::Object info;
					m_Grids[indexs.at(result)].save(info);
					sendIndexChange(indexs.at(result), info);
			}
		}
	}
}

namespace SandboxEngine
{
        bool LessThan(const BackPackGrid& g1, const BackPackGrid& g2)
        {
                int id1 = g1.getItemID();
                int id2 = g2.getItemID();

                if (id1 == 0) return false;
                if (id2 == 0) return true;

                return id1 < id2;
        }
}

void GridContainer::sortGrid()
{

	std::vector<BackPackGrid>gridbak(m_Grids.size());
	for (size_t i = 0; i < m_Grids.size(); i++)
	{
		gridbak[i] = m_Grids[i];
	}

	mergePack(false);
	std::vector<int> index;
	for (size_t i = 0; i < m_Grids.size(); i++)
	{
		index.push_back(m_Grids[i].getIndex());
	}
	std::sort(m_Grids.begin(), m_Grids.end(), SandboxEngine::LessThan);
	//发送变化
	jsonxx::Array data = getJson();
	sendAllChange(data);
}

void GridContainer::mergePack(bool sendchange )
{
	for (size_t i = 0; i < m_Grids.size(); i++)
	{
		if (m_Grids[i].def == NULL || m_Grids[i].def->StackMax <= m_Grids[i].getNum()) continue;
		BackPackGrid& grid1 = m_Grids[i];
		size_t j = i + 1;
		for (; j < m_Grids.size(); j++)
		{
			BackPackGrid& grid2 = m_Grids[j];
			if (grid2.def == NULL) continue;
			if (grid1.def->ID == grid2.def->ID)
			{
				//染色方块，id相同但颜色不同时不合并 by：Jeff 2023/02/06
				if (grid1.userdata_str != grid2.userdata_str && IsDyeableBlock(grid1.def->ID))
				{
					continue;
				}
				int num = grid1.def->StackMax - grid1.getNum();
				if (num >= grid2.getNum())
				{
					num = grid2.getNum();
					grid2.setItem(0, 0);
				}
				else
				{
					grid2.addNum(-num);
				}

				grid1.addNum(num);

				if (grid1.getNum() >= grid1.def->StackMax) break;
			}
		}
	}
	if (sendchange)
	{
		jsonxx::Array data = getJson();

		sendAllChange(data);
	}
	
}

int GridContainer::addItemByIndex(const BackPackGrid* data, int beginindex, int lenth)
{
	if (beginindex > m_Grids.size()||(beginindex + lenth) > m_Grids.size()|| beginindex < 0|| lenth<0)
	{
		return 0;
	}

	return InsertItemIntoArray(beginindex, lenth, data);
}

int GridContainer::InsertItemToSameGrids(int baseindex, int arraylen, int resid, int num)
{
	const ItemDef* def = GetDefManagerProxy()->getItemDef(resid);
	if (num <= 0 || def == NULL) return 0;

	int sum = 0;
	for (int i = 0; i < arraylen; i++)
	{
		BackPackGrid* grid = index2Grid(baseindex+i);
		if (grid && (grid->getItemID() == resid))
		{
			int putnum = def->StackMax - grid->getNum();
			if (putnum > num) putnum = num;
			if (grid->getIndex() >= BUILDBLUEPRINT_START_INDEX && grid->getIndex() < BUILDBLUEPRINT_START_INDEX + 1000)
				putnum = num;
			if (putnum > 0)
			{
				grid->addNum(putnum);
				jsonxx::Object info;
				grid->save(info);
				sendIndexChange(baseindex + i, info);
				num -= putnum;
				sum += putnum;
			}
			if (num == 0) break;
		}
	}
	return sum;
}

int GridContainer::InsertItemToEmptyGrids( int baseindex, int arraylen, const BackPackGrid* data, int* emptyIndex)
{

	int num = data->getNum();
	const GridRuneData* rune = &(data->getRuneData());
	// 是否有新符文石附魔
	bool hasTunestoneRune = false;

	const ItemDef* def = GetDefManagerProxy()->getItemDef(data->getItemID());
	if (num <= 0 || def == NULL) return 0;

	int sum = 0;
	for (int i = 0; i < arraylen; i++)
	{
		BackPackGrid* grid = index2Grid(baseindex + i);
		if (grid && grid->isEmpty())
		{
			int putnum = def->StackMax;
			if (putnum > num) putnum = num;
			if (grid->getIndex() >= BUILDBLUEPRINT_START_INDEX && grid->getIndex() < BUILDBLUEPRINT_START_INDEX + 1000)
				putnum = num;

			if (putnum > 0)
			{
				if (emptyIndex)
				{
					*emptyIndex = i;
				}

				SetBackPackGrid(*grid, data->getItemID(), putnum, data->getDuration(), data->getToughness());

				num -= putnum;
				sum += putnum;
				if (data->getNumEnchant() > 0)
				{
					grid->setEnchants(data->getNumEnchant(), data->getEnchants());
				}
				if (data->getRuneNum() > 0)
				{
					int num = sizeof(data->getRuneNum()) / sizeof(int);
					const GridRuneData& rundata = data->getRuneData();
					grid->getRuneData().setdata(rundata);
				}

				grid->userdata = data->userdata;
				if (!data->userdata_str.empty())
					grid->userdata_str = data->userdata_str;
				else
					grid->userdata_str = "";



				if (!data->m_effects.empty())
				{
					grid->m_effects.clear();
					grid->m_effects = data->m_effects;
				}

				if (data->getDataComponentsPtr() && data->getDataComponentsPtr()->size() > 0)
				{
					grid->copyDataDataComponentsFrom(*(data->getDataComponentsPtr()));
				}
				jsonxx::Object info;
				grid->save(info);
				sendIndexChange(baseindex + i, info);
			}

			if (num == 0) break;
		}
	}

	return sum;
}

int GridContainer::InsertItemToSameGridsWithUserData( int baseindex, int arraylen, int resid, int num, const char* userdata_str)
{
	const ItemDef* def = GetDefManagerProxy()->getItemDef(resid);
	if (num <= 0 || def == NULL) return 0;

	int sum = 0;
	for (int i = 0; i < arraylen; i++)
	{
		BackPackGrid* grid = index2Grid(baseindex+i);
		if (grid->getItemID() == resid && grid->userdata_str == userdata_str)
		{
			int putnum = def->StackMax - grid->getNum();
			if (putnum > num) putnum = num;
			if (grid->getIndex() >= BUILDBLUEPRINT_START_INDEX && grid->getIndex() < BUILDBLUEPRINT_START_INDEX + 1000)
				putnum = num;
			if (putnum > 0)
			{
				grid->addNum(putnum);
				jsonxx::Object info;
				grid->save(info);
				sendIndexChange(baseindex+i, info);
				num -= putnum;
				sum += putnum;
			}
			if (num == 0) break;
		}
	}
	return sum;
}

int GridContainer::InsertItemIntoArray(int baseindex, int arraylen, const BackPackGrid* data)
{

	int sum = 0;
	//染色方块根据id和userdata_str判断是否堆叠 by:Jeff 2022/12/14
	if (IsDyeableBlock(data->getItemID()))
	{
		sum = InsertItemToSameGridsWithUserData(baseindex, arraylen, data->getItemID(), data->getNum(), data->userdata_str.c_str());
	}
	else
	{
		sum = InsertItemToSameGrids(baseindex, arraylen, data->getItemID(), data->getNum());
	}

	if (sum < data->getNum())
	{
		BackPackGrid temp(*data);
		temp.setNum(data->getNum() - sum);
		sum += InsertItemToEmptyGrids(baseindex, arraylen, &temp);
	}
	return sum;
}



int  GridContainer::addItemLua(int itemid, int num, int durable, int toughness, void* userdata, const char* userdata_str, jsonxx::Value* enchant)
{
	if (enchant)
	{
		if (enchant->type_ == jsonxx::Value::ARRAY_)
		{
			jsonxx::Array* array = enchant->array_value_;
			int anum = static_cast<int>(array->size());
			int c_array[10];  //如果万一附魔等级超过10个，需要加大这个值,以及循环里的条件
			for (int i = 0; i < anum && i < 10; i++)
			{
				c_array[i] = array->get<jsonxx::Number>(i);
			}

			BackPackGrid grid;
			grid.setItem(itemid, num, durable, toughness, userdata, 1, 0, userdata_str);
			grid.setEnchants(anum, c_array);
			return addItemByGrid(&grid);
		}
	}

	BackPackGrid grid;
	grid.setItem(itemid, num, durable, toughness, userdata, 1, 0, userdata_str);
	return addItemByGrid(&grid);
}


int  GridContainer::addItemByGrid(const BackPackGrid* data)
{
	int sum = 0;
	if (data)
	{
		int itemid = data->getItemID();
		int num = data->getNum();
		std::string userdata_str = data->userdata_str;
		if (IsDyeableBlock(itemid))
		{
			const ItemDef* def = GetDefManagerProxy()->getItemDef(itemid);
			if (num <= 0 || def == NULL) return 0;
			int sum = 0;
			for (int i = 0; i < m_Grids.size(); i++)
			{
				BackPackGrid& grid = m_Grids[i];
				if (grid.getItemID() == itemid && grid.userdata_str == userdata_str)
				{
					int putnum = def->StackMax - grid.getNum();
					if (putnum > num) putnum = num;
					if (putnum > 0)
					{
						grid.addNum(putnum);
						jsonxx::Object info;
						grid.save(info);
						sendIndexChange(i, info);
						num -= putnum;
						sum += putnum;
					}
					if (num == 0) break;
				}
			}
			return sum;


		}
		else
		{
			const ItemDef* def = GetDefManagerProxy()->getItemDef(itemid);
			if (num <= 0 || def == NULL) return 0;
			for (int i = 0; i < m_Grids.size(); i++)
			{
				BackPackGrid& grid = m_Grids[i];
				if (grid.getItemID() == itemid)
				{
					int putnum = def->StackMax - grid.getNum();
					if (putnum > num) putnum = num;
					if (putnum > 0)
					{
						grid.addNum(putnum);
						jsonxx::Object info;
						grid.save(info);
						sendIndexChange(i, info);
						num -= putnum;
						sum += putnum;
					}

					if (num == 0) break;
				}
			}
		}

		if (sum < num)
		{
			num = num - sum;
			const GridRuneData rune = data->getRuneData();
			// 是否有新符文石附魔
			bool hasTunestoneRune = false;

			const ItemDef* def = GetDefManagerProxy()->getItemDef(itemid);
			if (num <= 0 || def == NULL) return 0;
			for (int i = 0; i < m_Grids.size(); i++)
			{
				BackPackGrid& grid = m_Grids[i];
				if (grid.isEmpty())
				{
					int putnum = def->StackMax;
					if (putnum > num) putnum = num;

					if (putnum > 0)
					{
						grid.setItem(itemid, putnum, data->getDuration(), data->getToughness());

						num -= putnum;
						sum += putnum;
						grid.getRuneData().reset();
						if (data->getNumEnchant() > 0)
						{
							grid.setEnchants(data->getNumEnchant(), data->getEnchants());
						}
						if (data->getRuneNum() > 0)
						{
							int num = sizeof(data->getRuneNum()) / sizeof(int);
							const GridRuneData& rundata = data->getRuneData();
							grid.getRuneData().setdata(rundata);
						}

						grid.userdata = data->userdata;
						if (!data->userdata_str.empty())
							grid.userdata_str = data->userdata_str;
						else
							grid.userdata_str = "";


						if (!data->m_effects.empty())
						{
							grid.m_effects.clear();
							grid.m_effects = data->m_effects;
						}

						if (data->getDataComponentsPtr() && data->getDataComponentsPtr()->size() > 0)
						{
							grid.copyDataDataComponentsFrom(*(data->getDataComponentsPtr()));
						}
						jsonxx::Object info;
						grid.save(info);
						sendIndexChange(i, info);
					}

					if (num == 0) break;
				}
			}

		}
	}

	if (sum > 0)
	{
		m_bModelDirtyForChunk = true;
	}
	return sum;
}
int GridContainer::addItembyGridCopyData(const GridCopyData& grid)
{
	int sum = 0;
	//染色方块根据id和userdata_str判断是否堆叠 by:Jeff 2022/12/14
	if (IsDyeableBlock(grid.resid))
	{
		const ItemDef* def = GetDefManagerProxy()->getItemDef(grid.resid);
		if (grid.num <= 0 || def == NULL)
		{
			sum = 0;
		}
		else
		{
			int num = grid.num;
			for (int i = 0; i < m_Grids.size(); i++)
			{
				BackPackGrid& lgrid = m_Grids[i];
				if (lgrid.getItemID() == grid.resid && grid.userdata_str == grid.userdata_str)
				{
					int putnum = def->StackMax - lgrid.getNum();
					if (putnum > num) putnum = num;

					if (putnum > 0)
					{
						lgrid.addNum(putnum);
						jsonxx::Object info;
						lgrid.save(info);
						sendIndexChange(i, info);
						num -= putnum;
						sum += putnum;
					}

					if (num == 0) break;
				}
			}
		}
	}
	else
	{
		int num = grid.num;
		const ItemDef* def = GetDefManagerProxy()->getItemDef(grid.resid);
		if (num <= 0 || def == NULL)
		{
			sum = 0;
		}
		else
		{
			for (int i = 0; i < m_Grids.size(); i++)
			{
				BackPackGrid& lgrid = m_Grids[i];
				if (lgrid.getItemID() == grid.resid)
				{
					int putnum = def->StackMax - lgrid.getNum();
					if (putnum > num) putnum = num;

					if (putnum > 0)
					{
						lgrid.addNum(putnum);
						jsonxx::Object info;
						lgrid.save(info);
						sendIndexChange(i, info);
						num -= putnum;
						sum += putnum;
					}

					if (num == 0) break;
				}
			}
		}
	}

	if (sum < grid.num)
	{
		GridCopyData tmp(grid);
		tmp.num = grid.num - sum;
		int num = tmp.num;
		const GridRuneData* rune = tmp.runedata;
		// 是否有新符文石附魔
		bool hasTunestoneRune = false;

		const ItemDef* def = GetDefManagerProxy()->getItemDef(tmp.resid);
		if (num <= 0 || def == NULL) return 0;

		sum += 0;
		for (int i = 0; i < m_Grids.size(); i++)
		{
			BackPackGrid& lgrid = m_Grids[i];
			if (lgrid.isEmpty())
			{
				int putnum = def->StackMax;
				if (putnum > num) putnum = num;

				if (putnum > 0)
				{

					SetBackPackGrid(lgrid, tmp.resid, putnum, tmp.duration, tmp.toughness);

					num -= putnum;
					sum += putnum;

					if (tmp.enchantnum > 0)
					{
						lgrid.setEnchants(tmp.enchantnum, tmp.enchants);
					}
					else if (tmp.tunestonenum > 0)
					{
						int num = sizeof(tmp.tunestones) / sizeof(int);
						int tunestonenum = Rainbow::Min(num, tmp.tunestonenum);
						for (int i = 0; i < tunestonenum; i++)
						{
							int rune_id = tmp.tunestones[i];
							auto* runDef = GetDefManagerProxy()->getRuneDef(rune_id);
							if (runDef)
							{
								float runeVal0 = 0.0;
								float runeVal1 = 0.0;
								MNSandbox::SandboxResult result = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("Rune_getRandomVals",
									MNSandbox::SandboxContext(nullptr).SetData_Usertype("def", runDef));
								if (result.IsExecSuccessed())
								{
									runeVal0 = (int)result.GetData_Number("val0");
									runeVal1 = (int)result.GetData_Number("val1");
									GridRuneItemData data(rune_id, runeVal0, runeVal1, 11612);
									lgrid.addRune(data);
									hasTunestoneRune = true;
								}
							}
						}

					}

					lgrid.userdata = tmp.userdata;
					if (!tmp.userdata_str.empty())
						lgrid.userdata_str = tmp.userdata_str;
					else
						lgrid.userdata_str = "";

					// 拷贝符文信息可能会冲掉自带的新符文石附魔
					if (rune) {//拷贝符文信息
						lgrid.getRuneData().setdata(*rune);
					}
					else if (!hasTunestoneRune)
					{
						lgrid.getRuneData().reset();
					}

					if (!tmp.effects.empty())
					{
						lgrid.m_effects.clear();
						lgrid.m_effects = tmp.effects;
					}

					if (tmp.componentsPtr && tmp.componentsPtr->size() > 0)
					{
						lgrid.copyDataDataComponentsFrom(*tmp.componentsPtr);
					}
					jsonxx::Object info;
					lgrid.save(info);
					sendIndexChange(i, info);
				}

				if (num == 0) break;
			}
		}
	}
	return sum;

}

int GridContainer::addItem(jsonxx::Object* info)
{
	if (info)
	{
		std::string s = info->json();
		BackPackGrid grid;
		grid.load(*info);
		return addItemByGrid(&grid);
	}
	return 0;
}

void GridContainer::removeItemByCount(int itemid, int count)
{
	for (size_t i = 0; i < m_Grids.size(); i++)
	{
		BackPackGrid& grid = m_Grids[i];
		if (grid.getItemID() == itemid)
		{
			if (count >= grid.getNum())
			{
				count -= grid.getNum();
				grid.addNum(-grid.getNum());
				grid.clear();
				jsonxx::Object info;
				grid.save(info);
				sendIndexChange(i, info);
			}
			else
			{
				grid.addNum(-count);
				jsonxx::Object info;
				grid.save(info);
				sendIndexChange(i, info);
				return;
			}
		}
	}
}

void GridContainer::LoadComponentData(const jsonxx::Object& componentData, bool fromArchive)
{
	ClientActor* actor = GetOwnerActor()->ToCast<ClientActor>();
	if (!actor || !actor->getWorld())
	{
		return;
	}

	if (actor->getWorld()->isRemoteMode())
		return;


	if (componentData.has<jsonxx::Object>("properties"))
	{
		const jsonxx::Object& properties = componentData.get<jsonxx::Object>("properties");
		if (properties.has<jsonxx::Number>("GriSize"))
		{
			int size = properties.get<jsonxx::Number>("GriSize");
			setGridsNum(size);
		}
	}


	
	//加载格子数据
	if (fromArchive)
	{
		//格子数据
		if (componentData.has<jsonxx::Array>("gridinfo")) {
			const jsonxx::Array& jsonArr = componentData.get<jsonxx::Array>("gridinfo");
			size_t num = jsonArr.size();
			setGridsNum(num);
			m_bModelDirtyForChunk = true;
			for (size_t i = 0; i < num; i++)
			{
				if (jsonArr.values().at(i)->is<jsonxx::Object>())
				{
					const jsonxx::Object& jaObj = jsonArr.get<jsonxx::Object>(i);
					BackPackGrid* grid = index2Grid(i);
					if (grid)
					{
						grid->load(jaObj);
					}
				}
			}
		}
	}
}

jsonxx::Object* GridContainer::GetComponentData()
{
	if (m_bModelDirtyForChunk)
	{
		CreateComponentData(m_cahcedComponentData);
		m_bModelDirtyForChunk = false;
	}
	
	return &m_cahcedComponentData;
}
void  GridContainer::CreateComponentData(jsonxx::Object& componentData)
{
	if (componentData.has<jsonxx::Array>("gridinfo"))
	{
		jsonxx::Array& info = componentData.get<jsonxx::Array>("gridinfo");
		getJson(info);
	}
	else
	{
		jsonxx::Array info;
		getJson(info);
		componentData << "gridinfo" << info;
	}
}


void GridContainer::OnBindGameObject(MNSandbox::GameObject* owner)
{
	ClientActor* Actor = dynamic_cast<ClientActor*>(owner);
	if (Actor)
	{
		Actor->BindActorGridComponent(this);
	}
}
void GridContainer::OnUnbindGameObject(MNSandbox::GameObject* owner)
{
	ClientActor* Actor = dynamic_cast<ClientActor*>(owner);
	if (Actor)
	{
		Actor->BindActorGridComponent(nullptr);
	}
}

int  GridContainer::addItemByCount(int itemid, int count)
{
	BackPackGrid grid;
	grid.setItem(itemid, count);
	return addItemByGrid(&grid);
}

void  GridContainer::sendIndexChange(int index, const jsonxx::Object& info)
{
	auto pScriptComponent = GetOwnerActor()->ToCast<ClientActor>()->getScriptComponent();
	if (pScriptComponent)
	{
		pScriptComponent->OnEvent((int)CE_OnGridChange, true, index);
	}
	m_bModelDirtyForChunk = true;
}

void  GridContainer::sendAllChange(const jsonxx::Array& info)
{
}
