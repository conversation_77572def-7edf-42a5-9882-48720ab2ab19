#include "TouchControl.h"
#include "PlayerControl.h"
#include "ActorLocoMotion.h"
#include "GameCamera.h"
#include "ClientInfoProxy.h"
#include "BlockMaterialMgr.h"
#include "ClientAccount.h"
#include "defmanager.h"
#include "ClientMob.h"
#include "StringDefCsv.h"
#include "IWorldConfig.h"
#include "ItemIconManager.h"
#include "Platforms/PlatformInterface.h"
#include "Input/OgreInputManager.h"
#include "OgreTimer.h"
#include "OgreScriptLuaVM.h"
#include "ui_framemgr.h"
#include "ui_scriptfunc.h"
#include "xml_uimgr.h"
#include <math.h>
#include "InputInfo.h"
#include "special_blockid.h"
#include "ActorBody.h"
#include "Entity/OgreEntity.h"
#include "VehicleControlInputs.h"
#include "container_driverseat.h"
#include "UILib/ui_scriptfunc.h"
#include "ObserverEvent.h"
#include "ObserverEventManager.h"
#include "container_peristele.h"
#include "BlockDefCsv.h"
#include "ToolDefCsv.h"

#include "SandboxObject.h"
#include "SandboxCoreDriver.h"
#include "SandboxEventDispatcherManager.h"
#include "ClientInfo.h"
#include "GameUI.h"
#include "Input/InputManager.h"
#include "Graphics/ScreenManager.h"
#include "GameStatic.h"

#include "UGCTouchControl.h"
#include "UGCModeManager.h"
#include "ItemDefCsv.h"
#include "UIRenderer.h"
#include "WorldManager.h"
#include "EditorSelect.h"
#include "EditorSelectCoordInteract.h"

USE_NS_SANDBOX;
using namespace MINIW;
using namespace Rainbow;
using namespace Rainbow::UILib;

float Angle2(const Vector2f& lhs, const Vector2f& rhs)
{
	float MagnitudeLhs = Sqrt(DotProduct(lhs, lhs));
	float MagnitudeRhs = Sqrt(DotProduct(rhs, rhs));
	return ::acos((std::min)(1.0f, (std::max)(-1.0f, DotProduct(lhs, rhs) / MagnitudeLhs * MagnitudeRhs)));
}

const int mCheckTapDistance = 20;
const double mCheckLongTapTime = 0.5;

extern bool IsInGunReloadCircle(int x, int y, float scale);

int UGCXFromRight(int x)
{
	return GetGameUIPtr()->GetSafeArea().GetRight() - int(x * UILib::GetScreenUIScale());
}
int UGCYFromBottom(int y)
{
	return GetGameUIPtr()->GetSafeArea().GetBottom() - int(y * UILib::GetScreenUIScale());
}

//extern inline int XFromRight(int x);
//extern inline int YFromBottom(int y);

bool UGCIsInGunReloadCircle(int x, int y, float scale)
{
	return false;

	//int cx = UGCXFromRight(239);
	//int cy = UGCYFromBottom(400);

	//return int(Sqrt((float)(x - cx)*(x - cx) + (y - cy)*(y - cy))) < 70 * scale;
}

bool UGCIsInPassOrCatchBallCirle(int x, int y, float scale)
{
	int cx = UGCXFromRight(66);
	int cy = UGCYFromBottom(233);

	return (int(Sqrt((float)((x - cx) * (x - cx) + (y - cy) * (y - cy)))) < 70 * scale);
}

UGCTouchControl::UGCTouchControl()
{
	m_eDragType = UGCDragType_NULL;
	m_fLastDistance = 0;
	m_nHandleUICount = 0;
	m_fTouchDownTime = 0;
	m_fTriggerLongPressTime = 0;
	m_bLongPressBreaked = true;
	m_rockerPos = MINIW::Point2D(250, UGCYFromBottom(190));
	m_flyBtnPos = MINIW::Point2D(230, 332);
	m_rockerWidth = 144;
	m_rockerHeight = 144;
	m_ugcTouchRes = UIRenderer::GetInstance().CreateTexture("ui/mobile/texture0/sceneeditor.png");
	m_ugcTouchMaterial = UIRenderer::GetInstance().CreateInstance();
	m_ugcTouchRockerMaterial = UIRenderer::GetInstance().CreateInstance();
	m_ugcTouchFlyBtnMaterial = UIRenderer::GetInstance().CreateInstance();
	m_RockerResBg = UIRenderer::GetInstance().CreateTexture("ui/mobile/texture0/bigtex/ugc_rocker_bg.png");
	m_FlyBtnResBg = UIRenderer::GetInstance().CreateTexture("ui/mobile/texture0/bigtex/ugc_fly_bg.png");
}

UGCTouchControl::~UGCTouchControl()
{

}

bool UGCTouchControl::isInUIArea(const Rainbow::InputEvent & inevent)
{
	//printf_console("HandleUIClickDown:");
	bool bHandled = true;
	int x = inevent.mousePosition.x;
	int y = inevent.mousePosition.y;

	int dx = x;
	const int screenHeight = GetGameUIPtr()->GetHeight();
	int dy = screenHeight - y;
	int direction = 0;

	Point2D rockerPos = m_rockerPos;

	int flyType = UGCCheckFlyArea(x, y, m_ScreenScaleFactor);

	if (flyType != 0)
	{
	}
	/*else if (IsInControl(x, y, direction) && !isRockerMode())
	{
	}*/
	else if (IsInJumpCircle(x, y, m_ScreenScaleFactor) && m_JumpID < 0 && m_showOperateUI && !m_bHideJumpUi)
	{
	}
	else if (m_ShowUseBtn && IsInUseCircle(x, y, m_ScreenScaleFactor) && m_UseID < 0)
	{
	}
	else if (m_ShowGunUseBtn && UGCIsInGunReloadCircle(x, y, m_ScreenScaleFactor) && m_ReloadID < 0)
	{
	}
	else if ((m_ShowBallUseBtn || m_ShowBasketBallUseBtn || m_ShowPushSnowBallUseBtn) && !m_bHideActionUi && UGCIsInPassOrCatchBallCirle(x, y, m_ScreenScaleFactor) && m_PassOrCatchBallID < 0)
	{
	}
	else if (IsInRockerArea(dx, dy))
	{

	}
	else
	{
		bHandled = false;
	}

	return bHandled;
}

void UGCTouchControl::SetRockerPos(int x, int y)
{
	m_rockerPos.x = x;
	m_rockerPos.y = UGCYFromBottom(y);
	//m_rockerPos = MINIW::Point2D(x, UGCYFromBottom(y));
}

void UGCTouchControl::SetFlyBtnPos(int x, int y)
{
	m_flyBtnPos.x = x;
	m_flyBtnPos.y = y;
	//m_flyBtnPos = MINIW::Point2D(x, y);
}

void UGCTouchControl::OnTouchCancel(const Rainbow::InputEvent & event)
{
	if (!g_pPlayerCtrl)
		return;
	int nCount = DelTouchPoint(event.button);
	m_RotateID = -1;

	if (HandleUIClickUp(event))
	{
		m_nHandleUICount--;
	}
	else
	{
		HandleSceneClickUp(event);
	}
}

void UGCTouchControl::renderRockerOperateUI()
{
	float scale = UILib::GetScreenUIScale();
	UIRenderer& uiRenderer = UIRenderer::GetInstance();
	XMLManager& xmlManager = GetGameUIPtr()->GetXMLManager();
	Point2D rockerPos = m_rockerPos;

	if (!UGCModeManager::getSingleton()->IsRunning() && UGCModeManager::getSingleton()->GetGameType() == UGCGAMETYPE::UGCGAMETYPE_HIGH)
	{
		//�߼��༭ģʽ����ʽ
		{
			int bgw = int(m_rockerWidth *scale);
			int bgh = int(m_rockerHeight * scale);
			int btnw = int(48 * scale);
			unsigned int color;
			Point2D pt1, pt2;


			if (m_MoveID >= 0)
			{
				pt1 = m_MoveStart;
				pt2 = m_MoveStart;// CalMovePointInRange(m_MoveStart, m_MovePos, (bgh - btnw) / 2);
				color = 0xffffffff;
				//color = 0xb2ffffff;
			}
			else
			{//ҡ�����ĵ�
				pt1 = Point2D(int(rockerPos.x * scale), rockerPos.y);
				pt2 = Point2D(int(rockerPos.x * scale), rockerPos.y);
				color = 0xffffffff;
				//color = 0x66ffffff;
			}

			if (TouchControl::isShowRocker())
			{
				uiRenderer.BeginDraw(m_ugcTouchRockerMaterial, m_RockerResBg);
				uiRenderer.StretchRect((float)(pt1.x - bgw / 2), (float)(pt1.y - bgw / 2), (float)bgw, (float)bgh, color);
				uiRenderer.EndDraw();
				uiRenderer.BeginDraw(m_ugcTouchMaterial, m_ugcTouchRes);
				//auto element = xmlManager.requestPackElement("ui/mobile/texture0/sceneeditor.xml", "ugc_rocker_bg.png");
				//if (element)
				//	uiRenderer.StretchRect((float)(pt1.x - bgw / 2), (float)(pt1.y - bgw / 2), (float)bgw, (float)bgh, color, element->x, element->y, element->w, element->h);

				auto element = xmlManager.requestPackElement("ui/mobile/texture0/sceneeditor.xml", "ugc_rocker_point.png");
				if (element)
					uiRenderer.StretchRect((float)(pt2.x - btnw / 2), (float)(pt2.y - btnw / 2), (float)btnw, (float)btnw, color, element->x, element->y, element->w, element->h);

				if (m_MoveID >= 0)
				{
					Rainbow::Vector2f direct(m_MovePos.x - m_MoveStart.x, m_MoveStart.y - m_MovePos.y);
					float angle = Rainbow::Rad2Deg(Rainbow::Angle(direct, Vector2f(0, 1)));
					if (CrossProduct(direct, Vector2f(0, 1)) < 0)
					{
						angle = -angle;
					}
					element = xmlManager.requestPackElement("ui/mobile/texture0/sceneeditor.xml", "ugc_rocker_light.png");
					if (element)
						uiRenderer.StretchRect((float)(pt2.x - btnw / 2), (float)(pt2.y - btnw / 2), (float)btnw, (float)btnw, color, element->x, element->y, element->w, element->h, Rainbow::UILib::UI_UVT_NORMAL, angle);
				}

				uiRenderer.EndDraw();
			}
		}
	}
	else
	{
		uiRenderer.BeginDraw(m_TouchRes4Material, m_TouchRes4);
		{
			int bgw = int(m_DpadWidth *scale);
			int bgh = int(m_DpadHeight * scale);
			int btnw = int(64 * scale);
			int arroww = int(35 * scale);
			int arrowh = int(18 * scale);
			unsigned int color;
			Point2D pt1, pt2;


			if (m_MoveID >= 0)
			{
				pt1 = m_MoveStart;
				pt2 = CalMovePointInRange(m_MoveStart, m_MovePos, (bgh - btnw) / 2);
				color = 0xb2ffffff;
			}
			else
			{//ҡ�����ĵ�
				pt1 = Point2D(int(rockerPos.x * scale), rockerPos.y);
				pt2 = Point2D(int(rockerPos.x * scale), rockerPos.y);
				color = 0x66ffffff;
			}

			if (TouchControl::isShowRocker())
			{
				auto element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "operating_steering_wheel.png");
				if (element)
					uiRenderer.StretchRect((float)(pt1.x - bgw / 2), (float)(pt1.y - bgw / 2), (float)bgw, (float)bgh, color, element->x, element->y, element->w, element->h);

				element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "img_steering_wheel.png");
				if (element)
					uiRenderer.StretchRect((float)(pt2.x - btnw / 2), (float)(pt2.y - btnw / 2), (float)btnw, (float)btnw, color, element->x, element->y, element->w, element->h);
			}
		}
		uiRenderer.EndDraw();
	}
}

void UGCTouchControl::renderFlyOperateUI()
{
	const float scale = UILib::GetScreenUIScale();
	UIRenderer& uiRenderer = UIRenderer::GetInstance();
	XMLManager& xmlManager = GetGameUIPtr()->GetXMLManager();
	Point2D flyPos = m_flyBtnPos;
	if (UGCModeManager::getSingleton()->GetGameType() != UGCGAMETYPE::UGCGAMETYPE_HIGH || UGCModeManager::getSingleton()->IsRunning())
	{
		flyPos = MINIW::Point2D(109, 332);
	}

	if (UGCModeManager::getSingleton()->GetGameType() == UGCGAMETYPE::UGCGAMETYPE_HIGH && GetWorldManagerPtr() && GetWorldManagerPtr()->isUGCEditMode())
	{
		//�߼��༭ģʽ����ʽ
		int x = UGCXFromRight(flyPos.x);
		int y = UGCYFromBottom(flyPos.y);
		uiRenderer.BeginDraw(m_ugcTouchFlyBtnMaterial, m_FlyBtnResBg);
		uiRenderer.StretchRect((float)x, (float)y, 64 * scale, 160 * scale, 0xffffffff);
		uiRenderer.EndDraw();

		//uiRenderer.BeginDraw(m_ugcTouchRes);
		//auto element = xmlManager.requestPackElement("ui/mobile/texture0/sceneeditor.xml", "ugc_fly_bg.png");
		//if (element)
		//	uiRenderer.StretchRect((float)x, (float)y, 64 * scale, 160 * scale, 0xffffffff, element->x, element->y, element->w, element->h);

		//y = UGCYFromBottom(m_flyBtnPos.y - 106);

		//element = xmlManager.requestPackElement("ui/mobile/texture0/sceneeditor", "img_board_res_1.png");
		//if (element)
		//	uiRenderer.StretchRect((float)x, (float)y, 102 * scale, 107 * scale, 0xffffffff, element->x, element->y, element->w, element->h, UI_UVT_MIRROEV);
		uiRenderer.BeginDraw(m_ugcTouchMaterial, m_ugcTouchRes);

		const char *pngName1 = "ugc_fly_up.png";
		const char *pngName2 = "ugc_fly_down.png";

		x = UGCXFromRight(flyPos.x - 17);
		y = UGCYFromBottom(flyPos.y - 27);

		auto element = xmlManager.requestPackElement("ui/mobile/texture0/sceneeditor.xml", pngName1);
		if (element)
			uiRenderer.StretchRect((float)x, (float)y, 26 * scale, 19 * scale, 0xffffffff, element->x, element->y, element->w, element->h);

		y = UGCYFromBottom(flyPos.y - 114);

		element = xmlManager.requestPackElement("ui/mobile/texture0/sceneeditor.xml", pngName2);
		if (element)
			uiRenderer.StretchRect((float)x, (float)y, 26 * scale, 19 * scale, 0xffffffff, element->x, element->y, element->w, element->h);

		uiRenderer.EndDraw();
	}
	else
	{
		uiRenderer.BeginDraw(m_TouchRes4Material, m_TouchRes4);
		int x = UGCXFromRight(flyPos.x);
		int y = UGCYFromBottom(flyPos.y);

		auto element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "img_board_res_1.png");
		if (element)
			uiRenderer.StretchRect((float)x, (float)y, 102 * scale, 107 * scale, 0xffffffff, element->x, element->y, element->w, element->h);

		y = UGCYFromBottom(flyPos.y - 106);

		element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "img_board_res_1.png");
		if (element)
			uiRenderer.StretchRect((float)x, (float)y, 102 * scale, 107 * scale, 0xffffffff, element->x, element->y, element->w, element->h, UI_UVT_MIRROEV);

		const char *pngName1 = "icon_up_down.png";
		const char *pngName2 = "icon_up_down.png";

		x = UGCXFromRight(flyPos.x - 29);
		y = UGCYFromBottom(flyPos.y - 32);

		element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", pngName1);
		if (element)
			uiRenderer.StretchRect((float)x, (float)y, 42 * scale, 26 * scale, 0xffffffff, element->x, element->y, element->w, element->h);

		y = UGCYFromBottom(flyPos.y - 160);

		element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", pngName2);
		if (element)
			uiRenderer.StretchRect((float)x, (float)y, 42 * scale, 26 * scale, 0xffffffff, element->x, element->y, element->w, element->h, UI_UVT_TURN180);
		uiRenderer.EndDraw();
	}
}

int UGCTouchControl::UGCCheckFlyArea(int x, int y, float scale)
{
	MINIW::Point2D flyPos = m_flyBtnPos;

	if (UGCModeManager::getSingleton()->GetGameType() != UGCGAMETYPE::UGCGAMETYPE_HIGH || UGCModeManager::getSingleton()->IsRunning())
	{
		flyPos = MINIW::Point2D(109, 332);
	}

	int tlx = UGCXFromRight(flyPos.x + 4);
	int tly = UGCYFromBottom(flyPos.y + 3);

	if (UGCModeManager::getSingleton()->GetGameType() == UGCGAMETYPE::UGCGAMETYPE_HIGH && UGCModeManager::getSingleton()->IsEditing())
	{
		//�߼��༭ģʽ����ʽ
		if (x >= tlx && x <= tlx + 68 * scale && y >= tly && y <= tly + 83 * scale)
		{
			return 1;
		}

		tly = UGCYFromBottom(flyPos.y - 114);
		if (x >= tlx && x <= tlx + 68 * scale && y >= tly && y <= tly + 114 * scale)
		{
			return -1;
		}
	}
	else
	{
		tlx = UGCXFromRight(113);
		tly = UGCYFromBottom(335);
		if (x >= tlx && x <= tlx + 110 * scale && y >= tly && y <= tly + 80 * scale)
		{
			return 1;
		}

		tly = UGCYFromBottom(165);
		if (x >= tlx && x <= tlx + 110 * scale && y >= tly && y <= tly + 80 * scale)
		{
			return -1;
		}
	}

	return 0;
}

void UGCTouchControl::showUseBtn(bool b)
{
	if (g_pPlayerCtrl == NULL)
	{
		m_ShowUseBtn = b;
		return;
	}
	bool isUGCHighMode = UGCModeManager::getSingleton()->IsEditing() && UGCModeManager::getSingleton()->GetGameType() == UGCGAMETYPE_HIGH;
	//非UGC高级模式
	if (!isUGCHighMode)
	{
		m_ShowUseBtn = b;
		return;
	}
	int curtool = g_pPlayerCtrl->getCurToolID();
	const ItemDef *pDef = ItemDefCsv::getInstance()->get(curtool);
	if (pDef == NULL)
	{
		m_ShowUseBtn = b;
		return;
	}
	//UGC高级模式下，微缩通过点击放置，所以隐藏使用按钮
	if (pDef->UseTarget == ITEM_USE_PACKING_FCM_ITEM)
		m_ShowUseBtn = false;
	else
		m_ShowUseBtn = b;
}

int UGCTouchControl::SetTouchPoint(int id, int x, int y)
{
	for (auto it = m_UGCTouchPoints.begin(); it != m_UGCTouchPoints.end(); it++)
	{
		if (it->m_nId == id)
		{
			it->m_nX = x;
			it->m_nY = y;
			return m_UGCTouchPoints.size();
		}
	}

	UGCTouchPoint point;
	point.m_nId = id;
	point.m_nX = x;
	point.m_nY = y;
	m_UGCTouchPoints.push_back(point);

	return m_UGCTouchPoints.size();
}

bool UGCTouchControl::HasTouchPoint(int id)
{
	if (m_UGCTouchPoints.size() > 0)
	{
		for (auto it = m_UGCTouchPoints.begin(); it != m_UGCTouchPoints.end(); it++)
		{
			if (it->m_nId == id)
				return true;
		}
	}

	return false;
}

int UGCTouchControl::DelTouchPoint(int id)
{
	for (auto it = m_UGCTouchPoints.begin(); it != m_UGCTouchPoints.end(); it++)
	{
		if (it->m_nId == id)
		{
			m_UGCTouchPoints.erase(it++);
			return m_UGCTouchPoints.size();
		}
	}

	return m_UGCTouchPoints.size();
}

bool UGCTouchControl::IsInRockerArea(int x, int y)
{
	//int r = int(450 * m_ScreenScaleFactor);

	//if (x * x + y * y < r * r)
	//	return true;

	//if ((x - m_rockerPos.x * m_ScreenScaleFactor) * (x - m_rockerPos.x * m_ScreenScaleFactor) + ((Root::getSingleton().getClientHeight() - y) - m_rockerPos.y) * ((Root::getSingleton().getClientHeight() - y) - m_rockerPos.y) < (160 * m_ScreenScaleFactor) * (160 * m_ScreenScaleFactor))
	//{
	//	return true;
	//}

	if (x < 390 * m_ScreenScaleFactor && y < 330 * m_ScreenScaleFactor)
		return true;

	return false;
}

void UGCTouchControl::OnChangeGameType()
{
	m_RotateID = -1;
	ResetAllInput();
}

void UGCTouchControl::ShowBlueprintUseBtn(bool isShow)
{
	m_ShowBlueprintUseBtn = isShow;
}

float UGCTouchControl::GetPointDistance(int x1, int y1, int x2, int y2)
{
	int offsetx = x1 - x2;
	int offsety = y1 - y2;

	float distance = Rainbow::Sqrt(offsetx * offsetx + offsety * offsety);

	return distance;
}

UGCDragType UGCTouchControl::GetDragType()
{
	if (m_UGCTouchPoints.size() >= 2)
	{
		int dx1 = m_UGCTouchPoints[0].m_nX - m_LastMovePoint1.m_nX;
		int dy1 = m_UGCTouchPoints[0].m_nY - m_LastMovePoint1.m_nY;
		int dx2 = m_UGCTouchPoints[1].m_nX - m_LastMovePoint2.m_nX;
		int dy2 = m_UGCTouchPoints[1].m_nY - m_LastMovePoint2.m_nY;

		if (dx1 == 0 || dy1 == 0 || dx2 == 0 || dy2 == 0)
		{
			//ֻ��һ���㶯��, ״̬����
			return m_eDragType;
		}
		else
		{
			if ((dx1 * dx2 + dy1 * dy2) < 0)
				return UGCDragType_Double_Stretch;
			else
				return UGCDragType_Double_Move;
		}
	}

	return UGCDragType_Single_Down;
}

void UGCTouchControl::tick()
{
	////这里时为了修复在编辑模式下 刚进地图 时转动镜头会被检测为长按删除方块
	//if (UGCModeManager::getSingleton()->GetType() == UGCMODETYPE::UGCMODETYPE_EDIT && m_RotateSubType == 0)
	//	m_RotateSubType = 2;

	TouchControl::tick();

	CheckLongPress();
}

bool UGCTouchControl::onInputEvent(const Rainbow::InputEvent& ev)
{
	if (g_pPlayerCtrl == NULL)
	{
		return false;
	}
	
	if (ev.type == InputEvent::kMouseDown)
	{
		if (HandleUIClickDown(ev))
		{
			m_nHandleUICount++;
		}
		else
		{
			HandleSceneClickDown(ev);
		}

		if (ev.button != m_MoveID)
		{
			//����ƶ���ť��ʱ��ת��ͷ
			int nCount = SetTouchPoint(ev.button, ev.mousePosition.x, ev.mousePosition.y);
			//printf_console("mousedown:nCount = %d", nCount);
			//printf_console("mousedown:button = %d", inevent.button);

			if (nCount == 1) {
				m_eDragType = UGCDragType_Single_Down;
				m_LastMovePoint1 = m_UGCTouchPoints[0];
				m_TouchDownStartPoint = m_UGCTouchPoints[0];
				m_fTouchDownTime = Rainbow::GetTimeSinceStartup();
			}
			else if (nCount == 2) {
				m_eDragType = UGCDragType_Double_Down;
				m_RotateID = -1;
				m_LastMovePoint1 = m_UGCTouchPoints[0];
				m_LastMovePoint2 = m_UGCTouchPoints[1];
				m_fLastDistance = GetPointDistance(m_LastMovePoint1.m_nX, m_LastMovePoint1.m_nY, m_LastMovePoint2.m_nX, m_LastMovePoint2.m_nY);
			}
			m_RotateTime = Timer::getSystemTick();
		}
		m_RotateSubType = 0;
	}
	else if (ev.type == InputEvent::kMouseUp)
	{
		int nCount = DelTouchPoint(ev.button);
		m_RotateID = -1;

		if (HandleUIClickUp(ev))
		{
			if (UGCModeManager::getSingleton()->IsEditing())
			{
				//m_nHandleUICount--;
				//m_UGCTouchPoints.clear();
			}
		}
		else
		{
			HandleSceneClickUp(ev);
		}
	}
	else if (ev.type == InputEvent::kMouseDrag)
	{
		if (HandleUIDrag(ev))
		{
			
		}

		if (ev.button != m_MoveID)
		{
			HandleSceneDrag(ev);
		}
		//else
		//{
		//	HandleSceneDrag(ev);
		//}
		touchMoveDx = 0;
		touchMoveDy = 0; 
		if (ev.button == m_RotateID)
		{
			if (m_RotateSubType == 0)
			{
				int maxmove = Max(Abs(ev.mousePosition.x - m_RotateStart.x), Abs(ev.mousePosition.y - m_RotateStart.y));
				if (maxmove > 20)
				{
					m_RotateSubType = 1;
					m_RotateStart.SetElement(ev.mousePosition.x, ev.mousePosition.y);
				}
			}
			else
			{
				int dx = ev.mousePosition.x - m_RotateStart.x;
				int dy = ev.mousePosition.y - m_RotateStart.y;
				if (m_reversalY) dy = -dy;

				if (g_pPlayerCtrl->getOWID() == NEWBIEWORLDID && GetClientInfoProxy()->getCurGuideLevel() == 1)
				{
					int step = GetClientInfoProxy()->getCurGuideStep();
					if (step < 5)
						dy = 0;
					if (step == 5 || step == 6)
						dx = 0;
				}
				if ((Abs(dx) > 0 || Abs(dy) > 0) && !isLockCamera())
				{
					if (m_RotateSubType != 2)
						m_RotateSubType = 1;
					touchMoveDx = dx;
					touchMoveDy = dy;
					m_RotateStart.SetElement(ev.mousePosition.x, ev.mousePosition.y);
					g_pPlayerCtrl->checkNewbieWorldProgress(3, "rotationview");
				}
				else
				{
					touchMoveDx = 0;
					touchMoveDy = 0;
				}
			}
		}
	}
	//else if (ev.msg == GIE_TOUCHCANCELLED)
	//{
	//	//这里无效, 切后台的时候没有触发'cancel'
	//	m_UGCTouchPoints.clear();
	//}

	return true;

	//return TouchControl::onInputEvent(ev);
}

bool UGCTouchControl::isRockerMode()
{
	//ugc�̶�Ϊҡ��ģʽ
	return true;
}

bool UGCTouchControl::HandleSceneClickDown(const Rainbow::InputEvent& inevent)
{
	//运行模式和拼搭模式只能单指操作场景 2023年2月8日17:22:49 codeby:lulei
	if (!UGCModeManager::getSingleton()->IsEditing() || UGCModeManager::getSingleton()->GetGameType() == UGCGAMETYPE::UGCGAMETYPE_BUILD)
	{
		m_UGCTouchPoints.clear();
	}
	else if (!HasTouchPoint(inevent.button) && m_UGCTouchPoints.size() >= 2)
	{
		//未触发到TouchCancel和TouchRelease的情况(如切后台等)
		m_UGCTouchPoints.clear();
	}
	int nCount = SetTouchPoint(inevent.button, inevent.mousePosition.x, inevent.mousePosition.y);
	//printf_console("mousedown:nCount = %d", nCount);
	//printf_console("mousedown:button = %d", inevent.button);

	if (nCount == 1) {
		m_eDragType = UGCDragType_Single_Down;
		m_LastMovePoint1 = m_UGCTouchPoints[0];
		m_TouchDownStartPoint = m_UGCTouchPoints[0];
		m_fTouchDownTime = Rainbow::GetTimeSinceStartup();
		int x = inevent.mousePosition.x;
		int y = inevent.mousePosition.y;
		m_FlyType = UGCCheckFlyArea(x, y, m_ScreenScaleFactor);
		if (m_FlyType == 0)
		{
			m_fTriggerLongPressTime = m_fTouchDownTime;
			m_bLongPressBreaked = false;
		}
		if (!g_pPlayerCtrl->canDig())
			m_bLongPressBreaked = true;

		//m_RotateID控制着旋转时一个点击图标的显示和挖掘进度得显示
		UGCModeManager *pUGCModeManager = UGCModeManager::getSingleton();
		if (pUGCModeManager->IsEditing() && pUGCModeManager->GetGameType() == UGCGAMETYPE::UGCGAMETYPE_HIGH)
			m_RotateID = -1;
		else
			m_RotateID = inevent.button;	//��Ӱ������Ч����Ⱦ
		m_RotateStart.SetElement(inevent.mousePosition.x, inevent.mousePosition.y);
	}
	else if (nCount == 2) {
		m_eDragType = UGCDragType_Double_Down;
		m_RotateID = -1;
		m_bLongPressBreaked = true;
		m_LastMovePoint1 = m_UGCTouchPoints[0];
		m_LastMovePoint2 = m_UGCTouchPoints[1];
		m_fLastDistance = GetPointDistance(m_LastMovePoint1.m_nX, m_LastMovePoint1.m_nY, m_LastMovePoint2.m_nX, m_LastMovePoint2.m_nY);
	}

	return true;
}

bool UGCTouchControl::HandleSceneClickUp(const Rainbow::InputEvent& inevent)
{
	int nCount = DelTouchPoint(inevent.button);
	int distance = GetPointDistance(inevent.mousePosition.x, inevent.mousePosition.y, m_TouchDownStartPoint.m_nX, m_TouchDownStartPoint.m_nY);
	//printf_console("mouseup:nCount = %d", nCount);

	m_RotateID = -1;

	if (nCount == 0)
	{
		double duration = Rainbow::GetTimeSinceStartup() - m_fTouchDownTime;
		isLongPress = false;

		if (duration < mCheckLongTapTime && distance < mCheckTapDistance)
		{
			//tap
			if (m_sightModel)
			{
				tapPosX = 0.5;
				tapPosY = 0.5;
			}
			else
			{
				tapPosX = inevent.mousePosition.x / float(GetGameUIPtr()->GetWidth());
				tapPosY = inevent.mousePosition.y / float(GetGameUIPtr()->GetHeight());
			}

			hasTap = true;
			tapTriggerMark = m_selfUpdateCount;
		}
	}
	else if (nCount == 1)
	{
		m_LastMovePoint1 = m_UGCTouchPoints[0];
	}

	if (nCount == 0)
	{
		m_eDragType = UGCDragType_NULL;
		m_bLongPressBreaked = true;
	}

	return true;
}

bool UGCTouchControl::HandleSceneDrag(const Rainbow::InputEvent& inevent)
{
	if (m_UGCTouchPoints.size() <= 0 || !g_pPlayerCtrl)
		return false;

	auto curArrow = EditorSelectCoordInteract::GetInstancePtr()->GetCurCoordArrow();
	if (curArrow != COOD_NONE)
		return false;

	int nCount = SetTouchPoint(inevent.button, inevent.mousePosition.x, inevent.mousePosition.y);
	//printf_console("test:kMouseDrag:count = %d", nCount);

	//if (m_eDragType == UGCDragType_Single_Down || m_eDragType == UGCDragType_Single_Rotate)
	if (nCount == 1)
	{
		//��ת
		//printf_console("test1111:rotate");
		m_eDragType = UGCDragType_Single_Rotate;

		int dx = m_UGCTouchPoints[0].m_nX - m_LastMovePoint1.m_nX;
		int dy = m_UGCTouchPoints[0].m_nY - m_LastMovePoint1.m_nY;

		if (Abs(dx) > 0 || Abs(dy) > 0)
		{
			m_LastMovePoint1 = m_UGCTouchPoints[0];
			const int screenWidth = GetGameUIPtr()->GetWidth();
			const int screenHeight = GetGameUIPtr()->GetHeight();

			GameCamera* pcamera = g_pPlayerCtrl->getCamera();
			if (pcamera) {
				pcamera->rotate(float(dx) * m_sensityvity /screenWidth, float(dy) * m_sensityvity / screenHeight);
			}
			EditorSelect::GetInstancePtr()->ShowGridMesh();
		}

		int distance = GetPointDistance(inevent.mousePosition.x, inevent.mousePosition.y, m_TouchDownStartPoint.m_nX, m_TouchDownStartPoint.m_nY);
		if (distance > mCheckTapDistance)
		{
			m_bLongPressBreaked = true;
		}

		//�ʵ�ǹ
		CheckColorCursor();
	}
	else
	{
		if (2 == m_UGCTouchPoints.size())
		{
			//���Ǳ༭ģʽ���Ҳ��Ǹ߼�ģʽ��֧��˫ָ����
			if (!UGCModeManager::getSingleton()->IsEditing() || UGCModeManager::getSingleton()->GetGameType() != UGCGAMETYPE::UGCGAMETYPE_HIGH)
				return false;

			m_eDragType = GetDragType();

			if (m_eDragType == UGCDragType_Double_Move)
			{
				//ƽ��
				//printf_console("test1111:move");
				int dx = m_UGCTouchPoints[0].m_nX - m_LastMovePoint1.m_nX;
				int dy = m_UGCTouchPoints[0].m_nY - m_LastMovePoint1.m_nY;

				if (Abs(dx) > 0 || Abs(dy) > 0)
				{
					WCoord WPos = g_pPlayerCtrl->getPosition();
					const WCoord& stPlayerPos = g_pPlayerCtrl->getPosition();
					Rainbow::Vector3f LookDir = g_pPlayerCtrl->getLookDir();
					Rainbow::Vector3f YDir = Rainbow::Vector3f(0, 1, 0);
					Rainbow::Vector3f XDir = Rainbow::Vector3f::Cross(YDir, LookDir);

					YDir = Rainbow::Vector3f::Cross(LookDir, XDir);
					YDir.NormalizeSafe();
					XDir.NormalizeSafe();

					YDir *= float(dy * m_sensityvity);
					XDir *= float(-dx * m_sensityvity);
					WPos += WCoord(YDir.x, YDir.y, YDir.z);
					WPos += WCoord(XDir.x, XDir.y, XDir.z);

#if 0
					World* pWorld = g_pPlayerCtrl->getWorld();
					if (pWorld)
					{
						if (UGCModeManager::GetInstancePtr()->IsPlayerClip())
						{
							float dy = WPos.y - stPlayerPos.y;
							if (dy > 0.000001f || dy < -0.000001)
							{
								WCoord blockpos = CoordDivBlock(stPlayerPos);
								if (dy > 0.000001f)
									blockpos.y++;
								else
									blockpos.y--;

								if (pWorld->getBlock(blockpos)->getResID() > 0)
								{
									WPos.y = stPlayerPos.y;
								}
							}
						}

						WCoord s, e;
						pWorld->getChunkRangeXZ(s.x, s.z, e.x, e.z);
						int iMinRange = MIN_INT / 16;
						int iMaxRange = MAX_INT / 16;
						bool bLimitTileX = s.x > iMinRange && e.x < iMaxRange;
						bool bLimitTileZ = s.z > iMinRange && e.z < iMaxRange;

						pWorld->getRangeXZ(s.x, s.z, e.x, e.z);
						if (bLimitTileX)
						{
							if (WPos.x < s.x)
							{
								WPos.x = s.x;
							}
							else if (WPos.x > e.x)
							{
								WPos.x = e.x;
							}
						}

						if (bLimitTileZ)
						{
							if (WPos.z < s.z)
							{
								WPos.z = s.z;
							}
							else if (WPos.z > e.z)
							{
								WPos.z = e.z;
							}
						}
					}
#else
					UGCModeManager::getSingleton()->CheckCameraClip(stPlayerPos, WPos);
#endif

					if (WPos.y < -10 * BLOCK_SIZE)
						WPos.y = -10 * BLOCK_SIZE + 50;

					g_pPlayerCtrl->setPosition(WPos);
				}

				m_LastMovePoint1 = m_UGCTouchPoints[0];
				m_LastMovePoint2 = m_UGCTouchPoints[1];
			}
			else if (m_eDragType == UGCDragType_Double_Stretch)
			{
				//����
				//printf_console("test1111:stretch");
				int nCurDistance = GetPointDistance(m_UGCTouchPoints[0].m_nX, m_UGCTouchPoints[0].m_nY, m_UGCTouchPoints[1].m_nX, m_UGCTouchPoints[1].m_nY);
				float offset = nCurDistance - m_fLastDistance;

				m_fLastDistance = nCurDistance;
				Rainbow::Vector3f LookDir = g_pPlayerCtrl->getLookDir();
				WCoord WPos = g_pPlayerCtrl->getPosition();
				const WCoord& stPlayerPos = g_pPlayerCtrl->getPosition();
				offset *= m_sensityvity;

				WPos = WPos + LookDir * offset;

#if 0
				World* pWorld = g_pPlayerCtrl->getWorld();
				if (pWorld)
				{
					if (UGCModeManager::GetInstancePtr()->IsPlayerClip())
					{
						float dy = WPos.y - stPlayerPos.y;
						if (dy > 0.000001f || dy < -0.000001)
						{
							WCoord blockpos = CoordDivBlock(stPlayerPos);
							if (dy > 0.000001f)
								blockpos.y++;
							else
								blockpos.y--;

							if (pWorld->getBlock(blockpos)->getResID() > 0)
							{
								WPos.y = stPlayerPos.y;
							}
						}
					}

					WCoord s, e;
					pWorld->getChunkRangeXZ(s.x, s.z, e.x, e.z);
					int iMinRange = MIN_INT / 16;
					int iMaxRange = MAX_INT / 16;
					bool bLimitTileX = s.x > iMinRange && e.x < iMaxRange;
					bool bLimitTileZ = s.z > iMinRange && e.z < iMaxRange;

					pWorld->getRangeXZ(s.x, s.z, e.x, e.z);
					if (bLimitTileX)
					{
						if (WPos.x < s.x)
						{
							WPos.x = s.x;
						}
						else if (WPos.x > e.x)
						{
							WPos.x = e.x;
						}
					}

					if (bLimitTileZ)
					{
						if (WPos.z < s.z)
						{
							WPos.z = s.z;
						}
						else if (WPos.z > e.z)
						{
							WPos.z = e.z;
						}
					}
				}
#else
				UGCModeManager::getSingleton()->CheckCameraClip(stPlayerPos, WPos);
#endif

				if (WPos.y < -10 * BLOCK_SIZE)
					WPos.y = -10 * BLOCK_SIZE + 50;
				g_pPlayerCtrl->setPosition(WPos);

				m_LastMovePoint1 = m_UGCTouchPoints[0];
				m_LastMovePoint2 = m_UGCTouchPoints[1];
			}
		}
	}

	return true;
}


bool UGCTouchControl::HandleUIClickDown(const Rainbow::InputEvent& inevent)
{
	if (!g_pPlayerCtrl)
		return false;

	//printf_console("HandleUIClickDown:");
	bool bHandled = true;
	int x = inevent.mousePosition.x;
	int y = inevent.mousePosition.y;
	const int screenHeight = GetGameUIPtr()->GetHeight();

	int dx = x;
	int dy = screenHeight - y;
	
	int direction = 0;
	const int frameCount = m_selfUpdateCount;

	Point2D rockerPos = m_rockerPos;

	m_FlyType = UGCCheckFlyArea(x, y, m_ScreenScaleFactor);

	if (g_pPlayerCtrl->getFlying() && m_FlyType != 0)
	{
		m_FlyID = inevent.button;
		m_FlyPos.SetElement(x, y);
		if (UGCCheckFlyArea(x, y, m_ScreenScaleFactor) == 1)
		{
			m_ButtonMap[BUTTON_FLYUP] = true;
			m_ButtonMap[BUTTON_FLYDOWN] = false;
		}
		else if (UGCCheckFlyArea(x, y, m_ScreenScaleFactor) == -1)
		{
			m_ButtonMap[BUTTON_FLYUP] = false;
			m_ButtonMap[BUTTON_FLYDOWN] = true;
		}
		else if (UGCCheckFlyArea(x, y, m_ScreenScaleFactor) == 0)
		{
			m_ButtonMap[BUTTON_FLYUP] = false;
			m_ButtonMap[BUTTON_FLYDOWN] = false;
		}
	}
	else
	{
		if (!isRockerMode())
		{
			m_isCanMove = true;
		}

		if (!isRockerMode() && IsInControl(x, y, direction))
		{
			//ǰ
			if (direction & 1)
			{
				m_ButtonDownMap[BUTTON_FORWARD] = true;
				m_ButtonDownMarks[BUTTON_FORWARD] = frameCount;
			}
			//��
			if (direction & 4)
			{
				m_ButtonDownMap[BUTTON_LEFT] = true;
				m_ButtonDownMarks[BUTTON_LEFT] = frameCount;
			}
			//��
			if (direction & 8)
			{
				m_ButtonDownMap[BUTTON_RIGHT] = true;
				m_ButtonDownMarks[BUTTON_RIGHT] = frameCount;
			}
			//��
			if (direction & 2)
			{
				m_ButtonDownMap[BUTTON_BACK] = true;
				m_ButtonDownMarks[BUTTON_BACK] = frameCount;
			}

			//��ǰ
			if (direction & 16)
			{
				m_ButtonDownMap[BUTTON_FORWARDLEFT] = true;
				m_ButtonDownMarks[BUTTON_FORWARDLEFT] = frameCount;
			}
			//��ǰ
			if (direction & 32)
			{
				m_ButtonDownMap[BUTTON_FORWARDRIGHT] = true;
				m_ButtonDownMarks[BUTTON_FORWARDRIGHT] = frameCount;
			}
		}

		if (IsInJumpCircle(x, y, m_ScreenScaleFactor) && m_JumpID < 0 && m_showOperateUI && !m_bHideJumpUi)
		{
			//printf_console("m_JumpID:");

			//��Ծ
			m_JumpID = inevent.button;
			m_JumpTime = 0;
			m_JumpSubType = 0;
			m_JumpPos.SetElement(x, y);
			m_ButtonMap[BUTTON_JUMP] = true;
			m_ButtonDownMap[BUTTON_JUMP] = true;
			m_ButtonMap[BUTTON_FLYDOWN] = false;
			m_ButtonDownMarks[BUTTON_JUMP] = frameCount;

			this->setTriggerKeys(BUTTON_JUMP, 'D');
		}
		else if (m_ShowUseBtn && IsInUseCircle(x, y, m_ScreenScaleFactor) && m_UseID < 0)
		{
			//printf_console("m_UseID:");
			//ʹ���ж�
			if (m_ShowGunUseBtn)
				IsInLeftOrRight(x, y, m_ScreenScaleFactor);
			else
				m_ShootPosition = -1;
			m_UseID = inevent.button;
			m_UseSubType = 0;
			m_UseTime = 0;
			m_UsePos.SetElement(x, y);
			m_ButtonDownMap[BUTTON_ACTION] = true;
			m_ButtonMap[BUTTON_ACTION] = true;
			m_ButtonDownMarks[BUTTON_ACTION] = frameCount;
		}
		else if ((m_ShowBallUseBtn || m_ShowBasketBallUseBtn || m_ShowPushSnowBallUseBtn) && !m_bHideActionUi && UGCIsInPassOrCatchBallCirle(x, y, m_ScreenScaleFactor) && m_PassOrCatchBallID < 0)
		{
			m_PassOrCatchBallID = inevent.button;
			m_PassOrCatchTime = 0;
			m_PassOrCatchPos.SetElement(x, y);
			m_ButtonDownMap[BUTTON__PASSBALL_OR_CATCHBALL] = true;
			m_ButtonMap[BUTTON__PASSBALL_OR_CATCHBALL] = true;
			m_ButtonDownMarks[BUTTON__PASSBALL_OR_CATCHBALL] = frameCount;
		}
		else if (m_ShowGunUseBtn && UGCIsInGunReloadCircle(x, y, m_ScreenScaleFactor) && m_ReloadID < 0)
		{
			//ǹ��reload�ж�
			//printf_console("m_ReloadID:");

			m_ReloadID = inevent.button;
			m_ReloadTime = 0;
			m_ReloadPos.SetElement(x, y);
			m_ButtonDownMap[BUTTON_GUNRELOAD] = true;
			m_ButtonMap[BUTTON_GUNRELOAD] = true;
			m_ButtonDownMarks[BUTTON_GUNRELOAD] = frameCount;
		}
		else if (IsInRockerArea(dx, dy))
		{
			//printf_console("m_MoveID:");
			if (isRockerMode())
			{
				int limitw = int(MOVE_WIDTH * m_ScreenScaleFactor) / 2;

				if (x < limitw) x = limitw;
				if (y < limitw) y = limitw;
				if (y > screenHeight - limitw) y = screenHeight - limitw;

				m_MoveID = inevent.button;
				m_MoveStart.SetElement(x, y);
				m_MovePos.SetElement(x, y);
                m_MoveTouchState =1;
			}
		}
		else
		{
			bHandled = false;
		}
	}

	return bHandled;
}

bool UGCTouchControl::HandleUIClickUp(const Rainbow::InputEvent& inevent)
{
	bool bHandled = true;
	const int frameCount = m_selfUpdateCount;

	int direction = 0;
	int x = inevent.mousePosition.x;
	int y = inevent.mousePosition.y;

	if (!isRockerMode() && IsInControl(x, y, direction))
	{
		if (direction & 1) //ǰ
		{
			this->setTriggerKeys(BUTTON_FORWARD, 'U');
		}
		else if (direction & 4) //��
		{
			this->setTriggerKeys(BUTTON_LEFT, 'U');
		}
		else if (direction & 8) //��
		{
			this->setTriggerKeys(BUTTON_RIGHT, 'U');
		}
		else if (direction & 2) //��
		{
			this->setTriggerKeys(BUTTON_BACK, 'U');
		}
		else if (direction & 16) //��ǰ
		{
			this->setTriggerKeys(BUTTON_FORWARD, 'U');
			this->setTriggerKeys(BUTTON_LEFT, 'U');
		}
		else if (direction & 32) //��ǰ
		{
			this->setTriggerKeys(BUTTON_FORWARD, 'U');
			this->setTriggerKeys(BUTTON_RIGHT, 'U');
		}
	}
    bool bmovebtnup = false;
	if (inevent.button == m_MoveID && isRockerMode())
	{
        m_MoveTouchState = 0;
		m_MoveID = -1;
		m_RockerTouchDx = 0;
		m_RockerTouchDy = 0;
        bmovebtnup = true;
	}
	else if (inevent.button == m_JumpID)
	{
		m_JumpID = -1;
		m_JumpSubType = -1;
		m_ButtonMap[BUTTON_JUMP] = false;
		m_ButtonUpMap[BUTTON_JUMP] = true;
		m_ButtonUpMarks[BUTTON_JUMP] = frameCount;
        this->setTriggerKeys(BUTTON_JUMP, 'U');
        g_pPlayerCtrl->triggerInputEvent(BUTTON_JUMP, "Up");
        
	}
	else if (inevent.button == m_UseID)
	{

		m_UseID = -1;
		m_JumpSubType = -1;
		m_ButtonMap[BUTTON_ACTION] = false;
		m_ButtonUpMap[BUTTON_ACTION] = true;
		m_ButtonUpMarks[BUTTON_ACTION] = frameCount;
		if (m_ButtonDownMarks[BUTTON_ACTION] == m_ButtonUpMarks[BUTTON_ACTION])
		{	
			//CodeBy: wangshuai 
			//如果按下跟松开是再同一帧，就把松开的帧数延迟一帧；
			//如果不改会出现的问题：逻辑 先TouchControl::OnInputEvent->再TouchControl::update->再走PlayerInputHelper::onUpdate
			//假如第319帧OnInputEvent 出现down 和up事件（m_ButtonUpMarks[BUTTON_ACTION] m_ButtonDownMarks[BUTTON_ACTION]均为319）->319帧TouchControl::update（framecount相等）
			//->319帧PlayerInputHelper::onUpdate，m_InputInfo->useActionEnd为true->319帧ActionIdleState,ToUse,UseState::doBeforeEntering;
			//->320帧TouchControl::update（framecount不相等 m_ButtonUpMarks[BUTTON_ACTION] m_ButtonDownMarks[BUTTON_ACTION]置为-1）->320帧PlayerInputHelper::onUpdate，m_InputInfo->useActionEnd为false
			//->320帧UseState::update(原本这里useActionEnd应该为true，然后ToActionIdle；但是因为上面同一帧up down导致这里是false，所以UseState::update为一直运行返回"",0.5s一次useItem)
			m_ButtonUpMarks[BUTTON_ACTION]++;
		}
	}
	else if (inevent.button == m_PassOrCatchBallID)
	{

		m_PassOrCatchBallID = -1;
		m_ButtonMap[BUTTON__PASSBALL_OR_CATCHBALL] = false;
		m_ButtonUpMap[BUTTON__PASSBALL_OR_CATCHBALL] = true;
		m_ButtonUpMarks[BUTTON__PASSBALL_OR_CATCHBALL] = frameCount;

	}
	else if (inevent.button == m_ReloadID)
	{

		m_ReloadID = -1;
		m_ButtonMap[BUTTON_GUNRELOAD] = false;
		m_ButtonUpMap[BUTTON_GUNRELOAD] = true;
		m_ButtonUpMarks[BUTTON_GUNRELOAD] = frameCount;
	}
	else if (inevent.button == m_FlyID)
	{
		m_FlyID = -1;
		m_FlyType = 0;
		m_ButtonMap[BUTTON_FLYUP] = false;
		m_ButtonMap[BUTTON_FLYDOWN] = false;
	}
	else
	{
		bHandled = false;
		isLongPress = false;
	}
    if (isRockerMode() && bmovebtnup && !m_Triggerkeys.empty() )
    {
        for (int i = 6; i < 10; ++i)
        {
            bool btrigger = false;
            auto iter = m_Triggerkeys.find(i);
            while (iter != m_Triggerkeys.end()) {
                m_Triggerkeys.erase(iter);
                iter = m_Triggerkeys.find(i);
                btrigger = true;
            }
            if (btrigger) {
                g_pPlayerCtrl->triggerInputEvent(i, "Up");
            }
        }
    }

	return bHandled;
}

bool UGCTouchControl::HandleUIDrag(const Rainbow::InputEvent& inevent)
{
	bool bHandled = true;
	if (inevent.button == m_MoveID && isRockerMode())
	{
		m_RockerTouchDx = inevent.mousePosition.x - m_MovePos.x;
		m_RockerTouchDy = inevent.mousePosition.y - m_MovePos.y;
		m_MovePos.SetElement(inevent.mousePosition.x, inevent.mousePosition.y);

        int direction = 0;
        if (IsInControl(inevent.mousePosition.x, inevent.mousePosition.y, direction))
        {
            if (direction == 0)
            {
                if (m_MoveTouchState == 1) {
                    calculateModeDir(inevent.mousePosition.x, inevent.mousePosition.y, direction);
                }
            }
            //前
            if (direction & 1)
            {
                this->setTriggerKeys(BUTTON_FORWARD, 'D');
            }
            //左
            else if (direction & 4)
            {
                this->setTriggerKeys(BUTTON_LEFT, 'D');
            }
            //右
            else  if (direction & 8)
            {
                this->setTriggerKeys(BUTTON_RIGHT, 'D');
            }
            //后
            else if (direction & 2)
            {
                this->setTriggerKeys(BUTTON_BACK, 'D');
            }
        }
	}
	else if (inevent.button == m_JumpID)
	{
		
	}
	else if (inevent.button == m_UseID)
	{
		
	}
	else if (inevent.button == m_FlyID)
	{

	}
	else
	{
		bHandled = false;
	}

	return bHandled;
}

bool UGCTouchControl::CheckLongPress()
{
	if (!m_bLongPressBreaked)
	{
		//����
		double curtime = Rainbow::GetTimeSinceStartup();
		double duration = curtime - m_fTriggerLongPressTime;

		if (duration > mCheckLongTapTime)
		{
			m_fTriggerLongPressTime = curtime;
			longPressTriggerMark = m_selfUpdateCount;
			triggerLongPress = true;
			isLongPress = true;

			if (m_sightModel)
			{
				longPressX = 0.5;
				longPressY = 0.5;
			}
			else
			{
				longPressX = m_TouchDownStartPoint.m_nX;
				longPressY = m_TouchDownStartPoint.m_nY;
				longPressX /= (float)GetGameUIPtr()->GetWidth();
				longPressY /= (float)GetGameUIPtr()->GetHeight();
			}

			return true;
		}
	}

	return false;
}

