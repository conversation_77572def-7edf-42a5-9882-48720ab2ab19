
#include "AIPlayerTargetHurtee.h"
#include "ClientActor.h"
#include "AINpc.h"
#include "ClientPlayer.h"
#include "ActorBoss.h"
#include "ActorLocoMotion.h"
#include "ToAttackTargetComponent.h"
#include "LivingAttrib.h"
#include "ActorBody.h"
#include "ActorVision.h"
#include "navigationpath.h"

AIPlayerTargetHurtee::AIPlayerTargetHurtee(AINpcPlayer *pActor)
	: m_LastBeAtkTimer(0), AIBase(pActor)
{
	setMutexBits(1);
	m_CheckSight = true;
}

bool AIPlayerTargetHurtee::willRun()
{
	WCoord pos = m_pAINpcPlayer->getPosition();
	 
	if (!m_pAINpcPlayer->isInHomeDist(pos.x, pos.y, pos.z))
	{
		m_pAINpcPlayer->setNeedRetHome(true);
		return false;
	}

	if (m_LastBeAtkTimer != m_pAINpcPlayer->getBeHurtTimer())
	{
		return true;
	}

	LivingAttrib* pLivingAttrib = dynamic_cast<LivingAttrib*>(m_pAINpcPlayer->getAttrib());
	if (nullptr != pLivingAttrib)
	{
		if (pLivingAttrib->getBuffEffectBankInfo(BuffAttrType::BUFFATTRT_FORBID_OPERATE))
		{
			return false;
		}
	}
	return false;
}

void AIPlayerTargetHurtee::start()
{
	ClientActor* target =  m_pAINpcPlayer->getBeAtk();
	if (target == NULL)
		return;

	if (target->getObjType() == OBJ_TYPE_ROLE)
		return;

	/*WCoord pos = m_pAINpcPlayer->getPosition();
	WCoord targetpos = target->getLocoMotion()->getPosition();
	WCoord vec = targetpos - m_pAINpcPlayer->getLocoMotion()->getPosition();

	float dist = vec.length();
	if (m_pAINpcPlayer->getViewDist() < dist)
	{
		//m_pAINpcPlayer->getNavigator()->tryMoveTo(targetpos.x, targetpos.y, targetpos.z, 1);
		m_pAINpcPlayer->getNavigator()->tryMoveTo(200, 700, 2900, 1);
	}
	else*/
	{
		auto targetComponent = m_pAINpcPlayer->getToAttackTargetComponent();
		if (targetComponent)
		{
			targetComponent->setTarget(target);
			m_pAINpcPlayer->setNeedRetHome(false);
		}
		m_LastBeAtkTimer = m_pAINpcPlayer->getBeAtkTimer();
	}

	 

	m_CheckSightCount = 0;
}

void AIPlayerTargetHurtee::reset()
{
	auto targetComponent = m_pAINpcPlayer->getToAttackTargetComponent();
	if (targetComponent)
	{
		targetComponent->setTarget(nullptr);
	}
}

bool AIPlayerTargetHurtee::continueRun()
{
	WCoord pos = m_pAINpcPlayer->getPosition();
	 
	if (!m_pAINpcPlayer->isInHomeDist(pos.x, pos.y, pos.z))
	{
		m_pAINpcPlayer->setBeHurtTarget(NULL);
		auto targetComponent = m_pAINpcPlayer->getToAttackTargetComponent();
		if (targetComponent)
		{
			targetComponent->setTarget(NULL);
			m_pAINpcPlayer->setNeedRetHome(true);
		}
		m_pAINpcPlayer->getNavigator()->clearPathEntity();
		return false;
	}

	ClientActor* target = NULL;
	auto targetComponent = m_pAINpcPlayer->getToAttackTargetComponent();
	if (targetComponent)
	{
		target = targetComponent->getTarget();
	}
	if (NULL == target)
	{
		return false;
	}

	if (target->isDead() || target->isInvulnerable(m_pAINpcPlayer))
	{
		m_pAINpcPlayer->setNeedRetHome(true);
		return false;
	}
 
	WCoord targetpos = target->getLocoMotion()->getPosition();
	WCoord vec = targetpos - m_pAINpcPlayer->getLocoMotion()->getPosition();

	float dist = vec.length();
	//if (m_pAINpcPlayer->getTraceDist() < dist)
	if (m_pAINpcPlayer->getViewDist() < dist)
	{
		m_pAINpcPlayer->setNeedRetHome(true);
		return false;
	}
	else
	{
		if (m_CheckSight)
		{
			if (m_pAINpcPlayer->getVision()->canSeeInAICache(target))
			{
				m_CheckSightCount = 0;
			}
			else if (++m_CheckSightCount > 60) //60stick 3s
			{
				m_pAINpcPlayer->setNeedRetHome(true);
				return false;
			}
		}

		return true;
	}
}

void AIPlayerTargetHurtee::update()
{
	return;
}