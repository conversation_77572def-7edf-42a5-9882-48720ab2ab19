
#ifndef __CONTAINER_H__
#define __CONTAINER_H__


#include "SandboxObject.h"
#include "proto_common.h"
#include "RuneDef.h"
#include "SandboxAutoRef.h"

class GridDataComponent;
class GunGridDataComponent;
class EntryGridDataComponent;
namespace jsonxx
{
	class Object;
}
 
namespace game {
    namespace common {
        class PB_ItemData;
		class PB_ArmFumo;
		class PB_ItemIndexGrid;
		class PB_ActorItem;
		class PB_Arm;
    }
}
//tolua_begin
#define ACTORMODEL_START_INDEX		-1     //联机，生物模型方块离开世界时，用来关闭界面的
#define BACKPACK_START_INDEX		0     //背包
#define SHORTCUT_START_INDEX		1000  //快捷栏(非家园或者家园玩法模式)
#define MINICRAFT_START_INDEX		2000  //简易合成栏
#define STORAGE_START_INDEX			3000 //储物箱
#define CRAFT_START_INDEX			4000  //合成栏
#define COMMON_START_INDEX			6000  //常用物品
#define MOUSE_PICKITEM_INDEX		7000  //鼠标拾取的物品
#define EQUIP_START_INDEX			8000  //装备栏位
#define FURNACE_START_INDEX			9000  //熔炉栏位
#define PRODUCT_LIST_TWO_INDEX		10000 //2*2制作物列表
#define COMMON_PRODUCT_LIST_INDEX   11000 //3*3常用制作物列表
#define EQUIP_PRODUCT_LIST_INDEX    12000 //3*3装备制作物列表
#define BUILD_PRODUCT_LIST_INDEX    13000 //3*3建筑制作物列表
#define MACHINE_PRODUCT_LIST_INDEX  14000 //3*3机械制作物列表
#define REPAIR_START_INDEX			15000 //修理栏
#define ENCHANT_START_INDEX			16000 //附魔台
#define SIGNS_START_INDEX           17000 //告示牌
#define NPCTRADE_START_INDEX        18000 //npc商店
#define FUNNEL_START_INDEX          19000 //漏斗
#define EMITTER_START_INDEX         20000 //发射器
#define HORSE_EQUIP_INDEX           21000 //马的装备
#define SENSOR_START_INDEX          22000 //感应器的比较物品栏
#define RECIPE_PRODUCT_LIST_INDEX   23000	//3*3配方制作物列表
#define FURNACE_OXY_START_INDEX		24000	//氧气炉栏位
#define INTERPRETER_START_INDEX		25000	//存放指令集栏位
#define BLUEPRINT_START_INDEX		26000	//蓝图工作台栏位
#define BUILDBLUEPRINT_START_INDEX	27000	//蓝图建筑消耗栏位
#define COLLIDER_START_INDEX		28000	//触碰方块筛选栏位
#define CUSTOMMODEL_START_INDEX		29000	//方块微雕工作台栏位
#define BRUSHMONSTER_START_INDEX    30000	// 刷怪方块栏位
#define BOOKCABINET_START_INDEX		31000	//书架方块栏位
#define EDITBOOK_START_INDEX        32000	//编书台栏位
#define VEHICLE_START_INDEX			33000	// 机械工作台栏位
#define ACTIONER_START_INDEX		34000	//动作序列器
#define DRIVER_SEAT_START_INDEX		35000	//驾驶座、乘客座
#define SENSOR_VALUE_START_INDEX		36000	//光照距离高度感应容器
#define BONFIRE_START_INDEX			37000	//篝火方块栏位
#define WORKSITE_START_INDEX			38000	// 物理工地栏位
#define TOMBSTONE_STRAT_INDEX			39000   //墓碑的工地栏位
#define ALTAR_STRAT_INDEX			    40000   //祭台的工地栏位
#define TAMED_MOB_START_INDEX			41000	// 驯服怪的栏位
#define SHORTCUTEX_START_INDEX			42000  //快捷栏(家园编辑模式)
#define POT_START_INDEX			43000  //锅
#define COOKING_PRODUCT_LIST_INDEX  44000 // 烹饪配方
#define RUNE_INLAY_CONTAINER_INDEX  45000 // 符文镶嵌
#define RUNE_MERGE_CONTAINER_INDEX  46000 // 符文合成
#define RUNE_AUTH_CONTAINER_INDEX   47000 // 符文鉴定
#define MINI_CLUB_INDEX             48000	// 跳舞方块 code by wuyuwang 2021年9月18日
#define PROPS_PRODUCT_LIST_INDEX             49000	// 3*3道具制作物列表
#define MATERIAL_PRODUCT_LIST_INDEX          50000	// 3*3材料制作物列表
#define DECORATE_PRODUCT_LIST_INDEX          51000	// 3*3装饰制作物列表 
#define SHORTCUT_START_INDEX_EDIT		     52000  //编辑模式下的快捷栏（非家园）
#define SHORTCUT_SINGLE_INDEX_EDIT			 53000  //编辑模式下的，高级模式下的快捷栏（非家园）
#define DETECTIONPIPE_START_INDEX         54000  //检测管道
#define MANUAL_EMITTER_START_INDEX         55000 //手动发射器
#define STOVE_START_INFEX                  56000 //秘银炉
#define NEWPOT_START_INDEX				   57000  //新锅
#define EXT_BACKPACK_START_INDEX		   58000  //扩展背包
#define WITHHOLD_BACKPACK_START_INDEX	   59000    //被预扣除 背包  用来存储被预扣除的物品

#define MAX_PACKINDEX_TYPE     150			/* 添加一种index之后 这里要一起对应加上去 不然新加的是没有索引的
											  约定0--100给c++用，101--149给lua用    !!!!!!!
											  这个值主要是backpack用到，150足够 code by:DemonYan  Desc：lua化
											*/

#define GRID_INDEX_BASIS	1000
//tolua_end

#define MAX_ITEM_ENCHANTS  5


struct ItemDef;
struct EnchantDef;
struct RuneDef;

namespace MNSandbox
{
	class MNTimer;
}

class EXPORT_SANDBOXENGINE BackPackGrid;
class BackPackGrid//tolua_exports
{//tolua_exports
	int index;
	int num;
	int durable;
	int maxDurable;    // 最大耐久会变化，所以需要单独保存
	int enchant_num;
	int toughness;		// 韧性
	int enchants[MAX_ITEM_ENCHANTS];
	GridRuneData runedata;
public:
	const ItemDef *def;
	void *userdata = NULL;		
	void *userdataEx;	//家园蓝图使用
	int enough;			//0材料不足 1材料充足
	int sortId;			//排序用的
	std::string userdata_str;
	std::string sid_str;	//家园物品服务器id
	std::map<std::string, GridEffect> m_effects;
	int m_ItemID;		//匹配def的itemid
	std::map<std::string, int> m_KVS;  // 扩展数据 如枪械配件
	int m_nWaterVolume;
	MNSandbox::AutoRef<MNSandbox::MNTimer> m_touRecTimer;	// 韧性恢复定时器
	bool isAttracted;//判断是否被吸引
	int m_nDataEX = 0;
public:
	//tolua_begin
	BackPackGrid() 
		: userdata_str("")
		, sid_str("")
		, def(nullptr)
		, userdata(nullptr)
		, userdataEx(nullptr)
		, enough(0)
		, sortId(0)
		, index(0)
		, num(0)
		, durable(0)
		, maxDurable(0)
		, enchant_num(0)
		, m_ItemID(0)
		, toughness(0)
		, m_touRecTimer(nullptr)
		, isAttracted(false)
		, m_nDataEX(0)
	{
		m_effects.clear();
	}

	BackPackGrid(const BackPackGrid &rhs);
	virtual ~BackPackGrid();
	void reset(int index);
	//tolua_end
	const BackPackGrid &operator=(const BackPackGrid &rhs);
	//tolua_begin
	void clear();
	int setItem(int itemid, int num, int durable = -1, int toughness = -1, void *userdata = 0, int enough = 1, int sortId = 0, const char *userdata_str = "", const char *pSid = "");
	int getItemID() const;
	int getInvolvedID() const;
	int getSrcItemID() const;
	int getNum() const;
	int getMaxStack() const;
	
	int getDuration() const;
	void setDuration(int dur);
	int getMaxDuration() const;	
	int getDefMaxDuration() const;	 // 获取默认最大耐久
	void setMaxDuration(int maxDur);

	void setKVData(const std::string& key, int value);
	int getKVData(const std::string& key) const;

	void setDataEX(int value);
	int getDataEX() { return m_nDataEX; };

	int getToughness() const;
	int getMaxToughness() const;
	int getItemMeshType() const;
	int getRuneNum() const;
	void setUserdataStr(const char *str);
	std::string getUserdataStr();
	const char* getUserDataStrLua() { return userdata_str.c_str(); };
	void setSidStr(const char *pSid);
	std::string getSidStr() { return  sid_str; }

	bool addEnchant(int id, bool isCountDur = true);
	int getUserDataInt()
	{
		return (int)(size_t)userdata;
	}
	int getIndex() const;
	bool isEmpty() const;
	
	void setToughness(int tou);
	void setUserDataInt(int i)
	{
		userdata = (void *)i;
	}
	int getWaterVolume();
	int getMaxWaterVolume();
	void addWaterVolume(int value);//此函数现在只用在水袋类道具。记录储水量
	void AddEffect(const char * effect , float scale);
	void RemoveEffect(const char* effect);

	void setItem(const BackPackGrid &src, int copynum=-1);
	//tolua_end

	void setIndex(int i);

	// 韧性恢复相关接口
	void recoverToughness();
	void stopRecoverToughness();
	//rune符文相关
	//tolua_begin
	GridRuneData& getRuneData();
	const GridRuneData& getRuneData() const;//获取符文属性数据
	// 获取当前格子的物品指定下标的的符文数据
	const GridRuneItemData* getRuneItemData(int runeIndex) const;
	//tolua_end
	void loadRunesAndEnchants(const BackPackGrid* data);//设置附魔符文属性信息
	void loadRunesAndEnchants(const game::common::PB_ArmFumo& src);//
	void saveRunesAndEnchants(game::common::PB_ArmFumo* dest) const;
	void loadRunesAndEnchants(const game::common::PB_ItemData &src);
	void saveRunesAndEnchants(game::common::PB_ItemData *dest) const;
	bool hasRuneOrEnchants()const;
	void changeDurationOnRuneOrEnchantChange(int old_dur_enchant);//耐久附魔或者符文改变(增加/删除 升级/降级)后   对耐久值的影响	

	bool addRune(const GridRuneItemData &one, bool isCountDur = true);//添加一条符文
	bool removeRune(int id, bool isCountDur = true);//移除一条符文
	bool replaceRune(const GridRuneItemData &one,  int index);

	bool getAttracted()//获取是否被吸引
	{
		return isAttracted;
	}
	void setAttracted(bool value)//设置是否被吸引
	{
		isAttracted = value;
	}
	int getNumEnchant() const;
	const int *getEnchants() const;
	int *getEnchants();
	int getIthEnchant(int i) const;
	//tolua_begin
	int addNum(int n);
	void setNum(int n);
	//给组件使用接口
	std::string getJsonStr();
	void loadJsonStr(std::string jsonstr);

	//组件修改tip使用
	void setModItemName(const std::string & name);
	void setModItemDesc(const std::string& desc);
	void setModExtradata(const std::string& extradata);

	
	std::string getModItemName();
	std::string getModItemDesc();
	std::string getModExtradata();

	//获取枪的最终伤害值
	float getGunDamage();

	//tolua_end
	void setEnchants(int n, const int *ids);
	bool removeEnchant(int id);

	flatbuffers::Offset<FBSave::ItemGrid> save(flatbuffers::FlatBufferBuilder &builder);
	flatbuffers::Offset<FBSave::ItemIndexGrid> saveWithIndex(flatbuffers::FlatBufferBuilder &builder);
	void load(const FBSave::ItemGrid *src);
	void load(const FBSave::ItemIndexGrid *src);
	
	//新存储接口
	void save(jsonxx::Object &obj);
	void saveWithIndex(jsonxx::Object &obj);
	void load(const jsonxx::Object &obj);
	void loadWithIndex(const jsonxx::Object &obj);

	int savePB(game::common::PB_ItemIndexGrid* equip);
	int loadPB(const game::common::PB_ItemIndexGrid& equip);

	int getDurationEnchant() const;
	int addDuration(int d, bool limit_maxdur=false);
	int addToughness(int t);

	int getConstUserDataInt() const;
	/**
		添加一个设置成员变量def的函数
	*/
	void setItemDef(const ItemDef* itemdef);

	bool haveEnchant(int type)const;
	int findEnchantIndex(int type)const;

	//Data组件
	GridDataComponent* getGunDataComponent();
	GridDataComponent* addGunDataComponent();
	GridDataComponent* getEntryDataComponent();
	GridDataComponent* addEntryDataComponent();
	GridDataComponent* getDataComponent(const std::string& name);
	void AddAndLoadDataComponent(const std::string& name, const jsonxx::Object& componentData);
	void removeDataComponent(std::string& name);
	void saveDataComponent(flatbuffers::FlatBufferBuilder& builder, flatbuffers::Offset<flatbuffers::Vector<int8_t>>& ugccomponentsOffset);
	void saveKVS(flatbuffers::FlatBufferBuilder& builder, flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::KVData>>>& kvsOffset);
	void loadDataComponent(const flatbuffers::Vector<int8_t>* datacomponents);
	void loadDataComponent(const BackPackGrid* data);
	void saveDataComponentPB(game::common::PB_ItemIndexGrid* item) const;
	void loadDataComponentPB(const game::common::PB_ItemIndexGrid& item);
	void saveDataComponentPB(game::common::PB_ActorItem* item) const;
	void loadDataComponentPB(const game::common::PB_ActorItem& item);
	void saveDataComponentPB(game::common::PB_ItemData* item) const;
	void loadDataComponentPB(const game::common::PB_ItemData& item);
	void saveDataComponentPB(game::common::PB_Arm* item) const;
	void loadDataComponentPB(const game::common::PB_Arm& item);
	void removeAllDataComponents();
	void copyDataDataComponentsFrom(const std::vector<GridDataComponent*>& dest);
	void copyDataDataComponents(const BackPackGrid& src);
	const std::vector<GridDataComponent*>* getDataComponentsPtr() const;//获取全部组件的指针
	bool hasDataComp() const
	{
		return m_dataComps.size() > 0;
	}
private:
	std::vector<GridDataComponent*> m_dataComps;//数据组件
	bool needSrcItemId() const;
	bool canChangeDuration() const;
};//tolua_exports

class EXPORT_SANDBOXENGINE BaseContainer;
class BaseContainer : public MNSandbox::Object {//tolua_exports
public:
	static void SetLuaOpenContainer(const char* classname, void* ptr);
	BaseContainer(int baseindex) : m_BaseIndex(baseindex), m_AttachToUI(false){}
	virtual ~BaseContainer();
	//tolua_begin
	virtual BackPackGrid *index2Grid(int index) = 0;
	virtual void afterChangeGrid(int index) = 0;
	//tolua_end
	virtual bool canPutItem(int index) = 0;
	virtual void onAttachUI() = 0;
	virtual void onDetachUI() = 0;

	//params:  bit0-bit2--dir
	//return 实际能插入多少个,  0表示满,  -1表示类型不对
	virtual int onInsertItem(const BackPackGrid &grid, int num, int params)
	{
		return 0;
	}
	//是否还能插入物品
	virtual bool canInsertItem(const BackPackGrid& grid, int param)
	{
		return false;
	}
	virtual BackPackGrid *onExtractItem(int params)
	{
		return NULL;
	}
	virtual void onSubtractItem(BackPackGrid *grid, int num); //从属于container的grid里面减去多少个物品

	virtual int getGridCount()
	{
		return getGridNum();
	}

	virtual int getGridNum()
	{
		return 0;
	}

	//新接口 会复制符文信息
	virtual int addItem_byGrid(const BackPackGrid* data);
	virtual int addItem_byGridWithNum(const BackPackGrid* data, int num);
	virtual int addItem_byGridCopyData(const GridCopyData& grid);
	//此老接口不建议再外部调用,尽量请使用addItem_byGridCopyData接口
	//virtual int addItem(int resid, int num, int durable=-1, int enchantnum=0, const int enchants[]=0, void *userdata=0, const char *userdata_str = "")
	//{
	//	return 0;
	//}

	int getBaseIndex()
	{
		return m_BaseIndex;
	}
	void setBaseIndex(int baseIndex)
	{
		m_BaseIndex = baseIndex;
	}
	bool isAttachedToUI()
	{
		return m_AttachToUI;
	}

protected:
	int m_BaseIndex;
	bool m_AttachToUI;
private:
	static void* s_CurrentOpenContainerLua;
};//tolua_exports

inline int SetBackPackGrid(BackPackGrid &grid, int itemid, int num, int durable=-1, int toughness = -1, void *userdata=0, int enough=1, int sortId = 0, const char *userdata_str = "", const char *sid_str = "")
{
	return grid.setItem(itemid, num, durable, toughness, userdata, enough, sortId, userdata_str, sid_str);
}
inline int SetBackPackGridWithClear(BackPackGrid &grid, int itemid, int num, int durable=-1, int toughness = -1, void *userdata=0, int enough=1, int sortId = 0, const char *userdata_str = "", const char *sid_str = "")
{
	if(itemid==0 || num==0) return SetBackPackGrid(grid, 0, 0);
	else return SetBackPackGrid(grid, itemid, num, durable, toughness, userdata, enough, sortId, userdata_str, sid_str);
}

EXPORT_SANDBOXENGINE extern int InsertItemToSameGrids(BaseContainer *container, int baseindex, BackPackGrid *gridarray, int arraylen, int resid, int num);
//extern int InsertItemToEmptyGrids(BaseContainer *container, int baseindex, BackPackGrid *gridarray, int arraylen, int resid, int num, int durable, int toughness, int enchantnum, const int enchants[], void *userdata, const char *userdata_str, int* emptyIndex = NULL);
//extern int InsertItemToEmptyGridsWithTunestone(BaseContainer *container, int baseindex, BackPackGrid *gridarray, int arraylen, int resid, int num, int durable, int toughness, int tunestonenum, const int tunestones[], void *userdata, const char *userdata_str, int* emptyIndex = NULL);
//extern int InsertItemIntoArray(BaseContainer *container, BackPackGrid *gridarray, int arraylen, int resid, int num, int durable, int toughness, int enchantnum, const int enchants[], void *userdata=0, const char *userdata_str="");
//extern int InsertItemIntoArrayWithTunestone(BaseContainer *container, BackPackGrid *gridarray, int arraylen, int resid, int num, int durable, int toughness, int tunestonenum, const int tunestones[], void *userdata=0, const char *userdata_str="");
EXPORT_SANDBOXENGINE extern int InsertItemIntoArray(BaseContainer *container, BackPackGrid *gridarray, int arraylen, const BackPackGrid &grid, int num);
EXPORT_SANDBOXENGINE extern void SubtractItemFromContainerEx(BaseContainer *container, BackPackGrid *grid, int num, int index);
EXPORT_SANDBOXENGINE extern int CalculateItemsComparatorInput(const BackPackGrid *gridarray[], int arraylen);
EXPORT_SANDBOXENGINE extern int CalculateItemsComparatorInputOld(const BackPackGrid *gridarray, int arraylen);
EXPORT_SANDBOXENGINE extern int CalculateItemsComparatorInput(const BackPackGrid *gridarray, int arraylen);
//extern int InsertItemToGridsByIndex(BaseContainer *container, int baseindex, BackPackGrid *gridarray, int arraylen, int gridIndex, int resid, int num, int durable, int toughness, int enchantnum, const int enchants[], void *userdata, const char *userdata_str);
EXPORT_SANDBOXENGINE extern int InsertItemToSameGridsWithUserData(BaseContainer* container, int baseindex, BackPackGrid* gridarray, int arraylen, int resid, int num, const char* userdata_str = "");


EXPORT_SANDBOXENGINE extern int InsertItemToEmptyGrids_byGridCopyData(BaseContainer *container, int baseindex, BackPackGrid *gridarray, int arraylen, const GridCopyData& grid, int* emptyIndex = NULL);
EXPORT_SANDBOXENGINE extern int InsertItemIntoArray_byGridCopyData(BaseContainer *container, BackPackGrid *gridarray, int arraylen, const GridCopyData& grid );
EXPORT_SANDBOXENGINE extern int InsertItemToGridsByIndex_byGridCopyData(BaseContainer *container, int baseindex, BackPackGrid *gridarray, int arraylen, int gridIndex, const GridCopyData& grid);

EXPORT_SANDBOXENGINE extern int g_BackgridCheckNumMethod;
///**
// * @brief 将tool_type转换为装备栏位置
// *
// * @param tool_type 装备类型
// * @return int 装备栏index, 非装备类型则返回0
// */
//EXPORT_SANDBOXENGINE extern int ToolType2EquipIndex(int tool_type);

EXPORT_SANDBOXENGINE \
	extern int CheckInsertItemIntoArray(BaseContainer* container, BackPackGrid* gridarray, int arraylen, const BackPackGrid& grid);//tolua_export
//tolua_begin
inline bool IsGridIndexType(int index, int gt)
{
	return index/GRID_INDEX_BASIS == gt/GRID_INDEX_BASIS;
}
/**
 * @brief 判断index是否属于装备栏
 * 
 * @param grid_index 需要判断的index
 * @return true 属于
 */
inline bool IsEquipIndex(int grid_index){
	return grid_index >= EQUIP_START_INDEX && grid_index < EQUIP_START_INDEX + 8;
}
/**
 * @brief 判断index是否属于坐骑装备栏
 * 
 * @param grid_index 需要判断的index
 * @return true 属于
 */
inline bool IsHorseEquipIndex(int grid_index){
	return grid_index >= HORSE_EQUIP_INDEX && grid_index <= HORSE_EQUIP_INDEX + 3;
}
//tolua_end


#endif