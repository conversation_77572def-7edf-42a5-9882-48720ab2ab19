
namespace FBSave;

enum Color:byte { Red = 0, Green, Blue = 2 }

struct Coord3 {
  x:int;
  y:int;
  z:int;
}

struct Vec2 {
  x : float;
  y : float;
}

struct Vec3 {
  x : float;
  y : float;
  z : float;
}

table BoxBound
{
	min : Vec3;
	max : Vec3;
}

struct Quat4 {
  x : float;
  y : float;
  z : float;
  w : float;
}

struct Mat3x3f {
  _11 : float;
  _12 : float;
  _13 : float;
  _21 : float;
  _22 : float;
  _23 : float;
  _31 : float;
  _32 : float;
  _33 : float;
}

struct Mat4x4f {
  _11 : float;
  _12 : float;
  _13 : float;
  _14 : float;
  _21 : float;
  _22 : float;
  _23 : float;
  _24 : float;
  _31 : float;
  _32 : float;
  _33 : float;
  _34 : float;
  _41 : float;
  _42 : float;
  _43 : float;
  _44 : float;
}

table SawtoothInfo
{
	sawtoothid : uint;
	pos : Vec3;
}

table ActorCommon {
  wid : ulong;
  pos : Coord3;
  motion : Vec3;
  yaw : float;
  pitch : float;
  falldist : float;
  flags : uint;
  flagsex : uint;
  liveticks : uint;
  attinfo : ActorAttInfo;
  masterobjid : ulong;
  sawtooth : [SawtoothInfo];
  sandboxnodes : string;
  firstsearch : byte;
}

table ActorAttInfo {
  maxhp : float = -1.0;
  hprecover : float = -1.0;
  walkspeed : float = -1.0;
  swimspeed : float = -1.0;
  jumppower : float = -1.0;
  punchattack : float = -1.0;
  rangeattack : float = -1.0;
  punchdefense : float = -1.0;
  rangedefense : float = -1.0;
  dodge : int = -1;
  attacktype : int = -1;
  immunetype : int = 0;
  settingatt : uint = 1023;
  unsighted : byte;
}

table ItemGridRune{
  runeid : int;
  runeval0 : float;
  runeval1 : float;
  itemid : int;
}

table KVData {
  key : string;
  val : int;
}

table ItemGrid {
  itemid : int;
  num : short;
  durable : short;
  enchants : [int];
  userdata : short;
  userdata_str : string;
  sid_str : string;
  userdataEx : int;
  runes : [ItemGridRune];
  toughness : short;
  datacomponents : [byte];
  maxdurable : short;
  KVS : [KVData];
  dataex : int;
}

table ItemIndexGrid {
  index : int;
  itemid : int;
  num : short;
  durable : short;
  enchants : [int];
  userdata : short;
  userdata_str : string;
  sid_str : string;
  userdataEx : int;
  runes : [ItemGridRune];
  toughness : short;
  datacomponents : [byte];
  maxdurable : short;
  KVS : [KVData];
  dataex : int;
}

struct ActorBuff {
  buffid : uint;
  bufflv : ushort;
  ticks : int;
}

struct AttribMod {
  attr : int;
  val : float;
}

table Achievement {
  id : int;
  val : int;
  state : byte;
  getaward : byte;
  completeyear : ushort;
  completemonth : byte;
  completeday : byte;
}

table AvatarModelData {
  modelfilename : string;  //绑定微缩模型的key
  scale : float;
  yaw : float;
  pitch : float;
  offsetpos : Coord3;
  roll : float;
  newrotatemode : bool = false;
  scale3 : Vec3;
}

table BoneModelData {
  bonename : string;
  avatarmodels : [AvatarModelData];
}

table VehicleBlockLine {
  from : ushort;
  to : ushort;
  canedit : byte;
}

table UgcVertexData {
  verts : [Vec3];
  indexes : [ushort];
}

table KeyValueData {
  key : string;
  value : short;
}

root_type UgcVertexData;