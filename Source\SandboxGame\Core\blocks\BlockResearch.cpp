#include "BlockResearch.h"
#include "BlockMaterialMgr.h"
#include "IClientPlayer.h"
#include "world.h"
#include "IClientGameInterface.h"
#include "Collision.h"
#include "VehicleWorld.h"
#include "IClientGameManagerInterface.h"
#include "backpack.h"

#include "SoundComponent.h"
#include "SandboxIdDef.h"
#include "WorldManager.h"
#include "ClientPlayer.h"
#include "container_research.h"
#include "BlockErosionHelper.h"
//#include "container_decomposition.h"

IMPLEMENT_BLOCKMATERIAL(BlockResearch)
void BlockResearch::init(int resid)
{
    MultiModelBlockMaterial::init(resid);
    SetToggle(BlockToggle_HasContainer, true);
}

void BlockResearch::update(unsigned int dtick)
{
	Super::update(dtick);

	//LOG_INFO("BlockResearch::update %d", dtick);
}

void BlockResearch::onBlockPlacedBy(World *pworld, const WCoord &blockpos, IClientPlayer *player)
{
	MultiModelBlockMaterial::onBlockPlacedBy(pworld, blockpos, player);
	ClientPlayer* playerTmp = player->GetPlayer();
	if (!playerTmp) return ;
	int blockdata = playerTmp->getCurPlaceDir();
	pworld->setBlockData(blockpos, blockdata, 3);

	int nGuideTask = 0;
	MNSandbox::GetGlobalEvent().Emit<int&>("ClientAccountMgr_getCurNoviceGuideTask",nGuideTask);
	if(playerTmp->getOWID() == NEWBIEWORLDID && nGuideTask == 13)
	{
		auto pgame = GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame);
		if(pgame)
		{
			int x = blockpos.x * 100 + 50;
			int y = blockpos.y * 100 + 50;
			int z = blockpos.z * 100 - 20;
			pgame->playEffect(x, y, z, "1034.ent");
		}
	}
}

int BlockResearch::getBlockGeomID(int *idbuf, int *dirbuf, const SectionDataHandler* sectionData, const WCoord &blockpos, World* world)
{
	int dir = sectionData->getBlock(blockpos).getData() & 3;

	idbuf[0] = 0;
	dirbuf[0] = dir;
	return 1;
}

bool BlockResearch::onMultiTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint, WorldContainer* container)
{
	if (pworld->isRemoteMode())
	{
		return true;
	}

	if (container && player)
	{
		// 是否禁止多人操作
		if (container->isOpenning())
		{
			return false;
		}

		pworld->getEffectMgr()->playSound(BlockCenterCoord(blockpos), "misc.chest_open", 1.0f, GenRandomFloat() * 0.2f + 0.8f);
		player->openContainer(container);

		return true;
	}

	return true;

	return false;
}

WorldContainer* BlockResearch::createMultiContainer(World* pworld, const WCoord& blockpos)
{
	//return SANDBOX_NEW(WorldStorageBox, blockpos);
	return SANDBOX_NEW(ContainerResearch, blockpos, m_BlockResID);
}

void BlockResearch::createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos)
{
	WCoord p0 = blockpos*BLOCK_SIZE;
	coldetect->addObstacle(p0, p0+WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
}

int BlockResearch::getPlaceBlockDataByPlayer(World* pworld, IClientPlayer* player)
{
	if (player)
	{
		ClientPlayer* playerTmp = player->GetPlayer();
		if (!playerTmp) return 0;
		// 对于板块，我们可以使用现有的朝向或者一个固定值
		return playerTmp->getCurPlaceDir();
	}
	return 0;
}

void BlockResearch::initGeomName()
{
	m_geomName = m_Def->Texture1.c_str();
}

// 使用宏来减少重复代码（多块方块需要获取核心位置）
IMPLEMENT_MULTIBLOCK_EROSION_DAMAGE_FUNCTIONS(BlockResearch)
