#include "container_sandboxGame.h"
#include "LuaInterfaceProxy.h"
#include "SandboxIdDef.h"
#include "world.h"
#include <WorldManager.h>
#include "BlockScene.h"
#include <BlockMaterialMgr.h>
#include "TerritoryManager.h"
#include "container_territory.h"
#include "DefManagerProxy.h"
#include "ClientPlayer.h"
#include "backpack.h"
#include "ClientActorHelper.h"
#include "ClientActorManager.h"
#include "ClientMob.h"
#include "BlockSocLogicInterface.h"
#include "GameNetManager.h"
#include "PlayerControl.h"

//-----------------------------------------------------containerWaterStorage--------------------------------------------------------------------------
/**********************************************************************************************
类    名：containerWaterStorage
功    能：蓄水方块container
********************************************************************************************* */
containerWaterStorage::containerWaterStorage():ErosionContainer(STORAGE_START_INDEX)
{
	m_NeedTick = true;
}

containerWaterStorage::containerWaterStorage(const WCoord& blockpos, int blockId) :ErosionContainer(blockpos, blockId, STORAGE_START_INDEX)
{
	m_NeedTick = true;
}

void containerWaterStorage::initCollectData(World* pworld)
{
	auto biome = pworld->getBiome(m_BlockPos.x, m_BlockPos.z);
	if (biome)
	{
		m_nOnceTickMax = biome->WaterStorageCollectOnceTime * CollectWaterTickToSecond;
		m_nOnceVolume = biome->WaterStorageCollectOnceVolume;
	}
	auto blockid = pworld->getBlockID(m_BlockPos);
	auto pitemdef = GetDefManagerProxy()->getItemDef(blockid);
	if (pitemdef)
	{
		m_nWaterVolumeMax = atoi(pitemdef->para.c_str());
	}
}

bool containerWaterStorage::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerWaterStorage*>(srcdata);
	loadErosionContainer(src->basedata());

	m_nCurWaterVolume = src->watervolume();

	return true;
}

flatbuffers::Offset<FBSave::ChunkContainer> containerWaterStorage::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerErosion(builder);

	auto actor = FBSave::CreateContainerWaterStorage(builder, basedata, m_nCurWaterVolume);

	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerWaterStorage, actor.Union());
}

void containerWaterStorage::enterWorld(World* pworld)
{
	ErosionContainer::enterWorld(pworld);
	initCollectData(pworld);
	registerUpdateTick();
	//刷新ui
	MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("RefreshWater",
		MNSandbox::SandboxContext(nullptr)
	);
	if (GetWorldManagerPtr())
	{
		
	}
	//updateEffect();
	pworld->markBlockForUpdate(m_BlockPos, true);
}

void containerWaterStorage::leaveWorld() {
	WorldContainer::leaveWorld();
	//stopEffect();
}

void containerWaterStorage::updateTick()
{
	ErosionContainer::updateTick();
	if (m_nOnceTickMax <= m_nTickCounter)
	{
		m_nTickCounter = 0;
		addWater(m_nOnceVolume);
	}
	else m_nTickCounter++;
	//m_desertSleepCount = -1;
}

void containerWaterStorage::addWater(int value)
{
	m_nCurWaterVolume += value;
	if (value < 0)
	{
		m_nCurWaterVolume = std::max(0, (int)m_nCurWaterVolume);
		m_World->markBlockForUpdate(m_BlockPos);
	}
	else
	{
		m_nCurWaterVolume = std::min((int)m_nWaterVolumeMax, (int)m_nCurWaterVolume);
		m_World->markBlockForUpdate(m_BlockPos);
	}
}

//void containerWaterStorage::updateEffect() {
//	if (m_World)
//	{
//		int blockdata = m_World->getBlockData(m_BlockPos);
//		if ((blockdata & 4) != 0)
//		{
//			return;
//		}
//		WCoord effectPos = getEffectPos(blockdata);
//		int maxage = Rainbow::MAX_INT;
//		m_World->getEffectMgr()->playParticleEffectAsync("particles/mob_3200_agonal.ent", effectPos, maxage);
//
//	}
//}

//void containerWaterStorage::stopEffect()
//{
//	if (m_World)
//	{
//		int blockdata = m_World->getBlockData(m_BlockPos);
//		stopEffectByBlockdata(blockdata);
//	}
//}

//void containerWaterStorage::stopEffectByBlockdata(int blockdata)
//{
//	if ((blockdata & 4) != 0)
//	{
//		return;
//	}
//	WCoord effectPos = getEffectPos(blockdata);
//	m_World->getEffectMgr()->stopParticleEffect("particles/mob_3200_agonal.ent", effectPos);
//}

//WCoord containerWaterStorage::getEffectPos(int blockdata)
//{
//	int placedir = blockdata & 3;
//	WCoord effectPos = NeighborCoord(m_BlockPos, ReverseDirection(placedir));
//	if (placedir == DIR_NEG_X)
//	{
//		effectPos = BlockCenterCoord(effectPos) + WCoord(30, 100, 0);
//	}
//	else if (placedir == DIR_POS_X)
//	{
//		effectPos = BlockCenterCoord(effectPos) + WCoord(-30, 100, 0);
//	}
//	else if (placedir == DIR_NEG_Z)
//	{
//		effectPos = BlockCenterCoord(effectPos) + WCoord(0, 100, 30);
//	}
//	else if (placedir == DIR_POS_Z)
//	{
//		effectPos = BlockCenterCoord(effectPos) + WCoord(0, 100, -30);
//	}
//	return effectPos;
//}

//-----------------------------------------------------containerArchitecture--------------------------------------------------------------------------
/**********************************************************************************************
类    名：containerArchitecture
功    能：建筑类型方块container
********************************************************************************************* */
containerArchitecture::containerArchitecture()
{
	m_NeedTick = true;
}

containerArchitecture::containerArchitecture(const WCoord& blockpos, int blockId, int bptypeid, int bplevel) 
	:ErosionContainer(blockpos, blockId)
	,m_nBluePrintLevel(bplevel)
	,m_nBluePrintTypeId(bptypeid)
{
	m_nCanUnDoTick = 120 * CollectWaterTickToSecond;
	m_NeedTick = true;
}

 bool containerArchitecture::load(const void* srcdata)
 {
 	auto src = reinterpret_cast<const FBSave::ContainerArchitecture*>(srcdata);
 	loadErosionContainer(src->basedata());

 	m_nBluePrintTypeId = src->blueprinttype();
	m_nBluePrintLevel = src->architecturelv();
	m_nCanUnDoTick = src->undo();
 	return true;
 }

 flatbuffers::Offset<FBSave::ChunkContainer> containerArchitecture::save(SAVE_BUFFER_BUILDER& builder)
 {
 	auto basedata = saveContainerErosion(builder);

 	auto actor = FBSave::CreateContainerArchitecture(builder, basedata, m_nBluePrintTypeId, m_nBluePrintLevel, m_nCanUnDoTick);

 	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerArchitecture, actor.Union());
 }

void containerArchitecture::enterWorld(World* pworld)
{
	ErosionContainer::enterWorld(pworld);
	registerUpdateTick();
	if (GetWorldManagerPtr())
	{

	}
	//updateEffect();
	pworld->markBlockForUpdate(m_BlockPos, true);
}

void containerArchitecture::leaveWorld() {
	ErosionContainer::leaveWorld();
	//stopEffect();
}

void containerArchitecture::updateTick()
{
	ErosionContainer::updateTick();
	if (m_nCanUnDoTick > 0) m_nCanUnDoTick--;
	//m_desertSleepCount = -1;
}

//void containerArchitecture::initBluePrintData()
//{
//	if (m_BlockID >= 2501 && m_BlockID <= 2508)
//	{
//		m_nBluePrintLevel = (m_BlockID - 2499) / 2;
//		m_nBluePrintTypeId = (m_BlockID - 2501) % 2 + 1;
//	}
//	else if (m_BlockID >= 2530 && m_BlockID <= 2541)
//	{
//		m_nBluePrintLevel = ((m_BlockID - 2530) / 3) + 1;
//		m_nBluePrintTypeId = (m_BlockID - 2530) % 3 + 3;
//	}
//}
bool containerArchitecture::getCanUnDo()
{ 
	return m_nCanUnDoTick > 0;
}

int containerArchitecture::checkArchitectureResEnough(int type, int upgradeLevel, ClientPlayer* player, bool deal)
{
	// 检查是否有有效的世界和蓝图数据
	if (m_nBluePrintTypeId == 0 || m_nBluePrintLevel == 0 || !player) {
		return -3;
	}
	// 获取建筑蓝图数据
	const ArchitecturalBlueprintCsvDef* blueprintDef = GetDefManagerProxy()->getArchitecturalBlueprintCsvDef(m_nBluePrintTypeId);
	if (!blueprintDef) {
		return -3;
	}

	float fProportion = 1.0f;
	int curLevel = m_nBluePrintLevel;
	if (type == 1)
	{
		if (m_nBluePrintLevel >= upgradeLevel)
		{
			return -2;
		}
		curLevel = upgradeLevel;
	}
	else if (type == 2)
	{
		fProportion = 0.1f;
	}
	else if (type == 3)
	{
		fProportion = 0.5f;
	}

	// 查找目标方块ID和所需材料
	int targetBlockId = -3;
	std::map<int, int> requiredMaterials;

	if (curLevel == BranchStyle)
	{
		if (blueprintDef->Branch.ProduceItemID > 0)
		{
			targetBlockId = blueprintDef->Branch.ProduceItemID;
			if (blueprintDef->Branch.ConsumeItemID > 0 && blueprintDef->Branch.Count > 0) {
				requiredMaterials[blueprintDef->Branch.ConsumeItemID] = blueprintDef->Branch.Count;
			}
		}
	}
	else if (curLevel == WoodStyle)
	{
		if (blueprintDef->Wood.ProduceItemID > 0)
		{
			targetBlockId = blueprintDef->Wood.ProduceItemID;
			if (blueprintDef->Wood.ConsumeItemID > 0 && blueprintDef->Wood.Count > 0) {
				requiredMaterials[blueprintDef->Wood.ConsumeItemID] = blueprintDef->Wood.Count;
			}
		}
	}
	else if (curLevel == StoneStyle)
	{
		if (blueprintDef->Stone.ProduceItemID > 0)
		{
			targetBlockId = blueprintDef->Stone.ProduceItemID;
			if (blueprintDef->Stone.ConsumeItemID > 0 && blueprintDef->Stone.Count > 0) {
				requiredMaterials[blueprintDef->Stone.ConsumeItemID] = blueprintDef->Stone.Count;
			}
		}
	}
	else if (curLevel == IronStyle)
	{
		if (blueprintDef->Iron.ProduceItemID > 0)
		{
			targetBlockId = blueprintDef->Iron.ProduceItemID;
			if (blueprintDef->Iron.ConsumeItemID > 0 && blueprintDef->Iron.Count > 0) {
				requiredMaterials[blueprintDef->Iron.ConsumeItemID] = blueprintDef->Iron.Count;
			}
		}
	}
	else if (curLevel == SteelStyle)
	{
		if (blueprintDef->Steel.ProduceItemID > 0)
		{
			targetBlockId = blueprintDef->Steel.ProduceItemID;
			if (blueprintDef->Steel.ConsumeItemID > 0 && blueprintDef->Steel.Count > 0) {
				requiredMaterials[blueprintDef->Steel.ConsumeItemID] = blueprintDef->Steel.Count;
			}
		}
	}


	// 如果没有有效的目标方块ID或所需材料，返回失败
	if (targetBlockId <= 0 || requiredMaterials.empty()) {
		return -3;
	}

	// 如果是上帝模式，直接返回0 成功
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode()) {
		return targetBlockId;
	}


	BackPack* backpack = player->getBackPack();
	if (!backpack) {
		return -3;
	}
	// 检查玩家背包中是否有足够的材料
	if (type != 3)
	{
		for (const auto& material : requiredMaterials) {
			int itemId = material.first;
			int count = material.second * fProportion;
			int haveNum = backpack->getItemCountInNormalPack(itemId);
			if (haveNum < count) {
				// 材料不足
				return -1;
			}
		}
	}
	if (deal)
	{
		if (type == 3)
		{
			// 处理资源消耗
			for (const auto& material : requiredMaterials) {
				int itemId = material.first;
				int count = std::ceil(material.second * fProportion);
				backpack->addItem(itemId, count, 1);
			}
		}
		else
		{
			// 处理资源消耗
			for (const auto& material : requiredMaterials) {
				int itemId = material.first;
				int count = std::ceil(material.second * fProportion);
				backpack->removeItemInNormalPack(itemId, count);
			}
		}
	}	
	return targetBlockId;
}

int containerArchitecture::onUpgradeBlock(int upgradeNum, ClientPlayer* player, bool deal)
{
	int retItemId = checkArchitectureResEnough(1, upgradeNum, player, deal);
	if (retItemId >= 0)
	{
		auto pworld = player->getWorld();
		if (pworld)
		{
			int olddata = pworld->getBlockData(m_BlockPos);
			pworld->setBlockAll(m_BlockPos, retItemId, olddata);
			player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 6023); //升级成功提示
		}
	}
	else
	{
		// 材料不足提示消息映射
		int messageId = 6019; // 默认木材不足
		
		if (upgradeNum == 1 || upgradeNum == 2) 
		{
			messageId = 6019; // BranchStyle和WoodStyle都需要木材 -> 木材不足
		}
		else if (upgradeNum == 3) 
		{
			messageId = 6020; // StoneStyle -> 石材不足
		}
		else if (upgradeNum == 4) 
		{
			messageId = 6021; // IronStyle -> 铁锭不足
		}
		else if (upgradeNum == 5) 
		{
			messageId = 6022; // SteelStyle -> 钛金锭不足
		}
		
		player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, messageId);
	}
	return retItemId;
}

//bool containerArchitecture::destoryRecycle(ClientPlayer* player)
//{	
//	// 检查是否有有效的世界和蓝图数据
//	if (m_nBluePrintTypeId == 0 || m_nBluePrintLevel == 0) {
//		return -3;
//	}
//	// 获取建筑蓝图数据
//	const ArchitecturalBlueprintCsvDef* blueprintDef = GetDefManagerProxy()->getArchitecturalBlueprintCsvDef(m_nBluePrintTypeId);
//	if (!blueprintDef) {
//		return -3;
//	}
//
//	float fProportion = 1.0f;
//	int curLevel = m_nBluePrintLevel;
//	// 查找目标方块ID和所需材料
//	int targetBlockId = -3;
//	std::map<int, int> requiredMaterials;
//
//	if (curLevel == BranchStyle)
//	{
//		if (blueprintDef->Branch.ProduceItemID > 0)
//		{
//			targetBlockId = blueprintDef->Branch.ProduceItemID;
//			if (blueprintDef->Branch.ConsumeItemID > 0 && blueprintDef->Branch.Count > 0) {
//				requiredMaterials[blueprintDef->Branch.ConsumeItemID] = blueprintDef->Branch.Count;
//			}
//		}
//	}
//	else if (curLevel == WoodStyle)
//	{
//		if (blueprintDef->Wood.ProduceItemID > 0)
//		{
//			targetBlockId = blueprintDef->Wood.ProduceItemID;
//			if (blueprintDef->Wood.ConsumeItemID > 0 && blueprintDef->Wood.Count > 0) {
//				requiredMaterials[blueprintDef->Wood.ConsumeItemID] = blueprintDef->Wood.Count;
//			}
//		}
//	}
//	else if (curLevel == StoneStyle)
//	{
//		if (blueprintDef->Stone.ProduceItemID > 0)
//		{
//			targetBlockId = blueprintDef->Stone.ProduceItemID;
//			if (blueprintDef->Stone.ConsumeItemID > 0 && blueprintDef->Stone.Count > 0) {
//				requiredMaterials[blueprintDef->Stone.ConsumeItemID] = blueprintDef->Stone.Count;
//			}
//		}
//	}
//	else if (curLevel == IronStyle)
//	{
//		if (blueprintDef->Iron.ProduceItemID > 0)
//		{
//			targetBlockId = blueprintDef->Iron.ProduceItemID;
//			if (blueprintDef->Iron.ConsumeItemID > 0 && blueprintDef->Iron.Count > 0) {
//				requiredMaterials[blueprintDef->Iron.ConsumeItemID] = blueprintDef->Iron.Count;
//			}
//		}
//	}
//	else if (curLevel == SteelStyle)
//	{
//		if (blueprintDef->Steel.ProduceItemID > 0)
//		{
//			targetBlockId = blueprintDef->Steel.ProduceItemID;
//			if (blueprintDef->Steel.ConsumeItemID > 0 && blueprintDef->Steel.Count > 0) {
//				requiredMaterials[blueprintDef->Steel.ConsumeItemID] = blueprintDef->Steel.Count;
//			}
//		}
//	}
//
//
//	// 如果没有有效的目标方块ID或所需材料，返回失败
//	if (targetBlockId <= 0 || requiredMaterials.empty()) {
//		return -3;
//	}
//
//	// 如果是上帝模式，直接返回0 成功
//	if (GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode()) {
//		return targetBlockId;
//	}
//
//
//	BackPack* backpack = player->getBackPack();
//	if (!backpack) {
//		return -3;
//	}
//	// 检查玩家背包中是否有足够的材料
//	for (const auto& material : requiredMaterials) {
//		int itemId = material.first;
//		int count = material.second * fProportion;
//		int haveNum = backpack->getItemCountInNormalPack(itemId);
//		if (haveNum < count) {
//			// 材料不足
//			return -1;
//		}
//	}
//
//	// 处理资源消耗
//	for (const auto& material : requiredMaterials) {
//		int itemId = material.first;
//		int count = material.second * fProportion;
//
//		backpack->removeItemInNormalPack(itemId, count);
//	}
//	return targetBlockId;
//}

//-----------------------------------------------------containerArchitecture--------------------------------------------------------------------------
/**********************************************************************************************
类    名：containerLinkMachineSource
功    能：类电机关方块container
********************************************************************************************* */

containerLinkMachine::containerLinkMachine() :WorldContainer(STORAGE_START_INDEX)
{
	m_NeedTick = false;
}

containerLinkMachine::containerLinkMachine(const WCoord& blockpos, int blockId, int type) :WorldContainer(blockpos, STORAGE_START_INDEX)
{
	m_NeedTick = false;
	m_nType = type;
}

bool containerLinkMachine::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerLinkMachine*>(srcdata);
	if (!src) return false;
	loadContainerCommon(src->basedata());

	m_nType = src->machinetype();
	m_nActiveState = src->activestate();
	m_nTickCounter = src->activeticker();
	if (m_nTickCounter) m_NeedTick = true;
	m_LinkMachineList.clear();
	auto machinelist = src->linkmachinelist();
	for (int i = 0; i < machinelist->size(); i++)
	{
		auto item = machinelist->Get(i);
		auto pos = Coord3ToWCoord(item->blockpos());
		m_LinkMachineList.push_back(LinkMachineData(item->blockid(), pos));
	}

	m_RandNpcList.clear();
	auto randnpclist = src->randnpclist();
	for (int i = 0; i < randnpclist->size(); i++)
	{
		auto item = randnpclist->Get(i);
		auto pos = Coord3ToWCoord(item->blockpos());
		m_RandNpcList.push_back(LinkMachineRandNpcData(item->blockid(), pos));
	}

	return true;
}

flatbuffers::Offset<FBSave::ChunkContainer> containerLinkMachine::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerCommon(builder);

	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::LinkMachine>>> linkmachinelistoffset = 0;
	std::vector<flatbuffers::Offset<FBSave::LinkMachine>> linkmachinelist0;
	for (auto item : m_LinkMachineList)
	{
		auto wcpos = WCoordToCoord3(item.blockpos);
		linkmachinelist0.push_back(FBSave::CreateLinkMachine(builder, item.blockid, &wcpos));
	}
	linkmachinelistoffset = builder.CreateVector(linkmachinelist0);

	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::LinkMachine>>> randnpclistoffset = 0;
	std::vector<flatbuffers::Offset<FBSave::LinkMachine>> randnpclist0;
	for (auto item : m_RandNpcList)
	{
		auto wcpos = WCoordToCoord3(item._base.blockpos);
		randnpclist0.push_back(FBSave::CreateLinkMachine(builder, item._base.blockid, &wcpos));
	}
	randnpclistoffset = builder.CreateVector(randnpclist0);

	auto actor = FBSave::CreateContainerLinkMachine(builder, basedata, m_nType, randnpclistoffset, linkmachinelistoffset, m_nActiveState, m_nTickCounter);

	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerLinkMachine, actor.Union());
}

void containerLinkMachine::addRandNpcBlock(int blockid, const WCoord& pos)
{
	m_RandNpcList.push_back(LinkMachineRandNpcData(blockid, pos));
}

void containerLinkMachine::addTargetMachine(int blockid, const WCoord& pos)
{
	m_LinkMachineList.push_back(LinkMachineData(blockid, pos));
}

int containerLinkMachine::getMachineRandNpc(int blockid)
{
	ChunkRandGen randGen;
	randGen.setSeed64(m_World->getChunkSeed(m_BlockPos.x, m_BlockPos.z) + m_RandNpcList.size());
	SocConstAtLua* luadata = GetLuaInterfaceProxy().get_soc_lua_const();
	auto iter = luadata->vRandNpcList.find(blockid);
	if (iter != luadata->vRandNpcList.end())
	{
		int weight = randGen.nextInt(iter->second._totalProbability);
		for (const auto& p : iter->second._vRandNpcItemList)
		{
			weight -= p._probability;
			if (weight <= 0) return p._id;
		}
	}
	return 0;
}

void containerLinkMachine::enterWorld(World* pworld)
{
	WorldContainer::enterWorld(pworld);
}
void containerLinkMachine::leaveWorld()
{
	WorldContainer::leaveWorld();
}

void containerLinkMachine::updateTick()
{
	if (m_nTickCounter > 0)
	{
		m_nTickCounter--;
		if (m_nTickCounter <= 0)
		{
			if (m_World->isRemoteMode())
				return;
			if (m_nType == ElectricSource)
			{
				unActivate();
			}
			else if (m_nType == ElectricTarget)
			{
				auto pmtl = m_World->getBlockMaterial(m_BlockPos);
				BlockSocElectricLogicInterface* pEleMtl = dynamic_cast<BlockSocElectricLogicInterface*>(pmtl);
				if (pEleMtl) pEleMtl->logicUnactive(m_World, m_BlockPos);
			}
		}
	}
}

bool containerLinkMachine::activating(int itemid, int count, ClientPlayer* player)
{
	if (checkMachineActive()) return true;
	if (m_World->isRemoteMode())
	{
		if (m_nType == ElectricSource)
		{
			setActiveState(1);
			PB_MachineSourceActiveCH activeHC;
			activeHC.set_x(m_BlockPos.x);
			activeHC.set_y(m_BlockPos.y);
			activeHC.set_z(m_BlockPos.z);
			GameNetManager::getInstance()->sendToHost(PB_MACHINESOURCE_ACTIVE_CH, activeHC);
			SocConstAtLua* luadata = GetLuaInterfaceProxy().get_soc_lua_const();
			m_nTickCounter = luadata->socElectricSourceEffectTime + 25;
			m_NeedTick = true;
		}
		return true;
	}
	setActiveState(1);
	if (m_nType == ElectricSource)
	{
		// 按itemid扣除道具，只有成功扣除后才激活机器
		if (player && itemid > 0 && count > 0)
		{
			BackPack* backpack = player->getBackPack();
			if (backpack)
			{
				// 使用removeItemInNormalPack方法扣除道具，返回实际扣除的数量
				int actualDeducted = backpack->removeItemInNormalPack(itemid, count);

				// 只有成功扣除道具后才调用激活
				if (actualDeducted >= count)
				{
					SocConstAtLua* luadata = GetLuaInterfaceProxy().get_soc_lua_const();
					m_nTickCounter = luadata->socElectricSourceEffectTime;
					m_NeedTick = true;
					if (m_RandNpcList.size())
					{
						auto pactorMgr = m_World->getActorMgr()->ToCastMgr();
						for (auto& item : m_RandNpcList)
						{
							if (item._posKey != 0)
							{
								auto findPosKeyMob = [&](ClientActor* pActor) -> bool
									{
										ClientMob* pMob = dynamic_cast<ClientMob*>(pActor);
										if (pMob && pMob->getMobPresetPosKey() == item._posKey)
											return true;
										return false;
									};
								auto pmob = pactorMgr->FindActor(findPosKeyMob);
								if (pmob) continue;
							}
							int npcid = getMachineRandNpc(item._base.blockid);
							auto npcPos = BlockCenterCoord(item._base.blockpos);
							auto posKey = ComputeWCoordHash(npcPos);
							item._posKey = posKey;
							m_World->getActorMgr()->ToCastMgr()->spawnMobDelay(npcPos, 1, npcid, false, false, 0, NULL, -1.f, NULL, posKey);
						}
					}
					if (m_LinkMachineList.size())
					{
						for (auto item : m_LinkMachineList)
						{
							auto pmtl = m_World->getBlockMaterial(item.blockpos);
							containerLinkMachine* ptargetcontainer = dynamic_cast<containerLinkMachine*>(pmtl->getCoreContainer(m_World, item.blockpos));
							if (ptargetcontainer) ptargetcontainer->activating();
						}
					}
				}
				else return false;
			}
		}
		else return false;
	}

	m_World->markBlockForUpdate(m_BlockPos); 
	return true;
	//if (checkMachineActive())
	//{
	//	auto pmtl = m_World->getBlockMaterial(m_BlockPos);
	//	BlockSocElectricLogicInterface* pEleMtl = dynamic_cast<BlockSocElectricLogicInterface*>(pmtl);
	//	if (pEleMtl) pEleMtl->logicActive(m_World, m_BlockPos);
	//}
}

void containerLinkMachine::unActivate()
{
	if (m_World->isRemoteMode())
		return;
	m_nTickCounter = 0;
	m_NeedTick = false;
	if (m_nType == ElectricSource)
	{
		if (!checkMachineActive()) return;
		setActiveState(-1);
		if (m_LinkMachineList.size())
		{
			for (auto item : m_LinkMachineList)
			{
				containerLinkMachine* ptargetcontainer = dynamic_cast<containerLinkMachine*>(m_World->getContainerMgr()->getContainer(item.blockpos));
				if (ptargetcontainer) ptargetcontainer->unActivate();
			}
		}
	}
	else if (m_nType == ElectricTarget)
	{
		setActiveState(-1);
		auto pmtl = m_World->getBlockMaterial(m_BlockPos);
		BlockSocElectricLogicInterface* pEleMtl = dynamic_cast<BlockSocElectricLogicInterface*>(pmtl);
		if (pEleMtl) pEleMtl->logicUnactive(m_World, m_BlockPos);
	}
	m_World->markBlockForUpdate(m_BlockPos);
}

void containerLinkMachine::targetMachineRealActive()
{
	if (m_nType == ElectricTarget)
	{
		SocConstAtLua* luadata = GetLuaInterfaceProxy().get_soc_lua_const();
		m_nTickCounter = luadata->socElectricDoorAutoCloseTime;
		m_NeedTick = true;
	}
}

bool containerLinkMachine::checkMachineActive()
{
	if (m_nType == ElectricSource)
	{
		return m_nActiveState != 0;
	}
	else if (m_nType == ElectricTarget)
	{
		if (m_nActiveState == 0) return false;
		if (m_LinkMachineList.size())
		{
			int totalSource = m_LinkMachineList.size();
			return totalSource <= m_nActiveState;
			//int activeCount = 0;
			//for (auto item : m_LinkMachineList)
			//{
			//	containerLinkMachine* ptargetcontainer = dynamic_cast<containerLinkMachine*>(m_World->getContainerMgr()->getContainer(item.blockpos));
			//	if (ptargetcontainer && ptargetcontainer->getState() == 1) activeCount++;
			//}
			//if (totalSource == activeCount) return true;
		}
		else
		{
			return true;
		}
	}
	return false;
}