#include "SandboxActorSubsystem.h"
#include "GameStatic.h"
#include "SandBoxManager.h"
#include "ActorFlyingBlock.h"
#include "ActorTrainCar.h"
#include "ActorBomb.h"
#include "ActorDragon.h"
#include "ClientActorThrowable.h"
#include "ActorFirework.h"
#include "ActorNpc.h"
#include "ClientAquaticComponent.h"
#include "ClientActorFishhook.h"
#include "ClientPipelineActor.h"
#include "ActorSnowHare.h"
#include "ClientActorNewArrow.h"
#include "ActorRocket.h"
#include "ActorHalfGiant.h"
#include "ActorGiant.h"
#include "ClientActorImpulse.h"
#include "ActorVehicleAssemble.h"
#include "ActorShapeShiftHorse.h"
#include "VehicleContainerActioner.h"
#include "ActorFlySnakeGod.h"
#include "ClientVacantBoss.h"
#include "ActorFishingVillager.h"
#include "ClientActorThornBall.h"
#include "ClientActorIcicle.h"
#include "ClientActorBaseLua.h"
#include "ActorFollow.h"
#include "FurnaceContainer.h"
#include "container_world_lua.h"
#include "container_mobspawner.h"
#include "container_funnel.h"
#include "container_emitter.h"
#include "container_horse_egg.h"
#include "container_dragoncup.h"
#include "container_itemexpo.h"
#include "container_mecha.h"
#include "container_sensor.h"
#include "container_railknot.h"
#include "container_alientotem.h"
#include "container_interpreterunit.h"
#include "container_regionreplicator.h"
#include "container_buildblueprint.h"	
#include "container_gianttotem.h"
#include "container_collider.h"
#include "container_modelcraft.h"
#include "container_transfer.h"
#include "container_bookEditorTable.h"
#include "container_actormodel.h"
#include "container_wheel.h"
#include "container_fullycustommodel.h"
#include "container_actioner.h"
#include "container_tombstone.h"
#include "container_altar.h"
#include "container_honorFrame.h"
#include "container_keypedestal.h"
#include "container_RevivalStatue.h"
#include "container_driverseat_model.h"

#include "container_arrowsigns.h"
#include "container_cliptrap.h"
#include "container_voidDragonFlower.h"
#include "container_hydarm.h"
#include "container_stone_core.h"
#include "container_worldselectmobspawner.h"
#include "container_modtransferconsole.h"
#include "container_computer.h"
#include "container_erosion_storage.h"
#include "container_research.h"


#include "ServerInterpolTick.h"
#include "DialogueComponent.h"
#include "PlotComponent.h"
#include "RevivePointComponent.h"
#include "CosumeAccumComponent.h"
#include "InWaterComponent.h"
#include "SunHurtComponent.h"
#include "BlockEnvEffectsComponent_Actor.h"
#include "SoundComponent.h"
#include "EffectComponent.h"
#include "PhysicsComponent.h"

#include "BlockRailNew.h"
#include "BlockModBase.h"



#include "ProjectileFactory.h"
#include "ProjectileLocoMotion.h"


#include "GameMode.h"
#include "CameraModel.h"
#include "BehaviorTreeManager.h"
#include "MapInfoRefreshCenter.h"
#include "UgcAssetMgr.h"
#include "SummonMonsterSiegeMgr.h"
#include "SkillManager.h"
#include "SprayPaintMgr.h"
#include "ActorCSProto.h"
#include "EnemyContextMgr.h"
#include "UGCActorBatchManager.h"
#include "GameEffectManager.h"
#include "EffectExplosion.h"
#include "PlanarReflectionEffect.h"
#include "BlockScene.h"
#include "UGCEntity.h"
#include "ModelItemMesh.h"
#include "FlyBasedPathFinder.h"
#include "SwimBasePathFinder.h"
#include "ClimbingPathFinder.h"
#include "WalkSwimPathFinder.h"
#include "GunSmithMgr.h"
#include "UgcAssetFileMgr.h"
#include "TrixenieLocomotion.h"

#include "GunGridDataComponent.h"
#include "EntryGridDataComponent.h"
#include "BulletMgr.h"
using namespace FBSave;
using namespace MNSandbox;

extern int g_all_physxitem;
extern int SeqType2ID(int seqtype, bool ispayer);
extern int CheckFlyArea(int x, int y, float scale);
extern void AddVertToArray(std::vector<BlockGeomVert>& vertarray, const Rainbow::Vector3f& pos, float u, float v);
extern void CalBezierCurveVertex(std::vector<BlockGeomVert>& verts, std::vector<unsigned short>& indices, const MINIW::CatmullRomCurve& bc, const Vector3f& originPos, bool needreset = false, float w1 = BLOCK_FSIZE * 1.5f / 4, float w2 = BLOCK_FSIZE / 6);
extern bool findBlockIn6Direction(World* pworld, int blockid, std::vector<WCoord>& retPos, const WCoord& blockpos, int maxDistance = 64);
extern bool GetVertXCuboidArray(std::vector<BlockGeomVert>& array1, std::vector<BlockGeomVert>& array2, std::vector<BlockGeomVert>& array3, std::vector<BlockGeomVert>& array4, std::vector<unsigned short>& indices, WCoord startPos, WCoord dim);
extern bool GetVertYCuboidArray(std::vector<BlockGeomVert>& array1, std::vector<BlockGeomVert>& array2, std::vector<BlockGeomVert>& array3, std::vector<BlockGeomVert>& array4, std::vector<unsigned short>& indices, WCoord startPos, WCoord dim);
extern bool GetVertZCuboidArray(std::vector<BlockGeomVert>& array1, std::vector<BlockGeomVert>& array2, std::vector<BlockGeomVert>& array3, std::vector<BlockGeomVert>& array4, std::vector<unsigned short>& indices, WCoord startPos, WCoord dim);
extern int CalDropItemCallCount(int rob_enchant_level, float addprob[3]);

ClientActor* CreateActorFromGeneralEnterAOIHC(const game::hc::PB_GeneralEnterAOIHC& pb)
{
	if (pb.has_actorobj()) {
		return SANDBOX_NEW(::ClientActor, true);
	}
	if (pb.has_actormob()) {
		const PB_ActorMob& mob = pb.actormob();
		return ClientMob::createFromDef(mob.defid(), 0, true, false);
	}
	if (pb.has_actoritem())
		return SANDBOX_NEW(ClientItem);
	if (pb.has_actornpc())
		return SANDBOX_NEW(::ActorNpc);
	if (pb.has_actoraquaticmob())
		// return SANDBOX_NEW(::ClientAquaticMob);
	{
		ClientMob* mob = SANDBOX_NEW(::ClientMob);
		mob->cacheClientAquaticComponent(mob->CreateComponent<ClientAquaticComponent>("ClientAquaticComponent"));
		return mob;
	}
	if (pb.has_actorflyblock())
		return SANDBOX_NEW(ActorFlyingBlock);
	if (pb.has_actorprojectile())
		return SANDBOX_NEW(ClientActorProjectile);
	if (pb.has_actorflymob())
		//return SANDBOX_NEW(::ClientFlyMob);
	{
		ClientMob* mob = SANDBOX_NEW(::ClientMob);
		mob->cacheClientFlyComponent(mob->CreateComponent<ClientFlyComponent>("ClientFlyComponent"));
		return mob;
	}
	if (pb.has_actorghost())
		return SANDBOX_NEW(::ActorGhost);
	if (pb.has_actorthornball())
		return SANDBOX_NEW(::ClientActorThornBall);
	if (pb.has_actorfishhook())
		return SANDBOX_NEW(::ClientActorFishhook);
	if (pb.has_actorpipeline())
		return SANDBOX_NEW(ClientPipleLineActor);
	if (pb.has_actorsnowhare())
		return SANDBOX_NEW(::ActorSnowHare);
	return NULL;
}

static WorldContainer* CreateWorldVehicleContainerFromUnionType(FBSave::ContainerUnion t, int pSubTypeLua)
{
	switch (t)
	{
	case FBSave::ContainerUnion_ContainerFurnace:
		return SANDBOX_NEW(FurnaceContainer);
	case FBSave::ContainerUnion_ContainerFurnaceOxy:
		return SANDBOX_NEW(WorldFurnaceOxy);
	case FBSave::ContainerUnion_ContainerPiston:
		return SANDBOX_NEW(WorldContainerHydarm);
	case FBSave::ContainerUnion_ContainerStorage:
		return SANDBOX_NEW(WorldStorageBox);
	case FBSave::ContainerUnion_ContainerErosionStorage:
		return SANDBOX_NEW(ErosionStorageBox);
	case FBSave::ContainerUnion_ContainerResearch:
		return SANDBOX_NEW(::ContainerResearch);
	case FBSave::ContainerUnion_ContainerString:
		return SANDBOX_NEW(WorldStringContainer);
	case FBSave::ContainerUnion_ContainerValue:
		return SANDBOX_NEW(WorldValueContainer);
	case FBSave::ContainerUnion_ContainerMobSpawner:
		return SANDBOX_NEW(WorldMobSpawner);
	case FBSave::ContainerUnion_ContainerSigns:
		return SANDBOX_NEW(WorldSignsContainer);
	case FBSave::ContainerUnion_ContainerFunnel:
		return SANDBOX_NEW(WorldFunnelContainer);
	case FBSave::ContainerUnion_ContainerEmitter:
		return SANDBOX_NEW(WorldEmitterContainer);
	case FBSave::ContainerUnion_ContainerEffect:
		return SANDBOX_NEW(WorldEffectContainer);
	case FBSave::ContainerUnion_ContainerHorseEgg:
		return SANDBOX_NEW(::ContainerHorseEgg);
	case FBSave::ContainerUnion_ContainerDragonCup:
		return SANDBOX_NEW(::ContainerDragonCup);
	case FBSave::ContainerUnion_ContainerItemExpo:
		return SANDBOX_NEW(::ContainerItemExpo);
	case FBSave::ContainerUnion_ContainerMecha:
		return SANDBOX_NEW(::ContainerMecha);
	case FBSave::ContainerUnion_ContainerSensor:
		return SANDBOX_NEW(::WorldSensorContainer);
	case FBSave::ContainerUnion_ContainerCollider:
		return SANDBOX_NEW(::WorldColliderContainer);
	case FBSave::ContainerUnion_ContainerRailKnot:
		return SANDBOX_NEW(::ContainerRailKnot);
	case FBSave::ContainerUnion_ContainerAlienTotem:
		return SANDBOX_NEW(WorldAlienTotemContainer);
	case FBSave::ContainerUnion_ContainerRadioUnit:
		return SANDBOX_NEW(::RadioUnitContainer);
	case FBSave::ContainerUnion_ContainerInterpreterUnit:
		return SANDBOX_NEW(::InterpreterUnitContainer);
	case FBSave::ContainerUnion_ContainerStoragePassword:
		return SANDBOX_NEW(::WorldStorageBoxPassword);
	case FBSave::ContainerUnion_ContainerStoneCore:
		return SANDBOX_NEW(::BlockStoneCoreContainer);
	case FBSave::ContainerUnion_ContainerGiantTotem:
		return SANDBOX_NEW(::WorldGiantTotemContainer);
	case FBSave::ContainerUnion_ContainerBlueprint:
		return SANDBOX_NEW(::WorldBlueprint);
	case FBSave::ContainerUnion_ContainerRegionReplicator:
		return SANDBOX_NEW(::ContainerRegionReplicator);
	case FBSave::ContainerUnion_ContainerBuildBluePrint:
		return SANDBOX_NEW(::ContainerBuildBluePrint);
	case FBSave::ContainerUnion_ContainerMeasureDistance:
		return SANDBOX_NEW(::WorldMeasureDistance);
	case FBSave::ContainerUnion_ContainerCustomModel:
		return SANDBOX_NEW(::ContainerModelCraft);
	case FBSave::ContainerUnion_ContainerSelectMobSpawner:
		return SANDBOX_NEW(::WorldSelectMobSpawner);
	case FBSave::ContainerUnion_ContainerTransfer:
		return SANDBOX_NEW(::WorldTransferContainer);
	case FBSave::ContainerUnion_ContainerBookEditorTable:
		return SANDBOX_NEW(WorldBookEditorTableContainer);
	case FBSave::ContainerUnion_ContainerBookCabinet:
		return SANDBOX_NEW(::WorldBookCabinet);
	case FBSave::ContainerUnion_ContainerActorModel:
		return SANDBOX_NEW(::ContainerActorModel);
	case FBSave::ContainerUnion_ContainerWheel:
		return SANDBOX_NEW(::ContainerWheel);
	case FBSave::ContainerUnion_ContainerWorkshop:
		return SANDBOX_NEW(::ContainerWorkshop);
	case FBSave::ContainerUnion_ContainerFullyCustomModel:
		return SANDBOX_NEW(::ContainerFullyCustomModel);
	case FBSave::ContainerUnion_ContainerActioner:
		return SANDBOX_NEW(::ContainerActioner);
	case FBSave::ContainerUnion_ContainerVehicleMecha:
		return SANDBOX_NEW(::VehicleContainerMecha);
	case FBSave::ContainerUnion_ContainerVehicleActioner:
		return SANDBOX_NEW(::VehicleContainerActioner);
	case FBSave::ContainerUnion_ContainerDriverSeat:
		return SANDBOX_NEW(::ContainerDriverSeat);
	case FBSave::ContainerUnion_ContainerArmPrismatic:
		return SANDBOX_NEW(::ContainerArmPrismatic);
	case FBSave::ContainerUnion_ContainerSensorValue:
		return SANDBOX_NEW(::WorldValueSensorContainer);
	case FBSave::ContainerUnion_ContainerBonFire:
		return SANDBOX_NEW(::WorldBonFire);
	case FBSave::ContainerUnion_ContainerTombStone:
		return SANDBOX_NEW(::TombStoneContainer);
	case FBSave::ContainerUnion_ContainerBed:
		return SANDBOX_NEW(::WorldBed);
	case FBSave::ContainerUnion_ContainerVillageTotem:
		return SANDBOX_NEW(::WorldVillageTotem);
	case FBSave::ContainerUnion_ContainerVillagerFlag:
		return SANDBOX_NEW(::WorldVillagerFlag);
	case FBSave::ContainerUnion_ContainerAltarTbl:
		return SANDBOX_NEW(AltarContainer);
	case FBSave::ContainerUnion_ContainerBlockLogo:
		return SANDBOX_NEW(::HonorFrameContainer);
	case FBSave::ContainerUnion_ContainerBlockKeyPedestal:
		return SANDBOX_NEW(::KeyPedestalContainer);
	case FBSave::ContainerUnion_ContainerRevivalStatue:
		return SANDBOX_NEW(::ContainerRevivalStatue);
	case FBSave::ContainerUnion_ContainerByLua:
		return WorldContainerLua::createLuaContainerFactory(pSubTypeLua, true);
	case FBSave::ContainerUnion_ContainerModel:
		return SANDBOX_NEW(::ContainerModel);
	case FBSave::ContainerUnion_ContainerDriverSeatModel:
		return SANDBOX_NEW(::ContainerDriverSeatModel);
	case FBSave::ContainerUnion_ContainerArrowSigns:
		return SANDBOX_NEW(::WorldArrowSignsContainer);
	default:
		assert(0);
		return NULL;
	}
}

WorldContainer* CreateWorldVehicleContainerFromChunkContainer(const FBSave::ChunkContainer* pChunkContainer)
{
	auto type = pChunkContainer->container_type();
	int subType = 0;
	if (type == FBSave::ContainerUnion_ContainerByLua)
	{
		auto con = reinterpret_cast<const FBSave::ContainerByLua*>(pChunkContainer->container());
		if (con)
		{
			subType = con->subTypeLua();
		}
	}
	return CreateWorldVehicleContainerFromUnionType(type, subType);
}

//static SandboxActorSubsystem* s_SandboxActorSubsystemInstance = nullptr;
MINIW::GameStatic<SandboxActorSubsystem> s_SandboxActorSubsystemInstance(MINIW::GameStatiInitType::kInitManual, 99);
SandboxActorSubsystem& GetSandboxActorSubsystemIns()
{
	return *s_SandboxActorSubsystemInstance.EnsureInitialized();
}

SandboxActorSubsystem* GetSandboxActorSubsystem()
{
	return s_SandboxActorSubsystemInstance.EnsureInitialized();
}
SandboxActorSubsystem::SandboxActorSubsystem()
{

}

SandboxActorSubsystem::~SandboxActorSubsystem()
{
	
}

bool SandboxActorSubsystem::Awake()
{
	ENG_NEW(UgcAssetMgr)();
	ENG_NEW(GunSmithMgr)();
	CreateModuleEvent();
	return true;
}

bool SandboxActorSubsystem::Init()
{
	MINIW::ScriptVM::game()->setUserTypePointer("UgcAssetMgr", "UgcAssetMgr", UgcAssetMgr::GetInstancePtr());

	MINIW::ScriptVM::game()->setUserTypePointer("GunSmithMgr", "GunSmithMgr", GunSmithMgr::GetInstancePtr());
	MINIW::ScriptVM::game()->setUserTypePointer("UgcAssetFileMgr", "UgcAssetFileMgr", UgcAssetFileMgr::GetInstancePtr());

	GetSandBoxManager().subscibeEx((IEventExcuteEx*)this, "PB_ACTOR_VILLAGER_INFO");

	GetSandBoxManager().subscibeEx((IEventExcuteEx*)this, "PB_PART_WEATHER_HC");

	GetSandBoxManager().subscibeEx((IEventExcuteEx*)this, "PB_GROUP_WEATHER_HC");

	GetSandBoxManager().subscibeEx((IEventExcuteEx*)this, "PB_CREATE_BLOCK_CH");

	GetSandBoxManager().subscibeEx((IEventExcuteEx*)this, "PB_ACTOR_SANDWORM_SHOW");

	GetSandBoxManager().subscibeEx((IEventExcuteEx*)this, "PB_ACTOR_SANDWORM_CAN_MOVE");

	GetSandBoxManager().subscibeEx((IEventExcuteEx*)this, "PB_ACTOR_SCASLE");

	SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, "PB_ACTOR_SANDWORM_NIBBLE_PLAYER");

	SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, "MOB_SCORPION_HIDE_TICK_CH");

	SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, "PB_ACTOR_CREATE_THORNBALL");
	SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, "PB_ACTOR_CREATE_THORNBALL2");
	SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, "PB_ACTOR_REBOUNDS_ATTACK_UP");
	SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, "PB_ACTOR_REBOUNDS_ATTACK_ROUND");
	SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, "PB_ATTR_SHAPE_SHIFT_RIGHT_CLICK_CH");// 属性变身右键点击
	SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, "PB_REMOVE_SAWTOOTH_THORNB");
	SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, "PB_CH_NOTICE_ATTACKED_UP");
	SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, "PB_CH_NOTICE_ATTACKED_ROUND");
	SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, "PB_CH_NOTICE_REMOVE_SAWTOOTH_THORNBA");
	SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, "PB_DESTORY_BLOCK_CH");// 破坏方块
	SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, "PB_WATER_PRESSURE_CH");// 客机水压同步给主机
	SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, "PB_COCONUT_HIT");
	SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, "PB_COCONUT_SKIP_NIGHT");
	SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, "PB_ATTR_SHAPE_SHIFT_SYNC");// 属性变身效果同步
	SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, "PB_ACTOR_SHARK_BITE_PLAYER_MOVE");//鲨鱼撕咬玩家移动同步
	SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, "PB_CRAB_INFO_SYNC");// 螃蟹钳击属性同步给客机
	SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, "PB_CRAB_CLICKCOUNT_RESET");// 重置螃蟹钳击点击次数给主机
	SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, "PB_HIPPOCAMPUS_REFRESHMODEL");// 重置螃蟹钳击点击次数给客机
	SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, "PB_HIPPOCAMPUS_CHANGECOLOR");// 重置螃蟹钳击点击次数给客机
	SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, "PB_BACKPACKGRID_DRUATION_HC");// 背包物品耐久度变化通知客机
	SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, "PB_GUNLOGIC_USE_WaterCanoonSkill");// 背包物品耐久度变化通知客机

	SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, "PB_ACTOR_SNOWMAN_PART_SHOW");//雪人部件通知客机
	SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, "PB_MOB_PART_SHOW");//怪物部件显示
	SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, "PB_PLAYER_SHAKE_CH"); //buff tick 同步给主机

	SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, "PB_ACTOR_DISSOLVE_COMPONENT_OPEN_HC"); //溶解同步给客机

	SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, "PB_COOKBOOKINFO_HC"); //烹饪信息同步给客机

	SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, "PB_STOVETAKE_CH"); //秘银炉拿东西同步给主机
	SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, "PB_SETHPVISIBLE_HC");


	//技能播放动作通知客机
	REGEVENT("PB_SKILLPLAYANIM_HC", DealSkillPlayAnimHC);
	//技能停止动作通知客机
	REGEVENT("PB_SKILLSTOPANIM_HC", DealSkillStopAnimHC);
	//技能播放特效通知客机
	REGEVENT("PB_SKILLPLAYBODYEFFECT_HC", DealSkillPlayBodyEffectHC);
	REGEVENT("PB_SKILLSTOPBODYEFFECT_HC", DealSkillStopBodyEffectHC);
	//技能播放特效通知客机
	REGEVENT("PB_SKILLWORLDPLAYBODYEFFECT_HC", DealSkillWorldPlayEffectHC);
	//技能进度通知客机
	REGEVENT("PB_Accumulator_HC", DealAccumulatorHC);
	//技能播放工具动作通知客机
	REGEVENT("PB_SKILLPLAYTOOLANIM_HC", DealSkillPlayToolAnimHC);
	//技能停止工具动作通知客机
	REGEVENT("PB_SKILLSTOPTOOLANIM_HC", DealSkillPlayToolAnimHC);
	//技能设置蓄力移动通知客机
	REGEVENT("PB_SKILLSETCHARGEMOVE_HC", DealSkillSetChargeMoveHC);
	//技能移动通知客机
	REGEVENT("PB_SKILLMOVE_HC", DealSkillMovePos);
	//技能摄像机拉进等通知客机
	REGEVENT("PB_SKILLCAMERA_HC", DealSkillCamera);

	REGEVENT("PB_SKILLSETOBJINFO_HC", DealSkillSetOBJInfo);

	//播放武器动画
	REGEVENT("PB_PLAYWEAPONANIM_HC", PlayWeaponAnimHC);
	REGEVENT("PB_PLAYWEAPONANIM_CH", PlayWeaponAnimCH);
	//停止武器动画
	REGEVENT("PB_STOPWEAPONANIM_HC", StopWeaponAnimHC);
	REGEVENT("PB_STOPWEAPONANIM_CH", StopWeaponAnimCH);

	//播放武器特效
	REGEVENT("PB_PLAYWEAPONMOTION_HC", PlayWeaponMotionHC);
	REGEVENT("PB_PLAYWEAPONMOTION_CH", PlayWeaponMotionCH);
	//停止武器特效
	REGEVENT("PB_STOPWEAPONMOTION_HC", StopWeaponMotionHC);
	REGEVENT("PB_STOPWEAPONMOTION_CH", StopWeaponMotionCH);

	REGEVENT("PB_SETLOCOTYPE_HC", SetLocoMotionTypeHC);
	return true;
}

bool SandboxActorSubsystem::Shut()
{
	for (auto& kv : m_eventCallbacks) {
		MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Unsubscribe(kv.first, kv.second);
	}
	UgcAssetMgr::Destroy();
	GunSmithMgr::Destroy();
	return true;
}

IGameMode* SandboxActorSubsystem::CreateGameMode(long long worldid)
{
	return  ENG_NEW(GameMode)(worldid);
}

BaseItemMesh* SandboxActorSubsystem::CreateClientItemModel(int itemid, ITEM_MODELDISP_TYPE disptype, float customscale/* = 1.0f*/, int userdata/* = 0*/, ITEM_MESH_TYPE meshtype/* = NORMAL_MESH*/, std::string userdatastr/* = ""*/, int layer/* = -1*/)
{
	return ClientItem::createItemModel(itemid, disptype,customscale,userdata,meshtype,userdatastr, layer);
}
MapInfoRefreshCenterInterface* SandboxActorSubsystem::CreateMapInfoRefreshCenter(World* world)
{
	return ENG_NEW(MapInfoRefreshCenter)(world);
}
BTManagerInterface* SandboxActorSubsystem::CreateBTManager()
{
	return ENG_NEW(BTManager)();
}
SummonMonsterSiegeMgrInterface* SandboxActorSubsystem::CreateSummonMonsterSiegeMgr(World* world)
{
	return ENG_NEW(SummonMonsterSiegeMgr)(world);
}
SkillManagerInterface* SandboxActorSubsystem::CreateSkillMgr()
{
	return ENG_NEW(SkillManager)();
}

SandboxMgrBase* SandboxActorSubsystem::CreateSandboxMgr(const std::string& mgrname)
{
	if (mgrname == "MeteorShowerMgr")
	{
		//return MeteorShowerManager::create();
		return nullptr;
	}
	else if (mgrname == "SprayPaintMgr")
	{
		ScriptVM::game()->setUserTypePointer("SprayPaintMgr", "SprayPaintMgr", SprayPaintMgr::create());
	}
	else if (mgrname == "BulletMgr")
	{
		return BulletMgr::create();
	}
	
	return nullptr;
}

Rainbow::IActorBody* SandboxActorSubsystem::CreateNewIActorBody()
{
	auto actorbody = ENG_NEW(ActorBody)(NULL);
	return actorbody;
}
EffectManager* SandboxActorSubsystem::CreateNewEffectManager(World* world)
{
	return ENG_NEW(GameEffectManager)(world);
}
void SandboxActorSubsystem::HandleEnemyContext(World* world, WorldManager* worldmgr)
{
	EnemyContextMgr::getIns()->setWorldAnManager(world, worldmgr);
	EnemyContextMgr::getIns()->tryCreateBoss();
}
long long SandboxActorSubsystem::PackObjId(long long id)
{
	return ClientActor::PackObjId(id);
}
Rainbow::IActorBody* SandboxActorSubsystem::CreateCustomModelActorBody(int type)
{
	ActorBody* pActorBody = ENG_NEW(ActorBody)(NULL);
	pActorBody->initCustomActor((ACTOR_MODEL_TYPE)type);
	return pActorBody;
}
Rainbow::IActorBody* SandboxActorSubsystem::CreateFullyCustomModelActorBody(int type, FullyCustomModel* fcm)
{
	ActorBody* pActorBody = ENG_NEW(ActorBody)(NULL);
	pActorBody->initFullyCustomActor(type, fcm, "", true);
	return pActorBody;
}

const std::string SandboxActorSubsystem::BodyGetSkinName(int slot, int level)
{
	return GetSkinName(slot, level).c_str();
}
const std::string SandboxActorSubsystem::BodyGetSkinEquipName(int slot, int level)
{
	return GetSkinEquipName( slot, level).c_str();
}
int SandboxActorSubsystem::BodySeqType2ID(int seqtype)
{
	return SeqType2ID(seqtype, true);
}

void SandboxActorSubsystem::GetTriggerPlayer(IClientPlayer*& Player, const WCoord& placepos, const int& dir)
{
	auto player = static_cast<ClientPlayer*>(Player);
	if (!Player)
	{
		player = SANDBOX_NEW(ClientPlayer);
		player->init(0, "", 0, NULL);
	}
	WCoord neighborpos = NeighborCoord(placepos, dir % 6);
	player->setPosition(neighborpos);

	WCoord dp = placepos - neighborpos;
	float yaw, pitch;
	Direction2PitchYaw(&yaw, &pitch, dp.toVector3());
	player->setFaceYaw(yaw);
	player->setFacePitch(pitch);

	Player = player;
}
void SandboxActorSubsystem::ResetObjId(long long id/* = 0x100000000LL*/)
{
	ClientActor::ResetObjId(id);
}

long long SandboxActorSubsystem::GetCurObjId()
{
	return ClientActor::GetCurObjId();
}

long long SandboxActorSubsystem::GenNextObjId()
{
	return ClientActor::GenNextObjId();
}

int SandboxActorSubsystem::GetCurViewRange(IClientPlayer* player)
{
	if (nullptr == player)
	{
		return ClientPlayer::GetCurViewRange(nullptr);
	}
	return ClientPlayer::GetCurViewRange(static_cast<ClientPlayer*>(player));
}

void SandboxActorSubsystem::SetViewRangeSetting(int range)
{
	ClientPlayer::SetViewRangeSetting(range);
}

void SandboxActorSubsystem::HideClientPlayerInfo(IClientPlayer* player)
{
	auto clientplayer = player->GetPlayer();
	if (!clientplayer)
	{
		return;
	}
	auto body = clientplayer->getBody();
	body->show(false, false, false);

	auto cameraModel = clientplayer->m_CameraModel;
	if (cameraModel)
	{
		cameraModel->show(false);
	}

	// if (player->m_CameraModel && player->m_CameraModel->m_HandModel && player->m_CameraModel->m_HandModel->GetGameObject())
	// {
	// 	//player->m_CameraModel->m_HandModel->GetGameObject()->SetActive(false);
	// }
	body->setHPVisible(false);
	body->setVisibleDispayName(false);
	body->setNameAndHpVisible(false);
	body->setAchieveVisible(false);
}

void SandboxActorSubsystem::AddCurActorFrame()
{
	ClientActor::m_CurActorFrame++;
}
int SandboxActorSubsystem::GetPlayerViewInnerRangeOffset()
{
	return ClientPlayer::m_ViewInnerRangeOffset;
}

void SandboxActorSubsystem::GetFinder(PathFinderBase*& finder, IClientActor* pActor, bool m_bCanDestroyblockPath, bool CanPassOpenWoodenDoors, bool CanPassClosedWoodenDoors,
	bool AvoidsWater, bool AvoidsCloud, bool CanSwimming, int mindistance, bool island, std::vector<int>* m_canDestroyBlocklist)
{
	ActorLocoMotion* loc = pActor->GetActor()->getLocoMotion();
	if (loc)
	{
		LivingLocoMotion* motion = loc->ToCast<LivingLocoMotion>();
		if (motion)
		{

			MoveAbilityType movtype = motion->getLocoMotionType();

			switch (movtype)
			{
			case LandLoc:
			case OnWater:
			{
				if (m_bCanDestroyblockPath)
				{
					finder = ENG_NEW(LandBasedPathFinder)(pActor->getWorld(), CanPassOpenWoodenDoors, CanPassClosedWoodenDoors, AvoidsWater, AvoidsCloud, CanSwimming, m_canDestroyBlocklist);
				}
				else
					finder = ENG_NEW(LandBasedPathFinder)(pActor->getWorld(), CanPassOpenWoodenDoors, CanPassClosedWoodenDoors, AvoidsWater, AvoidsCloud, CanSwimming);
			}
			break;
			case FlyLoc:
			{
				if (m_bCanDestroyblockPath)
				{
					finder = ENG_NEW(FlyBasedPathFinder)(pActor->getWorld(), CanPassOpenWoodenDoors, CanPassClosedWoodenDoors, AvoidsWater, AvoidsCloud, CanSwimming, m_canDestroyBlocklist);
				}
				else
					finder = ENG_NEW(FlyBasedPathFinder)(pActor->getWorld(), CanPassOpenWoodenDoors, CanPassClosedWoodenDoors, AvoidsWater, AvoidsCloud, CanSwimming);
			}
			break;
			case AquaticLoc:
			{
				if (m_bCanDestroyblockPath)
				{
					finder = ENG_NEW(SwimBasePathFinder)(pActor->getWorld(), CanPassOpenWoodenDoors, CanPassClosedWoodenDoors, AvoidsWater, AvoidsCloud, true, m_canDestroyBlocklist);
				}
				else
					finder = ENG_NEW(SwimBasePathFinder)(pActor->getWorld(), CanPassOpenWoodenDoors, CanPassClosedWoodenDoors, AvoidsWater, AvoidsCloud, true);
			}
			break;
			case Climbing:
			{
				if (m_bCanDestroyblockPath)
				{
					finder = ENG_NEW(ClimbingPathFinder)(pActor->getWorld(), CanPassOpenWoodenDoors, CanPassClosedWoodenDoors, AvoidsWater, AvoidsCloud, CanSwimming, m_canDestroyBlocklist);
				}
				else
					finder = ENG_NEW(ClimbingPathFinder)(pActor->getWorld(), CanPassOpenWoodenDoors, CanPassClosedWoodenDoors, AvoidsWater, AvoidsCloud, CanSwimming);
			}
			break;
			case LandLoc_CanSwin:
			{
				if (m_bCanDestroyblockPath)
				{
					finder = ENG_NEW(WalkSwimPathFinder)(pActor->getWorld(), CanPassOpenWoodenDoors, CanPassClosedWoodenDoors, false, AvoidsCloud, CanSwimming, m_canDestroyBlocklist);
				}
				else
					finder = ENG_NEW(WalkSwimPathFinder)(pActor->getWorld(), CanPassOpenWoodenDoors, CanPassClosedWoodenDoors, false, AvoidsCloud, CanSwimming);
			}
			default:
				break;
			}
		}
	}
}

bool SandboxActorSubsystem::IsActorType(IClientActor* actor, ActorType type)
{
	switch (type)
	{
	case ActorType::ACTOR_PROJECTILE:
		return dynamic_cast<ClientActorProjectile*>(actor) ? true : false;
		break;
	case ActorType::ACTOR_VEHICLE_ASSEMBLE:
		return dynamic_cast<::ActorVehicleAssemble*>(actor) ? true : false;
		break;
	case ActorType::ACTOR_BOMB:
		return dynamic_cast<ActorBomb*>(actor) ? true : false;
		break;
	case ActorType::ACTOR_ROCKET:
		return dynamic_cast<::ActorRocket*>(actor) ? true : false;
		break;
	case ActorType::ACTOR_SHAPE_SHIFT_HORSE:
		return dynamic_cast<::ActorShapeShiftHorse*>(actor) ? true : false;
		break;
	case ActorType::ACTOR_BASKETBALL:
		return dynamic_cast<::ActorBasketBall*>(actor) ? true : false;
		break;
	case ActorType::ACTOR_ITEM:
		return dynamic_cast<ClientItem*>(actor) ? true : false;
		break;
	case ActorType::ACTOR_TRAIN_CAR:
		return dynamic_cast<ActorTrainCar*>(actor) ? true : false;
		break;
	default:
		break;
	}
	return false;
}

bool SandboxActorSubsystem::IsLocoMotionType(IActorLocoMotion* loco, LocoMotionType type)
{
	switch (type)
	{
	case LocoMotionType::VEHICLE_ASSEMBLE_LOCOMOTION:
		return dynamic_cast<VehicleAssembleLocoMotion*>(loco);
		break;
	case LocoMotionType::PHYSICS_LOCOMOTION:
		return dynamic_cast<PhysicsLocoMotion*>(loco);
		break;
	default:
		break;
	}
	return false;
}

void SandboxActorSubsystem::shootIcicleAuto(int itemid, World* pworld, const WCoord& pos, int num)
{
	ClientActorIcicle::shootIcicleAuto(itemid, pworld, pos, num);
}

void SandboxActorSubsystem::throwItemByMob(World* pworld, IClientActor* shootactor, float strength, int itemId, int buffId, int randomYaw/* = 0*/, int randomPitch/* = 0*/, int dropType/* = 0*/)
{
	ProjectileFactory::throwItemByMob(pworld, static_cast<ClientActor*>(shootactor), strength, itemId, buffId, randomYaw, randomPitch, dropType);
}

IClientActor* SandboxActorSubsystem::throwItemByActorSpecifyTransform(World* pworld, IClientActor* shootactor, float yaw, float pitch, WCoord startPos, float strength, int itemId, bool setfire, bool canpickup, int fireLv/* = 0*/, unsigned char attachedEffect/* = 0*/, bool damageInsByShooter/* = true*/, bool speedInsByStrength/* = true*/)
{
	return ProjectileFactory::throwItemByActorSpecifyTransform(pworld, static_cast<ClientActor*>(shootactor), yaw, pitch, startPos, strength, itemId, setfire, canpickup, fireLv, attachedEffect, damageInsByShooter, speedInsByStrength);
}
int SandboxActorSubsystem::PlayerCalDropItemCallCount(int rob_enchant_level, float addprob[3])
{
	return CalDropItemCallCount(rob_enchant_level, addprob);
}
Rainbow::Model* SandboxActorSubsystem::LoadActorBodyModel(const char* path, const char* animfile)
{
	return ActorBody::LoadModel(path, animfile);
}

void SandboxActorSubsystem::ClearMoveForward(IClientActor* ipActor)
{
	ClientActor* pActor = ipActor ? ipActor->GetActor() : nullptr;
	if (pActor == nullptr)
	{
		return;
	}
	ActorLocoMotion* loc = pActor->getLocoMotion();
	if (!loc)
	{
		return;
	}

	TrixenieLocomotion* tloco = nullptr; //= loc->ToCast<TrixenieLocomotion>();
	LivingLocoMotion* loco = nullptr; //= loc->ToCast<LivingLocoMotion>();
	assert((loc->ToCast<LivingLocoMotion>() != nullptr || loc->ToCast<TrixenieLocomotion>() != nullptr));
	if (loco = loc->ToCast<LivingLocoMotion>())
	{
		loco->setMoveForward(0);
	}
	else if (tloco = loc->ToCast<TrixenieLocomotion>())
	{
		tloco->setMoveForward(0);
	}
}

void SandboxActorSubsystem::TickNavigatePath(IClientActor* ipActor, const WCoord& pos, float  m_Speed)
{
	ClientActor* pActor = ipActor ? ipActor->GetActor() : nullptr;
	if (pActor == nullptr)
	{
		return;
	}
	ActorLocoMotion* loc = pActor->getLocoMotion();
	if (!loc)
	{
		return;
	}
	LivingLocoMotion* loco = nullptr;
	if (loco = loc->ToCast<LivingLocoMotion>())
	{
		//陆地生物
		MoveAbilityType actortype = loco->getLocoMotionType();
		switch (actortype)
		{
		case LandLoc:
		case OnWater:
		case Climbing:
		{
			loco->setTarget(pos, m_Speed);
			/*m_pActor->playAnim(SEQ_WALK);*/
			/*m_pActor->skillPlayAnim(SEQ_WALK, 0, 0, 0);*/
		}
		break;
		case FlyLoc:
		{

			loco->setTarget(pos, m_Speed);
			loco->setBehaviorOn(BehaviorType::LashTagert);
			loco->m_SpeedInAir = pActor->getAttrib()->getMoveSpeed(Actor_Walk_Speed) * m_Speed * 100;
			loco->m_HasTarget = true;
			;
		}
		break;
		case AquaticLoc:
		{
			loco->m_MoveTarget = pos;
			loco->setBehaviorOn(BehaviorType::Wander);
			loco->m_SpeedInWater = m_Speed * 100;
			loco->m_HasTarget = true;
		}
		break;
		case LandLoc_CanSwin:
		{
			loco->setTarget(pos, m_Speed);
			loco->m_SpeedInWater = m_Speed * 100;
			if (loco->m_InWater)
			{
				loco->setJumping(true);
			}
		}
		default:
			break;
		}
	}

}

void SandboxActorSubsystem::PathFollowPlay(IClientActor* ipActor, int& bs, int& bh)
{
	ClientActor* pActor = ipActor ? ipActor->GetActor() : nullptr;
	if (pActor == nullptr)
	{
		return;
	}
	ActorLocoMotion* loc = pActor->getLocoMotion();
	if (!loc)
	{
		return;
	}
	LivingLocoMotion* loco = loc->ToCast<LivingLocoMotion>();
	if (loco)
	{
		MoveAbilityType actortype = loco->getLocoMotionType();
		if (actortype == FlyLoc)
		{
			pActor->playAnim(SEQ_MOB_FLY);
		}
	}
	bs = loc->m_BoundSize;
	bh = loc->m_BoundHeight;
}

void SandboxActorSubsystem::StoreGridData(game::common::PB_ItemData* dest, const BackPackGrid* src, int src_index/* = -1*/)
{
	storeGridData(dest, src, src_index);
}

void SandboxActorSubsystem::RestorePos(const game::common::PB_Pos& pos, WCoord* blockpos)
{
	restorePos(pos, blockpos);
}
void SandboxActorSubsystem::StorePos(game::common::PB_Pos* pstPos, const WCoord* blockpos)
{
	storePos(pstPos, blockpos);
}

void SandboxActorSubsystem::AddPlanarReflectionObject(BlockScene* m_Scene, World* pworld, Rainbow::Camera* m_Camera)
{
	Rainbow::GetPlanarReflection().Init(pworld, m_Camera, 512);
	Rainbow::GameObject* reflectObj = Rainbow::GetPlanarReflection().GetGameObject();
	if (reflectObj && !reflectObj->IsInScene())
	{
		m_Scene->AddGameObject(reflectObj);
	}
}
void SandboxActorSubsystem::RailAddVertToArray(std::vector<BlockGeomVert>& vertarray, const Rainbow::Vector3f& pos, float u, float v)
{
	AddVertToArray(vertarray, pos, u, v);
}
void SandboxActorSubsystem::RailCalBezierCurveVertex(std::vector<BlockGeomVert>& verts, std::vector<unsigned short>& indices, const MINIW::CatmullRomCurve& bc, const Vector3f& originPos, bool needreset, float w1, float w2)
{
	CalBezierCurveVertex(verts, indices, bc, originPos, needreset, w1, w2);
}
void SandboxActorSubsystem::RailclearRailBezierCurveData()
{
	BlockRailKnot::clearRailBezierCurveData();
}
bool SandboxActorSubsystem::FindBlockIn6Direction(World* pworld, int blockid, std::vector<WCoord>& retPos, const WCoord& blockpos, int maxDistance/* = 64*/)
{
	return findBlockIn6Direction(pworld, blockid, retPos, blockpos, maxDistance);
}
bool SandboxActorSubsystem::RegionReplicator_GetVertXCuboidArray(std::vector<BlockGeomVert>& array1, std::vector<BlockGeomVert>& array2, std::vector<BlockGeomVert>& array3, std::vector<BlockGeomVert>& array4, std::vector<unsigned short>& indices, WCoord startPos, WCoord dim)
{
	return GetVertXCuboidArray(array1, array2, array3, array4, indices, startPos, dim);
}
bool SandboxActorSubsystem::RegionReplicator_GetVertZCuboidArray(std::vector<BlockGeomVert>& array1, std::vector<BlockGeomVert>& array2, std::vector<BlockGeomVert>& array3, std::vector<BlockGeomVert>& array4, std::vector<unsigned short>& indices, WCoord startPos, WCoord dim)
{
	return GetVertZCuboidArray(array1, array2, array3, array4, indices, startPos, dim);
}
bool SandboxActorSubsystem::RegionReplicator_GetVertYCuboidArray(std::vector<BlockGeomVert>& array1, std::vector<BlockGeomVert>& array2, std::vector<BlockGeomVert>& array3, std::vector<BlockGeomVert>& array4, std::vector<unsigned short>& indices, WCoord startPos, WCoord dim)
{
	return GetVertYCuboidArray(array1, array2, array3, array4, indices, startPos, dim);
}
void SandboxActorSubsystem::InitAllNewBlockFunc()
{
	m_blockImpl.InitAllNewBlockFunc();
}
 BlockMaterial* SandboxActorSubsystem::CreateNewBlockObject(const char* name, int resid)
{
	return m_blockImpl.CreateNewBlockObject(name, resid);
}



Rainbow::Model* SandboxActorSubsystem::CreateOmodModel(std::string skey)
{
	return UgcAssetMgr::GetInstance().CreateOmodModel(skey);
}
Rainbow::Model* SandboxActorSubsystem::CreateOmodModelHand(std::string skey)
{
	return UgcAssetMgr::GetInstance().CreateOmodModelHand(skey);
}

Rainbow::Model* SandboxActorSubsystem::CreateObjModel(std::string skey)
{
	return UgcAssetMgr::GetInstance().CreateObjModel(skey);
}

Rainbow::Model* SandboxActorSubsystem::CreateObjModelHand(std::string skey)
{
	return UgcAssetMgr::GetInstance().CreateObjModelHand(skey);
}

Rainbow::SharePtr<Rainbow::Texture2D> SandboxActorSubsystem::GenOmodModelTexture(std::string skey)
{
	return UgcAssetMgr::GetInstance().GenOmodModelTexture(skey);
}
Rainbow::SharePtr<Rainbow::Texture2D> SandboxActorSubsystem::GenObjModelTexture(std::string skey)
{
	return UgcAssetMgr::GetInstance().GenObjModelTexture(skey);
}

Rainbow::SharePtr<Rainbow::Texture2D> SandboxActorSubsystem::GetObjModelIcon(std::string skey, int& u, int& v, int& width, int& height, int& r, int& g, int& b)
{
	return UgcAssetMgr::GetInstance().GetObjModelIcon(skey, u, v, width, height, r, g, b);
}

Rainbow::SharePtr<Rainbow::Texture2D> SandboxActorSubsystem::GetOmodModelIcon(std::string skey, int& u, int& v, int& width, int& height, int& r, int& g, int& b)
{
	return UgcAssetMgr::GetInstance().GetOmodModelIcon(skey, u, v, width, height, r, g, b);
}

void SandboxActorSubsystem::InitSceneObjFactory(MNSandbox::SandboxFactoryNormal<MNSandbox::SandboxNode>* factory)
{
	factory->AddProduct("ClientMob", [&]() -> MNSandbox::AutoRef<MNSandbox::SandboxNode> {
		auto ptr = ClientMob::NewInstance();
		if (ptr)
		{
			ptr->initDefault();
		}
		return ptr.StaticToCast<MNSandbox::SandboxNode>();
		});

	factory->AddProduct("ActorExpandTemplate", [&]() -> MNSandbox::AutoRef<MNSandbox::SandboxNode> {
		auto ptr = ClientActorBaseLua::NewInstance();
		if (ptr)
		{
			ptr->LuaPluginMgr().BindLuaPluginByFile("sandboxengine/sandboxcore/actors/ActorExpandTemplate.lua");
			ptr->Event().Emit("OnInit");
		}
		return ptr.StaticToCast<MNSandbox::SandboxNode>();
		});
	factory->AddProduct("SpiderMob", [&]() -> MNSandbox::AutoRef<MNSandbox::SandboxNode> {
		auto ptr = ClientFlyMob::NewInstance();
		if (ptr)
		{
			ptr->LuaPluginMgr().BindLuaPluginByFile("sandboxengine/sandboxengine/sandboxcore/mobs/SpiderMob.lua");
			ptr->Event().Emit("OnInit");
		}
		return ptr.StaticToCast<MNSandbox::SandboxNode>();
		});
}

void SandboxActorSubsystem::InitComponentFactory(MNSandbox::SandboxFactoryNormal<ActorComponentBase>* factory)
{
	factory->AddProduct("ActorFollow", []() -> MNSandbox::AutoRef<ActorComponentBase> {
		return SANDBOX_NEW(ActorFollow);
		});
	factory->AddProduct("PlayerFollow", []() -> MNSandbox::AutoRef<ActorComponentBase> {
		return SANDBOX_NEW(PlayerFollow);
		});
	factory->AddProduct("ServerInterpolTick", []() -> MNSandbox::AutoRef<ActorComponentBase> {
		return SANDBOX_NEW(ServerInterpolTick);
		});
	factory->AddProduct("DialogueComponent", []() -> MNSandbox::AutoRef<ActorComponentBase> {
		return SANDBOX_NEW(DialogueComponent);
		});
	factory->AddProduct("PlotComponent", []() -> MNSandbox::AutoRef<ActorComponentBase> {
		return SANDBOX_NEW(PlotComponent);
		});

	factory->AddProduct("RevivePointComponent", []() -> MNSandbox::AutoRef<ActorComponentBase> {
		return SANDBOX_NEW(RevivePointComponent);
		});
	factory->AddProduct("CosumeAccumComponent", []() -> MNSandbox::AutoRef<ActorComponentBase> {
		auto comp = SANDBOX_NEW(CosumeAccumComponent);
		comp->LuaPluginMgr().BindLuaPluginByFile("sandboxengine/sandboxengine/sandboxcore/components/CosumeAccumComponent.lua");
		comp->Event().Emit("OnInit");
		return comp;
		});
	//factory->AddProduct("InWaterComponent", []() -> MNSandbox::AutoRef<ActorComponentBase> {
	//	return SANDBOX_NEW(InWaterComponent);
	//});
	factory->AddProduct("InWaterComponent_Horse", []() -> MNSandbox::AutoRef<ActorComponentBase> {
		return SANDBOX_NEW(InWaterComponent_Horse);
		});
	//factory->AddProduct("InWaterComponent_Player", []() -> MNSandbox::AutoRef<ActorComponentBase> {
	//	return SANDBOX_NEW(InWaterComponent_Player);
	//});
	factory->AddProduct("InWaterComponent_Player", []() -> MNSandbox::AutoRef<ActorComponentBase> {
		auto comp = SANDBOX_NEW(ActorComponentBase);
		comp->LuaPluginMgr().BindLuaPluginByFile("sandboxengine/sandboxengine/sandboxcore/components/InWaterComponent_player.lua");
		comp->Event().Emit("OnInit");
		return comp;
		});
	factory->AddProduct("SunHurtComponent", []() -> MNSandbox::AutoRef<ActorComponentBase> {
		return SANDBOX_NEW(SunHurtComponent);
		});
	factory->AddProduct("SunHurtComponentEffect", []() -> MNSandbox::AutoRef<ActorComponentBase> {
		auto comp = SANDBOX_NEW(SunHurtComponent);
		comp->SetCanPlayEffect(true);
		return comp;
		});
	factory->AddProduct("BlockEnvEffectsComponent_Actor", []() -> MNSandbox::AutoRef<ActorComponentBase> {
		return SANDBOX_NEW(BlockEnvEffectsComponent_Actor);
		});

	factory->AddProduct("SoundComponent", []() -> MNSandbox::AutoRef<ActorComponentBase> {
		return SANDBOX_NEW(SoundComponent);
		});
	factory->AddProduct("EffectComponent", []() -> MNSandbox::AutoRef<ActorComponentBase> {
		return SANDBOX_NEW(EffectComponent);
		});
	factory->AddProduct("PhysicsComponent", []() -> MNSandbox::AutoRef<ActorComponentBase> {
		return SANDBOX_NEW(PhysicsComponent);
		});
}

int SandboxActorSubsystem::TouchCtlCheckFlyArea(int x, int y, float scale)
{
	return CheckFlyArea(x, y, scale);
}
int SandboxActorSubsystem::GetAllPhysxItem()
{
	return g_all_physxitem;
}

std::string SandboxActorSubsystem::UGCGetInfo()
{
	return UGCActorBatchManager::GetInstancePtr()->GetInfo();
}
Rainbow::Entity* SandboxActorSubsystem::GetUGCEntity(Rainbow::Entity* entity)
{
	return dynamic_cast<Rainbow::UGCEntity*>(entity);
}

void SandboxActorSubsystem::BindHandModelForUGCEntity(CameraModel* model)
{
	Rainbow::UGCEntity* ugcEntity = static_cast<Rainbow::UGCEntity*>(model->m_ToolModel->getUGCEntity());
	if (ugcEntity)
	{
		MNSandbox::WeakRef<MNSandbox::Ref> self = ugcEntity->GetWeakRef();
		ugcEntity->FinishCallback([model, self](bool success) {
			if (!self || !success)
				return;

			if (!model->m_HandModel || !model->m_ToolModel)
				return;

			if (model->UsePrefabHandModel())
			{
				model->m_ToolModel->SetLayer(Rainbow::CustomGameLayerIndex::kLayerIndexCustom_FRONT_SCENE);
				model->m_HandModel->BindNodeToBone(model->m_ToolModel->GetTransform(), "d_item");
				model->m_ToolModel->SetPosition(Rainbow::Vector3f::zero);
				model->m_ToolModel->SetRotation(-90, 0, -90);
			}
			else
			{
				//这个主模型的layer先设置，会导致子模型，配件模型跟主模型不在一个layer上，看起来会错位，所以改为在回调里面绑定和设置layer
				model->m_ToolModel->SetLayer(Rainbow::CustomGameLayerIndex::kLayerIndexCustom_FRONT_SCENE);
			
				//主武器
				int mainToolSlot = 101;
				if (g_pPlayerCtrl && g_pPlayerCtrl->getCustomGunDef())
				{
					//新枪械在模型加载完之后绑定模型
					const CustomGunDef* cgunDef = g_pPlayerCtrl->getCustomGunDef();
					mainToolSlot = cgunDef->gunHandSlot == 1 ? 201 : mainToolSlot;
				}
				model->m_HandModel->AttachNodeToBone(model->m_ToolModel->GetTransform(), mainToolSlot);
			}

			if (model->m_ToolModel->IsKindOf<ModelItemMesh>())
			{
				ModelItemMesh* itemModelMesh = static_cast<ModelItemMesh*>(model->m_ToolModel);
				if (itemModelMesh)
				{
					itemModelMesh->SetShow(model->isShow());
					//就算看不见，也得给我播动画
					Rainbow::Model* itemModel = itemModelMesh->GetModel();
					if (itemModel && itemModel->IsKindOf<Rainbow::ModelNew>())
					{
						Rainbow::ModelNew* itemModelNew = static_cast<Rainbow::ModelNew*>(itemModel);
						if (itemModelNew)
							itemModelNew->SetAnimatorSetCullingMode(Rainbow::Animator::CullingMode::kCullNone);
					}
				}
			}

			model->SetWeaponModelIsLoading(false);
			});
	}
}
void SandboxActorSubsystem::SetModelAsync(Rainbow::Entity* entity, Rainbow::Model** model, bool isPrefab, float scale)
{
	auto ugcentity = dynamic_cast<Rainbow::UGCEntity*>(entity);

	MNSandbox::WeakRef<MNSandbox::Ref> self = ugcentity->GetWeakRef();
	ugcentity->FinishCallback([this, self, ugcentity,  model, isPrefab, scale](bool success) {
		if (!self || !success)
			return;

		*model = ugcentity->GetMainModel();
		if (*model)
		{
			if (isPrefab)
			{
				Rainbow::Vector3f realScale = (*model)->GetScale();
				realScale *= scale;
				(*model)->SetScale(realScale);
			}
			else
			{
				Rainbow::ColourValue cv(0.0f, 0.0f, 0.0f, 1.0f);
				(*model)->SetInstanceAmbient(cv);
				Rainbow::Vector3f realScale(scale, scale, scale);
				(*model)->SetScale(realScale);
			}
		}
		});
}
long long SandboxActorSubsystem::createProjectileWithShooter(int itemid, int posx, int posy, int posz, float dirx, float diry, float dirz, int speed, ClientActor* pActor, World* world)
{
	if (!world)
	{
		return -1;
	}
	if (speed < 0.0f)
		speed = (int)world->calcProjectileSpeed(itemid);
	if (speed < 0.0f)
		speed = 300;

	// 创建投掷物
	const ProjectileDef* projectileDef = GetDefManagerProxy()->getProjectileDef(itemid, true);
	if (!projectileDef) { return 0; }

	ClientActorProjectile* projectile = ProjectileFactory::createProjectile(projectileDef->ID, pActor);
	if (!projectile) { return 0; }

	if (pActor)
		projectile->setShootingActor(pActor); // 开发者需要在spawnActor之前知道shooter

	static_cast<ClientActorMgr*>(world->getActorMgr())->spawnActor(projectile, WCoord(posx, posy, posz), 0, 0);
	projectile->m_StartPos = projectile->getPosition();

	ProjectileLocoMotion* locomove = static_cast<ProjectileLocoMotion*>(projectile->getLocoMotion());
	locomove->setThrowableHeading(Rainbow::Vector3f(dirx, diry, dirz), (float)speed, 1.1f);

	projectile->playMotion(projectile->m_ProjectileDef->TailEffect.c_str());
	if (GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate)
	{
		// 近战、远程、元素伤害
		int damageType = projectile->m_ProjectileDef->DamageType;
		if (projectile->m_ProjectileDef->AttackType == 0)
		{
			if (damageType == 0 || damageType == 2 || damageType == 3)
			{
				assert(false && "Projectiledef damageType error!");
			}
			projectile->m_AttackPointsNew[damageType] = projectile->m_ProjectileDef->AttackValue;
			projectile->m_atkType = (1 << damageType);
		}
		// 独立爆炸伤害
		else
		{
			projectile->m_atkType = (1 << ATTACK_EXPLODE);
			if (damageType < 4)
			{
				projectile->m_ExplodePoints[0] = projectile->m_ProjectileDef->AttackValue;
			}
			else
			{
				projectile->m_ExplodePoints[damageType] = projectile->m_ProjectileDef->AttackValue;
			}
		}

		projectile->m_strength = 1.0f;
	}
	else
	{
		projectile->m_AttackPoints = projectile->m_ProjectileDef->AttackValue;
	}
	if (projectile->m_ProjectileDef->TriggerCondition == 4)
	{
		projectile->m_ImpactTimeMark = 0;
	}

	return projectile->getObjId();
}



void SandboxActorSubsystem::shootArrowAuto(int posx, int posy, int posz, float dirx, float diry, float dirz, int shooterObjIdHigh, int shooterObjIdLow, World* world)
{
	long long t = shooterObjIdHigh;
	long long shooterObjId = t << 32 | shooterObjIdLow;
	//ClientActorArrow::shootArrowAuto(this, WCoord(posx, posy, posz), Rainbow::Vector3f(dirx, diry, dirz), 300.0f, 1.1f, shooterObjId);
	ClientActorNewArrow::shootNewArrowAuto(world, WCoord(posx, posy, posz), Rainbow::Vector3f(dirx, diry, dirz), 300.0f, 1.1f, shooterObjId);
}

void SandboxActorSubsystem::shootImpulseAuto(int itemid, int posx, int posy, int posz, float dirx, float diry, float dirz, int shooterObjIdHigh, int shooterObjIdLow, World* world)
{
	float speed = GetDefManagerProxy()->getProjectileDef(itemid)->InitSpeed;
	long long t = shooterObjIdHigh;
	long long shooterObjId = t << 32 | shooterObjIdLow;
	ClientActorImpulse::shootImpulseAuto(itemid, world, WCoord(posx, posy, posz), Rainbow::Vector3f(dirx, diry, dirz), speed, 1.1f, shooterObjId);
}

void SandboxActorSubsystem::shootProjectileAuto(int itemid, int posx, int posy, int posz, float dirx, float diry, float dirz, int shooterObjIdHigh, int shooterObjIdLow, World* world, int durable, float speedRate)
{
	if (!world)
	{
		return;
	}
	float speed = world->calcProjectileSpeed(itemid);
	if (speed < 0.0f)
		speed = 300.0f;
	speed *= speedRate;
	long long t = shooterObjIdHigh;
	long long shooterObjId = t << 32 | shooterObjIdLow;
	ClientActorProjectile::shootProjectileAuto(itemid, world, WCoord(posx, posy, posz), Rainbow::Vector3f(dirx, diry, dirz), speed, 1.1f, shooterObjId, durable);
}

void SandboxActorSubsystem::throwItemAuto(int itemid, int posx, int posy, int posz, float dirx, float diry, float dirz, int shooterObjIdHigh, int shooterObjIdLow, World* world)
{
	long long t = shooterObjIdHigh;
	long long shooterObjId = t << 32 | shooterObjIdLow;
	ClientActorThrowable::throwItemAuto(world, itemid, WCoord(posx, posy, posz), Rainbow::Vector3f(dirx, diry, dirz), 300.0f, 1.1f, shooterObjId);
}

void SandboxActorSubsystem::createActorFirework(int posx, int posy, int posz, float dirx, float diry, float dirz, int firetype, int firedata, World* world)
{
	WCoord position = WCoord(posx, posy, posz); //WCoord(posx, posy, posz) + 50 * Rainbow::Vector3f(dirx, diry, dirz)
	::ActorFirework::create(world, position, firetype, firedata);
}
void SandboxActorSubsystem::dragonFlowerAttack(int angle, const WCoord& pos, World* world)
{
	if (!world || !world->getContainerMgr())
	{
		return;
	}
	DragonFlowerContainer* container = dynamic_cast<DragonFlowerContainer*>(world->getContainerMgr()->getContainer(pos));
	if (!container)
	{
		return;
	}
	container->playAttackAnim(angle);
}

void SandboxActorSubsystem::HandleLuaCurWorld(MINIW::ScriptVM* scriptvm, World* world)
{
	scriptvm->setUserTypePointer("CurWorld", "World", world);

	bool isnull = world ? false : true;
	scriptvm->callFunction("OverloadWorldFunction","b", isnull);
}

void SandboxActorSubsystem::getAllPlayers(std::vector<ClientPlayer*>& players)
{
	if (GetWorldManagerPtr())
	{
		std::vector<IClientPlayer*> iplayers;
		GetWorldManagerPtr()->getAllPlayers(iplayers);
		for (size_t i = 0; i < iplayers.size(); i++)
		{
			players.push_back(iplayers[i]->GetPlayer());
		}
	}
}
IActorBoss* SandboxActorSubsystem::CreateBossForTerrian(TerrType type, int defid, const WCoord& pos, World* world, int missionflags/* = 0*/, WorldMapData* mapdata/* = nullptr*/, int bossindex/* = 0*/)
{
	if (CHUNK_GEN_EARTH_CORE == type)
	{
		::ActorDragon* actor = ENG_NEW(::ActorDragon)();
		actor->init(defid);
		actor->setSpawnPoint(pos);
		if (mapdata && !mapdata->bosses.empty())
		{
			actor->loadBossData(&mapdata->bosses[0]);
		}
		else actor->resetMissionFlags(missionflags);

		WCoord pos = actor->getPosition();
		world->syncLoadChunk(CoordDivSection(pos.x), CoordDivSection(pos.z));
		world->getActorMgr()->ToCastMgr()->spawnBoss(actor);
		return actor;
	}
	else if (CHUNK_GEN_NORMAL == type)
	{
		if (defid == 3878)//羽蛇神id
		{
			::ActorFlySnakeGod* actor = ENG_NEW(::ActorFlySnakeGod)();
			actor->init(3878);
			actor->loadBossData(&mapdata->bosses[bossindex]);
			const WCoord& pos = actor->getPosition();
			world->syncLoadChunk(CoordDivSection(pos.x), CoordDivSection(pos.z));
			world->getActorMgr()->ToCastMgr()->spawnBoss(actor);
			return actor;
		}
		else if (defid == 3515)
		{
			ClientVacantBoss* actor = ENG_NEW(ClientVacantBoss)();
			actor->init(3515);
			actor->loadBossData(&mapdata->bosses[bossindex]);
			const WCoord& pos = actor->getPosition();
			world->syncLoadChunk(CoordDivSection(pos.x), CoordDivSection(pos.z));
			world->getActorMgr()->ToCastMgr()->spawnBoss(actor);
			actor->startAppeal_(false);
			return actor;
		}
		else if (defid == 3516)
		{
			ClientVacantBoss* actor = ENG_NEW(ClientVacantBoss)();
			actor->init(3516);
			actor->loadBossData(&mapdata->bosses[bossindex]);
			const WCoord& pos = actor->getPosition();
			world->syncLoadChunk(CoordDivSection(pos.x), CoordDivSection(pos.z));
			world->getActorMgr()->ToCastMgr()->spawnBoss(actor);
			actor->startAppeal_(false);
			return actor;
		}
	}
	else if (CHUNK_GEN_PLANET_SPACE == type)
	{
		if (defid == 3510)
		{
			::ActorHalfGiant* actor = ENG_NEW(::ActorHalfGiant)();
			actor->init(defid);

			if (mapdata && !mapdata->bosses.empty())
			{
				actor->loadBossData(&mapdata->bosses[0]);
			}
			else
			{
				actor->resetMissionFlags(missionflags);
				actor->setSpawnPoint(pos);
			}

			WCoord pos = actor->getPosition();
			world->syncLoadChunk(CoordDivSection(pos.x), CoordDivSection(pos.z));
			world->getActorMgr()->ToCastMgr()->spawnBoss(actor);
			return actor;
		}
		else if (defid == 3514)
		{
			::ActorGiant* actor = ENG_NEW(::ActorGiant)();
			actor->init(defid);

			if (mapdata && !mapdata->bosses.empty())
			{
				actor->loadBossData(&mapdata->bosses[0]);
			}
			else
			{
				actor->resetMissionFlags(missionflags);
				actor->setSpawnPoint(pos);
			}

			WCoord pos = actor->getPosition();
			world->syncLoadChunk(CoordDivSection(pos.x), CoordDivSection(pos.z));
			world->getActorMgr()->ToCastMgr()->spawnBoss(actor);
			return actor;
		}
	}
	else if (GOD_TEMPLE_BUILD == type)
	{
		::ActorFlySnakeGod* actor = ENG_NEW(::ActorFlySnakeGod);
		actor->init(3878);//altarPos*BLOCK_SIZE);
		actor->setSpawnPoint(pos + WCoord(0, 1, 0));

		const WCoord& pos = actor->getPosition();
		world->syncLoadChunk(CoordDivSection(pos.x), CoordDivSection(pos.z));
		world->getActorMgr()->ToCastMgr()->spawnBoss(actor);
		return actor;
	}
	return nullptr;
}

void SandboxActorSubsystem::PackPlayerAttrib(game::common::PB_PlayerBriefInfo* briefInfo, BRIEF_INFO_SYNC_TYPE type, IClientPlayer* iplayer)
{
	ClientPlayer* player = dynamic_cast<ClientPlayer*>(iplayer);
	PlayerAttrib& playerAttrib = *player->getPlayerAttrib();
	if (type & BIS_HP)
		briefInfo->set_hp(playerAttrib.getHP());
	if (type & BIS_HP_MAX)
		briefInfo->set_maxhp(playerAttrib.getMaxHP());
	if (type & BIS_HP_OVERFLOW)
		briefInfo->set_overflowhp(playerAttrib.getOverflowHP());

	if (type & BIS_STRENGTH)
		briefInfo->set_strength(playerAttrib.getStrength());
	if (type & BIS_STRENGTH_MAX)
		briefInfo->set_maxstrength(playerAttrib.getBasicMaxStrength());
	if (type & BIS_STRENGTH_OF)
		briefInfo->set_overflowstrength(playerAttrib.getBasicOverflowStrength());
	if (type & BIS_THIRST)
		briefInfo->set_thirst(playerAttrib.getThirst());
	if (type & BIS_THIRST_MAX)
		briefInfo->set_maxthirst(playerAttrib.getBasicMaxThirst());
	if (type & BIS_THIRST_OF)
		briefInfo->set_overflowthirst(playerAttrib.getBasicOverflowThirst());
}

void SandboxActorSubsystem::DoExplosion(World* world, IClientActor* pActor, const WCoord& pos, int explosionRadius, bool flaming, bool smoking, int dirmask, int damageType, const  std::string& soundPath, const std::string& effectPath)
{
	if (dirmask == 0)
	{
		ExplosionGeneral explosion(world, pActor, explosionRadius * BLOCK_SIZE, pos, flaming, smoking, damageType, soundPath, effectPath);
		explosion.doExplosionA();
		explosion.doExplosionB();
	}
	else
	{
		ExplosionDir explosion(world, pActor, explosionRadius * BLOCK_SIZE, pos, flaming, smoking, dirmask);
		explosion.doExplosionA();
		explosion.doExplosionB();
	}
}

void SandboxActorSubsystem::CreateExplosionNew(World* world, IClientActor* pActor, const WCoord& pos, int radius, bool upHalf, float atkValue, bool smoking, bool fromSkill)
{
	ExplosionGeneral explosion(world, pActor, radius * BLOCK_SIZE, pos, false, smoking, 0, "", "");
	explosion.SetNewAttack(upHalf, atkValue, fromSkill);
	explosion.doExplosionA();
	explosion.doExplosionB();
}
void SandboxActorSubsystem::SetEffectEnable(bool value)
{
	Rainbow::GetPlanarReflection().SetEffectEnable(value);
}

void SandboxActorSubsystem::DestroyPlanarReflection()
{
	Rainbow::GetPlanarReflection().Destroy();
	Rainbow::GetPlanarReflection().UnRegisterEvent();
}
void SandboxActorSubsystem::PlanarReflectionSetWorld(World* world)
{
	Rainbow::GetPlanarReflection().SetWorld(world);
}
void SandboxActorSubsystem::ModShowWithChunk(WorldContainer* container, bool value)
{
	::ContainerMod* pModContainer = static_cast<::ContainerMod*>(container);
	pModContainer->ShowWithChunk(!value);
}

void SandboxActorSubsystem::getAllNearPlayersUin(World* world, std::vector<int>& uinList, WCoord& pos, float range, bool isAll /*= false*/)
{
	auto ipactormgr = world->getActorMgr();
	unsigned short mapid = world->getCurMapID();
	auto pactormgr = ipactormgr ? ipactormgr->ToCastMgr() : nullptr;
	if (pactormgr)
	{
		if (isAll)
		{
			auto players = pactormgr->getAllPlayer();
			for (auto player : players)
			{
				uinList.push_back(player->getUin());
			}
		}
		else
		{
			std::vector<ClientPlayer*> players;
			auto fun = [](ClientActor* player, void* data)->bool
			{
				return (g_pPlayerCtrl != player) && (player->getCurMapID() == (*(unsigned short*)data));
			};
			WCoord searchPos = pos * BLOCK_SIZE;
			searchPos.x -= range / 2;
			searchPos.z -= range / 2;
			
			pactormgr->selectNearAllPlayers(players, searchPos, range, fun, &mapid);
			for (auto player : players)
			{
				uinList.push_back(player->getUin());
			}
		}
	}
}
BlockModBase* SandboxActorSubsystem::getModBlockMaterial(World* world, WCoord& pos)
{
	auto pmtl = world->getBlockMaterial(pos);
	if (!pmtl)
		return NULL;

	if (pmtl->BlockTypeId() == BlockMaterial::BlockType_Mod)
	{
		return static_cast<BlockModBase*>(pmtl);
	}
	return NULL;
}

int SandboxActorSubsystem::AddGridData(WorldStorageBox* box, int itemid, GridCopyData& copydata)
{
	int result = -1;

	ItemDef* def = GetDefManagerProxy()->getItemDef(itemid);
	if (def)
	{
		if (def->IsDefCustomGun)
		{
			std::vector<GridDataComponent*> components;
			GunGridDataComponent gunData;
			gunData.setGunId(itemid);
			gunData.GenModEntryByMade();
			gunData.RandomParam();
			components.push_back(&gunData);
			copydata.componentsPtr = &components;
			result = box->addItem_byGridCopyData(copydata);
		}
		else if (def->IsDefEquip)
		{
			std::vector<GridDataComponent*> components;
			EntryGridDataComponent entryData;
			entryData.setEquipId(itemid);
			entryData.GenModEntryByMade();
			components.push_back(&entryData);
			copydata.componentsPtr = &components;
			result = box->addItem_byGridCopyData(copydata);
		}
	}
	return result;
}

int SandboxActorSubsystem::AddGridData(ErosionStorageBox* box, int itemid, GridCopyData& copydata)
{
	int result = -1;

	ItemDef* def = GetDefManagerProxy()->getItemDef(itemid);
	if (def)
	{
		if (def->IsDefCustomGun)
		{
			std::vector<GridDataComponent*> components;
			GunGridDataComponent gunData;
			gunData.setGunId(itemid);
			gunData.GenModEntryByMade();
			gunData.RandomParam();
			components.push_back(&gunData);
			copydata.componentsPtr = &components;
			result = box->addItem_byGridCopyData(copydata);
		}
		else if (def->IsDefEquip)
		{
			std::vector<GridDataComponent*> components;
			EntryGridDataComponent entryData;
			entryData.setEquipId(itemid);
			entryData.GenModEntryByMade();
			components.push_back(&entryData);
			copydata.componentsPtr = &components;
			result = box->addItem_byGridCopyData(copydata);
		}
	}
	return result;
}

float SandboxActorSubsystem::GetGunDamage(GridDataComponent* gridcomp)
{
	GunGridDataComponent* comp = static_cast<GunGridDataComponent*>(gridcomp);
	if (!comp)
	{
		return 0.f;
	}

	CustomGunDef* def = comp->GetGunDef();
	if (!def)
		return 0.f;
	float baseDamage = 0.f;
	float baseDamageBonus = 0.f;
	comp->GetGunEntryAttrDamage(baseDamage, baseDamageBonus);
	baseDamage += def->baseDamage;
	baseDamageBonus += def->baseDamageBonus;
	baseDamageBonus = std::fmaxf(0, 1 + baseDamageBonus);
	baseDamage *= baseDamageBonus;
	return baseDamage;
}

GridDataComponent* SandboxActorSubsystem::CreateGridDataComponent(const std::string& type)
{
	if (0 == strcmp(type.c_str(), "gungrid"))
	{
		return ENG_NEW(GunGridDataComponent);
	}
	else if (0 == strcmp(type.c_str(), "entrygrid"))
	{
		return ENG_NEW(EntryGridDataComponent);
	}
	else
		return nullptr;
}